# 柴管家智能客服系统

<div align="center">

![柴管家Logo](docs/assets/logo.png)

**基于Chatwoot架构的AI原生多渠道聚合智能客服平台**

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Backend CI](https://github.com/chaiguanjia/chaiguanjia/workflows/后端CI%2FCD流水线/badge.svg)](https://github.com/chaiguanjia/chaiguanjia/actions)
[![Frontend CI](https://github.com/chaiguanjia/chaiguanjia/workflows/前端CI%2FCD流水线/badge.svg)](https://github.com/chaiguanjia/chaiguanjia/actions)
[![codecov](https://codecov.io/gh/chaiguanjia/chaiguanjia/branch/main/graph/badge.svg)](https://codecov.io/gh/chaiguanjia/chaiguanjia)

[English](README.en.md) | 简体中文

</div>

## 🌟 项目简介

柴管家是一个基于Chatwoot成熟架构，采用AI原生设计理念的智能客服系统。系统通过统一的API接口聚合多个社交平台和电商渠道，结合先进的人工智能技术，为用户提供高效、智能的客服解决方案。

### ✨ 核心特性

- 🔌 **多渠道聚合**：支持微信、抖音、小红书、闲鱼、知识星球等9个主流平台
- 🤖 **AI原生设计**：集成通义千问、GPT-4、Claude等多种AI模型
- 🔄 **智能路由**：基于置信度的智能模型路由和人机协作
- 📚 **知识增强**：RAG技术结合知识库，提升回复准确性
- 🔧 **工作流自动化**：可视化工作流设计，支持复杂业务逻辑
- 📊 **实时监控**：完善的监控和告警机制
- 🚀 **云原生架构**：支持Docker容器化和Kubernetes部署

### 🏗️ 技术架构

```mermaid
graph TB
    subgraph "前端层"
        A[React + TypeScript]
        B[shadcn/ui组件库]
        C[Zustand状态管理]
    end

    subgraph "API层"
        D[FastAPI + Python]
        E[WebSocket实时通信]
        F[RESTful API]
    end

    subgraph "业务层"
        G[平台适配器]
        H[AI智能服务]
        I[知识库管理]
        J[工作流引擎]
        K[人机协作]
    end

    subgraph "数据层"
        L[PostgreSQL]
        M[Redis缓存]
        N[RabbitMQ]
    end

    subgraph "外部服务"
        O[通义千问/GPT-4/Claude]
        P[微信/抖音/小红书等平台]
    end

    A --> D
    B --> D
    C --> D
    D --> G
    D --> H
    D --> I
    D --> J
    D --> K
    G --> L
    H --> M
    I --> L
    J --> N
    H --> O
    G --> P

    style A fill:#e3f2fd
    style H fill:#f3e5f5
    style O fill:#e8f5e8
    style P fill:#fff3e0
```

## 🚀 快速开始

### 环境要求

- **Python**: 3.11+
- **Node.js**: 18+
- **Docker**: 20.10+
- **PostgreSQL**: 15+
- **Redis**: 7+

### 一键安装

```bash
# 克隆项目
git clone https://github.com/chaiguanjia/chaiguanjia.git
cd chaiguanjia

# 自动安装依赖（使用国内镜像源）
./scripts/setup/install-deps.sh

# 启动开发环境
docker-compose up -d
```

### 手动安装

#### 1. 后端服务

```bash
cd backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖（使用清华源）
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip install -r requirements/development.txt

# 数据库迁移
alembic upgrade head

# 启动服务
python app/main.py
```

#### 2. 前端应用

```bash
cd frontend

# 配置npm镜像源
npm config set registry https://registry.npmmirror.com

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

#### 3. 访问系统

- 🌐 **前端界面**: <http://localhost:3000>
- 🔧 **API文档**: <http://localhost:8000/docs>
- 📊 **系统监控**: <http://localhost:8000/health>

## 📖 文档

- 📚 [完整文档](docs/README.md)
- 🏗️ [架构设计](docs/architecture/overview.md)
- 🔌 [API接口](docs/api/README.md)
- 🚀 [部署指南](docs/deployment/quick-start.md)
- 💻 [开发指南](docs/development/setup.md)

## 🔌 支持平台

| 平台 | 状态 | 功能支持 |
|------|------|----------|
| 微信 | ✅ 已支持 | 消息收发、群聊管理 |
| 抖音 | ✅ 已支持 | 私信管理、评论回复 |
| 小红书 | 🔄 开发中 | 私信管理、笔记评论 |
| 闲鱼 | ✅ 已支持 | 商品咨询、交易管理 |
| 知识星球 | 🔄 开发中 | 问答互动、内容管理 |
| QQ | 📋 计划中 | 消息收发 |
| 微博 | 📋 计划中 | 私信管理 |
| 钉钉 | 📋 计划中 | 企业通信 |
| 飞书 | 📋 计划中 | 企业协作 |

## 🤖 AI集成

### 支持的AI模型

- **通义千问**: 阿里云大语言模型，中文理解能力强
- **GPT-4**: OpenAI最新模型，推理能力出色
- **Claude**: Anthropic AI助手，安全性高
- **更多模型**: 支持自定义AI提供商接入

### 智能功能

- 🧠 **智能回复**: 基于上下文的自动回复生成
- 📊 **置信度评估**: 多维度评估AI回复质量
- 🔄 **模型路由**: 根据问题类型智能选择最优模型
- 👥 **人机协作**: AI处理简单问题，复杂问题转人工
- 📚 **知识增强**: RAG技术结合知识库提升准确性

## 📊 系统特性

### 性能指标

- ⚡ **响应时间**: API平均响应时间 < 200ms
- 🔄 **并发处理**: 支持10,000+并发连接
- 📈 **消息吞吐**: 每秒处理1,000+条消息
- 💾 **存储优化**: 智能数据压缩和缓存策略

### 安全保障

- 🔐 **数据加密**: 端到端数据加密传输
- 🛡️ **权限控制**: 基于角色的细粒度权限管理
- 🔍 **审计日志**: 完整的操作审计记录
- 🚨 **安全监控**: 实时安全威胁检测和告警

## 🧪 测试

```bash
# 运行所有测试
npm run test:all

# 后端测试
cd backend
pytest tests/ -v --cov=app

# 前端测试
cd frontend
npm run test
npm run test:e2e

# 性能测试
npm run test:performance
```

## 📈 监控和运维

### 系统监控

- 📊 **性能指标**: CPU、内存、网络、磁盘使用率
- 🔍 **应用监控**: API响应时间、错误率、并发数
- 🤖 **AI监控**: 模型响应时间、置信度分布、使用量
- 💬 **业务监控**: 消息处理量、用户活跃度、转化率

### 告警机制

- 📧 **邮件告警**: 系统异常邮件通知
- 💬 **微信告警**: 关键问题微信群通知
- 📱 **钉钉告警**: 企业级钉钉机器人推送
- 🔔 **Slack告警**: 国际化团队Slack通知

## 🤝 贡献指南

我们欢迎所有形式的贡献！请阅读 [贡献指南](docs/development/contributing.md) 了解详情。

### 贡献方式

- 🐛 [报告Bug](https://github.com/chaiguanjia/chaiguanjia/issues/new?template=bug_report.md)
- ✨ [功能建议](https://github.com/chaiguanjia/chaiguanjia/issues/new?template=feature_request.md)
- 📚 [文档改进](https://github.com/chaiguanjia/chaiguanjia/issues/new?template=documentation.md)
- 💻 [提交代码](https://github.com/chaiguanjia/chaiguanjia/pulls)

## 📄 开源协议

本项目基于 [MIT协议](LICENSE) 开源。

## 🎯 路线图

### 第一阶段 (2024 Q1)

- [x] 基础架构搭建
- [x] 微信、闲鱼适配器
- [x] AI服务集成
- [ ] 基础Web界面

### 第二阶段 (2024 Q2)

- [ ] 抖音、小红书适配器
- [ ] 知识库RAG功能
- [ ] 工作流自动化
- [ ] 移动端支持

### 第三阶段 (2024 Q3)

- [ ] 更多平台适配器
- [ ] 高级AI功能
- [ ] 企业级特性
- [ ] 私有化部署

## 💬 社区支持

- 📧 **邮箱**: <<EMAIL>>
- 💬 **微信群**: [加入讨论群](https://example.com/wechat)
- 🐛 **GitHub Issues**: [问题反馈](https://github.com/chaiguanjia/chaiguanjia/issues)
- 📖 **文档中心**: [在线文档](https://docs.chaiguanjia.com)

## ⭐ 致谢

感谢以下开源项目的支持：

- [Chatwoot](https://github.com/chatwoot/chatwoot) - 提供了优秀的多渠道客服架构
- [FastAPI](https://github.com/tiangolo/fastapi) - 现代化的Python Web框架
- [React](https://github.com/facebook/react) - 用户界面构建库
- [shadcn/ui](https://github.com/shadcn-ui/ui) - 美观的React组件库

---

<div align="center">

**如果这个项目对您有帮助，请考虑给我们一个 ⭐**

Made with ❤️ by [柴管家团队](https://github.com/chaiguanjia)

</div>
