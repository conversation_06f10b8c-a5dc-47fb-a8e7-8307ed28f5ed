# 柴管家项目 Git 忽略配置
# 确保敏感信息和临时文件不被提交到版本控制

# ==========================================
# 环境变量和配置文件
# ==========================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# 密钥和证书文件
*.key
*.pem
*.crt
*.cert
*.p12
*.pfx
secrets/
certificates/

# ==========================================
# Python 相关
# ==========================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff
instance/
.webassets-cache

# Scrapy stuff
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# ==========================================
# Node.js / Frontend 相关
# ==========================================
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ==========================================
# 数据库相关
# ==========================================
# SQLite 数据库
*.db
*.sqlite
*.sqlite3

# PostgreSQL 备份文件
*.sql
*.dump

# MySQL 备份文件
*.sql.gz

# ==========================================
# Docker 相关
# ==========================================
# 忽略某些容器运行时文件
.dockerignore

# ==========================================
# 编辑器和IDE
# ==========================================
# VSCode
.vscode/settings.json.user
.vscode/launch.json.user
.vscode/tasks.json.user

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# Atom
.atom/

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# ==========================================
# 操作系统相关
# ==========================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==========================================
# 项目特定文件
# ==========================================
# 日志文件
logs/
*.log
*.log.*

# 临时文件
tmp/
temp/
.tmp/
.temp/

# 备份文件
backups/
*.backup
*.bak

# 上传文件目录
uploads/
media/
static/media/

# 缓存目录
.cache/
cache/

# 构建产物
build/
dist/
out/

# 测试产物
.coverage
coverage/
test-results/
test-reports/

# 监控和性能分析
.monitoring/
.profiling/
*.prof

# AI 模型文件（通常很大）
*.model
*.pkl
*.h5
*.pb
models/
checkpoints/

# ==========================================
# 云服务和部署相关
# ==========================================
# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# AWS
.aws/

# Azure
.azure/

# Google Cloud
.gcloud/

# Kubernetes
*.kubeconfig

# ==========================================
# 开发工具缓存
# ==========================================
# Webpack
.webpack/

# Parcel
.parcel-cache/

# Vite
.vite/

# Turbo
.turbo/

# Rush
common/temp/

# ==========================================
# 其他
# ==========================================
# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 系统文件
*.sys
*.dll

# 临时文件
*.tmp
*.temp

# 锁文件（根据需要调整）
package-lock.json
yarn.lock
pnpm-lock.yaml
