name: 前端CI/CD流水线

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'frontend/**'
      - '.github/workflows/frontend-ci.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'frontend/**'

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        registry-url: 'https://registry.npmmirror.com'

    - name: 缓存npm依赖
      uses: actions/cache@v3
      with:
        path: ~/.npm
        key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-node-

    - name: 安装依赖
      run: |
        cd frontend
        npm config set registry https://registry.npmmirror.com
        npm ci

    - name: 代码格式检查
      run: |
        cd frontend
        npm run lint

    - name: TypeScript类型检查
      run: |
        cd frontend
        npx tsc --noEmit

    - name: 运行单元测试
      run: |
        cd frontend
        npm run test:coverage

    - name: 构建应用
      run: |
        cd frontend
        npm run build

    - name: 上传构建产物
      uses: actions/upload-artifact@v3
      with:
        name: frontend-build-${{ matrix.node-version }}
        path: frontend/dist/

    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        directory: ./frontend/coverage
        flags: frontend
        name: frontend-coverage

  e2e-test:
    runs-on: ubuntu-latest
    needs: test

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        registry-url: 'https://registry.npmmirror.com'

    - name: 安装依赖
      run: |
        cd frontend
        npm config set registry https://registry.npmmirror.com
        npm ci

    - name: 安装Playwright
      run: |
        cd frontend
        npx playwright install --with-deps

    - name: 运行E2E测试
      run: |
        cd frontend
        npm run test:e2e

    - name: 上传测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: frontend/playwright-report/

  build:
    runs-on: ubuntu-latest
    needs: [test, e2e-test]
    if: github.ref == 'refs/heads/main'

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: 登录到Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: 构建并推送Docker镜像
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: true
        tags: |
          ghcr.io/${{ github.repository }}/frontend:latest
          ghcr.io/${{ github.repository }}/frontend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
