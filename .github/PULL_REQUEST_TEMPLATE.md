# Pull Request

## 📋 变更描述

请详细描述本次PR的变更内容：

- [ ] 新功能开发
- [ ] Bug修复
- [ ] 代码重构
- [ ] 文档更新
- [ ] 测试改进
- [ ] 性能优化
- [ ] 其他：_____

## 🔗 相关Issue

Closes #(issue编号)

## 📝 变更详情

### 新增功能
- 

### 修复问题
- 

### 破坏性变更
- [ ] 是，有破坏性变更
- [ ] 否，无破坏性变更

如果有破坏性变更，请详细说明：

## 🧪 测试

- [ ] 添加了新的单元测试
- [ ] 添加了新的集成测试
- [ ] 更新了现有测试
- [ ] 所有测试通过
- [ ] 手动测试通过

### 测试说明
请描述如何测试这些变更：

1. 
2. 
3. 

## 📱 前端变更（如适用）

- [ ] UI/UX变更
- [ ] 新增页面/组件
- [ ] API接口变更
- [ ] 响应式设计

### 截图/录屏
如有UI变更，请提供截图或录屏：

## 🔧 后端变更（如适用）

- [ ] 数据库schema变更
- [ ] API端点变更
- [ ] 新增依赖包
- [ ] 配置文件变更

### 数据库迁移
- [ ] 需要数据库迁移
- [ ] 不需要数据库迁移

## 📚 文档

- [ ] 更新了相关文档
- [ ] 更新了API文档
- [ ] 更新了README
- [ ] 不需要文档更新

## ✅ 检查清单

请确认以下各项：

- [ ] 代码符合项目编码规范
- [ ] 提交信息清晰明确
- [ ] 已添加必要的测试
- [ ] 所有测试通过
- [ ] 代码已通过lint检查
- [ ] 已更新相关文档
- [ ] 无安全隐患
- [ ] 性能影响已评估

## 👥 审查者

请@相关团队成员进行代码审查：

- @后端团队：
- @前端团队：
- @测试团队：

## 📋 额外信息

其他需要审查者注意的信息：
