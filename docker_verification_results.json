{"docker_services": {"elasticsearch_status": {"success": true, "details": "运行正常: running", "timestamp": **********.5658638}, "nginx_status": {"success": true, "details": "运行正常: running", "timestamp": **********.56589}, "postgresql_status": {"success": true, "details": "运行正常: running", "timestamp": **********.5658932}, "rabbitmq_status": {"success": true, "details": "运行正常: running", "timestamp": **********.5658948}, "redis_status": {"success": true, "details": "运行正常: running", "timestamp": **********.565897}}, "postgresql": {"connection": {"success": true, "details": "数据库连接正常", "timestamp": **********.629925}, "query": {"success": true, "details": "SQL查询正常", "timestamp": **********.686719}, "chinese_support": {"success": true, "details": "中文支持正常", "timestamp": **********.742883}}, "redis": {"connection": {"success": true, "details": "Redis连接正常", "timestamp": **********.796225}, "read_write": {"success": true, "details": "读写操作正常", "timestamp": **********.911425}, "chinese_support": {"success": true, "details": "中文支持正常", "timestamp": **********.067792}}, "rabbitmq": {"status": {"success": true, "details": "RabbitMQ状态正常", "timestamp": **********.414439}, "vhost": {"success": true, "details": "虚拟主机配置正常", "timestamp": **********.64912}, "user": {"success": true, "details": "用户配置正常", "timestamp": **********.894542}}, "elasticsearch": {"health": {"success": true, "details": "集群状态: green", "timestamp": **********.950165}, "index_create": {"success": true, "details": "索引创建正常", "timestamp": **********.0968058}}, "nginx": {"config": {"success": true, "details": "配置文件正常", "timestamp": **********.237342}, "process": {"success": true, "details": "Nginx进程运行正常", "timestamp": **********.321503}}}