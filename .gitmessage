# 柴管家项目 Git 提交信息模板
# 使用方法: git config commit.template .gitmessage

# ==========================================
# 提交信息格式规范
# ==========================================
# <类型>(<范围>): <简短描述>
#
# <详细描述>
#
# <底部信息>

# ==========================================
# 类型说明 (必须)
# ==========================================
# feat:     新功能
# fix:      修复bug
# docs:     文档更新
# style:    代码格式调整（不影响功能）
# refactor: 重构代码（既不是新功能也不是修复bug）
# test:     添加或修改测试
# chore:    构建工具、依赖管理等
# perf:     性能优化
# ci:       CI/CD 相关
# build:    构建系统或外部依赖的更改
# revert:   回滚之前的提交

# ==========================================
# 范围说明 (可选)
# ==========================================
# backend:  后端相关
# frontend: 前端相关
# api:      API 接口
# auth:     认证授权
# db:       数据库
# docker:   容器化
# config:   配置文件
# deps:     依赖管理
# ui:       用户界面
# tests:    测试相关
# docs:     文档相关
# deploy:   部署相关

# ==========================================
# 示例
# ==========================================
# feat(auth): 添加JWT令牌认证功能
#
# - 实现用户登录和注册接口
# - 添加JWT令牌生成和验证
# - 更新API权限验证中间件
#
# Closes #123

# fix(db): 修复用户表查询性能问题
#
# - 为user表的email字段添加索引
# - 优化用户查询SQL语句
# - 修复N+1查询问题
#
# Fixes #456

# docs(api): 更新API文档和示例
#
# - 添加认证接口文档
# - 更新错误响应格式说明
# - 补充请求示例代码

# ==========================================
# 提交信息规范要求
# ==========================================
# 1. 第一行不超过50个字符
# 2. 第二行必须是空行
# 3. 详细描述每行不超过72个字符
# 4. 使用现在时态（"添加"而不是"添加了"）
# 5. 首字母小写（除非是专有名词）
# 6. 结尾不加句号
# 7. 如果解决了issue，在底部添加 "Closes #123" 或 "Fixes #123"

# ==========================================
# 底部信息格式
# ==========================================
# Closes #123, #456      # 关闭issue
# Fixes #789             # 修复issue
# Refs #123              # 引用issue
# Co-authored-by: 姓名 <邮箱>  # 共同作者
# Reviewed-by: 姓名 <邮箱>     # 代码审查者
# Tested-by: 姓名 <邮箱>       # 测试者

# ==========================================
# 破坏性更改标记
# ==========================================
# 如果有破坏性更改，在类型后添加 !
# 例如: feat(api)!: 重构用户API接口
# 并在描述中详细说明破坏性更改
