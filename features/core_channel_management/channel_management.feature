Feature: 渠道管理功能
  作为一个IP运营者
  我希望能够管理已连接的渠道账号
  以便更好地组织和识别不同的账号

  Background:
    Given 用户已登录柴管家系统
    And 系统中存在以下已连接的渠道：
      | 平台 | 账号名称 | 别名 | 状态 | 连接时间 | Cookie状态 |
      | 闲鱼 | xianyu_shop_123 | 主店铺 | 已连接 | 2025-08-01 10:30:00 | 有效 |
      | 闲鱼 | xianyu_shop_456 | 客服店铺 | 已连接 | 2025-08-02 14:20:00 | 有效 |
      | 抖音 | dy_789 | 官方号 | 已连接 | 2025-08-03 09:15:00 | N/A |

  Scenario: 设置渠道别名
    Given 用户在渠道列表中找到账号"xianyu_shop_123"
    When 用户点击"编辑"按钮
    And 在别名字段输入"业务咨询店铺"
    And 点击"保存"按钮
    Then 系统应该显示"保存成功"提示
    And 渠道列表中该账号的别名应该更新为"业务咨询店铺"
    And 系统应该记录别名修改的操作日志
    And 别名修改应该立即在所有相关界面生效

  Scenario: 别名输入验证
    Given 用户在渠道列表中找到账号"xianyu_shop_123"
    When 用户点击"编辑"按钮
    And 在别名字段输入空白内容
    And 点击"保存"按钮
    Then 系统应该显示"别名不能为空"错误提示
    And 别名字段应该保持原有值
    And 用户应该能够重新输入有效别名

  Scenario: 软删除渠道连接
    Given 用户在渠道列表中找到账号"xianyu_shop_456"
    When 用户点击"删除"按钮
    And 在确认对话框中点击"确定"
    Then 系统应该显示"删除成功"提示
    And 该账号应该从主渠道列表中移除
    And 该账号在数据库中应该被标记为"已删除"状态
    And 相关的消息历史应该保留但标记为已删除
    And 系统应该停止该渠道的消息监听服务
    And Cookie配置应该保留但标记为已删除
    And 该渠道不应该出现在消息页面中
    And 该渠道不应该出现在数据分析页面中
    And 该渠道不应该计入任何统计报表中
    And 软删除操作应该记录到操作日志中

  Scenario: 取消删除操作
    Given 用户在渠道列表中找到账号"xianyu_shop_456"
    When 用户点击"删除"按钮
    And 在确认对话框中点击"取消"
    Then 确认对话框应该关闭
    And 该账号应该仍然存在于渠道列表中
    And 账号状态保持不变
    And Cookie配置保持不变
    And 不应该有任何数据被修改

  Scenario: 查看历史渠道
    Given 用户在渠道管理页面
    And 系统中存在已软删除的渠道"xianyu_shop_789"
    When 用户点击"历史渠道"按钮
    Then 系统应该显示历史渠道页面
    And 页面应该显示所有被软删除的渠道列表
    And 列表应该包含以下信息：
      | 字段 | 描述 |
      | 平台类型 | 闲鱼 |
      | 账号ID | xianyu_shop_789 |
      | 原别名 | 测试店铺 |
      | 删除时间 | 2025-08-02 16:30:00 |
      | 删除原因 | 用户主动删除 |
    And 每个渠道应该显示"恢复"和"彻底删除"按钮
    And 页面应该显示"这些渠道已被删除，不会出现在数据统计中"的提示

  Scenario: 恢复已删除渠道
    Given 用户在历史渠道页面
    And 页面显示已删除的渠道"xianyu_shop_789"
    When 用户点击该渠道的"恢复"按钮
    Then 系统应该显示恢复确认对话框
    And 对话框应该显示提示文案："恢复后该渠道的数据将重新汇入数据大盘，包括消息记录、统计分析等，确认恢复吗？"
    And 对话框应该提供"确认"和"取消"按钮
    When 用户点击"确认"按钮
    Then 系统应该显示"恢复成功"提示
    And 该渠道应该从历史渠道列表中移除
    And 该渠道应该重新出现在主渠道列表中
    And 渠道状态应该恢复为"已连接"
    And Cookie配置应该恢复为可用状态
    And 系统应该重新启动该渠道的消息监听服务
    And 该渠道的消息历史应该重新计入统计
    And 该渠道应该重新出现在数据分析页面中
    And 恢复操作应该记录到操作日志中

  Scenario: 取消恢复操作
    Given 用户在历史渠道页面
    And 页面显示已删除的渠道"xianyu_shop_789"
    When 用户点击该渠道的"恢复"按钮
    And 在恢复确认对话框中点击"取消"
    Then 确认对话框应该关闭
    And 该渠道应该仍然存在于历史渠道列表中
    And 渠道状态保持"已删除"
    And 不应该有任何数据被修改

  Scenario: 彻底删除渠道
    Given 用户在历史渠道页面
    And 页面显示已删除的渠道"xianyu_shop_789"
    When 用户点击该渠道的"彻底删除"按钮
    Then 系统应该显示红色警告对话框
    And 对话框应该显示警告文案："警告：将彻底删除该渠道及其所有数据，包括消息记录、配置信息等，此操作不可恢复，请谨慎操作！"
    And 对话框应该提供红色的"确认删除"和"取消"按钮
    When 用户点击"确认删除"按钮
    Then 系统应该显示"彻底删除成功"提示
    And 该渠道应该从历史渠道列表中移除
    And 该渠道的所有数据应该从数据库中物理删除
    And 相关的消息历史应该被物理删除
    And 存储的Cookie信息应该被安全清理
    And 所有相关的配置信息应该被清理
    And 彻底删除操作应该记录到操作日志中

  Scenario: 取消彻底删除操作
    Given 用户在历史渠道页面
    And 页面显示已删除的渠道"xianyu_shop_789"
    When 用户点击该渠道的"彻底删除"按钮
    And 在警告对话框中点击"取消"
    Then 警告对话框应该关闭
    And 该渠道应该仍然存在于历史渠道列表中
    And 渠道状态保持"已删除"
    And 不应该有任何数据被修改

  Scenario: 查看渠道详细信息
    Given 用户在渠道列表中找到账号"xianyu_shop_123"
    When 用户点击账号名称
    Then 系统应该显示渠道详情页面
    And 页面应该包含以下信息：
      | 字段 | 值 |
      | 平台类型 | 闲鱼 |
      | 账号ID | xianyu_shop_123 |
      | 别名 | 主店铺 |
      | 连接时间 | 2025-08-01 10:30:00 |
      | 最后活跃时间 | 2025-08-03 15:45:00 |
      | 消息统计 | 今日收到消息: 25条 |
      | 连接状态 | 正常 |
      | Cookie状态 | 有效 |
      | 上次Cookie更新 | 2025-08-01 10:30:00 |


  Scenario: 渠道排序和筛选
    Given 渠道列表中有多个不同平台的账号
    When 用户点击"平台"列标题
    Then 渠道列表应该按平台类型排序
    When 用户选择"只显示闲鱼"筛选条件
    Then 列表应该只显示闲鱼平台的账号
    And 筛选条件应该在页面刷新后保持

  Scenario: 渠道状态切换
    Given 账号"xianyu_shop_123"当前状态为"已连接"
    When 用户点击"暂停"按钮
    Then 账号状态应该变更为"已暂停"
    And 系统应该停止该渠道的消息接收
    And 状态变更应该有确认提示
    When 用户再次点击"启用"按钮
    Then 账号状态应该恢复为"已连接"
    And 系统应该重新开始消息接收
    And 系统应该验证Cookie是否仍然有效


  Scenario: 验证软删除渠道在主列表中隐藏
    Given 系统中存在正常渠道"xianyu_shop_123"
    And 系统中存在已软删除的渠道"xianyu_shop_456"
    When 用户访问主渠道列表页面
    Then 列表应该显示渠道"xianyu_shop_123"
    And 列表不应该显示渠道"xianyu_shop_456"
    And 渠道总数统计不应该包含已删除渠道
    And 筛选和搜索功能不应该返回已删除渠道

  Scenario: 验证软删除渠道在数据统计中隐藏
    Given 系统中存在正常渠道"xianyu_shop_123"，今日消息数为25条
    And 系统中存在已软删除的渠道"xianyu_shop_456"，今日消息数为15条
    When 用户查看数据分析页面
    Then 今日总消息数应该显示为25条
    And 不应该包含已删除渠道的15条消息
    And 渠道数量统计应该显示为1个
    And 所有图表和报表都不应该包含已删除渠道的数据

  Scenario: 验证软删除渠道在消息页面中隐藏
    Given 系统中存在正常渠道"xianyu_shop_123"
    And 系统中存在已软删除的渠道"xianyu_shop_456"
    When 用户访问消息页面
    Then 渠道选择下拉框应该显示"xianyu_shop_123"
    And 渠道选择下拉框不应该显示"xianyu_shop_456"
    And 消息列表不应该显示来自已删除渠道的消息
    And 消息统计不应该包含已删除渠道的数据

  Scenario: 历史渠道页面访问权限
    Given 用户具有渠道管理权限
    When 用户访问渠道管理页面
    Then 应该能够看到"历史渠道"按钮
    When 用户点击"历史渠道"按钮
    Then 应该能够成功访问历史渠道页面
    And 页面应该显示"已删除渠道管理"标题
    And 页面应该显示返回主列表的链接

  Scenario: 软删除渠道的Cookie配置保留验证
    Given 闲鱼渠道"xianyu_shop_456"已配置有效Cookie
    When 用户软删除该渠道
    Then Cookie配置应该在数据库中保留
    And Cookie状态应该标记为"已删除"
    When 用户从历史渠道恢复该渠道
    Then Cookie配置应该完整恢复
    And Cookie状态应该恢复为原有状态
    And 系统应该重新验证Cookie有效性
