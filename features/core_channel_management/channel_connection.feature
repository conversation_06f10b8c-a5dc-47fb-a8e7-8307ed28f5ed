Feature: 渠道连接管理
  作为一个IP运营者
  我希望能够连接和管理多个社交平台账号
  以便统一处理来自不同平台的消息

  Background:
    Given 用户已登录柴管家系统
    And 用户位于渠道管理页面

  Scenario: 成功连接闲鱼账号
    Given 用户点击"添加渠道"按钮
    And 选择"闲鱼"平台
    When 用户输入有效的闲鱼Cookie信息
    And 系统验证Cookie有效性成功
    Then 系统应该显示"连接成功"提示
    And 渠道列表中应该显示新添加的闲鱼账号
    And 账号状态应该显示为"已连接"
    And 系统应该记录连接时间戳
    And Cookie配置应该被安全加密保存
    And 系统应该初始化该渠道的消息监听服务

  Scenario: Cookie输入方式 - 手动输入
    Given 用户点击"添加渠道"按钮
    And 选择"闲鱼"平台
    When 用户选择"手动输入Cookie"方式
    And 在文本框中粘贴Cookie字符串
    And 点击"验证Cookie"按钮
    Then 系统应该解析Cookie格式
    And 验证必要的Cookie字段是否完整
    And 显示Cookie验证结果

  Scenario: 连接失败 - Cookie无效
    Given 用户点击"添加渠道"按钮
    And 选择"闲鱼"平台
    When 用户输入无效或格式错误的Cookie信息
    And 系统验证Cookie失败
    Then 系统应该显示"Cookie无效或格式错误"错误提示
    And 应该提供Cookie获取指导链接
    And 用户应该能够重新输入Cookie
    And 系统应该清理无效的Cookie数据

  Scenario: 重复连接同一账号
    Given 系统中已存在一个闲鱼账号"测试闲鱼店铺A"
    And 用户点击"添加渠道"按钮
    And 选择"闲鱼"平台
    When 用户尝试连接相同的闲鱼账号
    Then 系统应该显示"该账号已存在"错误提示
    And 不应该创建重复的渠道记录
    And 系统应该显示现有账号的详细信息
    And 用户应该能够选择"查看现有账号"或"连接其他账号"


  Scenario: 网络异常时的连接处理
    Given 用户点击"添加渠道"按钮
    And 选择"闲鱼"平台
    And 网络连接不稳定
    When 用户尝试验证Cookie
    Then 系统应该显示"网络连接异常，请检查网络后重试"
    And 系统应该提供"重试"按钮
    And 用户点击"重试"后应该重新验证Cookie

  Scenario: 多平台账号连接
    Given 用户已成功连接一个闲鱼账号
    When 用户继续添加抖音账号
    And 完成抖音平台的授权流程
    Then 系统应该显示两个不同平台的账号
    And 每个账号应该有独立的状态显示
    And 系统应该能够同时监听两个平台的消息

  Scenario: Cookie权限验证
    Given 用户点击"添加渠道"按钮
    And 选择"闲鱼"平台
    When 用户输入的Cookie缺少必要的权限信息
    Then 系统应该显示"Cookie权限不足"错误提示
    And 系统应该说明所需的具体Cookie字段
    And 用户应该能够查看Cookie获取指南

  Scenario: Cookie失效自动检测
    Given 用户已连接闲鱼账号并正常使用
    When 闲鱼平台Cookie突然失效
    And 系统尝试访问闲鱼API时收到认证错误
    Then 系统应该自动检测到Cookie失效
    And 立即更新渠道状态为"连接异常"
    And 向用户发送Cookie失效通知
    And 暂停该渠道的所有自动化操作

  Scenario: 连接状态实时更新
    Given 用户正在配置闲鱼账号Cookie
    When Cookie验证状态发生变化
    Then 页面应该实时更新连接状态
    And 不需要用户手动刷新页面
    And 状态变化应该有明确的视觉反馈
    And Cookie验证进度应该实时显示

  Scenario: 连接历史记录
    Given 用户已完成多次渠道连接操作
    When 用户查看连接历史
    Then 系统应该显示所有连接尝试的记录
    And 记录应该包含时间、平台、状态、结果
    And Cookie相关的操作应该有特殊标记
    And 用户应该能够筛选和搜索历史记录
    And 失败的连接应该显示详细的错误信息

  Scenario: Cookie安全管理
    Given 用户已成功连接闲鱼账号
    When 系统存储Cookie信息
    Then Cookie应该使用AES加密存储
    And 敏感信息不应该出现在日志中
    And 系统应该定期清理过期的Cookie备份
    And 用户应该能够查看Cookie的脱敏信息
