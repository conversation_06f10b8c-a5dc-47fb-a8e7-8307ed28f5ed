Feature: 渠道状态监控
  作为一个IP运营者
  我希望能够实时监控渠道连接状态
  以便及时发现和处理连接异常

  Background:
    Given 用户已登录柴管家系统
    And 系统中存在多个已连接的渠道
    And 监控服务正在运行

  Scenario: 正常状态监控
    Given 所有渠道连接正常
    When 用户访问渠道监控页面
    Then 所有渠道状态应该显示为"正常"
    And 状态指示灯应该显示为绿色
    And 最后检查时间应该显示为当前时间
    And 系统应该显示"所有渠道运行正常"的总体状态

  Scenario: 检测到Cookie失效
    Given 闲鱼渠道"xianyu_shop_123"的Cookie已失效
    When 系统进行状态检查
    Then 该渠道状态应该显示为"Cookie失效"
    And 状态指示灯应该显示为红色
    And 系统应该发送Cookie失效通知给用户
    And 异常详情应该显示"Cookie已过期，需要重新配置"
    And 系统应该记录Cookie失效的时间和原因
    And 系统应该暂停该渠道的所有自动化操作

  Scenario: 自动重连机制 - 网络异常
    Given 闲鱼渠道"xianyu_shop_123"出现网络连接中断
    When 系统检测到连接异常
    Then 系统应该自动尝试重新连接
    And 重连状态应该显示为"重连中"
    And 状态指示灯应该显示为黄色
    And 系统应该显示重连进度
    When 重连成功后
    Then 渠道状态应该恢复为"正常"
    And 系统应该记录重连成功日志
    And 用户应该收到"重连成功"通知

  Scenario: 重连失败处理 - Cookie问题
    Given 闲鱼渠道"xianyu_shop_123"连接中断
    And 自动重连尝试3次均失败
    And 失败原因为Cookie无效
    When 系统完成重连尝试
    Then 渠道状态应该显示为"需要更新Cookie"
    And 状态指示灯应该显示为红色
    And 系统应该发送"Cookie需要更新"通知
    And 用户应该能够看到"更新Cookie"按钮
    And 系统应该显示"Cookie认证失败"的具体原因

  Scenario: 手动重连操作 - Cookie更新
    Given 闲鱼渠道"xianyu_shop_123"状态为"Cookie失效"
    When 用户点击"更新Cookie"按钮
    Then 系统应该显示Cookie更新对话框
    And 保留原有的渠道配置信息
    When 用户输入新的有效Cookie
    And 点击"验证并保存"
    Then 系统应该验证Cookie有效性
    And 渠道状态应该恢复为"正常"
    And 系统应该显示"Cookie更新成功"提示
    And Cookie更新操作应该被记录到日志中


  Scenario: 监控数据统计 - 包含Cookie指标
    Given 系统收集了一周的监控数据
    When 用户查看统计报告
    Then 系统应该显示以下统计信息：
      | 指标 | 描述 |
      | 总体可用性 | 99.5% |
      | 平均响应时间 | 2.3秒 |
      | 异常次数 | 3次 |
      | 自动恢复次数 | 2次 |
      | 手动干预次数 | 1次 |
      | Cookie更新次数 | 5次 |
