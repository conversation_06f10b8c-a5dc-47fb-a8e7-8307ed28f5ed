Feature: 用户登录
  As a 已注册的IP运营者
  I want 能够通过验证码或密码快速登录系统
  So that 安全便捷地访问柴管家的各项功能

  Background:
    Given 用户访问登录页面
    And 手机号 "13800138000" 已经注册

  # 快乐路径场景 - 验证码登录
  Scenario: 使用验证码成功登录
    Given 用户在登录页面
    And 登录方式默认为验证码登录
    When 用户输入已注册手机号 "13800138000"
    And 用户点击获取验证码按钮
    And 用户输入正确的验证码 "123456"
    And 用户点击登录按钮
    Then 系统应该建立用户会话
    And 系统应该记录登录信息
    And 用户应该跳转到工作台主界面

  # 快乐路径场景 - 密码登录
  Scenario: 使用密码成功登录
    Given 用户在登录页面
    When 用户切换到密码登录方式
    And 用户输入已注册手机号 "13800138000"
    And 用户输入正确密码 "password123"
    And 用户点击登录按钮
    Then 系统应该建立用户会话
    And 系统应该记录登录信息
    And 用户应该跳转到工作台主界面

  # 悲伤路径场景 - 未注册手机号
  Scenario: 使用未注册手机号登录
    Given 用户在登录页面
    When 用户输入未注册手机号 "13900139000"
    And 用户点击获取验证码按钮
    Then 系统应该显示提示 "该手机号未注册，请先注册"

  # 悲伤路径场景 - 验证码登录失败
  Scenario: 验证码错误
    Given 用户在登录页面
    And 用户已输入已注册手机号 "13800138000"
    And 用户已获取验证码
    When 用户输入错误的验证码 "000000"
    And 用户点击登录按钮
    Then 系统应该显示错误提示 "验证码错误，请重新输入"

  Scenario: 验证码过期
    Given 用户在登录页面
    And 用户已输入已注册手机号 "13800138000"
    And 用户已获取验证码
    And 验证码已过期（5分钟后）
    When 用户输入验证码 "123456"
    And 用户点击登录按钮
    Then 系统应该显示错误提示 "验证码已过期，请重新获取"

  # 悲伤路径场景 - 密码登录失败
  Scenario: 密码错误
    Given 用户在登录页面
    And 用户已切换到密码登录方式
    When 用户输入已注册手机号 "13800138000"
    And 用户输入错误密码 "wrongpassword"
    And 用户点击登录按钮
    Then 系统应该显示错误提示 "密码错误，请重新输入"

  Scenario: 连续登录失败导致账户锁定
    Given 用户在登录页面
    And 用户已切换到密码登录方式
    And 用户已连续4次登录失败
    When 用户输入已注册手机号 "13800138000"
    And 用户输入错误密码 "wrongpassword"
    And 用户点击登录按钮
    Then 系统应该锁定账户30分钟
    And 系统应该显示提示 "账户已锁定，请30分钟后重试"

  Scenario: 账户锁定期间尝试登录
    Given 用户在登录页面
    And 用户账户 "13800138000" 已被锁定
    When 用户输入已注册手机号 "13800138000"
    And 用户输入正确密码 "password123"
    And 用户点击登录按钮
    Then 系统应该显示提示 "账户已锁定，请30分钟后重试"

  # 可选路径场景
  Scenario: 在验证码登录和密码登录之间切换
    Given 用户在登录页面
    And 登录方式默认为验证码登录
    When 用户点击切换到密码登录
    Then 页面应该显示密码输入框
    And 验证码相关控件应该隐藏
    When 用户点击切换到验证码登录
    Then 页面应该显示验证码输入框
    And 密码输入框应该隐藏

  Scenario: 跳转到注册页面
    Given 用户在登录页面
    When 用户点击"立即注册"链接
    Then 用户应该跳转到注册页面

  Scenario: 跳转到密码重置页面
    Given 用户在登录页面
    When 用户点击"忘记密码"链接
    Then 用户应该跳转到密码重置页面

  # 验证码发送限制场景
  Scenario: 验证码发送倒计时
    Given 用户在登录页面
    And 用户已输入已注册手机号 "13800138000"
    When 用户点击获取验证码按钮
    Then 系统应该发送验证码
    And 获取验证码按钮应该显示60秒倒计时
    And 倒计时期间按钮应该不可点击

  # 错误提示清晰性验证
  Scenario Outline: 错误提示信息验证
    Given 用户在登录页面
    When 用户遇到登录错误情况 "<error_scenario>"
    Then 系统应该显示清晰明确的错误提示
    And 错误提示应该帮助用户理解问题并采取正确行动

    Examples:
      | error_scenario |
      | 验证码错误     |
      | 验证码过期     |
      | 密码错误       |
      | 账户锁定       |
      | 手机号未注册   |