Feature: 统一消息流处理
  As a IP运营者
  I want 能在一个统一的界面看到并回复所有已接入账号收到的消息
  So that 不用再在各个App之间来回切换，从而极大地提升效率

  Background:
    Given 用户已登录系统
    And 用户已成功接入多个外部平台账号
    And 所有接入的账号都处于在线状态

  # 消息接收和聚合场景 - CRUDL分析
  Scenario: 创建统一消息流
    Given 外部平台有新消息产生
    When 微信平台收到消息 "你好，请问有什么产品？"
    And QQ平台收到消息 "价格怎么样？"
    And 钉钉平台收到消息 "什么时候发货？"
    Then 所有消息应该实时聚合到统一的会话列表中
    And 每条消息应该标明来源平台
    And 消息接收延迟应该小于5秒

  Scenario: 查看统一会话列表
    Given 系统已接收多个平台的消息
    When 用户查看会话列表
    Then 会话列表应该显示所有平台的对话
    And 每个会话应该清晰标明来源账号（使用账号别名）
    And 会话列表项应该显示联系人/群聊头像、名称、最新消息摘要、时间戳
    And 会话列表应该显示未读消息数角标

  Scenario: 更新会话状态
    Given 会话列表中有未读消息
    When 用户点击查看某个会话
    Then 该会话的未读状态应该被清除
    And 未读消息数角标应该相应更新
    And 会话状态应该实时同步

  Scenario: 删除会话记录
    Given 会话列表中有多个会话
    When 用户选择删除某个会话
    And 用户确认删除操作
    Then 该会话应该从列表中移除
    And 相关的消息历史应该被清理
    And 删除操作不应该影响其他会话

  # 消息查看和历史记录场景
  Scenario: 查看完整对话历史
    Given 用户选择一个会话
    When 用户点击进入对话窗口
    Then 对话窗口应该显示该会话的完整上下文历史记录
    And 对话窗口顶部应该正确显示联系人/群聊名称和来源平台
    And 历史消息应该按时间顺序排列

  Scenario: 滚动加载更多历史消息
    Given 用户在对话窗口中
    And 当前显示的历史消息有限
    When 用户向上滚动到消息顶部
    Then 系统应该自动加载更多历史消息
    And 加载过程应该流畅无卡顿
    And 消息顺序应该保持正确

  # 消息发送场景 - 快乐路径
  Scenario: 通过原始账号发送回复
    Given 用户在微信平台的对话窗口中
    When 用户在输入框输入回复 "我们有A产品和B产品可供选择"
    And 用户点击发送按钮
    Then 消息应该通过原始的微信账号准确送达给终端用户
    And 发送的消息应该在对话窗口中显示
    And 会话列表应该更新最新消息摘要

  Scenario: 发送不同类型的消息内容
    Given 用户在对话窗口中
    When 用户发送文本消息 "感谢您的咨询"
    Then 文本消息应该正确显示和发送
    When 用户发送图片消息
    Then 图片应该正确显示和发送
    When 用户发送文件消息
    Then 文件应该正确显示和发送

  # 消息发送场景 - 悲伤路径
  Scenario: 消息发送失败处理
    Given 用户在对话窗口中
    And 网络连接不稳定
    When 用户发送消息 "这是一条测试消息"
    And 消息发送失败
    Then 系统应该提供明确的错误提示
    And 用户应该能够重新发送该消息
    And 失败的消息应该有明显的标识

  Scenario: 原始账号离线时的处理
    Given 用户在对话窗口中
    And 对应的原始账号已离线
    When 用户尝试发送消息
    Then 系统应该提示 "账号已离线，无法发送消息"
    And 用户应该被引导检查账号连接状态

  # 搜索和筛选功能场景
  Scenario: 按联系人名称搜索会话
    Given 会话列表中有多个会话
    When 用户在搜索框输入联系人名称 "张三"
    And 用户点击搜索按钮
    Then 系统应该显示所有包含"张三"的会话
    And 搜索结果应该高亮显示匹配的关键词

  Scenario: 按消息内容搜索
    Given 会话列表中有多个会话
    When 用户在搜索框输入消息内容关键词 "产品价格"
    Then 系统应该显示包含"产品价格"的相关会话
    And 搜索应该支持模糊匹配

  Scenario: 按平台筛选会话
    Given 会话列表中有来自不同平台的会话
    When 用户选择筛选条件 "仅显示微信会话"
    Then 会话列表应该只显示来自微信平台的会话
    And 其他平台的会话应该被隐藏

  Scenario: 按未读状态筛选会话
    Given 会话列表中有已读和未读的会话
    When 用户选择筛选条件 "仅显示未读会话"
    Then 会话列表应该只显示有未读消息的会话
    And 已读会话应该被隐藏

  # 会话排序功能场景
  Scenario: 按时间排序会话
    Given 会话列表中有多个会话
    When 用户选择按时间排序
    Then 会话应该按最新消息时间降序排列
    And 最近有消息的会话应该显示在顶部

  Scenario: 按未读消息数排序
    Given 会话列表中有不同未读消息数的会话
    When 用户选择按未读消息数排序
    Then 未读消息多的会话应该排在前面
    And 已读会话应该排在后面

  # 实时性能场景
  Scenario: 消息实时接收性能
    Given 外部平台产生新消息
    When 消息从外部平台发送到柴管家系统
    Then 消息应该在5秒内出现在工作台
    And 实时性应该满足95%的情况下小于5秒的要求

  Scenario: 界面响应性能
    Given 用户在正常网络环境下
    When 用户首次加载应用主界面
    Then 界面加载时间应该小于3秒
    And 会话列表应该快速显示

  # 多平台兼容性场景
  Scenario Outline: 不同平台消息处理
    Given 用户已接入 "<platform>" 平台
    When "<platform>" 平台收到新消息
    Then 消息应该正确显示在统一会话列表中
    And 消息格式应该保持原有特性
    And 回复应该能够正确发送回 "<platform>" 平台

    Examples:
      | platform |
      | 微信     |
      | QQ       |
      | 钉钉     |
      | 企业微信 |

  # 错误处理和容错场景
  Scenario: 网络中断后的自动恢复
    Given 系统正常运行中
    When 网络连接中断
    And 网络连接恢复
    Then 系统应该自动重新连接所有渠道
    And 用户无需手动操作
    And 中断期间的消息应该能够正常接收

  Scenario: 部分平台连接异常
    Given 用户接入了多个平台
    When 其中一个平台连接异常
    Then 其他平台应该继续正常工作
    And 系统应该提示异常平台的状态
    And 用户应该能够尝试重新连接异常平台

  # 用户体验优化场景
  Scenario: 会话列表信息完整性
    Given 用户查看会话列表
    Then 每个会话项应该包含所有关键信息
    And 信息显示应该清晰易读
    And 界面布局应该合理美观

  Scenario: 对话窗口用户体验
    Given 用户在对话窗口中
    Then 消息显示应该清晰区分发送方和接收方
    And 时间戳应该准确显示
    And 输入框应该支持常见的文本编辑功能