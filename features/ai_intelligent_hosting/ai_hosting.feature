Feature: AI智能托管与安全接管
  As a IP运营者
  I want 能将常规对话放心地交给AI自动处理，但当AI对回复没有把握时，它应该足够"聪明"地停下来并向我求助，同时我也能随时无缝地接管任何对话
  So that 实现效率与安全的统一

  Background:
    Given 用户已登录系统
    And 用户有活跃的对话会话

  # 工作模式设置场景 - CRUDL分析
  Scenario: 创建会话的工作模式设置
    Given 用户有一个新的对话会话
    When 用户为该会话设置工作模式
    Then 系统应该提供"人工模式"和"AI托管模式"选项
    And 默认模式应该是"人工模式"
    And 模式设置应该成功保存

  Scenario: 查看会话列表中的工作模式状态
    Given 用户有多个对话会话
    And 不同会话设置了不同的工作模式
    When 用户查看会话列表
    Then 每个会话应该清晰标明当前状态
    And 状态标识应该包括"人工模式"、"AI托管中"、"待人工接管"
    And 状态标识应该使用清晰的视觉方式区分

  Scenario: 更新会话的工作模式
    Given 会话当前处于"人工模式"
    When 用户将该会话切换为"AI托管模式"
    Then 会话状态应该更新为"AI托管中"
    And 状态变更应该在会话列表中实时显示

  Scenario: 删除会话的托管设置
    Given 会话当前处于"AI托管模式"
    When 用户取消AI托管设置
    Then 会话应该恢复为"人工模式"
    And 所有自动处理应该停止

  # AI托管模式 - 快乐路径场景
  Scenario: AI高置信度自动回复
    Given 会话处于"AI托管模式"
    When 系统收到新消息 "你们的营业时间是什么？"
    And AI生成回复内容
    And AI置信度评估结果为 0.9
    Then 系统应该自动发送AI生成的回复
    And 会话状态应该保持"AI托管中"
    And 自动回复应该有明确的日志记录

  Scenario: AI置信度刚好达到阈值
    Given 会话处于"AI托管模式"
    And AI置信度阈值设置为 0.8
    When 系统收到新消息
    And AI置信度评估结果为 0.8
    Then 系统应该自动发送AI生成的回复
    And 回复发送时间应该小于3秒

  # AI托管模式 - 悲伤路径场景
  Scenario: AI低置信度暂停托管
    Given 会话处于"AI托管模式"
    When 系统收到新消息 "这个问题比较复杂，需要详细解答"
    And AI生成回复内容
    And AI置信度评估结果为 0.6
    Then 系统绝不应该自动发送该回复
    And 该会话的AI托管状态应该被自动暂停
    And 会话状态应该变更为"待人工接管"

  Scenario: AI置信度刚好低于阈值
    Given 会话处于"AI托管模式"
    And AI置信度阈值设置为 0.8
    When 系统收到新消息
    And AI置信度评估结果为 0.79
    Then 系统绝不应该自动发送该回复
    And 会话应该转为"待人工接管"状态

  # 人工接管场景 - 快乐路径
  Scenario: 运营者通过接管按钮接管会话
    Given 会话状态为"待人工接管"
    When 运营者点击"接管"按钮
    Then 会话状态应该立即切换为"人工模式"
    And 运营者应该能够正常输入和发送消息
    And 接管操作应该无缝完成

  Scenario: 运营者通过直接输入接管会话
    Given 会话状态为"待人工接管"
    When 运营者直接在输入框输入文字
    Then 会话状态应该自动切换为"人工模式"
    And 运营者输入的内容应该正常显示
    And 接管应该无需额外确认

  Scenario: 运营者主动接管AI托管中的会话
    Given 会话状态为"AI托管中"
    When 运营者点击"接管"按钮
    Then 会话状态应该立即切换为"人工模式"
    And AI自动处理应该立即停止
    And 运营者应该获得完全控制权

  # 通知机制场景
  Scenario: 低置信度触发通知
    Given 会话处于"AI托管模式"
    And 用户已配置通知设置
    When AI置信度低于阈值触发人工接管
    Then 系统应该向运营者发出通知
    And 该会话应该在列表中被高亮标记
    And 通知应该明确指出需要人工介入

  Scenario: 配置通知方式
    Given 用户访问通知设置
    When 用户配置"待人工接管"的通知方式
    Then 系统应该提供桌面通知选项
    And 系统应该提供声音提醒选项
    And 用户应该能够自由选择通知方式

  # 置信度评估场景
  Scenario Outline: 不同置信度的处理
    Given 会话处于"AI托管模式"
    When AI生成回复的置信度为 <confidence_score>
    Then 系统应该执行 <action>

    Examples:
      | confidence_score | action     |
      | 0.9             | 自动发送   |
      | 0.8             | 自动发送   |
      | 0.79            | 暂停托管   |
      | 0.5             | 暂停托管   |
      | 0.2             | 暂停托管   |

  # 日志记录场景
  Scenario: AI自动回复日志记录
    Given 会话处于"AI托管模式"
    When AI自动发送回复
    Then 系统应该记录详细的日志信息
    And 日志应该包含消息内容、置信度分数、发送时间
    And 日志应该便于后续审查

  Scenario: 查看AI托管日志
    Given 系统已记录多条AI自动回复日志
    When 运营者查看托管日志
    Then 日志应该按时间顺序显示
    And 日志应该包含所有必要的审查信息
    And 日志应该支持筛选和搜索

  # 模式切换场景
  Scenario: 人工模式切换到AI托管模式
    Given 会话当前处于"人工模式"
    When 运营者将会话切换为"AI托管模式"
    Then 会话状态应该变更为"AI托管中"
    And 系统应该开始监听新消息进行自动处理
    And 切换应该立即生效

  Scenario: AI托管模式切换到人工模式
    Given 会话当前处于"AI托管模式"
    When 运营者将会话切换为"人工模式"
    Then AI自动处理应该立即停止
    And 会话状态应该变更为"人工模式"
    And 运营者应该获得完全控制权

  # 错误处理和边界场景
  Scenario: AI服务异常时的处理
    Given 会话处于"AI托管模式"
    When AI服务暂时不可用
    And 系统收到新消息
    Then 会话应该自动转为"待人工接管"状态
    And 系统应该通知运营者AI服务异常
    And 消息不应该丢失

  Scenario: 置信度评估异常
    Given 会话处于"AI托管模式"
    When AI无法正确评估置信度
    Then 系统应该默认为低置信度处理
    And 会话应该转为"待人工接管"状态
    And 系统应该记录异常情况

  # 多会话并发处理场景
  Scenario: 同时处理多个AI托管会话
    Given 运营者有多个会话处于"AI托管模式"
    When 多个会话同时收到新消息
    Then 系统应该能够并发处理所有会话
    And 每个会话的处理应该独立进行
    And 处理结果不应该相互影响