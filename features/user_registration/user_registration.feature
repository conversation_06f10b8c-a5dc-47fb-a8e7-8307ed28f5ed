Feature: 用户注册
  As a 新用户
  I want 能够使用手机号和验证码进行注册并设置密码
  So that 创建账户并开始使用柴管家系统

  Background:
    Given 用户访问注册页面

  # 快乐路径场景
  Scenario: 成功注册新用户账户
    Given 用户在注册页面
    When 用户输入有效的手机号 "13800138000"
    And 用户点击获取验证码按钮
    And 用户输入正确的验证码 "123456"
    And 用户设置密码 "password123"
    And 用户确认密码 "password123"
    And 用户点击注册按钮
    Then 系统应该创建用户账户
    And 系统应该自动为用户登录
    And 用户应该跳转到主界面
    And 用户信息应该正确保存到系统中

  # 悲伤路径场景 - 手机号格式验证
  Scenario Outline: 手机号格式验证失败
    Given 用户在注册页面
    When 用户输入无效的手机号 "<invalid_phone>"
    Then 系统应该显示错误提示 "手机号格式不正确，请输入11位数字"
    And 获取验证码按钮应该被禁用

    Examples:
      | invalid_phone |
      | 1380013800    |
      | 138001380001  |
      | 1380013800a   |
      | abc1380013800 |

  Scenario: 手机号已注册的情况
    Given 用户在注册页面
    And 手机号 "13800138000" 已经注册
    When 用户输入手机号 "13800138000"
    And 用户点击获取验证码按钮
    Then 系统应该显示提示 "该手机号已注册，请直接登录"

  Scenario: 验证码错误
    Given 用户在注册页面
    And 用户已输入有效手机号 "13800138000"
    And 用户已获取验证码
    When 用户输入错误的验证码 "000000"
    And 用户设置密码 "password123"
    And 用户确认密码 "password123"
    And 用户点击注册按钮
    Then 系统应该显示错误提示 "验证码错误，请重新输入"

  Scenario: 验证码过期
    Given 用户在注册页面
    And 用户已输入有效手机号 "13800138000"
    And 用户已获取验证码
    And 验证码已过期（5分钟后）
    When 用户输入验证码 "123456"
    And 用户设置密码 "password123"
    And 用户确认密码 "password123"
    And 用户点击注册按钮
    Then 系统应该显示提示 "验证码已过期，请重新获取验证码"

  # 密码验证场景
  Scenario Outline: 密码格式验证
    Given 用户在注册页面
    And 用户已输入有效手机号和验证码
    When 用户设置密码 "<password>"
    And 用户确认密码 "<password>"
    And 用户点击注册按钮
    Then 系统应该显示错误提示 "<error_message>"

    Examples:
      | password | error_message                    |
      | 123      | 密码长度至少8位，包含字母和数字  |
      | 12345678 | 密码长度至少8位，包含字母和数字  |
      | abcdefgh | 密码长度至少8位，包含字母和数字  |

  Scenario: 确认密码不一致
    Given 用户在注册页面
    And 用户已输入有效手机号和验证码
    When 用户设置密码 "password123"
    And 用户确认密码 "password456"
    And 用户点击注册按钮
    Then 系统应该显示错误提示 "两次输入密码不一致，请重新输入"

  # 验证码发送限制场景
  Scenario: 验证码发送倒计时
    Given 用户在注册页面
    And 用户已输入有效手机号 "13800138000"
    When 用户点击获取验证码按钮
    Then 系统应该发送验证码
    And 获取验证码按钮应该显示60秒倒计时
    And 倒计时期间按钮应该不可点击

  # 可选路径场景
  Scenario: 跳转到登录页面
    Given 用户在注册页面
    When 用户点击"已有账户？立即登录"链接
    Then 用户应该跳转到登录页面

  # 验证码输入限制
  Scenario: 验证码只接受6位数字
    Given 用户在注册页面
    And 用户已输入有效手机号并获取验证码
    When 用户在验证码输入框输入非6位数字内容
    Then 验证码输入框应该只接受6位数字