Feature: 登录状态管理
  As a 已登录的IP运营者
  I want 系统能够通过定时检测机制安全地维护我的登录状态最多7天，并在环境异常时要求重新验证
  So that 在保证账户安全的前提下获得流畅的使用体验

  Background:
    Given 用户已成功登录系统
    And 系统记录了登录时间戳和环境信息

  # 快乐路径场景 - 正常会话维护
  Scenario: 7天内正常维护登录状态
    Given 用户登录时间不超过7天
    And 用户IP地址和设备标识未发生变化
    When 系统执行定时检测任务
    Then 系统应该保持用户登录状态
    And 用户应该能够正常访问系统功能

  Scenario: 用户操作更新活跃时间
    Given 用户处于已登录状态
    When 用户执行任何系统操作
    Then 系统应该更新用户最后活跃时间
    And 登录状态应该继续维持

  # 时效性检查场景
  Scenario: 会话超过7天自动过期
    Given 用户登录时间已超过7天
    When 系统执行定时检测任务
    Then 系统应该标记会话为过期状态
    When 用户下次访问系统
    Then 用户应该自动跳转到登录页面
    And 系统应该弹窗提示 "登录已过期，请重新登录"

  # 环境安全检查场景
  Scenario: IP地址变化触发重新验证
    Given 用户登录时间不超过7天
    And 用户IP地址发生变化
    When 系统执行定时检测任务
    Then 系统应该标记会话需要重新验证
    When 用户下次访问系统
    Then 用户应该跳转到登录页面
    And 系统应该弹窗显示 "检测到登录环境异常，为保障账户安全，请重新登录"

  Scenario: 设备标识变化触发重新验证
    Given 用户登录时间不超过7天
    And 用户设备标识发生变化
    When 系统执行定时检测任务
    Then 系统应该标记会话需要重新验证
    When 用户下次访问系统
    Then 用户应该跳转到登录页面
    And 系统应该弹窗显示 "检测到登录环境异常，为保障账户安全，请重新登录"

  # 主动退出场景
  Scenario: 用户主动退出登录
    Given 用户处于已登录状态
    When 用户点击退出登录按钮
    Then 系统应该立即使登录凭证失效
    And 用户应该跳转到登录页面
    And 用户的业务数据应该完全保留

  # 数据完整性验证场景
  Scenario: 重新登录后数据完整性验证
    Given 用户因会话过期被要求重新登录
    When 用户成功重新登录
    Then 用户应该能够正常访问之前的所有数据
    And 渠道配置应该保持不变
    And 消息记录应该保持不变
    And 知识库内容应该保持不变

  # 定时检测任务场景
  Scenario: 定时检测任务正常执行
    Given 系统启动定时检测任务
    When 每5分钟到达检测时间点
    Then 系统应该检查所有活跃会话的时效性
    And 系统应该检查所有活跃会话的环境安全性
    And 检测任务应该不影响系统正常性能

  Scenario: 多用户并发定时检测
    Given 系统中有多个活跃用户会话
    When 系统执行定时检测任务
    Then 系统应该正确处理所有用户的会话检测
    And 每个用户的检测结果应该独立准确
    And 检测过程不应该相互干扰

  # 容错机制场景
  Scenario: 网络异常时的容错处理
    Given 系统执行定时检测任务时发生网络异常
    When 网络异常导致检测失败
    Then 系统应该保持用户当前登录状态
    And 用户应该能够继续正常使用系统
    And 系统应该在下次检测时重新执行

  # 弹窗提示场景
  Scenario Outline: 不同场景下的弹窗提示
    Given 用户会话状态为 "<session_status>"
    When 用户访问系统
    Then 系统应该显示相应的弹窗提示 "<popup_message>"
    And 弹窗信息应该清晰明确
    And 用户应该能够理解需要重新登录的原因

    Examples:
      | session_status | popup_message                                    |
      | 会话过期       | 登录已过期，请重新登录                           |
      | 环境异常       | 检测到登录环境异常，为保障账户安全，请重新登录   |

  # 会话状态检查场景
  Scenario: 用户访问时的会话状态检查
    Given 用户尝试访问系统
    When 系统检查用户会话状态
    Then 系统应该根据会话标记执行相应操作
    And 如果会话有效，用户应该正常访问
    And 如果会话无效，用户应该被引导重新登录

  # 登录凭证失效场景
  Scenario Outline: 不同情况下的登录凭证失效
    Given 用户登录凭证因 "<reason>" 需要失效
    When 系统使登录凭证失效
    Then 用户的业务数据应该完全保留不受影响
    And 用户重新登录后应该能够访问所有数据

    Examples:
      | reason   |
      | 会话过期 |
      | 环境异常 |
      | 主动退出 |