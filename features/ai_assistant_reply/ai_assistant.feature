Feature: AI副驾式辅助回复
  As a IP运营者
  I want AI能像一个副驾驶一样，实时地帮我分析对方意图并提供回复建议
  So that 不仅能加快我的回复速度，还能提升我的回复质量

  Background:
    Given 用户已登录系统
    And 知识库中已有相关问答对内容

  # 知识库管理场景 - CRUDL分析
  Scenario: 创建新的问答对
    Given 用户访问知识库管理功能
    When 用户点击添加问答对按钮
    And 用户输入问题 "产品价格是多少？"
    And 用户输入答案 "我们的产品价格为999元，现在有优惠活动。"
    And 用户点击保存按钮
    Then 问答对应该成功保存到知识库
    And 系统应该显示保存成功提示

  Scenario: 查询知识库中的问答对
    Given 知识库中已有多个问答对
    When 用户在知识库搜索框输入关键词 "价格"
    And 用户点击搜索按钮
    Then 系统应该显示包含"价格"关键词的所有问答对
    And 搜索结果应该高亮显示匹配的关键词

  Scenario: 更新现有问答对
    Given 知识库中存在问答对 "产品价格是多少？"
    When 用户选择该问答对进行编辑
    And 用户修改答案为 "我们的产品价格为899元，限时优惠中。"
    And 用户点击保存按钮
    Then 问答对应该成功更新
    And 系统应该显示更新成功提示

  Scenario: 删除问答对
    Given 知识库中存在问答对 "产品价格是多少？"
    When 用户选择该问答对
    And 用户点击删除按钮
    And 用户确认删除操作
    Then 问答对应该从知识库中删除
    And 系统应该显示删除成功提示

  Scenario: 批量查看知识库问答对列表
    Given 知识库中有多个问答对
    When 用户访问知识库管理页面
    Then 系统应该显示所有问答对的列表
    And 列表应该包含问题、答案预览、创建时间等信息
    And 列表应该支持分页显示

  # AI意图识别场景 - 快乐路径
  Scenario: AI成功识别用户意图
    Given 用户正在与终端用户对话
    And 终端用户发送消息 "你们的产品多少钱？"
    When AI分析该消息
    Then AI应该在智能看板区域显示意图识别结果 "咨询价格"
    And 意图识别结果应该清晰可见

  Scenario Outline: AI识别不同类型的用户意图
    Given 用户正在与终端用户对话
    When 终端用户发送消息 "<user_message>"
    And AI分析该消息
    Then AI应该识别意图为 "<intent_type>"

    Examples:
      | user_message           | intent_type  |
      | 有什么优惠活动吗？     | 询问活动详情 |
      | 这个产品怎么样？       | 产品介绍     |
      | 能便宜点吗？           | 咨询价格     |
      | 什么时候发货？         | 物流咨询     |

  # AI回复建议生成场景 - 快乐路径
  Scenario: AI基于知识库生成回复建议
    Given 终端用户询问 "产品价格是多少？"
    And 知识库中有相关的问答对
    When AI分析对话上下文
    Then AI应该生成至少一条完整的回复建议
    And 回复建议应该基于知识库内容
    And 回复建议应该准确回答用户问题

  Scenario: 用户直接使用AI回复建议
    Given AI已生成回复建议 "我们的产品价格为999元，现在有优惠活动。"
    When 用户点击使用该回复建议
    Then 回复建议应该自动填入输入框
    And 用户可以直接发送该回复

  Scenario: 用户修改AI回复建议后发送
    Given AI已生成回复建议 "我们的产品价格为999元，现在有优惠活动。"
    When 用户点击使用该回复建议
    And 用户修改回复内容为 "我们的产品价格为999元，现在有限时优惠活动，欢迎咨询。"
    And 用户发送修改后的回复
    Then 修改后的回复应该成功发送给终端用户

  # 悲伤路径场景
  Scenario: 知识库中没有相关内容时的处理
    Given 终端用户询问 "你们支持火星配送吗？"
    And 知识库中没有相关内容
    When AI分析该消息
    Then AI应该提示 "建议人工处理"
    And AI应该不生成自动回复建议

  Scenario: AI无法识别用户意图
    Given 终端用户发送消息 "asdfghjkl"
    When AI分析该消息
    Then AI应该显示 "意图不明确"
    And AI应该提示需要人工处理

  # 多轮对话上下文场景
  Scenario: AI考虑完整对话历史生成建议
    Given 用户与终端用户已有以下对话历史：
      | 终端用户 | 你们有什么产品？     |
      | 运营者   | 我们有A产品和B产品   |
      | 终端用户 | A产品多少钱？        |
    When AI分析最新消息 "A产品多少钱？"
    Then AI应该结合对话上下文
    And AI应该生成针对A产品价格的回复建议
    And 回复建议应该体现对话的连贯性

  # 界面布局和用户体验场景
  Scenario: AI副驾模块界面布局验证
    Given 用户正在进行对话
    When AI副驾模块显示分析结果和建议
    Then AI副驾模块应该布局清晰
    And AI副驾模块不应该干扰正常的对话操作
    And 用户应该能够轻松查看AI分析结果

  # 实时性能场景
  Scenario: AI实时分析和建议生成
    Given 用户正在与终端用户对话
    When 终端用户发送新消息
    Then AI应该在2秒内完成意图分析
    And AI应该在2秒内生成回复建议
    And 分析结果应该实时显示在智能看板区域

  # 知识库搜索功能场景
  Scenario: 知识库搜索功能验证
    Given 知识库中有多个问答对
    When 用户使用搜索功能查找特定内容
    Then 搜索结果应该准确匹配关键词
    And 搜索功能应该支持模糊匹配
    And 搜索结果应该按相关性排序

  # 错误处理场景
  Scenario: AI服务异常时的处理
    Given AI服务暂时不可用
    When 终端用户发送消息
    Then 系统应该显示 "AI分析服务暂时不可用"
    And 用户应该仍能正常进行人工回复
    And 系统不应该影响正常的对话功能