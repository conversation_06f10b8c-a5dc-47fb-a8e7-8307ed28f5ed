#!/usr/bin/env python3
"""
安全测试环境检查脚本
验证测试环境是否准备就绪
"""

import os
import sys
import subprocess
import requests
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SecurityEnvironmentChecker:
    """安全测试环境检查器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.security_dir = Path(__file__).parent
        self.base_url = "http://localhost:8000"
        
    def check_docker_environment(self) -> Tuple[bool, str]:
        """检查Docker环境"""
        try:
            # 检查Docker是否运行
            result = subprocess.run(
                ["docker", "ps"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode != 0:
                return False, "Docker未运行或未安装"
            
            # 检查项目容器是否运行
            result = subprocess.run(
                ["docker-compose", "ps"], 
                cwd=self.project_root,
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if "chaiguanjia" not in result.stdout:
                return False, "项目容器未运行，请执行 docker-compose up -d"
            
            return True, "Docker环境正常"
            
        except subprocess.TimeoutExpired:
            return False, "Docker命令超时"
        except FileNotFoundError:
            return False, "Docker未安装"
        except Exception as e:
            return False, f"Docker检查失败: {e}"
    
    def check_application_health(self) -> Tuple[bool, str]:
        """检查应用程序健康状态"""
        try:
            # 检查健康检查端点
            response = requests.get(f"{self.base_url}/health", timeout=5)
            
            if response.status_code == 200:
                health_data = response.json()
                if health_data.get("status") == "healthy":
                    return True, f"应用程序健康，版本: {health_data.get('version', 'unknown')}"
                else:
                    return False, f"应用程序状态异常: {health_data.get('status')}"
            else:
                return False, f"健康检查失败，状态码: {response.status_code}"
                
        except requests.exceptions.ConnectionError:
            return False, "无法连接到应用程序，请确保服务正在运行"
        except requests.exceptions.Timeout:
            return False, "应用程序响应超时"
        except Exception as e:
            return False, f"健康检查失败: {e}"
    
    def check_api_endpoints(self) -> Tuple[bool, str]:
        """检查关键API端点"""
        endpoints = [
            "/docs",
            "/redoc", 
            "/api/v1/health",
            "/api/v1/auth/login",
        ]
        
        failed_endpoints = []
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                if response.status_code not in [200, 405]:  # 405 for POST-only endpoints
                    failed_endpoints.append(f"{endpoint} ({response.status_code})")
            except Exception as e:
                failed_endpoints.append(f"{endpoint} (连接失败)")
        
        if failed_endpoints:
            return False, f"以下端点不可访问: {', '.join(failed_endpoints)}"
        else:
            return True, "所有关键API端点可访问"
    
    def check_database_connection(self) -> Tuple[bool, str]:
        """检查数据库连接"""
        try:
            # 通过API检查数据库连接
            response = requests.get(f"{self.base_url}/api/v1/monitoring/database", timeout=10)
            
            if response.status_code == 200:
                db_status = response.json()
                if db_status.get("success"):
                    return True, "数据库连接正常"
                else:
                    return False, f"数据库连接异常: {db_status.get('message')}"
            else:
                return False, f"数据库状态检查失败，状态码: {response.status_code}"
                
        except Exception as e:
            return False, f"数据库连接检查失败: {e}"
    
    def check_security_tools(self) -> Tuple[bool, str]:
        """检查安全工具配置"""
        required_files = [
            self.security_dir / "config" / "bandit.yaml",
            self.security_dir / "config" / "safety.json",
            self.security_dir / "config" / "zap_config.json",
            self.security_dir / "config" / "sqlmap_config.json",
            self.security_dir / "payloads" / "sql_injection.txt",
            self.security_dir / "payloads" / "xss.txt",
            self.security_dir / "scripts" / "jwt_test.py"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not file_path.exists():
                missing_files.append(str(file_path.relative_to(self.project_root)))
        
        if missing_files:
            return False, f"缺少安全工具配置文件: {', '.join(missing_files)}"
        else:
            return True, "安全工具配置完整"
    
    def check_test_data(self) -> Tuple[bool, str]:
        """检查测试数据"""
        try:
            # 检查是否有测试用户
            response = requests.post(
                f"{self.base_url}/api/v1/auth/login",
                json={
                    "email": "<EMAIL>",
                    "password": "testpassword"
                },
                timeout=5
            )
            
            # 如果返回401，说明认证系统工作正常
            if response.status_code in [401, 422]:
                return True, "认证系统工作正常"
            elif response.status_code == 200:
                return True, "测试用户可用"
            else:
                return False, f"认证系统异常，状态码: {response.status_code}"
                
        except Exception as e:
            return False, f"测试数据检查失败: {e}"
    
    def check_network_connectivity(self) -> Tuple[bool, str]:
        """检查网络连接"""
        try:
            # 检查外部网络连接（用于下载安全规则等）
            response = requests.get("https://httpbin.org/get", timeout=5)
            if response.status_code == 200:
                return True, "外部网络连接正常"
            else:
                return False, f"外部网络连接异常，状态码: {response.status_code}"
                
        except Exception as e:
            return False, f"网络连接检查失败: {e}"
    
    def run_comprehensive_check(self) -> Dict[str, Tuple[bool, str]]:
        """运行全面的环境检查"""
        checks = {
            "Docker环境": self.check_docker_environment,
            "应用程序健康": self.check_application_health,
            "API端点": self.check_api_endpoints,
            "数据库连接": self.check_database_connection,
            "安全工具": self.check_security_tools,
            "测试数据": self.check_test_data,
            "网络连接": self.check_network_connectivity
        }
        
        results = {}
        
        logger.info("开始安全测试环境检查...")
        
        for check_name, check_func in checks.items():
            logger.info(f"检查 {check_name}...")
            try:
                success, message = check_func()
                results[check_name] = (success, message)
                
                if success:
                    logger.info(f"✓ {check_name}: {message}")
                else:
                    logger.error(f"✗ {check_name}: {message}")
                    
            except Exception as e:
                error_msg = f"检查过程中发生错误: {e}"
                results[check_name] = (False, error_msg)
                logger.error(f"✗ {check_name}: {error_msg}")
        
        return results
    
    def generate_report(self, results: Dict[str, Tuple[bool, str]]) -> str:
        """生成检查报告"""
        report = []
        report.append("# 安全测试环境检查报告")
        report.append("")
        report.append(f"检查时间: {__import__('datetime').datetime.now().isoformat()}")
        report.append("")
        
        passed_checks = sum(1 for success, _ in results.values() if success)
        total_checks = len(results)
        
        report.append(f"## 总体状态")
        report.append(f"- 通过检查: {passed_checks}/{total_checks}")
        report.append(f"- 成功率: {passed_checks/total_checks*100:.1f}%")
        report.append("")
        
        report.append("## 详细结果")
        report.append("")
        
        for check_name, (success, message) in results.items():
            status = "✓ 通过" if success else "✗ 失败"
            report.append(f"### {check_name}")
            report.append(f"**状态**: {status}")
            report.append(f"**详情**: {message}")
            report.append("")
        
        if passed_checks == total_checks:
            report.append("## 结论")
            report.append("🎉 环境检查全部通过，可以开始安全渗透测试！")
        else:
            report.append("## 建议")
            report.append("⚠️ 请修复失败的检查项后再开始安全测试。")
        
        return "\n".join(report)

def main():
    """主函数"""
    checker = SecurityEnvironmentChecker()
    results = checker.run_comprehensive_check()
    
    # 生成报告
    report = checker.generate_report(results)
    
    # 保存报告
    report_file = checker.security_dir / "reports" / "environment_check_report.md"
    report_file.parent.mkdir(exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    logger.info(f"检查报告已保存到: {report_file}")
    
    # 检查是否所有项目都通过
    all_passed = all(success for success, _ in results.values())
    
    if all_passed:
        logger.info("🎉 环境检查全部通过，可以开始安全渗透测试！")
        sys.exit(0)
    else:
        logger.error("⚠️ 环境检查未全部通过，请修复问题后重试。")
        sys.exit(1)

if __name__ == "__main__":
    main()
