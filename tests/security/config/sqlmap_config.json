{"target_urls": ["http://localhost:8000/api/v1/users/search?keyword=test", "http://localhost:8000/api/v1/permissions/search?keyword=test", "http://localhost:8000/api/v1/roles/search?keyword=test"], "headers": {"Authorization": "Bearer <JWT_TOKEN>", "Content-Type": "application/json"}, "techniques": "BEUSTQ", "level": 3, "risk": 2, "threads": 5, "batch": true, "output_dir": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/tests/security/reports/sqlmap"}