# 安全测试环境检查报告

检查时间: 2025-08-11T21:34:15.893499

## 总体状态
- 通过检查: 3/7
- 成功率: 42.9%

## 详细结果

### Docker环境
**状态**: ✓ 通过
**详情**: Docker环境正常

### 应用程序健康
**状态**: ✗ 失败
**详情**: 无法连接到应用程序，请确保服务正在运行

### API端点
**状态**: ✗ 失败
**详情**: 以下端点不可访问: /docs (连接失败), /redoc (连接失败), /api/v1/health (连接失败), /api/v1/auth/login (连接失败)

### 数据库连接
**状态**: ✗ 失败
**详情**: 数据库连接检查失败: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/v1/monitoring/database (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x10117d6a0>: Failed to establish a new connection: [Errno 61] Connection refused'))

### 安全工具
**状态**: ✓ 通过
**详情**: 安全工具配置完整

### 测试数据
**状态**: ✗ 失败
**详情**: 测试数据检查失败: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/v1/auth/login (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1011883d0>: Failed to establish a new connection: [Errno 61] Connection refused'))

### 网络连接
**状态**: ✓ 通过
**详情**: 外部网络连接正常

## 建议
⚠️ 请修复失败的检查项后再开始安全测试。