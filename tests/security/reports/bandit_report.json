{"errors": [], "generated_at": "2025-08-11T13:52:01Z", "metrics": {"/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/api/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 8, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/api/v1/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/api/v1/error_monitoring.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 281, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/api/v1/files.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 333, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/api/v1/health.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 255, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/api/v1/monitoring.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 202, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/api/v1/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 24, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/api/v1/search.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 443, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/app.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 88, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 117, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/error_handlers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 303, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/error_monitor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 345, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/exceptions.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 2, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 378, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/middleware.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 269, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/middleware_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 234, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 101, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/test_middleware.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 301, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/main.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 25, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 12, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/api/auth_endpoints.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 347, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/api/auth_middleware.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 309, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/api/dependencies.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 244, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/api/permission_endpoints.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 439, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/api/role_endpoints.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 461, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/api/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 19, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/api/user_endpoints.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 477, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/models/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 34, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/models/auth_models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 276, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/models/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 252, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/services/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 22, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/services/auth_service.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 341, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/services/authing_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 331, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/services/jwt_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 288, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/services/permission_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 714, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/services/role_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 468, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/services/user_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 419, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/tests/test_auth_system.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 2, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 339, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/cache/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 55, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/cache/cache_manager.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 504, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/cache/distributed_lock.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 449, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/cache/examples.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 397, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/cache/permission_cache.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 281, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/cache/redis_client.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 240, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/cache/session_cache.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 367, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 33, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 256, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/consumer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 528, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/examples.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 330, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/exceptions.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 68, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/handlers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 349, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 308, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 228, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/monitor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 517, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/publisher.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 360, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/retry_handler.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 365, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 292, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/monitoring/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 18, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/monitoring/alert_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 522, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/monitoring/health_monitor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 465, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/monitoring/metrics_collector.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 458, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/monitoring/security_logger.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 3, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 406, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/search/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 48, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/search/elasticsearch_client.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 348, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/search/examples.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 517, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/search/index_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 287, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/search/search_models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 248, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/search/search_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 460, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/storage/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 57, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/storage/examples.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 518, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/storage/file_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 432, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/storage/file_processor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 300, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/storage/minio_client.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 364, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/storage/storage_models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 163, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 29, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/app.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 149, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 175, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/decorators.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 252, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/examples.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 212, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/handlers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 512, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 388, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 293, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/monitor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 441, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/registry.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 288, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/retry_handler.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 419, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/scheduler.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 406, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/database/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 66, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/database/config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 86, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/database/connection.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 204, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/database/examples.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 431, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/database/tests.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 369, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/examples/error_handling_demo.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 4, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 324, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/examples/middleware_demo.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 111, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/models/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 101, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/models/ai_enhanced.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 203, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/models/base.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 120, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/models/core.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 281, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/init_auth_system.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 222, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/install_elasticsearch_plugins.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 242, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/monitor_cache_system.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 350, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/monitor_file_storage.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 459, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/monitor_message_queue.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 254, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/monitor_search_system.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 466, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/monitor_tasks.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 381, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/setup_file_storage.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 196, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/setup_search_system.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 73, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/start_message_queue.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 204, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/start_task_worker.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 139, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/test_cache_system.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 2, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 426, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/test_file_storage.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 400, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/test_message_queue.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 446, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/test_search_system.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 503, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/test_task_system.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 472, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/validate_cache_setup.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 71, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/api_based_db_test.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 148, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/benchmark_test.py": {"CONFIDENCE.HIGH": 4, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 4, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 271, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/database_performance_test.py": {"CONFIDENCE.HIGH": 4, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 4, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 457, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/generate_final_report.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 711, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/locustfile.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 236, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/performance_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 643, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/run_performance_tests.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 530, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/system_monitor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 389, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/test_error_handling.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 474, "nosec": 0, "skipped_tests": 0}, "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/test_middleware_acceptance.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 213, "nosec": 0, "skipped_tests": 0}, "_totals": {"CONFIDENCE.HIGH": 36, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 17, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 2, "SEVERITY.LOW": 47, "SEVERITY.MEDIUM": 4, "SEVERITY.UNDEFINED": 0, "loc": 36273, "nosec": 0, "skipped_tests": 0}}, "results": [{"code": "22     # 服务器配置\n23     host: str = \"0.0.0.0\"\n24     port: int = 8000\n", "col_offset": 16, "end_col_offset": 25, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/config.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 605, "link": "https://cwe.mitre.org/data/definitions/605.html"}, "issue_severity": "MEDIUM", "issue_text": "Possible binding to all interfaces.", "line_number": 23, "line_range": [23], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b104_hardcoded_bind_all_interfaces.html", "test_id": "B104", "test_name": "hardcoded_bind_all_interfaces"}, {"code": "36     UNAUTHORIZED = \"E3000\"\n37     INVALID_TOKEN = \"E3001\"\n38     TOKEN_EXPIRED = \"E3002\"\n", "col_offset": 20, "end_col_offset": 27, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/exceptions.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'E3001'", "line_number": 37, "line_range": [37], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "37     INVALID_TOKEN = \"E3001\"\n38     TOKEN_EXPIRED = \"E3002\"\n39     INSUFFICIENT_PERMISSIONS = \"E3003\"\n", "col_offset": 20, "end_col_offset": 27, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/core/exceptions.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'E3002'", "line_number": 38, "line_range": [38], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "32 \n33     uvicorn.run(app, host=\"0.0.0.0\", port=8000)\n", "col_offset": 26, "end_col_offset": 35, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/main.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 605, "link": "https://cwe.mitre.org/data/definitions/605.html"}, "issue_severity": "MEDIUM", "issue_text": "Possible binding to all interfaces.", "line_number": 33, "line_range": [33], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b104_hardcoded_bind_all_interfaces.html", "test_id": "B104", "test_name": "hardcoded_bind_all_interfaces"}, {"code": "230                     db.commit()\n231             except:\n232                 pass\n233 \n", "col_offset": 12, "end_col_offset": 20, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/services/auth_service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 231, "line_range": [231, 232], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "363         with pytest.raises(ValueError):\n364             RegisterRequest(\n365                 email=\"<EMAIL>\", name=\"Test User\", password=\"123\"  # 太短\n366             )\n367 \n", "col_offset": 12, "end_col_offset": 13, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/tests/test_auth_system.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: '123'", "line_number": 364, "line_range": [364, 365, 366], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b106_hardcoded_password_funcarg.html", "test_id": "B106", "test_name": "hardcoded_password_funcarg"}, {"code": "378         )\n379         assert request.password == \"StrongPassword123\"\n380 \n", "col_offset": 35, "end_col_offset": 54, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/modules/user_management/tests/test_auth_system.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'StrongPassword123'", "line_number": 379, "line_range": [379], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "9 import logging\n10 import pickle\n11 from enum import Enum\n", "col_offset": 0, "end_col_offset": 13, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/cache/cache_manager.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 502, "link": "https://cwe.mitre.org/data/definitions/502.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with pickle module.", "line_number": 10, "line_range": [10], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b403-import-pickle", "test_id": "B403", "test_name": "blacklist"}, {"code": "126             elif self.config.serializer == CacheSerializer.PICKLE:\n127                 return pickle.loads(bytes.fromhex(value))\n128             else:  # STRING\n", "col_offset": 23, "end_col_offset": 57, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/cache/cache_manager.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 502, "link": "https://cwe.mitre.org/data/definitions/502.html"}, "issue_severity": "MEDIUM", "issue_text": "Pickle and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue.", "line_number": 127, "line_range": [127], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b301-pickle", "test_id": "B301", "test_name": "blacklist"}, {"code": "228         print(\"\\n3. Refresh Token管理:\")\n229         token_id = \"refresh_token_456\"\n230         token_data = {\n", "col_offset": 19, "end_col_offset": 38, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/cache/examples.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'refresh_token_456'", "line_number": 229, "line_range": [229], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "40         self.JWT_BLACKLIST_PREFIX = \"jwt:blacklist:\"\n41         self.REFRESH_TOKEN_PREFIX = \"refresh:token:\"\n42         self.LOGIN_ATTEMPTS_PREFIX = \"login:attempts:\"\n", "col_offset": 36, "end_col_offset": 52, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/cache/session_cache.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'refresh:token:'", "line_number": 41, "line_range": [41], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "99             jitter_range = delay * 0.1  # 10% 抖动\n100             delay += random.uniform(-jitter_range, jitter_range)\n101             delay = max(1, delay)  # 确保延迟至少1秒\n", "col_offset": 21, "end_col_offset": 64, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/messaging/retry_handler.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 100, "line_range": [100], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "27     LOGOUT = \"logout\"\n28     TOKEN_REFRESH = \"token_refresh\"\n29     TOKEN_REVOKED = \"token_revoked\"\n", "col_offset": 20, "end_col_offset": 35, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/monitoring/security_logger.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'token_refresh'", "line_number": 28, "line_range": [28], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "28     TOKEN_REFRESH = \"token_refresh\"\n29     TOKEN_REVOKED = \"token_revoked\"\n30 \n", "col_offset": 20, "end_col_offset": 35, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/monitoring/security_logger.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'token_revoked'", "line_number": 29, "line_range": [29], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "41     ACCOUNT_SUSPENDED = \"account_suspended\"\n42     PASSWORD_CHANGED = \"password_changed\"\n43     EMAIL_VERIFIED = \"email_verified\"\n", "col_offset": 23, "end_col_offset": 41, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/monitoring/security_logger.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'password_changed'", "line_number": 42, "line_range": [42], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "213 \n214                         retry_countdown += random.randint(0, 30)\n215 \n", "col_offset": 43, "end_col_offset": 64, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/decorators.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 214, "line_range": [214], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "201         # 模拟文件生成\n202         export_file = f\"/tmp/user_{user_id}_export.{export_format}\"\n203         with open(export_file, \"w\") as f:\n", "col_offset": 22, "end_col_offset": 67, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/handlers.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 377, "link": "https://cwe.mitre.org/data/definitions/377.html"}, "issue_severity": "MEDIUM", "issue_text": "Probable insecure usage of temp file/directory.", "line_number": 202, "line_range": [202], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b108_hardcoded_tmp_directory.html", "test_id": "B108", "test_name": "hardcoded_tmp_directory"}, {"code": "133         elif self.config.strategy == RetryStrategy.RANDOM:\n134             delay = random.randint(\n135                 self.config.base_delay,\n136                 min(self.config.base_delay * 2, self.config.max_delay),\n137             )\n138 \n", "col_offset": 20, "end_col_offset": 13, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/retry_handler.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 134, "line_range": [134, 135, 136, 137], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "144             jitter_min, jitter_max = self.config.jitter_range\n145             jitter = random.randint(jitter_min, jitter_max)\n146             delay += jitter\n", "col_offset": 21, "end_col_offset": 59, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/app/shared/tasks/retry_handler.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 145, "line_range": [145], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "78             # 创建用户\n79             user = User(\n80                 email=\"<EMAIL>\",\n81                 name=\"演示用户\",\n82                 password_digest=\"hashed_password\",\n83                 role=\"agent\",\n84                 ai_assistant_enabled=True,\n85             )\n86             session.add(user)\n", "col_offset": 19, "end_col_offset": 13, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/database/examples.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'hashed_password'", "line_number": 79, "line_range": [79, 80, 81, 82, 83, 84, 85], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b106_hardcoded_password_funcarg.html", "test_id": "B106", "test_name": "hardcoded_password_funcarg"}, {"code": "251         # 创建用户\n252         user = User(\n253             email=\"<EMAIL>\",\n254             name=\"测试用户\",\n255             password_digest=\"hashed_password\",\n256             role=\"agent\",\n257         )\n258         test_db.add(user)\n", "col_offset": 15, "end_col_offset": 9, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/database/tests.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'hashed_password'", "line_number": 252, "line_range": [252, 253, 254, 255, 256, 257], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b106_hardcoded_password_funcarg.html", "test_id": "B106", "test_name": "hardcoded_password_funcarg"}, {"code": "98             raise_unauthorized(\"缺少访问令牌\")\n99         if token != \"valid-token\":\n100             raise_unauthorized(\"访问令牌无效\")\n", "col_offset": 20, "end_col_offset": 33, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/examples/error_handling_demo.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'valid-token'", "line_number": 99, "line_range": [99], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "198 \n199         for _ in range(random.randint(3, 8)):\n200             error_code, message, severity = random.choice(error_scenarios)\n", "col_offset": 23, "end_col_offset": 43, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/examples/error_handling_demo.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 199, "line_range": [199], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "199         for _ in range(random.randint(3, 8)):\n200             error_code, message, severity = random.choice(error_scenarios)\n201             await record_error_event(\n", "col_offset": 44, "end_col_offset": 74, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/examples/error_handling_demo.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 200, "line_range": [200], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "204                 severity=severity,\n205                 user_id=f\"user_{random.randint(1, 100)}\",\n206                 request_path=\"/demo/errors/trigger-multiple\",\n", "col_offset": 32, "end_col_offset": 54, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/examples/error_handling_demo.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 205, "line_range": [205], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "124             await client.get(\"http://localhost:8000/demo/error\")\n125         except:\n126             pass\n127 \n", "col_offset": 8, "end_col_offset": 16, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/examples/middleware_demo.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 125, "line_range": [125, 126], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "9 import logging\n10 import subprocess\n11 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/install_elasticsearch_plugins.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 10, "line_range": [10], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "74         try:\n75             result = subprocess.run(\n76                 command.split(), capture_output=True, text=True, timeout=300\n77             )\n78 \n", "col_offset": 21, "end_col_offset": 13, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/install_elasticsearch_plugins.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 75, "line_range": [75, 76, 77], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "116         )\n117         result = subprocess.run(check_cmd.split(), capture_output=True, text=True)\n118 \n", "col_offset": 17, "end_col_offset": 82, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/install_elasticsearch_plugins.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 117, "line_range": [117], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "76                 await close_redis()\n77             except:\n78                 pass\n79 \n", "col_offset": 12, "end_col_offset": 20, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/monitor_cache_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 77, "line_range": [77, 78], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "189                 await self.minio_client.delete_file(file_path)\n190             except Exception:\n191                 pass\n192 \n", "col_offset": 12, "end_col_offset": 20, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/monitor_file_storage.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 190, "line_range": [190, 191], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "217                 await self.minio_client.delete_file(file_path)\n218             except Exception:\n219                 pass\n220 \n", "col_offset": 12, "end_col_offset": 20, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/monitor_file_storage.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 218, "line_range": [218, 219], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "268                 # 清屏\n269                 os.system(\"clear\" if os.name == \"posix\" else \"cls\")\n270 \n", "col_offset": 16, "end_col_offset": 67, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/monitor_message_queue.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "HIGH", "issue_text": "Starting a process with a shell, possible injection detected, security issue.", "line_number": 269, "line_range": [269], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b605_start_process_with_a_shell.html", "test_id": "B605", "test_name": "start_process_with_a_shell"}, {"code": "293 \n294             except Exception:\n295                 # 如果没有测试数据，跳过这些测试\n296                 pass\n297 \n", "col_offset": 12, "end_col_offset": 20, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/monitor_search_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 294, "line_range": [294, 295, 296], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "347 \n348             os.system(\"clear\" if os.name == \"posix\" else \"cls\")\n349 \n", "col_offset": 12, "end_col_offset": 63, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/monitor_tasks.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "HIGH", "issue_text": "Starting a process with a shell, possible injection detected, security issue.", "line_number": 348, "line_range": [348], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b605_start_process_with_a_shell.html", "test_id": "B605", "test_name": "start_process_with_a_shell"}, {"code": "102             await close_elasticsearch()\n103         except Exception:\n104             pass\n105 \n", "col_offset": 8, "end_col_offset": 16, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/setup_search_system.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 103, "line_range": [103, 104], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "249             # 测试JWT黑名单\n250             test_token = \"test.jwt.token.123\"\n251             await session_cache.add_token_to_blacklist(test_token, ttl=300)\n", "col_offset": 25, "end_col_offset": 45, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/test_cache_system.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'test.jwt.token.123'", "line_number": 250, "line_range": [250], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "257             # 测试refresh token\n258             token_id = \"refresh_token_123\"\n259             token_data = {\n", "col_offset": 23, "end_col_offset": 42, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/test_cache_system.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'refresh_token_123'", "line_number": 258, "line_range": [258], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "464             await self.minio_client.delete_file(\"test/.placeholder\")\n465         except Exception:\n466             pass\n467 \n", "col_offset": 8, "end_col_offset": 16, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/test_file_storage.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 465, "line_range": [465, 466], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "9 import os\n10 import subprocess\n11 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/test_message_queue.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 10, "line_range": [10], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "446                         queue_info_count += 1\n447                 except Exception:\n448                     pass\n449 \n", "col_offset": 16, "end_col_offset": 24, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/scripts/test_message_queue.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 447, "line_range": [447, 448], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "10 import statistics\n11 import subprocess\n12 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/benchmark_test.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 11, "line_range": [11], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "45             print(f\"运行 ab 测试: {url} (请求:{num_requests}, 并发:{concurrency})\")\n46             result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)\n47 \n", "col_offset": 21, "end_col_offset": 85, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/benchmark_test.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 46, "line_range": [46], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "212                 # AB测试 (如果可用)\n213                 if subprocess.run([\"which\", \"ab\"], capture_output=True).returncode == 0:\n214                     try:\n", "col_offset": 19, "end_col_offset": 71, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/benchmark_test.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 213, "line_range": [213], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "212                 # AB测试 (如果可用)\n213                 if subprocess.run([\"which\", \"ab\"], capture_output=True).returncode == 0:\n214                     try:\n", "col_offset": 19, "end_col_offset": 71, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/benchmark_test.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 213, "line_range": [213], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "10 import statistics\n11 import subprocess\n12 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/database_performance_test.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 11, "line_range": [11], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "206             init_cmd = [\"pgbench\", \"-i\", \"-s\", \"1\", \"chaiguanjia\"]\n207             init_result = subprocess.run(\n208                 init_cmd, capture_output=True, text=True, timeout=120\n209             )\n210 \n", "col_offset": 26, "end_col_offset": 13, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/database_performance_test.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 207, "line_range": [207, 208, 209], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "228             start_time = time.time()\n229             result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)\n230             total_time = time.time() - start_time\n", "col_offset": 21, "end_col_offset": 85, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/database_performance_test.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 229, "line_range": [229], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "259             start_time = time.time()\n260             result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)\n261             total_time = time.time() - start_time\n", "col_offset": 21, "end_col_offset": 85, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/database_performance_test.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 260, "line_range": [260], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "127         search_data = {\n128             \"query\": random.choice(search_queries),\n129             \"index\": \"test_index\",\n", "col_offset": 21, "end_col_offset": 50, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/locustfile.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 128, "line_range": [128], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "273     # 每100个请求收集一次系统指标\n274     if random.randint(1, 100) == 1:\n275         metrics = system_monitor.collect_metrics()\n", "col_offset": 7, "end_col_offset": 29, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/locustfile.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 274, "line_range": [274], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "10 import os\n11 import subprocess\n12 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/run_performance_tests.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 11, "line_range": [11], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "215         try:\n216             result = subprocess.run(\n217                 cmd, capture_output=True, text=True, timeout=duration + 60\n218             )\n219 \n", "col_offset": 21, "end_col_offset": 13, "filename": "/Users/<USER>/Documents/Augment/chaiguanjia8_10/backend/tests/performance/run_performance_tests.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 216, "line_range": [216, 217, 218], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}]}