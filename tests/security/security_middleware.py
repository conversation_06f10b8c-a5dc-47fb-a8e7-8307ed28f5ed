"""
安全中间件增强
提供额外的安全保护功能
"""

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import time
import logging
from typing import Dict, Set
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class SecurityEnhancementMiddleware(BaseHTTPMiddleware):
    """安全增强中间件"""

    def __init__(self, app, rate_limit: int = 100, time_window: int = 60):
        super().__init__(app)
        self.rate_limit = rate_limit
        self.time_window = time_window
        self.request_counts: Dict[str, deque] = defaultdict(deque)
        self.blocked_ips: Set[str] = set()

    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        client_ip = self._get_client_ip(request)

        # 检查IP是否被阻止
        if client_ip in self.blocked_ips:
            logger.warning(f"Blocked IP attempted access: {client_ip}")
            return JSONResponse(
                status_code=429,
                content={"error": "Too many requests"}
            )

        # 速率限制检查
        if self._is_rate_limited(client_ip):
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            return JSONResponse(
                status_code=429,
                content={"error": "Rate limit exceeded"}
            )

        # 安全头检查
        if not self._validate_security_headers(request):
            logger.warning(f"Security headers validation failed for IP: {client_ip}")
            return JSONResponse(
                status_code=400,
                content={"error": "Invalid request headers"}
            )

        # 处理请求
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time

        # 添加安全响应头
        self._add_security_headers(response)

        # 记录请求日志
        self._log_request(request, response, process_time, client_ip)

        return response

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        return request.client.host if request.client else "unknown"

    def _is_rate_limited(self, client_ip: str) -> bool:
        """检查是否超过速率限制"""
        now = time.time()
        requests = self.request_counts[client_ip]

        # 清理过期请求
        while requests and requests[0] < now - self.time_window:
            requests.popleft()

        # 检查请求数量
        if len(requests) >= self.rate_limit:
            # 如果超过限制，将IP加入阻止列表
            self.blocked_ips.add(client_ip)
            return True

        # 记录当前请求
        requests.append(now)
        return False

    def _validate_security_headers(self, request: Request) -> bool:
        """验证安全头"""
        # 检查User-Agent
        user_agent = request.headers.get("User-Agent", "")
        if not user_agent or len(user_agent) < 10:
            return False

        # 检查可疑的User-Agent
        suspicious_agents = ["sqlmap", "nikto", "nmap", "masscan", "zap"]
        if any(agent in user_agent.lower() for agent in suspicious_agents):
            return False

        return True

    def _add_security_headers(self, response: Response):
        """添加安全响应头"""
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"

    def _log_request(self, request: Request, response: Response, process_time: float, client_ip: str):
        """记录请求日志"""
        logger.info(
            f"Request: {request.method} {request.url.path} "
            f"IP: {client_ip} "
            f"Status: {response.status_code} "
            f"Time: {process_time:.3f}s"
        )

        # 记录可疑请求
        if response.status_code >= 400:
            logger.warning(
                f"Suspicious request: {request.method} {request.url.path} "
                f"IP: {client_ip} Status: {response.status_code}"
            )
