#!/usr/bin/env python3
"""
安全测试工具配置脚本
用于配置和初始化安全渗透测试工具
"""

import os
import sys
import subprocess
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SecurityToolsSetup:
    """安全工具配置类"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.security_dir = Path(__file__).parent
        self.tools_dir = self.security_dir / "tools"
        self.config_dir = self.security_dir / "config"
        self.reports_dir = self.security_dir / "reports"
        
        # 创建必要的目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录结构"""
        directories = [
            self.tools_dir,
            self.config_dir,
            self.reports_dir,
            self.security_dir / "scripts",
            self.security_dir / "payloads",
            self.security_dir / "logs"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建目录: {directory}")
    
    def check_python_dependencies(self) -> bool:
        """检查Python安全工具依赖"""
        required_packages = [
            "requests",
            "beautifulsoup4",
            "sqlparse",
            "pyjwt",
            "cryptography",
            "bandit",
            "safety",
            "semgrep"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
                logger.info(f"✓ {package} 已安装")
            except ImportError:
                missing_packages.append(package)
                logger.warning(f"✗ {package} 未安装")
        
        if missing_packages:
            logger.info("安装缺失的包...")
            subprocess.run([
                sys.executable, "-m", "pip", "install"
            ] + missing_packages, check=True)
        
        return len(missing_packages) == 0
    
    def setup_bandit_config(self):
        """配置Bandit代码安全扫描工具"""
        bandit_config = {
            "tests": [
                "B101", "B102", "B103", "B104", "B105", "B106", "B107",
                "B108", "B110", "B112", "B201", "B301", "B302", "B303",
                "B304", "B305", "B306", "B307", "B308", "B309", "B310",
                "B311", "B312", "B313", "B314", "B315", "B316", "B317",
                "B318", "B319", "B320", "B321", "B322", "B323", "B324",
                "B325", "B401", "B402", "B403", "B404", "B405", "B406",
                "B407", "B408", "B409", "B410", "B411", "B412", "B413",
                "B501", "B502", "B503", "B504", "B505", "B506", "B507",
                "B601", "B602", "B603", "B604", "B605", "B606", "B607",
                "B608", "B609", "B610", "B611", "B701", "B702", "B703"
            ],
            "skips": ["B101"],  # 跳过assert语句检查（测试代码中常用）
            "exclude_dirs": [
                "tests",
                "migrations",
                "venv",
                "__pycache__"
            ]
        }
        
        config_file = self.config_dir / "bandit.yaml"
        with open(config_file, 'w') as f:
            import yaml
            yaml.dump(bandit_config, f, default_flow_style=False)
        
        logger.info(f"Bandit配置已保存到: {config_file}")
    
    def setup_safety_config(self):
        """配置Safety依赖安全检查工具"""
        safety_config = {
            "ignore": [],  # 忽略的漏洞ID
            "output": "json",
            "full-report": True
        }
        
        config_file = self.config_dir / "safety.json"
        with open(config_file, 'w') as f:
            json.dump(safety_config, f, indent=2)
        
        logger.info(f"Safety配置已保存到: {config_file}")
    
    def create_zap_config(self):
        """创建OWASP ZAP配置"""
        zap_config = {
            "target_url": "http://localhost:8000",
            "api_key": "changeme",
            "scan_policy": "Default Policy",
            "context_name": "chaiguanjia",
            "authentication": {
                "type": "jwt",
                "login_url": "/api/v1/auth/login",
                "username_field": "email",
                "password_field": "password"
            },
            "exclude_urls": [
                ".*logout.*",
                ".*\\.css",
                ".*\\.js",
                ".*\\.png",
                ".*\\.jpg",
                ".*\\.gif"
            ]
        }
        
        config_file = self.config_dir / "zap_config.json"
        with open(config_file, 'w') as f:
            json.dump(zap_config, f, indent=2)
        
        logger.info(f"ZAP配置已保存到: {config_file}")
    
    def create_sqlmap_config(self):
        """创建SQLMap配置"""
        sqlmap_config = {
            "target_urls": [
                "http://localhost:8000/api/v1/users/search?keyword=test",
                "http://localhost:8000/api/v1/permissions/search?keyword=test",
                "http://localhost:8000/api/v1/roles/search?keyword=test"
            ],
            "headers": {
                "Authorization": "Bearer <JWT_TOKEN>",
                "Content-Type": "application/json"
            },
            "techniques": "BEUSTQ",
            "level": 3,
            "risk": 2,
            "threads": 5,
            "batch": True,
            "output_dir": str(self.reports_dir / "sqlmap")
        }
        
        config_file = self.config_dir / "sqlmap_config.json"
        with open(config_file, 'w') as f:
            json.dump(sqlmap_config, f, indent=2)
        
        logger.info(f"SQLMap配置已保存到: {config_file}")
    
    def create_test_payloads(self):
        """创建测试载荷文件"""
        # SQL注入载荷
        sql_payloads = [
            "' OR '1'='1",
            "' OR '1'='1' --",
            "' OR '1'='1' /*",
            "'; DROP TABLE users; --",
            "' UNION SELECT NULL,NULL,NULL --",
            "' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 --",
            "1' AND SLEEP(5) --",
            "1' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a) --"
        ]
        
        # XSS载荷
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus><option>test</option></select>"
        ]
        
        # 命令注入载荷
        command_payloads = [
            "; ls -la",
            "| whoami",
            "&& cat /etc/passwd",
            "; cat /etc/shadow",
            "| id",
            "&& ps aux",
            "; netstat -an",
            "| env"
        ]
        
        payloads = {
            "sql_injection": sql_payloads,
            "xss": xss_payloads,
            "command_injection": command_payloads
        }
        
        for payload_type, payload_list in payloads.items():
            payload_file = self.security_dir / "payloads" / f"{payload_type}.txt"
            with open(payload_file, 'w') as f:
                f.write('\n'.join(payload_list))
            logger.info(f"载荷文件已创建: {payload_file}")
    
    def create_test_scripts(self):
        """创建测试脚本"""
        # 创建JWT测试脚本
        jwt_test_script = '''#!/usr/bin/env python3
"""JWT安全测试脚本"""

import jwt
import requests
import json
from datetime import datetime, timedelta

def test_jwt_security(base_url, token):
    """测试JWT令牌安全性"""
    print("=== JWT安全测试 ===")
    
    # 测试1: 解码JWT令牌
    try:
        decoded = jwt.decode(token, options={"verify_signature": False})
        print(f"JWT载荷: {json.dumps(decoded, indent=2)}")
    except Exception as e:
        print(f"JWT解码失败: {e}")
    
    # 测试2: 尝试修改JWT令牌
    try:
        header = jwt.get_unverified_header(token)
        payload = jwt.decode(token, options={"verify_signature": False})
        
        # 修改用户ID
        payload['sub'] = '999999'
        modified_token = jwt.encode(payload, 'secret', algorithm=header.get('alg', 'HS256'))
        
        # 测试修改后的令牌
        headers = {'Authorization': f'Bearer {modified_token}'}
        response = requests.get(f"{base_url}/api/v1/users/profile", headers=headers)
        print(f"修改令牌测试: {response.status_code}")
        
    except Exception as e:
        print(f"JWT修改测试失败: {e}")

if __name__ == "__main__":
    # 使用示例
    base_url = "http://localhost:8000"
    token = "your_jwt_token_here"
    test_jwt_security(base_url, token)
'''
        
        script_file = self.security_dir / "scripts" / "jwt_test.py"
        with open(script_file, 'w') as f:
            f.write(jwt_test_script)
        script_file.chmod(0o755)
        
        logger.info(f"JWT测试脚本已创建: {script_file}")
    
    def run_setup(self):
        """运行完整的安全工具配置"""
        logger.info("开始配置安全测试工具...")
        
        try:
            # 检查Python依赖
            self.check_python_dependencies()
            
            # 配置各种工具
            self.setup_bandit_config()
            self.setup_safety_config()
            self.create_zap_config()
            self.create_sqlmap_config()
            
            # 创建测试资源
            self.create_test_payloads()
            self.create_test_scripts()
            
            logger.info("✓ 安全工具配置完成!")
            logger.info(f"配置文件位置: {self.config_dir}")
            logger.info(f"测试脚本位置: {self.security_dir / 'scripts'}")
            logger.info(f"载荷文件位置: {self.security_dir / 'payloads'}")
            
        except Exception as e:
            logger.error(f"配置失败: {e}")
            sys.exit(1)

def main():
    """主函数"""
    setup = SecurityToolsSetup()
    setup.run_setup()

if __name__ == "__main__":
    main()
