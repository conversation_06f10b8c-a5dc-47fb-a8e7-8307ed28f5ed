#!/usr/bin/env python3
"""JWT安全测试脚本"""

import jwt
import requests
import json
from datetime import datetime, timedelta

def test_jwt_security(base_url, token):
    """测试JWT令牌安全性"""
    print("=== JWT安全测试 ===")
    
    # 测试1: 解码JWT令牌
    try:
        decoded = jwt.decode(token, options={"verify_signature": False})
        print(f"JWT载荷: {json.dumps(decoded, indent=2)}")
    except Exception as e:
        print(f"JWT解码失败: {e}")
    
    # 测试2: 尝试修改JWT令牌
    try:
        header = jwt.get_unverified_header(token)
        payload = jwt.decode(token, options={"verify_signature": False})
        
        # 修改用户ID
        payload['sub'] = '999999'
        modified_token = jwt.encode(payload, 'secret', algorithm=header.get('alg', 'HS256'))
        
        # 测试修改后的令牌
        headers = {'Authorization': f'Bearer {modified_token}'}
        response = requests.get(f"{base_url}/api/v1/users/profile", headers=headers)
        print(f"修改令牌测试: {response.status_code}")
        
    except Exception as e:
        print(f"JWT修改测试失败: {e}")

if __name__ == "__main__":
    # 使用示例
    base_url = "http://localhost:8000"
    token = "your_jwt_token_here"
    test_jwt_security(base_url, token)
