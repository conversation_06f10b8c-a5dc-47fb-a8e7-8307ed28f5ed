#!/usr/bin/env python3
"""
静态安全分析脚本
对代码库进行静态安全分析，不依赖于运行时环境
"""

import os
import sys
import subprocess
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StaticSecurityAnalyzer:
    """静态安全分析器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.security_dir = Path(__file__).parent
        self.reports_dir = self.security_dir / "reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        # 安全问题统计
        self.security_issues = {
            "critical": [],
            "high": [],
            "medium": [],
            "low": [],
            "info": []
        }
    
    def run_bandit_scan(self) -> Dict[str, Any]:
        """运行Bandit代码安全扫描"""
        logger.info("运行Bandit安全扫描...")
        
        try:
            # 运行Bandit扫描
            result = subprocess.run([
                "bandit", "-r", str(self.project_root / "backend"),
                "-f", "json",
                "-o", str(self.reports_dir / "bandit_report.json"),
                "--skip", "B101",  # 跳过assert语句检查
                "--exclude", "tests,migrations,venv,__pycache__"
            ], capture_output=True, text=True, timeout=120)
            
            # 读取报告
            report_file = self.reports_dir / "bandit_report.json"
            if report_file.exists():
                with open(report_file, 'r') as f:
                    bandit_report = json.load(f)
                
                # 分析结果
                issues = bandit_report.get("results", [])
                for issue in issues:
                    severity = issue.get("issue_severity", "UNDEFINED").lower()
                    confidence = issue.get("issue_confidence", "UNDEFINED").lower()
                    
                    security_issue = {
                        "tool": "bandit",
                        "type": issue.get("test_name", "Unknown"),
                        "severity": severity,
                        "confidence": confidence,
                        "file": issue.get("filename", ""),
                        "line": issue.get("line_number", 0),
                        "description": issue.get("issue_text", ""),
                        "code": issue.get("code", "")
                    }
                    
                    # 根据严重程度分类
                    if severity == "high":
                        self.security_issues["high"].append(security_issue)
                    elif severity == "medium":
                        self.security_issues["medium"].append(security_issue)
                    elif severity == "low":
                        self.security_issues["low"].append(security_issue)
                    else:
                        self.security_issues["info"].append(security_issue)
                
                logger.info(f"Bandit扫描完成，发现 {len(issues)} 个问题")
                return bandit_report
            else:
                logger.warning("Bandit报告文件不存在")
                return {}
                
        except subprocess.TimeoutExpired:
            logger.error("Bandit扫描超时")
            return {}
        except Exception as e:
            logger.error(f"Bandit扫描失败: {e}")
            return {}
    
    def analyze_hardcoded_secrets(self) -> List[Dict[str, Any]]:
        """分析硬编码密钥和敏感信息"""
        logger.info("分析硬编码密钥和敏感信息...")
        
        secrets_patterns = [
            (r'password\s*=\s*["\'][^"\']{8,}["\']', "硬编码密码"),
            (r'secret\s*=\s*["\'][^"\']{16,}["\']', "硬编码密钥"),
            (r'api_key\s*=\s*["\'][^"\']{16,}["\']', "硬编码API密钥"),
            (r'token\s*=\s*["\'][^"\']{20,}["\']', "硬编码令牌"),
            (r'["\'][A-Za-z0-9+/]{40,}={0,2}["\']', "可能的Base64编码密钥"),
            (r'sk-[A-Za-z0-9]{48}', "OpenAI API密钥"),
            (r'ghp_[A-Za-z0-9]{36}', "GitHub个人访问令牌"),
            (r'xoxb-[A-Za-z0-9-]{50,}', "Slack Bot令牌"),
        ]
        
        secrets_found = []
        
        # 扫描Python文件
        for py_file in self.project_root.rglob("*.py"):
            if any(exclude in str(py_file) for exclude in ["venv", "__pycache__", ".git"]):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern, description in secrets_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        secrets_found.append({
                            "tool": "secrets_analyzer",
                            "type": "hardcoded_secret",
                            "severity": "high",
                            "file": str(py_file.relative_to(self.project_root)),
                            "line": line_num,
                            "description": description,
                            "match": match.group(),
                            "pattern": pattern
                        })
                        
            except Exception as e:
                logger.warning(f"无法读取文件 {py_file}: {e}")
        
        # 扫描配置文件
        config_files = [".env", "config.py", "settings.py", "docker-compose.yml"]
        for config_file in config_files:
            config_path = self.project_root / config_file
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    for pattern, description in secrets_patterns:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            line_num = content[:match.start()].count('\n') + 1
                            secrets_found.append({
                                "tool": "secrets_analyzer",
                                "type": "hardcoded_secret",
                                "severity": "critical",
                                "file": config_file,
                                "line": line_num,
                                "description": f"{description} (配置文件)",
                                "match": match.group(),
                                "pattern": pattern
                            })
                            
                except Exception as e:
                    logger.warning(f"无法读取配置文件 {config_path}: {e}")
        
        # 添加到安全问题列表
        for secret in secrets_found:
            if secret["severity"] == "critical":
                self.security_issues["critical"].append(secret)
            elif secret["severity"] == "high":
                self.security_issues["high"].append(secret)
        
        logger.info(f"发现 {len(secrets_found)} 个潜在的硬编码密钥")
        return secrets_found
    
    def analyze_sql_injection_risks(self) -> List[Dict[str, Any]]:
        """分析SQL注入风险"""
        logger.info("分析SQL注入风险...")
        
        sql_patterns = [
            (r'\.execute\s*\(\s*["\'].*%.*["\']', "字符串格式化SQL查询"),
            (r'\.execute\s*\(\s*f["\'].*\{.*\}.*["\']', "f-string SQL查询"),
            (r'\.execute\s*\(\s*.*\+.*\)', "字符串拼接SQL查询"),
            (r'query\s*=\s*["\'].*%.*["\']', "字符串格式化查询构建"),
            (r'sql\s*=\s*.*\+.*', "字符串拼接SQL构建"),
        ]
        
        sql_risks = []
        
        for py_file in self.project_root.rglob("*.py"):
            if any(exclude in str(py_file) for exclude in ["venv", "__pycache__", ".git", "tests"]):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern, description in sql_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        sql_risks.append({
                            "tool": "sql_analyzer",
                            "type": "sql_injection_risk",
                            "severity": "high",
                            "file": str(py_file.relative_to(self.project_root)),
                            "line": line_num,
                            "description": description,
                            "code": match.group(),
                            "recommendation": "使用参数化查询或ORM"
                        })
                        
            except Exception as e:
                logger.warning(f"无法读取文件 {py_file}: {e}")
        
        # 添加到安全问题列表
        for risk in sql_risks:
            self.security_issues["high"].append(risk)
        
        logger.info(f"发现 {len(sql_risks)} 个潜在的SQL注入风险")
        return sql_risks
    
    def analyze_xss_risks(self) -> List[Dict[str, Any]]:
        """分析XSS风险"""
        logger.info("分析XSS风险...")
        
        xss_patterns = [
            (r'render_template_string\s*\(.*\)', "动态模板渲染"),
            (r'\.innerHTML\s*=', "直接设置innerHTML"),
            (r'document\.write\s*\(', "使用document.write"),
            (r'eval\s*\(', "使用eval函数"),
            (r'dangerouslySetInnerHTML', "React危险HTML设置"),
        ]
        
        xss_risks = []
        
        # 扫描Python文件
        for py_file in self.project_root.rglob("*.py"):
            if any(exclude in str(py_file) for exclude in ["venv", "__pycache__", ".git"]):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern, description in xss_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        xss_risks.append({
                            "tool": "xss_analyzer",
                            "type": "xss_risk",
                            "severity": "medium",
                            "file": str(py_file.relative_to(self.project_root)),
                            "line": line_num,
                            "description": description,
                            "code": match.group(),
                            "recommendation": "使用安全的模板引擎和输出编码"
                        })
                        
            except Exception as e:
                logger.warning(f"无法读取文件 {py_file}: {e}")
        
        # 扫描JavaScript/TypeScript文件
        for js_file in self.project_root.rglob("*.js"):
            try:
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern, description in xss_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        xss_risks.append({
                            "tool": "xss_analyzer",
                            "type": "xss_risk",
                            "severity": "medium",
                            "file": str(js_file.relative_to(self.project_root)),
                            "line": line_num,
                            "description": description,
                            "code": match.group(),
                            "recommendation": "使用安全的DOM操作方法"
                        })
                        
            except Exception as e:
                logger.warning(f"无法读取文件 {js_file}: {e}")
        
        # 添加到安全问题列表
        for risk in xss_risks:
            self.security_issues["medium"].append(risk)
        
        logger.info(f"发现 {len(xss_risks)} 个潜在的XSS风险")
        return xss_risks
    
    def run_comprehensive_analysis(self) -> Dict[str, Any]:
        """运行全面的静态安全分析"""
        logger.info("开始全面的静态安全分析...")
        
        analysis_results = {
            "timestamp": __import__('datetime').datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "analyses": {}
        }
        
        # 运行各种分析
        try:
            analysis_results["analyses"]["bandit"] = self.run_bandit_scan()
        except Exception as e:
            logger.error(f"Bandit分析失败: {e}")
            analysis_results["analyses"]["bandit"] = {"error": str(e)}
        
        try:
            analysis_results["analyses"]["secrets"] = self.analyze_hardcoded_secrets()
        except Exception as e:
            logger.error(f"密钥分析失败: {e}")
            analysis_results["analyses"]["secrets"] = {"error": str(e)}
        
        try:
            analysis_results["analyses"]["sql_injection"] = self.analyze_sql_injection_risks()
        except Exception as e:
            logger.error(f"SQL注入分析失败: {e}")
            analysis_results["analyses"]["sql_injection"] = {"error": str(e)}
        
        try:
            analysis_results["analyses"]["xss"] = self.analyze_xss_risks()
        except Exception as e:
            logger.error(f"XSS分析失败: {e}")
            analysis_results["analyses"]["xss"] = {"error": str(e)}
        
        # 汇总统计
        analysis_results["summary"] = {
            "total_issues": sum(len(issues) for issues in self.security_issues.values()),
            "critical_issues": len(self.security_issues["critical"]),
            "high_issues": len(self.security_issues["high"]),
            "medium_issues": len(self.security_issues["medium"]),
            "low_issues": len(self.security_issues["low"]),
            "info_issues": len(self.security_issues["info"])
        }
        
        # 保存完整报告
        report_file = self.reports_dir / "static_security_analysis.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"静态安全分析完成，报告保存到: {report_file}")
        return analysis_results

def main():
    """主函数"""
    analyzer = StaticSecurityAnalyzer()
    results = analyzer.run_comprehensive_analysis()
    
    # 打印摘要
    summary = results["summary"]
    print("\n" + "="*60)
    print("静态安全分析报告摘要")
    print("="*60)
    print(f"总问题数: {summary['total_issues']}")
    print(f"严重问题: {summary['critical_issues']}")
    print(f"高危问题: {summary['high_issues']}")
    print(f"中危问题: {summary['medium_issues']}")
    print(f"低危问题: {summary['low_issues']}")
    print(f"信息问题: {summary['info_issues']}")
    print("="*60)
    
    # 如果有严重或高危问题，返回非零退出码
    if summary['critical_issues'] > 0 or summary['high_issues'] > 0:
        print("⚠️  发现严重或高危安全问题，请及时修复！")
        sys.exit(1)
    else:
        print("✅ 未发现严重安全问题")
        sys.exit(0)

if __name__ == "__main__":
    main()
