# 测试环境安全配置
# 请勿在生产环境中使用这些值

# JWT测试令牌
TEST_JWT_TOKEN=mock_jwt_token_for_testing_only
TEST_INVALID_TOKEN=invalid.token.string

# 测试密码
TEST_PASSWORD_WEAK=onlyletters
TEST_PASSWORD_STRONG=StrongPassword123
TEST_PASSWORD_DEFAULT=Password123

# 数据库测试配置
TEST_DB_PASSWORD=test_password_123
TEST_DB_HOST=localhost
TEST_DB_PORT=5432

# Redis测试配置
TEST_REDIS_PASSWORD=test_redis_password

# 加密密钥（测试用）
TEST_ENCRYPTION_KEY=test_encryption_key_32_chars_long

# API测试密钥
TEST_API_KEY=test_api_key_for_testing_only

# 注意：
# 1. 这些值仅用于测试环境
# 2. 生产环境必须使用强密码和随机生成的密钥
# 3. 定期轮换所有密钥和密码
# 4. 使用专业的密钥管理系统
