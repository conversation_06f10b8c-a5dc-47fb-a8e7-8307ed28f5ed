#!/usr/bin/env python3
"""
系统集成验证脚本
Task I-4.4：系统集成验证的自动化测试脚本
"""

import asyncio
import json
import subprocess
import sys
import time
from typing import Any, Dict, List

import httpx
import pika
import psycopg2
import redis
from elasticsearch import Elasticsearch


class SystemIntegrationVerifier:
    """系统集成验证器"""

    def __init__(self):
        self.results = {
            "environment_verification": {},
            "component_verification": {},
            "api_verification": {},
            "data_flow_verification": {},
            "performance_verification": {},
            "security_verification": {},
            "monitoring_verification": {},
            "error_handling_verification": {},
            "final_integration_verification": {},
        }
        self.errors = []

    def log_result(
        self, category: str, test_name: str, success: bool, details: str = ""
    ):
        """记录测试结果"""
        if category not in self.results:
            self.results[category] = {}

        self.results[category][test_name] = {
            "success": success,
            "details": details,
            "timestamp": time.time(),
        }

        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} [{category}] {test_name}: {details}")

        if not success:
            self.errors.append(f"[{category}] {test_name}: {details}")

    def verify_docker_environment(self) -> bool:
        """阶段1：环境基础验证"""
        print("\n=== 阶段1：环境基础验证 ===")

        try:
            # 检查Docker Compose服务状态
            result = subprocess.run(
                ["docker-compose", "ps", "--format", "json"],
                capture_output=True,
                text=True,
                cwd="/Users/<USER>/Documents/Augment/chaiguanjia8_10",
            )

            if result.returncode != 0:
                self.log_result(
                    "environment_verification",
                    "docker_compose_status",
                    False,
                    f"Docker Compose命令失败: {result.stderr}",
                )
                return False

            # 解析服务状态
            services = []
            for line in result.stdout.strip().split("\n"):
                if line.strip():
                    try:
                        service = json.loads(line)
                        services.append(service)
                    except json.JSONDecodeError:
                        continue

            # 验证核心服务状态
            core_services = [
                "postgresql",
                "redis",
                "rabbitmq",
                "elasticsearch",
                "nginx",
            ]
            running_services = []

            for service in services:
                service_name = service.get("Service", "")
                state = service.get("State", "")

                if service_name in core_services:
                    if "running" in state.lower():
                        running_services.append(service_name)
                        self.log_result(
                            "environment_verification",
                            f"{service_name}_status",
                            True,
                            f"服务运行正常: {state}",
                        )
                    else:
                        self.log_result(
                            "environment_verification",
                            f"{service_name}_status",
                            False,
                            f"服务状态异常: {state}",
                        )

            # 检查网络连通性
            self.verify_network_connectivity()

            return len(running_services) >= 4  # 至少4个核心服务运行

        except Exception as e:
            self.log_result(
                "environment_verification", "docker_environment", False, str(e)
            )
            return False

    def verify_network_connectivity(self):
        """验证网络连通性"""
        # 测试服务端口连通性
        ports_to_test = [
            ("PostgreSQL", "localhost", 5432),
            ("Redis", "localhost", 6379),
            ("RabbitMQ", "localhost", 5672),
            ("RabbitMQ Management", "localhost", 15672),
            ("Elasticsearch", "localhost", 9200),
            ("Nginx", "localhost", 80),
        ]

        for service_name, host, port in ports_to_test:
            try:
                import socket

                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((host, port))
                sock.close()

                if result == 0:
                    self.log_result(
                        "environment_verification",
                        f"{service_name}_connectivity",
                        True,
                        f"端口 {port} 连通正常",
                    )
                else:
                    self.log_result(
                        "environment_verification",
                        f"{service_name}_connectivity",
                        False,
                        f"端口 {port} 连接失败",
                    )
            except Exception as e:
                self.log_result(
                    "environment_verification",
                    f"{service_name}_connectivity",
                    False,
                    str(e),
                )

    def verify_components(self) -> bool:
        """阶段2：组件功能验证"""
        print("\n=== 阶段2：组件功能验证 ===")

        success_count = 0
        total_tests = 5

        # PostgreSQL验证
        if self.verify_postgresql():
            success_count += 1

        # Redis验证
        if self.verify_redis():
            success_count += 1

        # RabbitMQ验证
        if self.verify_rabbitmq():
            success_count += 1

        # Elasticsearch验证
        if self.verify_elasticsearch():
            success_count += 1

        # Nginx验证
        if self.verify_nginx():
            success_count += 1

        return success_count >= 4  # 至少4个组件正常

    def verify_postgresql(self) -> bool:
        """验证PostgreSQL数据库"""
        try:
            # 使用Docker exec直接测试数据库连接
            result = subprocess.run(
                [
                    "docker",
                    "exec",
                    "chaiguanjia_postgresql",
                    "psql",
                    "-U",
                    "dev_admin",
                    "-d",
                    "chaiguanjia_dev",
                    "-c",
                    "SELECT version();",
                ],
                capture_output=True,
                text=True,
                timeout=10,
            )

            if result.returncode == 0:
                version_info = result.stdout.strip()

                # 测试中文支持
                chinese_result = subprocess.run(
                    [
                        "docker",
                        "exec",
                        "chaiguanjia_postgresql",
                        "psql",
                        "-U",
                        "dev_admin",
                        "-d",
                        "chaiguanjia_dev",
                        "-c",
                        "SELECT '测试中文' as test_chinese;",
                    ],
                    capture_output=True,
                    text=True,
                    timeout=10,
                )

                if chinese_result.returncode == 0:
                    self.log_result(
                        "component_verification",
                        "postgresql",
                        True,
                        f"PostgreSQL连接正常，版本信息获取成功，中文支持正常",
                    )
                    return True
                else:
                    self.log_result(
                        "component_verification",
                        "postgresql",
                        False,
                        f"PostgreSQL中文测试失败: {chinese_result.stderr}",
                    )
                    return False
            else:
                self.log_result(
                    "component_verification",
                    "postgresql",
                    False,
                    f"PostgreSQL连接失败: {result.stderr}",
                )
                return False

            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]

            # 测试中文支持
            cursor.execute("SELECT '测试中文' as test_chinese;")
            chinese_test = cursor.fetchone()[0]

            cursor.close()
            conn.close()

            self.log_result(
                "component_verification",
                "postgresql",
                True,
                f"PostgreSQL连接正常，版本: {version[:50]}..., 中文支持: {chinese_test}",
            )
            return True

        except Exception as e:
            self.log_result("component_verification", "postgresql", False, str(e))
            return False

    def verify_redis(self) -> bool:
        """验证Redis缓存"""
        try:
            r = redis.Redis(
                host="localhost",
                port=6379,
                password="chaiguanjia2024",
                decode_responses=True,
            )

            # 测试基本操作
            r.set("test_key", "test_value", ex=60)
            value = r.get("test_key")

            # 测试中文支持
            r.set("test_chinese", "测试中文", ex=60)
            chinese_value = r.get("test_chinese")

            # 清理测试数据
            r.delete("test_key", "test_chinese")

            if value == "test_value" and chinese_value == "测试中文":
                self.log_result(
                    "component_verification",
                    "redis",
                    True,
                    "Redis连接正常，读写功能正常，中文支持正常",
                )
                return True
            else:
                self.log_result(
                    "component_verification",
                    "redis",
                    False,
                    f"Redis读写测试失败: {value}, {chinese_value}",
                )
                return False

        except Exception as e:
            self.log_result("component_verification", "redis", False, str(e))
            return False

    def verify_rabbitmq(self) -> bool:
        """验证RabbitMQ消息队列"""
        try:
            connection = pika.BlockingConnection(
                pika.ConnectionParameters(
                    host="localhost",
                    port=5672,
                    virtual_host="chaiguanjia_dev",
                    credentials=pika.PlainCredentials("dev_admin", "dev_password"),
                )
            )

            channel = connection.channel()

            # 声明测试队列
            queue_name = "test_queue"
            channel.queue_declare(queue=queue_name, durable=False)

            # 发送测试消息
            test_message = "测试消息"
            channel.basic_publish(
                exchange="", routing_key=queue_name, body=test_message
            )

            # 接收测试消息
            method_frame, header_frame, body = channel.basic_get(queue=queue_name)

            if body and body.decode("utf-8") == test_message:
                # 确认消息
                channel.basic_ack(method_frame.delivery_tag)

                # 清理测试队列
                channel.queue_delete(queue=queue_name)

                connection.close()

                self.log_result(
                    "component_verification",
                    "rabbitmq",
                    True,
                    "RabbitMQ连接正常，消息收发正常，中文支持正常",
                )
                return True
            else:
                connection.close()
                self.log_result(
                    "component_verification",
                    "rabbitmq",
                    False,
                    f"RabbitMQ消息收发测试失败: {body}",
                )
                return False

        except Exception as e:
            self.log_result("component_verification", "rabbitmq", False, str(e))
            return False

    def verify_elasticsearch(self) -> bool:
        """验证Elasticsearch搜索引擎"""
        try:
            es = Elasticsearch([{"host": "localhost", "port": 9200, "scheme": "http"}])

            # 检查集群健康状态
            health = es.cluster.health()

            if health["status"] in ["green", "yellow"]:
                # 测试索引操作
                index_name = "test_index"

                # 创建测试文档
                doc = {
                    "title": "测试文档",
                    "content": "这是一个测试文档，用于验证Elasticsearch功能",
                    "timestamp": time.time(),
                }

                # 索引文档
                es.index(index=index_name, id=1, body=doc)

                # 刷新索引
                es.indices.refresh(index=index_name)

                # 搜索文档
                search_result = es.search(
                    index=index_name, body={"query": {"match": {"title": "测试"}}}
                )

                # 清理测试索引
                es.indices.delete(index=index_name, ignore=[400, 404])

                if search_result["hits"]["total"]["value"] > 0:
                    self.log_result(
                        "component_verification",
                        "elasticsearch",
                        True,
                        f"Elasticsearch连接正常，集群状态: {health['status']}, 中文搜索正常",
                    )
                    return True
                else:
                    self.log_result(
                        "component_verification",
                        "elasticsearch",
                        False,
                        "Elasticsearch搜索功能测试失败",
                    )
                    return False
            else:
                self.log_result(
                    "component_verification",
                    "elasticsearch",
                    False,
                    f"Elasticsearch集群状态异常: {health['status']}",
                )
                return False

        except Exception as e:
            self.log_result("component_verification", "elasticsearch", False, str(e))
            return False

    def verify_nginx(self) -> bool:
        """验证Nginx反向代理"""
        try:
            import requests

            # 测试Nginx根路径
            response = requests.get("http://localhost:80", timeout=10)

            if response.status_code == 200:
                self.log_result(
                    "component_verification",
                    "nginx",
                    True,
                    f"Nginx连接正常，状态码: {response.status_code}",
                )
                return True
            else:
                self.log_result(
                    "component_verification",
                    "nginx",
                    False,
                    f"Nginx响应异常，状态码: {response.status_code}",
                )
                return False

        except Exception as e:
            self.log_result("component_verification", "nginx", False, str(e))
            return False


if __name__ == "__main__":
    verifier = SystemIntegrationVerifier()

    print("开始系统集成验证...")
    print("=" * 60)

    # 执行验证
    env_ok = verifier.verify_docker_environment()
    comp_ok = verifier.verify_components()

    # 输出结果摘要
    print("\n" + "=" * 60)
    print("验证结果摘要:")
    print(f"环境基础验证: {'✅ 通过' if env_ok else '❌ 失败'}")
    print(f"组件功能验证: {'✅ 通过' if comp_ok else '❌ 失败'}")

    if verifier.errors:
        print(f"\n发现 {len(verifier.errors)} 个问题:")
        for error in verifier.errors:
            print(f"  - {error}")

    # 保存详细结果
    with open(
        "system_integration_verification_results.json", "w", encoding="utf-8"
    ) as f:
        json.dump(verifier.results, f, ensure_ascii=False, indent=2)

    print(f"\n详细结果已保存到: system_integration_verification_results.json")

    # 返回退出码
    sys.exit(0 if env_ok and comp_ok else 1)
