#!/usr/bin/env python3
"""
基于Docker的系统集成验证脚本
Task I-4.4：系统集成验证 - 使用Docker exec避免网络连接问题
"""

import json
import subprocess
import sys
import time
from typing import Any, Dict


class DockerBasedVerifier:
    """基于Docker的系统验证器"""

    def __init__(self):
        self.results = {}
        self.errors = []

    def log_result(
        self, category: str, test_name: str, success: bool, details: str = ""
    ):
        """记录测试结果"""
        if category not in self.results:
            self.results[category] = {}

        self.results[category][test_name] = {
            "success": success,
            "details": details,
            "timestamp": time.time(),
        }

        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} [{category}] {test_name}: {details}")

        if not success:
            self.errors.append(f"[{category}] {test_name}: {details}")

    def run_docker_command(self, cmd: list, timeout: int = 10) -> tuple:
        """运行Docker命令"""
        try:
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "命令执行超时"
        except Exception as e:
            return False, "", str(e)

    def verify_docker_services(self) -> bool:
        """验证Docker服务状态"""
        print("\n=== 阶段1：Docker服务状态验证 ===")

        success, stdout, stderr = self.run_docker_command(
            ["docker-compose", "ps", "--format", "json"]
        )

        if not success:
            self.log_result(
                "docker_services",
                "compose_status",
                False,
                f"Docker Compose状态检查失败: {stderr}",
            )
            return False

        # 解析服务状态
        services = []
        for line in stdout.strip().split("\n"):
            if line.strip():
                try:
                    service = json.loads(line)
                    services.append(service)
                except json.JSONDecodeError:
                    continue

        # 检查核心服务
        core_services = ["postgresql", "redis", "rabbitmq", "elasticsearch", "nginx"]
        running_count = 0

        for service in services:
            service_name = service.get("Service", "")
            state = service.get("State", "")

            if service_name in core_services:
                if "running" in state.lower():
                    running_count += 1
                    self.log_result(
                        "docker_services",
                        f"{service_name}_status",
                        True,
                        f"运行正常: {state}",
                    )
                else:
                    self.log_result(
                        "docker_services",
                        f"{service_name}_status",
                        False,
                        f"状态异常: {state}",
                    )

        return running_count >= 4

    def verify_postgresql(self) -> bool:
        """验证PostgreSQL数据库"""
        print("\n=== PostgreSQL验证 ===")

        # 检查数据库连接
        success, stdout, stderr = self.run_docker_command(
            [
                "docker",
                "exec",
                "chaiguanjia_postgresql",
                "pg_isready",
                "-U",
                "admin",
                "-d",
                "chaiguanjia",
            ]
        )

        if not success:
            self.log_result(
                "postgresql", "connection", False, f"连接检查失败: {stderr}"
            )
            return False

        self.log_result("postgresql", "connection", True, "数据库连接正常")

        # 测试SQL查询
        success, stdout, stderr = self.run_docker_command(
            [
                "docker",
                "exec",
                "chaiguanjia_postgresql",
                "psql",
                "-U",
                "admin",
                "-d",
                "chaiguanjia",
                "-c",
                "SELECT version();",
            ]
        )

        if success:
            self.log_result("postgresql", "query", True, "SQL查询正常")
        else:
            self.log_result("postgresql", "query", False, f"SQL查询失败: {stderr}")
            return False

        # 测试中文支持
        success, stdout, stderr = self.run_docker_command(
            [
                "docker",
                "exec",
                "chaiguanjia_postgresql",
                "psql",
                "-U",
                "admin",
                "-d",
                "chaiguanjia",
                "-c",
                "SELECT '测试中文数据库' as chinese_test;",
            ]
        )

        if success:
            self.log_result("postgresql", "chinese_support", True, "中文支持正常")
        else:
            self.log_result(
                "postgresql", "chinese_support", False, f"中文测试失败: {stderr}"
            )

        return True

    def verify_redis(self) -> bool:
        """验证Redis缓存"""
        print("\n=== Redis验证 ===")

        # 测试Redis连接
        success, stdout, stderr = self.run_docker_command(
            [
                "docker",
                "exec",
                "chaiguanjia_redis",
                "redis-cli",
                "-a",
                "chaiguanjia2024",
                "ping",
            ]
        )

        if not success or "PONG" not in stdout:
            self.log_result("redis", "connection", False, f"连接失败: {stderr}")
            return False

        self.log_result("redis", "connection", True, "Redis连接正常")

        # 测试读写操作
        success, stdout, stderr = self.run_docker_command(
            [
                "docker",
                "exec",
                "chaiguanjia_redis",
                "redis-cli",
                "-a",
                "chaiguanjia2024",
                "set",
                "test_key",
                "test_value",
            ]
        )

        if success:
            success, stdout, stderr = self.run_docker_command(
                [
                    "docker",
                    "exec",
                    "chaiguanjia_redis",
                    "redis-cli",
                    "-a",
                    "chaiguanjia2024",
                    "get",
                    "test_key",
                ]
            )

            if success and "test_value" in stdout:
                self.log_result("redis", "read_write", True, "读写操作正常")

                # 清理测试数据
                self.run_docker_command(
                    [
                        "docker",
                        "exec",
                        "chaiguanjia_redis",
                        "redis-cli",
                        "-a",
                        "chaiguanjia2024",
                        "del",
                        "test_key",
                    ]
                )
            else:
                self.log_result("redis", "read_write", False, f"读取失败: {stderr}")
                return False
        else:
            self.log_result("redis", "read_write", False, f"写入失败: {stderr}")
            return False

        # 测试中文支持
        success, stdout, stderr = self.run_docker_command(
            [
                "docker",
                "exec",
                "chaiguanjia_redis",
                "redis-cli",
                "-a",
                "chaiguanjia2024",
                "set",
                "chinese_test",
                "测试中文缓存",
            ]
        )

        if success:
            success, stdout, stderr = self.run_docker_command(
                [
                    "docker",
                    "exec",
                    "chaiguanjia_redis",
                    "redis-cli",
                    "-a",
                    "chaiguanjia2024",
                    "get",
                    "chinese_test",
                ]
            )

            if success and "测试中文缓存" in stdout:
                self.log_result("redis", "chinese_support", True, "中文支持正常")
                # 清理
                self.run_docker_command(
                    [
                        "docker",
                        "exec",
                        "chaiguanjia_redis",
                        "redis-cli",
                        "-a",
                        "chaiguanjia2024",
                        "del",
                        "chinese_test",
                    ]
                )
            else:
                self.log_result("redis", "chinese_support", False, "中文读取失败")
        else:
            self.log_result("redis", "chinese_support", False, "中文写入失败")

        return True

    def verify_rabbitmq(self) -> bool:
        """验证RabbitMQ消息队列"""
        print("\n=== RabbitMQ验证 ===")

        # 检查RabbitMQ状态
        success, stdout, stderr = self.run_docker_command(
            ["docker", "exec", "chaiguanjia_rabbitmq", "rabbitmqctl", "status"]
        )

        if not success:
            self.log_result("rabbitmq", "status", False, f"状态检查失败: {stderr}")
            return False

        self.log_result("rabbitmq", "status", True, "RabbitMQ状态正常")

        # 检查虚拟主机
        success, stdout, stderr = self.run_docker_command(
            ["docker", "exec", "chaiguanjia_rabbitmq", "rabbitmqctl", "list_vhosts"]
        )

        if success and "chaiguanjia" in stdout:
            self.log_result("rabbitmq", "vhost", True, "虚拟主机配置正常")
        else:
            self.log_result("rabbitmq", "vhost", False, f"虚拟主机检查失败: {stderr}")

        # 检查用户权限
        success, stdout, stderr = self.run_docker_command(
            ["docker", "exec", "chaiguanjia_rabbitmq", "rabbitmqctl", "list_users"]
        )

        if success and "admin" in stdout:
            self.log_result("rabbitmq", "user", True, "用户配置正常")
        else:
            self.log_result("rabbitmq", "user", False, f"用户检查失败: {stderr}")

        return True

    def verify_elasticsearch(self) -> bool:
        """验证Elasticsearch搜索引擎"""
        print("\n=== Elasticsearch验证 ===")

        # 检查集群健康状态
        success, stdout, stderr = self.run_docker_command(
            [
                "docker",
                "exec",
                "chaiguanjia_elasticsearch",
                "curl",
                "-s",
                "http://localhost:9200/_cluster/health",
            ]
        )

        if not success:
            self.log_result("elasticsearch", "health", False, f"健康检查失败: {stderr}")
            return False

        try:
            health_data = json.loads(stdout)
            status = health_data.get("status", "unknown")

            if status in ["green", "yellow"]:
                self.log_result("elasticsearch", "health", True, f"集群状态: {status}")
            else:
                self.log_result(
                    "elasticsearch", "health", False, f"集群状态异常: {status}"
                )
                return False
        except json.JSONDecodeError:
            self.log_result("elasticsearch", "health", False, "健康状态解析失败")
            return False

        # 测试索引操作
        success, stdout, stderr = self.run_docker_command(
            [
                "docker",
                "exec",
                "chaiguanjia_elasticsearch",
                "curl",
                "-s",
                "-X",
                "PUT",
                "http://localhost:9200/test_index",
                "-H",
                "Content-Type: application/json",
                "-d",
                '{"settings": {"number_of_shards": 1}}',
            ]
        )

        if success:
            self.log_result("elasticsearch", "index_create", True, "索引创建正常")

            # 清理测试索引
            self.run_docker_command(
                [
                    "docker",
                    "exec",
                    "chaiguanjia_elasticsearch",
                    "curl",
                    "-s",
                    "-X",
                    "DELETE",
                    "http://localhost:9200/test_index",
                ]
            )
        else:
            self.log_result(
                "elasticsearch", "index_create", False, f"索引创建失败: {stderr}"
            )

        return True

    def verify_nginx(self) -> bool:
        """验证Nginx反向代理"""
        print("\n=== Nginx验证 ===")

        # 检查Nginx配置
        success, stdout, stderr = self.run_docker_command(
            ["docker", "exec", "chaiguanjia_nginx", "nginx", "-t"]
        )

        if success:
            self.log_result("nginx", "config", True, "配置文件正常")
        else:
            self.log_result("nginx", "config", False, f"配置检查失败: {stderr}")
            return False

        # 检查Nginx进程
        success, stdout, stderr = self.run_docker_command(
            ["docker", "exec", "chaiguanjia_nginx", "ps", "aux"]
        )

        if success and "nginx" in stdout:
            self.log_result("nginx", "process", True, "Nginx进程运行正常")
        else:
            self.log_result("nginx", "process", False, "Nginx进程检查失败")

        return True


def main():
    """主函数"""
    verifier = DockerBasedVerifier()

    print("开始基于Docker的系统集成验证...")
    print("=" * 60)

    # 执行验证
    results = {}
    results["docker_services"] = verifier.verify_docker_services()
    results["postgresql"] = verifier.verify_postgresql()
    results["redis"] = verifier.verify_redis()
    results["rabbitmq"] = verifier.verify_rabbitmq()
    results["elasticsearch"] = verifier.verify_elasticsearch()
    results["nginx"] = verifier.verify_nginx()

    # 输出结果摘要
    print("\n" + "=" * 60)
    print("验证结果摘要:")

    passed_count = sum(1 for result in results.values() if result)
    total_count = len(results)

    for component, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{component}: {status}")

    print(f"\n总体结果: {passed_count}/{total_count} 组件通过验证")

    if verifier.errors:
        print(f"\n发现 {len(verifier.errors)} 个问题:")
        for error in verifier.errors:
            print(f"  - {error}")

    # 保存详细结果
    with open("docker_verification_results.json", "w", encoding="utf-8") as f:
        json.dump(verifier.results, f, ensure_ascii=False, indent=2)

    print(f"\n详细结果已保存到: docker_verification_results.json")

    # 返回退出码
    overall_success = passed_count >= total_count * 0.8  # 80%通过率
    sys.exit(0 if overall_success else 1)


if __name__ == "__main__":
    main()
