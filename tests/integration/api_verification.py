#!/usr/bin/env python3
"""
API接口验证脚本
Task I-4.4：系统集成验证 - API接口功能验证
"""

import json
import time
import sys
import requests
from typing import Dict, Any


class APIVerifier:
    """API接口验证器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.results = {}
        self.errors = []
        self.session = requests.Session()
        self.session.timeout = 10
        
    def log_result(self, category: str, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        if category not in self.results:
            self.results[category] = {}
        
        self.results[category][test_name] = {
            "success": success,
            "details": details,
            "timestamp": time.time()
        }
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} [{category}] {test_name}: {details}")
        
        if not success:
            self.errors.append(f"[{category}] {test_name}: {details}")
    
    def test_basic_connectivity(self) -> bool:
        """测试基本连通性"""
        print("\n=== 基本连通性测试 ===")
        
        try:
            # 测试根路径
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                data = response.json()
                self.log_result("connectivity", "root_endpoint", True, 
                              f"根路径访问正常，应用: {data.get('app_name', 'Unknown')}")
                return True
            else:
                self.log_result("connectivity", "root_endpoint", False, 
                              f"根路径访问失败，状态码: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            self.log_result("connectivity", "root_endpoint", False, 
                          "无法连接到API服务，请检查服务是否启动")
            return False
        except Exception as e:
            self.log_result("connectivity", "root_endpoint", False, str(e))
            return False
    
    def test_health_endpoints(self) -> bool:
        """测试健康检查接口"""
        print("\n=== 健康检查接口测试 ===")
        
        health_endpoints = [
            "/health",
            "/api/v1/health",
            "/api/v1/health/detailed"
        ]
        
        success_count = 0
        
        for endpoint in health_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                
                if response.status_code == 200:
                    data = response.json()
                    self.log_result("health", f"endpoint_{endpoint.replace('/', '_')}", True, 
                                  f"健康检查正常，状态: {data.get('status', 'unknown')}")
                    success_count += 1
                elif response.status_code == 404:
                    self.log_result("health", f"endpoint_{endpoint.replace('/', '_')}", False, 
                                  "接口不存在 (404)")
                else:
                    self.log_result("health", f"endpoint_{endpoint.replace('/', '_')}", False, 
                                  f"状态码异常: {response.status_code}")
                    
            except Exception as e:
                self.log_result("health", f"endpoint_{endpoint.replace('/', '_')}", False, str(e))
        
        return success_count > 0
    
    def test_api_documentation(self) -> bool:
        """测试API文档接口"""
        print("\n=== API文档接口测试 ===")
        
        doc_endpoints = [
            "/docs",
            "/redoc",
            "/openapi.json"
        ]
        
        success_count = 0
        
        for endpoint in doc_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                
                if response.status_code == 200:
                    if endpoint == "/openapi.json":
                        # 验证OpenAPI规范
                        try:
                            openapi_spec = response.json()
                            if "openapi" in openapi_spec and "info" in openapi_spec:
                                self.log_result("documentation", f"endpoint_{endpoint.replace('/', '_')}", True, 
                                              f"OpenAPI规范正常，版本: {openapi_spec.get('info', {}).get('version', 'unknown')}")
                                success_count += 1
                            else:
                                self.log_result("documentation", f"endpoint_{endpoint.replace('/', '_')}", False, 
                                              "OpenAPI规范格式异常")
                        except json.JSONDecodeError:
                            self.log_result("documentation", f"endpoint_{endpoint.replace('/', '_')}", False, 
                                          "OpenAPI规范JSON解析失败")
                    else:
                        # HTML文档页面
                        if "html" in response.headers.get("content-type", "").lower():
                            self.log_result("documentation", f"endpoint_{endpoint.replace('/', '_')}", True, 
                                          "API文档页面正常")
                            success_count += 1
                        else:
                            self.log_result("documentation", f"endpoint_{endpoint.replace('/', '_')}", False, 
                                          "文档页面格式异常")
                elif response.status_code == 404:
                    self.log_result("documentation", f"endpoint_{endpoint.replace('/', '_')}", False, 
                                  "文档接口不存在 (404)")
                else:
                    self.log_result("documentation", f"endpoint_{endpoint.replace('/', '_')}", False, 
                                  f"状态码异常: {response.status_code}")
                    
            except Exception as e:
                self.log_result("documentation", f"endpoint_{endpoint.replace('/', '_')}", False, str(e))
        
        return success_count > 0
    
    def test_monitoring_endpoints(self) -> bool:
        """测试监控接口"""
        print("\n=== 监控接口测试 ===")
        
        monitoring_endpoints = [
            "/api/v1/monitoring/metrics",
            "/api/v1/monitoring/status",
            "/api/v1/monitoring/system"
        ]
        
        success_count = 0
        
        for endpoint in monitoring_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                
                if response.status_code == 200:
                    data = response.json()
                    self.log_result("monitoring", f"endpoint_{endpoint.replace('/', '_')}", True, 
                                  f"监控接口正常，数据类型: {type(data).__name__}")
                    success_count += 1
                elif response.status_code == 404:
                    self.log_result("monitoring", f"endpoint_{endpoint.replace('/', '_')}", False, 
                                  "监控接口不存在 (404)")
                else:
                    self.log_result("monitoring", f"endpoint_{endpoint.replace('/', '_')}", False, 
                                  f"状态码异常: {response.status_code}")
                    
            except Exception as e:
                self.log_result("monitoring", f"endpoint_{endpoint.replace('/', '_')}", False, str(e))
        
        return success_count > 0
    
    def test_search_endpoints(self) -> bool:
        """测试搜索接口"""
        print("\n=== 搜索接口测试 ===")
        
        try:
            # 测试搜索接口
            response = self.session.get(f"{self.base_url}/api/v1/search", 
                                      params={"q": "test", "limit": 10})
            
            if response.status_code == 200:
                data = response.json()
                self.log_result("search", "search_endpoint", True, 
                              f"搜索接口正常，返回数据类型: {type(data).__name__}")
                return True
            elif response.status_code == 404:
                self.log_result("search", "search_endpoint", False, 
                              "搜索接口不存在 (404)")
                return False
            else:
                self.log_result("search", "search_endpoint", False, 
                              f"状态码异常: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_result("search", "search_endpoint", False, str(e))
            return False
    
    def test_file_endpoints(self) -> bool:
        """测试文件管理接口"""
        print("\n=== 文件管理接口测试 ===")
        
        try:
            # 测试文件列表接口
            response = self.session.get(f"{self.base_url}/api/v1/files")
            
            if response.status_code == 200:
                data = response.json()
                self.log_result("files", "list_endpoint", True, 
                              f"文件列表接口正常，返回数据类型: {type(data).__name__}")
                return True
            elif response.status_code == 404:
                self.log_result("files", "list_endpoint", False, 
                              "文件管理接口不存在 (404)")
                return False
            else:
                self.log_result("files", "list_endpoint", False, 
                              f"状态码异常: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_result("files", "list_endpoint", False, str(e))
            return False
    
    def test_cors_headers(self) -> bool:
        """测试CORS头部配置"""
        print("\n=== CORS配置测试 ===")
        
        try:
            # 发送OPTIONS请求测试CORS
            response = self.session.options(f"{self.base_url}/api/v1/health")
            
            cors_headers = {
                "Access-Control-Allow-Origin": response.headers.get("Access-Control-Allow-Origin"),
                "Access-Control-Allow-Methods": response.headers.get("Access-Control-Allow-Methods"),
                "Access-Control-Allow-Headers": response.headers.get("Access-Control-Allow-Headers")
            }
            
            if any(cors_headers.values()):
                self.log_result("cors", "headers_present", True, 
                              f"CORS头部配置正常: {cors_headers}")
                return True
            else:
                self.log_result("cors", "headers_present", False, 
                              "未检测到CORS头部配置")
                return False
                
        except Exception as e:
            self.log_result("cors", "headers_present", False, str(e))
            return False
    
    def test_error_handling(self) -> bool:
        """测试错误处理"""
        print("\n=== 错误处理测试 ===")
        
        try:
            # 测试404错误
            response = self.session.get(f"{self.base_url}/api/v1/nonexistent")
            
            if response.status_code == 404:
                try:
                    error_data = response.json()
                    if "detail" in error_data or "message" in error_data:
                        self.log_result("error_handling", "404_response", True, 
                                      "404错误响应格式正确")
                    else:
                        self.log_result("error_handling", "404_response", False, 
                                      "404错误响应格式异常")
                except json.JSONDecodeError:
                    self.log_result("error_handling", "404_response", False, 
                                  "404错误响应不是有效JSON")
            else:
                self.log_result("error_handling", "404_response", False, 
                              f"预期404但得到: {response.status_code}")
            
            return True
            
        except Exception as e:
            self.log_result("error_handling", "404_response", False, str(e))
            return False


def main():
    """主函数"""
    verifier = APIVerifier()
    
    print("开始API接口验证...")
    print("=" * 60)
    
    # 执行验证
    results = {}
    results["connectivity"] = verifier.test_basic_connectivity()
    
    if results["connectivity"]:
        results["health"] = verifier.test_health_endpoints()
        results["documentation"] = verifier.test_api_documentation()
        results["monitoring"] = verifier.test_monitoring_endpoints()
        results["search"] = verifier.test_search_endpoints()
        results["files"] = verifier.test_file_endpoints()
        results["cors"] = verifier.test_cors_headers()
        results["error_handling"] = verifier.test_error_handling()
    else:
        print("❌ 基本连通性测试失败，跳过其他测试")
        results.update({
            "health": False,
            "documentation": False,
            "monitoring": False,
            "search": False,
            "files": False,
            "cors": False,
            "error_handling": False
        })
    
    # 输出结果摘要
    print("\n" + "=" * 60)
    print("API验证结果摘要:")
    
    passed_count = sum(1 for result in results.values() if result)
    total_count = len(results)
    
    for category, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{category}: {status}")
    
    print(f"\n总体结果: {passed_count}/{total_count} 类别通过验证")
    
    if verifier.errors:
        print(f"\n发现 {len(verifier.errors)} 个问题:")
        for error in verifier.errors:
            print(f"  - {error}")
    
    # 保存详细结果
    with open("api_verification_results.json", "w", encoding="utf-8") as f:
        json.dump(verifier.results, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细结果已保存到: api_verification_results.json")
    
    # 返回退出码
    overall_success = passed_count >= total_count * 0.6  # 60%通过率
    sys.exit(0 if overall_success else 1)


if __name__ == "__main__":
    main()
