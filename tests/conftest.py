"""
柴管家智能客服系统 - 全局测试配置
提供测试夹具和共享配置，支持单元测试、集成测试和端到端测试
"""

import pytest
import asyncio
import os
from typing import AsyncGenerator, Generator
from unittest.mock import AsyncMock, MagicMock

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from httpx import AsyncClient

# 导入应用模块
from backend.app.main import app
from backend.app.config.settings import settings
from backend.app.shared.database.session import get_db


# 测试环境配置
@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于异步测试"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# 数据库测试夹具
@pytest.fixture(scope="session")
async def test_db_engine():
    """创建测试数据库引擎"""
    test_database_url = os.getenv(
        "TEST_DATABASE_URL",
        "postgresql+asyncpg://test:test@localhost:5432/chaiguanjia_test"
    )
    
    engine = create_async_engine(
        test_database_url,
        echo=False,
        future=True
    )
    
    yield engine
    
    await engine.dispose()


@pytest.fixture
async def test_db_session(test_db_engine) -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话"""
    async_session = sessionmaker(
        test_db_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.rollback()


# API测试夹具
@pytest.fixture
def test_client() -> Generator[TestClient, None, None]:
    """创建同步测试客户端"""
    with TestClient(app) as client:
        yield client


@pytest.fixture
async def async_test_client() -> AsyncGenerator[AsyncClient, None]:
    """创建异步测试客户端"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


# 认证测试夹具
@pytest.fixture
def mock_user():
    """模拟用户数据"""
    return {
        "id": 1,
        "email": "<EMAIL>",
        "username": "testuser",
        "is_active": True,
        "is_admin": False,
        "created_at": "2024-01-01T00:00:00Z"
    }


@pytest.fixture
def admin_user():
    """模拟管理员用户数据"""
    return {
        "id": 2,
        "email": "<EMAIL>",
        "username": "admin",
        "is_active": True,
        "is_admin": True,
        "created_at": "2024-01-01T00:00:00Z"
    }


@pytest.fixture
def auth_headers(mock_user):
    """生成认证头部"""
    # 这里应该生成真实的JWT token
    mock_token = os.getenv("TEST_JWT_TOKEN", "mock_jwt_token_for_testing")
    return {"Authorization": f"Bearer {mock_token}"}


# AI服务测试夹具
@pytest.fixture
def mock_ai_provider():
    """模拟AI服务提供商"""
    mock = AsyncMock()
    mock.generate_response.return_value = {
        "response": "这是AI生成的回复",
        "confidence": 0.85,
        "model": "qwen-plus",
        "processing_time": 0.5
    }
    return mock


@pytest.fixture
def mock_knowledge_service():
    """模拟知识库服务"""
    mock = AsyncMock()
    mock.search_knowledge.return_value = [
        {
            "id": 1,
            "title": "测试知识点",
            "content": "这是测试知识库内容",
            "score": 0.9
        }
    ]
    return mock


# 消息队列测试夹具
@pytest.fixture
def mock_message_queue():
    """模拟消息队列"""
    mock = AsyncMock()
    mock.publish.return_value = True
    mock.subscribe.return_value = AsyncMock()
    return mock


# 缓存测试夹具
@pytest.fixture
def mock_redis():
    """模拟Redis缓存"""
    mock = AsyncMock()
    mock.get.return_value = None
    mock.set.return_value = True
    mock.delete.return_value = True
    return mock


# 平台适配器测试夹具
@pytest.fixture
def mock_wechat_adapter():
    """模拟微信适配器"""
    mock = AsyncMock()
    mock.send_message.return_value = {"success": True, "message_id": "wx_123"}
    mock.process_webhook.return_value = {
        "message_type": "text",
        "content": "用户消息",
        "sender_id": "user_123"
    }
    return mock


@pytest.fixture
def mock_xianyu_adapter():
    """模拟闲鱼适配器"""
    mock = AsyncMock()
    mock.send_message.return_value = {"success": True, "message_id": "xy_456"}
    mock.process_webhook.return_value = {
        "message_type": "text",
        "content": "闲鱼用户消息",
        "sender_id": "xy_user_456"
    }
    return mock


# 测试数据夹具
@pytest.fixture
def sample_message_data():
    """示例消息数据"""
    return {
        "id": 1,
        "content": "这是一条测试消息",
        "message_type": "text",
        "sender_id": "user_123",
        "channel_id": 1,
        "conversation_id": 1,
        "created_at": "2024-01-01T00:00:00Z"
    }


@pytest.fixture
def sample_conversation_data():
    """示例会话数据"""
    return {
        "id": 1,
        "title": "测试会话",
        "status": "open",
        "channel_id": 1,
        "contact_id": 1,
        "assignee_id": None,
        "created_at": "2024-01-01T00:00:00Z"
    }


@pytest.fixture
def sample_channel_data():
    """示例渠道数据"""
    return {
        "id": 1,
        "name": "微信测试渠道",
        "platform": "wechat",
        "status": "active",
        "config": {
            "app_id": "test_app_id",
            "app_secret": "test_app_secret"
        },
        "created_at": "2024-01-01T00:00:00Z"
    }


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 标记单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 标记集成测试"
    )
    config.addinivalue_line(
        "markers", "e2e: 标记端到端测试"
    )
    config.addinivalue_line(
        "markers", "performance: 标记性能测试"
    )
    config.addinivalue_line(
        "markers", "security: 标记安全测试"
    )
    config.addinivalue_line(
        "markers", "slow: 标记慢速测试"
    )


# 测试环境清理
@pytest.fixture(autouse=True)
async def cleanup_test_data(test_db_session):
    """每个测试后清理测试数据"""
    yield
    # 在这里添加清理逻辑
    await test_db_session.rollback()


# 并发测试支持
@pytest.fixture
def parallel_test_config():
    """并发测试配置"""
    return {
        "max_workers": 4,
        "timeout": 30,
        "retry_count": 3
    }
