# 柴管家容器化环境

## 🎯 Task I-1.1 交付成果

本目录包含了柴管家项目的完整容器化环境搭建方案，符合《柴管家基础设施搭建方案.md》中 Task I-1.1 的所有验收标准。

## 📁 文件结构

```
├── docker-compose.yml              # 主配置文件
├── docker-compose.override.yml     # 开发环境配置
├── docker-compose.prod.yml         # 生产环境配置
├── env.example                     # 环境变量示例
├── start-dev.sh                    # 快速启动脚本
├── scripts/
│   └── docker/
│       ├── start.sh                # 完整管理脚本
│       └── init-db.sh              # 数据库初始化脚本
└── docs/
    └── deployment/
        └── 容器化部署指南.md        # 详细部署文档
```

## 🚀 快速开始

### 方式一：一键启动（推荐新手）

```bash
# 确保 Docker 和 Docker Compose 已安装
./start-dev.sh
```

### 方式二：使用管理脚本

```bash
# 初始化环境
./scripts/docker/start.sh setup

# 启动开发环境
./scripts/docker/start.sh dev

# 查看帮助
./scripts/docker/start.sh help
```

### 方式三：原生 Docker Compose

```bash
# 复制环境配置
cp env.example .env

# 启动开发环境
docker-compose up -d

# 启动生产环境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## ✅ 验收标准达成情况

| 验收标准 | 状态 | 说明 |
|----------|------|------|
| ✅ 通过`docker-compose up`可以一键启动所有服务 | **已完成** | 支持开发和生产环境 |
| ✅ 所有容器正常启动，服务间网络通信正常 | **已完成** | 使用独立网络，内部通信 |
| ✅ 数据卷映射正确，数据持久化正常 | **已完成** | 所有关键数据都有持久化 |
| ✅ 容器重启后数据不丢失 | **已完成** | 使用命名数据卷 |
| ✅ 新团队成员可以在15分钟内启动完整环境 | **已完成** | 提供一键启动脚本 |

## 🛠️ 服务组件

### 核心服务

| 服务 | 镜像 | 端口 | 功能 |
|------|------|------|------|
| **PostgreSQL** | postgres:15-alpine | 5432 | 主数据库 |
| **Redis** | redis:7-alpine | 6379 | 缓存系统 |
| **RabbitMQ** | rabbitmq:3.12-management | 5672/15672 | 消息队列 |
| **Elasticsearch** | elasticsearch:8.10.4 | 9200 | 搜索引擎 |
| **Backend** | 自构建 | 8000 | FastAPI 应用 |
| **Frontend** | 自构建 | 3000 | React 应用 |
| **Nginx** | nginx:alpine | 80/443 | 反向代理 |

### 开发工具（仅开发环境）

| 工具 | 端口 | 功能 | 访问地址 |
|------|------|------|----------|
| **pgAdmin** | 5050 | PostgreSQL 管理 | http://localhost:5050 |
| **Redis Commander** | 8081 | Redis 管理 | http://localhost:8081 |
| **MailHog** | 8025 | 邮件测试 | http://localhost:8025 |
| **ES Head** | 9100 | Elasticsearch 管理 | http://localhost:9100 |

## 🔧 环境配置

### 开发环境特性

- 代码热重载
- 调试端口开放
- 开发工具集成
- 测试数据种子
- 宽松的安全配置

### 生产环境特性

- 服务副本扩展
- 资源限制配置
- SSL/TLS 支持
- 日志聚合
- 健康检查
- 自动重启策略

## 📊 性能指标

### 资源使用预期

| 环境 | CPU | 内存 | 存储 |
|------|-----|------|------|
| **开发环境** | 2-4核 | 4-6GB | 10-20GB |
| **生产环境** | 4-8核 | 8-16GB | 50-100GB |

### 性能目标

- API 响应时间: < 500ms
- 容器启动时间: < 60s
- 数据库查询: < 100ms
- 缓存命中率: > 90%

## 🔐 安全特性

### 网络安全

- 独立 Docker 网络
- 服务间内部通信
- 最小端口暴露原则

### 数据安全

- 环境变量管理
- 密码复杂度要求
- 数据传输加密
- 定期备份策略

### 访问控制

- 基于角色的权限控制
- JWT 令牌认证
- API 访问限流
- CORS 策略配置

## 📋 日常管理

### 常用命令

```bash
# 查看服务状态
./scripts/docker/start.sh status

# 查看服务日志
./scripts/docker/start.sh logs [service]

# 重启服务
./scripts/docker/start.sh restart

# 停止服务
./scripts/docker/start.sh stop

# 清理环境
./scripts/docker/start.sh clean
```

### 数据库管理

```bash
# 初始化数据库
./scripts/docker/init-db.sh

# 数据库迁移
docker-compose exec backend alembic upgrade head

# 数据库备份
docker-compose exec postgresql pg_dump -U admin chaiguanjia > backup.sql
```

## 🚨 故障排除

### 常见问题

1. **端口占用**: 修改 `.env` 文件中的端口配置
2. **内存不足**: 减少服务副本数或增加系统内存
3. **磁盘空间不足**: 清理 Docker 缓存 `docker system prune`
4. **网络问题**: 重建 Docker 网络

### 日志位置

- 应用日志: `logs/` 目录
- 容器日志: `docker-compose logs [service]`
- 系统日志: `/var/log/` (容器内)

## 📚 文档参考

- [容器化部署指南](../docs/deployment/容器化部署指南.md) - 详细部署说明
- [环境变量配置](../env.example) - 完整配置参考
- [Docker Compose 官方文档](https://docs.docker.com/compose/)

## 🔄 版本信息

- **创建时间**: 2024年1月
- **版本**: v1.0
- **维护团队**: 技术架构组
- **更新频率**: 根据需求迭代

## 🎉 下一步

容器化环境搭建完成后，可以进行：

1. [阶段二：核心框架建设](../项目文档/柴管家基础设施搭建方案.md#阶段二核心框架建设)
2. [API 框架搭建](../项目文档/柴管家基础设施搭建方案.md#task-i-21api框架搭建)
3. [数据库架构设计](../项目文档/柴管家基础设施搭建方案.md#task-i-22数据库架构设计)

---

🎯 **任务状态**: ✅ Task I-1.1 容器化环境搭建 - 已完成  
📋 **交付物**: 所有验收标准已达成，环境可投入使用
