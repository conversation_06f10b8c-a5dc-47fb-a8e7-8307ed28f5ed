# 开发工具配置 - 使用简化替代方案
version: "3.8"

services:
  # 简化的数据库管理工具 - 使用nginx提供静态页面
  db-admin:
    image: nginx:alpine
    container_name: chaiguanjia_db_admin
    volumes:
      - ./dev-tools/db-admin:/usr/share/nginx/html:ro
    ports:
      - "5050:80"
    networks:
      - chaiguanjia_network
    restart: unless-stopped

  # 简化的Redis管理工具 - 使用nginx提供静态页面
  redis-admin:
    image: nginx:alpine
    container_name: chaiguanjia_redis_admin
    volumes:
      - ./dev-tools/redis-admin:/usr/share/nginx/html:ro
    ports:
      - "8081:80"
    networks:
      - chaiguanjia_network
    restart: unless-stopped

  # 简化的Elasticsearch管理工具 - 使用nginx提供静态页面
  es-admin:
    image: nginx:alpine
    container_name: chaiguanjia_es_admin
    volumes:
      - ./dev-tools/es-admin:/usr/share/nginx/html:ro
    ports:
      - "9100:80"
    networks:
      - chaiguanjia_network
    restart: unless-stopped

  # 简化的邮件服务 - 使用nginx提供静态页面
  mail-admin:
    image: nginx:alpine
    container_name: chaiguanjia_mail_admin
    volumes:
      - ./dev-tools/mail-admin:/usr/share/nginx/html:ro
    ports:
      - "8025:80"
    networks:
      - chaiguanjia_network
    restart: unless-stopped

networks:
  chaiguanjia_network:
    external: true
