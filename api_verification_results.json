{"connectivity": {"root_endpoint": {"success": true, "details": "根路径访问正常，应用: 柴管家API", "timestamp": **********.72298}}, "health": {"endpoint__health": {"success": false, "details": "状态码异常: 500", "timestamp": **********.730857}, "endpoint__api_v1_health": {"success": true, "details": "健康检查正常，状态: unknown", "timestamp": **********.737303}, "endpoint__api_v1_health_detailed": {"success": true, "details": "健康检查正常，状态: unknown", "timestamp": **********.844383}}, "documentation": {"endpoint__docs": {"success": true, "details": "API文档页面正常", "timestamp": **********.848741}, "endpoint__redoc": {"success": true, "details": "API文档页面正常", "timestamp": **********.851739}, "endpoint__openapi.json": {"success": true, "details": "OpenAPI规范正常，版本: 1.0.0", "timestamp": **********.859993}}, "monitoring": {"endpoint__api_v1_monitoring_metrics": {"success": true, "details": "监控接口正常，数据类型: dict", "timestamp": **********.8651571}, "endpoint__api_v1_monitoring_status": {"success": false, "details": "状态码异常: 500", "timestamp": **********.8727732}, "endpoint__api_v1_monitoring_system": {"success": false, "details": "状态码异常: 500", "timestamp": **********.880372}}, "search": {"search_endpoint": {"success": false, "details": "状态码异常: 500", "timestamp": **********.8869581}}, "files": {"list_endpoint": {"success": true, "details": "文件列表接口正常，返回数据类型: dict", "timestamp": **********.893619}}, "cors": {"headers_present": {"success": false, "details": "未检测到CORS头部配置", "timestamp": **********.90123}}, "error_handling": {"404_response": {"success": false, "details": "预期404但得到: 500", "timestamp": **********.9068701}}}