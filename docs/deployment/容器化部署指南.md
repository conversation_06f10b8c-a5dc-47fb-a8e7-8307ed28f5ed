# 柴管家容器化部署指南

## 文档概述

本文档详细介绍了柴管家系统的容器化部署方案，包括开发环境和生产环境的配置、启动、管理和维护。

## 目录

1. [环境准备](#环境准备)
2. [快速开始](#快速开始)
3. [开发环境部署](#开发环境部署)
4. [生产环境部署](#生产环境部署)
5. [服务管理](#服务管理)
6. [故障排除](#故障排除)
7. [性能优化](#性能优化)

---

## 环境准备

### 系统要求

| 组件 | 最低要求 | 推荐配置 |
|------|----------|----------|
| **操作系统** | Linux/macOS/Windows | Ubuntu 20.04+ / macOS 12+ |
| **CPU** | 2核心 | 4核心+ |
| **内存** | 4GB | 8GB+ |
| **存储** | 20GB | 50GB+ SSD |
| **Docker** | 20.10+ | 最新稳定版 |
| **Docker Compose** | 2.0+ | 最新稳定版 |

### 软件安装

#### 1. 安装 Docker

**Ubuntu/Debian:**
```bash
# 使用官方安装脚本（使用国内镜像源）
curl -fsSL https://get.docker.com | bash -s docker --mirror Aliyun

# 启动并启用 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到 docker 组
sudo usermod -aG docker $USER
```

**macOS:**
```bash
# 使用 Homebrew 安装
brew install --cask docker
```

**Windows:**
下载并安装 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)

#### 2. 安装 Docker Compose

```bash
# 下载 Docker Compose（使用国内镜像）
sudo curl -L "https://get.daocloud.io/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 设置执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

#### 3. 配置 Docker 镜像源

创建或编辑 `/etc/docker/daemon.json`：

```json
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
```

重启 Docker 服务：
```bash
sudo systemctl restart docker
```

---

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd chaiguanjia8_10
```

### 2. 环境配置

```bash
# 复制环境变量配置文件
cp env.example .env

# 根据需要修改配置
vim .env
```

### 3. 一键启动

```bash
# 初始化环境
./scripts/docker/start.sh setup

# 启动开发环境
./scripts/docker/start.sh dev
```

### 4. 验证部署

访问以下地址验证服务：

- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000/docs
- RabbitMQ管理: http://localhost:15672

---

## 开发环境部署

### 环境架构

```mermaid
graph TB
    subgraph "开发环境架构"
        subgraph "前端服务"
            REACT[React 开发服务器<br/>端口: 3000]
        end
        
        subgraph "后端服务"
            API[FastAPI 应用<br/>端口: 8000]
            WORKER[Celery Worker<br/>后台任务]
        end
        
        subgraph "数据存储"
            PG[(PostgreSQL<br/>端口: 5432)]
            REDIS[(Redis<br/>端口: 6379)]
            ES[(Elasticsearch<br/>端口: 9200)]
            MQ[RabbitMQ<br/>端口: 5672/15672]
        end
        
        subgraph "开发工具"
            PGADMIN[pgAdmin<br/>端口: 5050]
            REDISCMD[Redis Commander<br/>端口: 8081]
            MAILHOG[MailHog<br/>端口: 8025]
        end
        
        subgraph "反向代理"
            NGINX[Nginx<br/>端口: 80]
        end
    end
    
    REACT --> API
    API --> PG
    API --> REDIS
    API --> ES
    API --> MQ
    WORKER --> MQ
    NGINX --> REACT
    NGINX --> API
    
    style REACT fill:#61dafb
    style API fill:#009688
    style PG fill:#336791
    style REDIS fill:#dc382d
```

### 详细启动步骤

#### 1. 环境检查

```bash
# 检查 Docker 状态
docker --version
docker-compose --version
docker info

# 检查端口占用
netstat -tuln | grep -E ':(3000|8000|5432|6379|9200|5672|15672)'
```

#### 2. 配置环境变量

编辑 `.env` 文件，重要配置项：

```env
# 开发环境标识
ENVIRONMENT=development
DEBUG=true

# 数据库配置
POSTGRES_DB=chaiguanjia_dev
POSTGRES_USER=dev_admin
POSTGRES_PASSWORD=dev_password

# API 配置
REACT_APP_API_URL=http://localhost:8000
```

#### 3. 启动服务

```bash
# 方式一：使用启动脚本（推荐）
./scripts/docker/start.sh dev

# 方式二：使用 Docker Compose
docker-compose up -d

# 方式三：重新构建并启动
./scripts/docker/start.sh dev --build
```

#### 4. 初始化数据库

```bash
# 执行数据库迁移
./scripts/docker/init-db.sh

# 或手动执行
docker-compose exec backend alembic upgrade head
```

#### 5. 验证服务状态

```bash
# 查看服务状态
./scripts/docker/start.sh status

# 查看服务日志
./scripts/docker/start.sh logs

# 查看特定服务日志
./scripts/docker/start.sh logs backend
```

### 开发工具访问

| 工具 | 地址 | 用户名 | 密码 |
|------|------|--------|------|
| **pgAdmin** | http://localhost:5050 | <EMAIL> | admin123 |
| **Redis Commander** | http://localhost:8081 | - | - |
| **RabbitMQ 管理** | http://localhost:15672 | dev_admin | dev_password |
| **MailHog** | http://localhost:8025 | - | - |
| **ES Head** | http://localhost:9100 | - | - |

---

## 生产环境部署

### 环境架构

```mermaid
graph TB
    subgraph "生产环境架构"
        subgraph "负载均衡层"
            LB[Nginx 负载均衡<br/>SSL 终端]
        end
        
        subgraph "应用层"
            API1[Backend Instance 1]
            API2[Backend Instance 2]
            WEB1[Frontend Instance 1]
            WEB2[Frontend Instance 2]
        end
        
        subgraph "任务处理层"
            WORKER1[Celery Worker 1]
            WORKER2[Celery Worker 2]
            BEAT[Celery Beat]
        end
        
        subgraph "数据存储层"
            PG[(PostgreSQL<br/>主从复制)]
            REDIS[(Redis<br/>持久化)]
            ES[(Elasticsearch<br/>集群)]
            MQ[RabbitMQ<br/>集群]
        end
        
        subgraph "监控层"
            MONITOR[监控系统]
            ALERT[告警系统]
            LOG[日志系统]
        end
    end
    
    LB --> API1
    LB --> API2
    LB --> WEB1
    LB --> WEB2
    
    API1 --> PG
    API2 --> PG
    API1 --> REDIS
    API2 --> REDIS
    
    WORKER1 --> MQ
    WORKER2 --> MQ
    BEAT --> MQ
    
    style LB fill:#ff9800
    style PG fill:#336791
    style MONITOR fill:#4caf50
```

### 生产环境配置

#### 1. 环境变量配置

创建生产环境 `.env.prod` 文件：

```env
# 生产环境配置
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=your-super-secure-secret-key

# 数据库配置（使用强密码）
POSTGRES_DB=chaiguanjia_prod
POSTGRES_USER=prod_admin
POSTGRES_PASSWORD=extremely-secure-password

# Redis 配置
REDIS_PASSWORD=redis-secure-password

# SSL 证书路径
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# 监控配置
SENTRY_DSN=https://your-sentry-dsn
```

#### 2. 启动生产环境

```bash
# 使用生产环境配置
cp .env.prod .env

# 启动生产环境
./scripts/docker/start.sh prod --build

# 或使用 Docker Compose
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

#### 3. SSL 证书配置

```bash
# 创建 SSL 证书目录
mkdir -p infrastructure/nginx/ssl

# 使用 Let's Encrypt 获取证书
certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem infrastructure/nginx/ssl/cert.pem
cp /etc/letsencrypt/live/your-domain.com/privkey.pem infrastructure/nginx/ssl/key.pem
```

#### 4. 数据库备份配置

```bash
# 创建备份脚本
cat > scripts/backup/db-backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backups/postgresql"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
mkdir -p $BACKUP_DIR

docker-compose exec postgresql pg_dump -U $POSTGRES_USER $POSTGRES_DB | gzip > $BACKUP_DIR/backup_$TIMESTAMP.sql.gz

# 保留最近 30 天的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
EOF

chmod +x scripts/backup/db-backup.sh

# 添加到 crontab
echo "0 2 * * * /path/to/project/scripts/backup/db-backup.sh" | crontab -
```

---

## 服务管理

### 常用管理命令

```bash
# 服务状态管理
./scripts/docker/start.sh status    # 查看状态
./scripts/docker/start.sh stop      # 停止服务
./scripts/docker/start.sh restart   # 重启服务

# 日志查看
./scripts/docker/start.sh logs              # 查看所有日志
./scripts/docker/start.sh logs backend      # 查看后端日志
./scripts/docker/start.sh logs --tail=100   # 查看最近100行

# 环境清理
./scripts/docker/start.sh clean             # 交互式清理
./scripts/docker/start.sh clean --force     # 强制清理
```

### 容器操作

```bash
# 进入容器
docker-compose exec backend bash
docker-compose exec postgresql psql -U admin -d chaiguanjia

# 查看容器资源使用
docker stats

# 重启特定服务
docker-compose restart backend
docker-compose restart postgresql

# 查看服务依赖
docker-compose config --services
```

### 数据库管理

```bash
# 数据库迁移
docker-compose exec backend alembic upgrade head
docker-compose exec backend alembic downgrade -1

# 数据库备份
docker-compose exec postgresql pg_dump -U admin chaiguanjia > backup.sql

# 数据库还原
docker-compose exec -T postgresql psql -U admin chaiguanjia < backup.sql

# 查看数据库连接
docker-compose exec postgresql psql -U admin -d chaiguanjia -c "SELECT * FROM pg_stat_activity;"
```

---

## 故障排除

### 常见问题

#### 1. 端口占用问题

```bash
# 查看端口占用
lsof -i :8000
netstat -tuln | grep 8000

# 修改端口配置
vim .env  # 修改 BACKEND_PORT=8001
```

#### 2. 容器启动失败

```bash
# 查看详细错误信息
docker-compose logs backend

# 检查容器状态
docker-compose ps

# 重新构建镜像
docker-compose build --no-cache backend
```

#### 3. 数据库连接问题

```bash
# 检查数据库容器状态
docker-compose exec postgresql pg_isready -U admin

# 查看数据库日志
docker-compose logs postgresql

# 重置数据库
docker-compose down postgresql
docker volume rm chaiguanjia8_10_postgres_data
docker-compose up -d postgresql
```

#### 4. 内存不足

```bash
# 查看系统资源
free -h
df -h

# 清理 Docker 缓存
docker system prune -f
docker volume prune -f

# 限制容器内存使用
# 编辑 docker-compose.yml 添加:
# deploy:
#   resources:
#     limits:
#       memory: 512M
```

### 日志分析

#### 应用日志位置

| 服务 | 日志位置 | 说明 |
|------|----------|------|
| **Backend** | `/app/logs/` | 应用日志 |
| **Nginx** | `/var/log/nginx/` | 访问和错误日志 |
| **PostgreSQL** | `/var/lib/postgresql/data/log/` | 数据库日志 |
| **Redis** | `/data/redis.log` | Redis 日志 |

#### 日志查看命令

```bash
# 实时查看应用日志
docker-compose logs -f backend

# 查看错误日志
docker-compose logs backend | grep ERROR

# 查看最近的日志
docker-compose logs --tail=100 backend

# 导出日志
docker-compose logs backend > backend.log
```

### 性能监控

#### 资源监控

```bash
# 查看容器资源使用
docker stats --no-stream

# 查看容器进程
docker-compose exec backend top

# 查看磁盘使用
docker system df
```

#### 性能指标

| 指标 | 正常值 | 告警阈值 |
|------|--------|----------|
| **API响应时间** | <500ms | >1000ms |
| **内存使用率** | <70% | >85% |
| **CPU使用率** | <60% | >80% |
| **磁盘使用率** | <80% | >90% |

---

## 性能优化

### 容器优化

#### 1. 镜像优化

```dockerfile
# 使用多阶段构建
FROM python:3.11-slim as builder
# 构建阶段

FROM python:3.11-slim as runtime
# 运行阶段
```

#### 2. 资源限制

```yaml
# docker-compose.yml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
```

#### 3. 健康检查优化

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 60s
```

### 数据库优化

#### PostgreSQL 配置

```sql
-- 连接池配置
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';

-- 重载配置
SELECT pg_reload_conf();
```

#### Redis 配置

```conf
# redis.conf
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 网络优化

#### Nginx 配置

```nginx
# nginx.conf
worker_processes auto;
worker_connections 1024;

upstream backend {
    least_conn;
    server backend:8000 max_fails=3 fail_timeout=30s;
}

# 启用 gzip 压缩
gzip on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json;

# 缓存配置
location /static/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

---

## 附录

### 环境变量参考

完整的环境变量配置请参考 `env.example` 文件。

### 端口映射表

| 服务 | 容器端口 | 主机端口 | 说明 |
|------|----------|----------|------|
| Frontend | 3000 | 3000 | React 开发服务器 |
| Backend | 8000 | 8000 | FastAPI 应用 |
| PostgreSQL | 5432 | 5432 | 数据库 |
| Redis | 6379 | 6379 | 缓存 |
| RabbitMQ | 5672 | 5672 | 消息队列 |
| RabbitMQ 管理 | 15672 | 15672 | 管理界面 |
| Elasticsearch | 9200 | 9200 | 搜索引擎 |
| Nginx | 80 | 80 | HTTP |
| Nginx | 443 | 443 | HTTPS |

### 数据卷说明

| 数据卷 | 用途 | 持久化 |
|--------|------|--------|
| postgres_data | PostgreSQL 数据 | 是 |
| redis_data | Redis 数据 | 是 |
| rabbitmq_data | RabbitMQ 数据 | 是 |
| es_data | Elasticsearch 数据 | 是 |
| backend_logs | 应用日志 | 是 |
| nginx_logs | Nginx 日志 | 是 |

---

*文档版本: v1.0*  
*最后更新: 2024年1月*  
*维护团队: 技术架构组*
