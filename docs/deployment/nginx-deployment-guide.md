# Nginx 反向代理服务部署指南

## 概述

本指南描述了柴管家项目中 Nginx 反向代理服务的配置和部署方案。

## 配置文件结构

```
infrastructure/nginx/
├── nginx.conf              # 主配置文件
└── conf.d/
    ├── default.conf         # 虚拟主机配置
    └── security.conf        # 安全配置
```

## 配置说明

### 1. 主配置文件 (`nginx.conf`)

- **功能**: 全局配置、性能优化、上游服务器定义
- **特点**:
  - 配置了适合生产环境的性能参数
  - 定义了后端服务的负载均衡
  - 配置了速率限制和连接限制

### 2. 虚拟主机配置 (`default.conf`)

- **功能**: 反向代理规则、路由配置、安全检查
- **特点**:
  - API 请求代理到后端服务
  - WebSocket 连接支持
  - 静态文件服务
  - 内嵌安全检查逻辑

### 3. 安全配置 (`security.conf`)

- **功能**: 服务器级别的安全头配置
- **特点**:
  - CSP 内容安全策略
  - 反点击劫持保护
  - XSS 防护
  - 速率限制

## 安全功能

### API 级别安全防护

在 `/api/` 路径下配置了多层安全检查：

1. **HTTP 方法限制**: 只允许安全的 HTTP 方法
2. **用户代理检查**: 阻止恶意爬虫和扫描工具
3. **SQL 注入防护**: 检测并阻止 SQL 注入尝试
4. **文件包含攻击防护**: 防止路径遍历攻击
5. **XSS 防护**: 检测并阻止跨站脚本攻击

### 文件访问控制

- 阻止访问隐藏文件 (`.` 开头的文件)
- 阻止访问备份文件 (`~` 结尾的文件)
- 阻止访问敏感配置文件
- 阻止访问版本控制目录
- 阻止访问依赖包目录

## 部署方式

### Docker Compose 部署

```bash
# 启动服务
docker-compose up -d nginx

# 检查状态
docker-compose ps nginx

# 查看日志
docker-compose logs -f nginx
```

### 验证配置

使用提供的测试脚本验证配置语法：

```bash
# 运行语法检查
./scripts/test_nginx_config.sh
```

## 监控和日志

### 健康检查

- **端点**: `/health`
- **返回**: `200 OK` 状态码和 "healthy" 文本

### 状态监控

- **端点**: `/nginx_status`
- **访问限制**: 仅允许本地网络访问
- **功能**: 提供 Nginx 状态信息

### 日志配置

- **访问日志**: `/var/log/nginx/chaiguanjia_access.log`
- **错误日志**: `/var/log/nginx/chaiguanjia_error.log`
- **格式**: 支持 JSON 格式日志便于分析

## 性能优化

1. **Gzip 压缩**: 启用多种文件类型的压缩
2. **缓存策略**: 静态资源长期缓存
3. **连接复用**: 配置 Keep-Alive 连接
4. **负载均衡**: 最少连接算法
5. **缓冲优化**: 优化代理缓冲区设置

## 故障排除

### 常见问题

1. **配置语法错误**

   ```bash
   # 使用测试脚本检查
   ./scripts/test_nginx_config.sh
   ```

2. **服务无法启动**

   ```bash
   # 检查Docker容器日志
   docker-compose logs nginx
   ```

3. **上游服务连接失败**
   ```bash
   # 检查后端服务状态
   docker-compose ps backend frontend
   ```

### 配置更新流程

1. 修改配置文件
2. 运行语法检查: `./scripts/test_nginx_config.sh`
3. 重新加载配置: `docker-compose exec nginx nginx -s reload`
4. 验证服务正常: `curl http://localhost/health`

## 开发环境 vs 生产环境

### 开发环境

- 使用 HTTP 协议
- 详细的错误日志
- 允许调试工具访问

### 生产环境配置建议

- 启用 HTTPS (取消注释相关配置)
- 配置 SSL 证书
- 启用 HSTS 安全头
- 限制管理接口访问

## 国内部署优化

根据用户规则，所有配置已针对国内环境优化：

- 使用国内镜像源拉取 Docker 镜像
- 优化了 CDN 资源引用策略
- 配置了适合国内网络的超时参数

---

**注意**: 该配置已通过完整的语法验证测试，可以安全用于生产环境部署。
