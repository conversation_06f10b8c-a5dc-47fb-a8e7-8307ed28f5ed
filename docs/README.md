# 柴管家智能客服系统 - 项目文档

欢迎使用柴管家智能客服系统文档！本系统基于Chatwoot成熟架构，采用AI原生设计理念，为用户提供多渠道聚合的智能客服解决方案。

## 📚 文档导航

### 🏗️ 架构设计

- [系统架构概览](architecture/overview.md) - 系统整体架构设计
- [设计原则](architecture/design-principles.md) - 核心设计原则和理念
- [技术栈说明](architecture/technology-stack.md) - 技术选型和说明
- [数据流设计](architecture/data-flow.md) - 系统数据流和处理机制
- [安全架构](architecture/security.md) - 系统安全设计
- [性能设计](architecture/performance.md) - 性能优化策略

### 🔌 API接口

- [OpenAPI规范](api/openapi.yaml) - 完整的API接口规范
- [认证说明](api/authentication.md) - API认证机制
- [渠道管理API](api/channels.md) - 渠道管理相关接口
- [消息处理API](api/messages.md) - 消息处理相关接口
- [AI服务API](api/ai-services.md) - AI智能服务接口
- [知识库API](api/knowledge.md) - 知识库管理接口
- [工作流API](api/workflows.md) - 工作流引擎接口
- [Webhook API](api/webhooks.md) - Webhook回调接口

### 🔄 平台适配器

- [适配器概览](platform-adapters/overview.md) - 平台适配器架构说明
- [微信适配器](platform-adapters/wechat.md) - 微信平台集成指南
- [抖音适配器](platform-adapters/douyin.md) - 抖音平台集成指南
- [小红书适配器](platform-adapters/xiaohongshu.md) - 小红书平台集成指南
- [闲鱼适配器](platform-adapters/xianyu.md) - 闲鱼平台集成指南
- [知识星球适配器](platform-adapters/zhishixingqiu.md) - 知识星球平台集成指南
- [自定义适配器开发](platform-adapters/custom-adapter.md) - 自定义适配器开发指南

### 🤖 AI集成

- [AI提供商集成](ai-integration/providers.md) - AI服务提供商集成说明
- [模型路由策略](ai-integration/model-routing.md) - 智能模型路由机制
- [置信度评估](ai-integration/confidence.md) - AI回复置信度评估
- [知识库RAG](ai-integration/knowledge-rag.md) - 检索增强生成机制
- [人机协作](ai-integration/human-collaboration.md) - 人工智能协作机制

### 🚀 部署运维

- [快速开始](deployment/quick-start.md) - 系统快速部署指南
- [Docker部署](deployment/docker.md) - 容器化部署方案
- [Kubernetes部署](deployment/kubernetes.md) - K8s生产环境部署
- [监控配置](deployment/monitoring.md) - 系统监控和告警配置
- [备份策略](deployment/backup.md) - 数据备份和恢复策略
- [故障排除](deployment/troubleshooting.md) - 常见问题和解决方案

### 💻 开发指南

- [开发环境搭建](development/setup.md) - 本地开发环境配置
- [编码规范](development/coding-standards.md) - 代码风格和规范
- [测试指南](development/testing.md) - 单元测试和集成测试
- [数据库设计](development/database.md) - 数据库结构设计说明
- [贡献指南](development/contributing.md) - 项目贡献指南

#### 模块开发
- [平台适配器开发](development/modules/platform-adapters.md)
- [AI服务开发](development/modules/ai-services.md)
- [知识库开发](development/modules/knowledge.md)
- [工作流开发](development/modules/workflows.md)

### 📖 用户手册

- [快速入门](user-guide/getting-started.md) - 系统使用入门
- [渠道配置](user-guide/channel-setup.md) - 多渠道接入配置
- [AI配置](user-guide/ai-configuration.md) - AI功能配置指南
- [知识库管理](user-guide/knowledge-base.md) - 知识库创建和管理
- [工作流设计](user-guide/workflow-design.md) - 自动化工作流设计
- [常见问题](user-guide/troubleshooting.md) - 用户常见问题解答

### 📝 更新日志

- [变更日志](changelog/CHANGELOG.md) - 版本更新记录
- [迁移指南](changelog/migration-guides/) - 版本迁移指南

## 🎯 快速链接

- [项目概览](../README.md) - 返回项目主页
- [在线API文档](http://localhost:8000/docs) - 本地API文档
- [开发环境搭建](development/setup.md) - 开始开发
- [Docker快速部署](deployment/docker.md) - 快速体验系统

## 📞 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获取支持：

- 📧 邮箱支持：<EMAIL>
- 💬 技术交流群：[加入群聊]
- 🐛 问题反馈：[GitHub Issues]
- 📚 知识库：[在线帮助中心]

---

**柴管家团队** - 让智能客服更简单！
