# 认证授权系统 API 文档

## 概述

本文档详细介绍了柴管家系统的认证授权 API 接口，包括用户注册、登录、权限管理、角色管理等功能。

## 基本信息

- **Base URL**: `https://api.chaiguanjia.com/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 认证流程

### 获取访问令牌

所有需要认证的接口都需要在请求头中包含有效的访问令牌：

```http
Authorization: Bearer <access_token>
```

### 令牌刷新

访问令牌过期后，可以使用刷新令牌获取新的访问令牌，无需重新登录。

## API 接口

### 1. 用户认证

#### 1.1 用户注册

**接口**: `POST /auth/register`

**描述**: 注册新用户账户

**请求体**:

```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "name": "张三",
  "phone": "+86-13812345678"
}
```

**响应**:

```json
{
  "message": "注册成功",
  "user": {
    "id": 123,
    "email": "<EMAIL>",
    "name": "张三",
    "is_active": true,
    "created_at": "2024-01-01T10:00:00Z"
  },
  "tokens": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  }
}
```

**错误响应**:

```json
{
  "detail": [
    {
      "field": "email",
      "message": "邮箱已存在"
    }
  ]
}
```

#### 1.2 用户登录

**接口**: `POST /auth/login`

**描述**: 用户登录获取访问令牌

**请求体**:

```json
{
  "identifier": "<EMAIL>",
  "password": "SecurePassword123!",
  "remember_me": false
}
```

**响应**:

```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "user": {
    "id": 123,
    "email": "<EMAIL>",
    "name": "张三",
    "roles": ["user"],
    "permissions": ["read:profile", "update:profile"]
  }
}
```

#### 1.3 令牌刷新

**接口**: `POST /auth/refresh`

**描述**: 使用刷新令牌获取新的访问令牌

**请求体**:

```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**响应**:

```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "Bearer",
  "expires_in": 3600
}
```

#### 1.4 用户登出

**接口**: `POST /auth/logout`

**描述**: 登出当前用户，撤销访问令牌

**请求头**:

```http
Authorization: Bearer <access_token>
```

**响应**:

```json
{
  "message": "登出成功"
}
```

#### 1.5 获取当前用户信息

**接口**: `GET /auth/me`

**描述**: 获取当前登录用户的详细信息

**请求头**:

```http
Authorization: Bearer <access_token>
```

**响应**:

```json
{
  "id": 123,
  "email": "<EMAIL>",
  "name": "张三",
  "nickname": "小张",
  "avatar": "https://cdn.example.com/avatar/123.jpg",
  "phone": "+86-13812345678",
  "is_active": true,
  "is_email_verified": true,
  "is_phone_verified": true,
  "roles": ["user"],
  "permissions": ["read:profile", "update:profile"],
  "last_login_at": "2024-01-01T10:00:00Z",
  "created_at": "2024-01-01T08:00:00Z"
}
```

#### 1.6 更新用户信息

**接口**: `PUT /auth/me`

**描述**: 更新当前用户的个人信息

**请求头**:

```http
Authorization: Bearer <access_token>
```

**请求体**:

```json
{
  "name": "张三丰",
  "nickname": "张大师",
  "avatar": "https://cdn.example.com/avatar/new-123.jpg",
  "gender": "male",
  "birthdate": "1990-01-01",
  "address": "北京市朝阳区"
}
```

**响应**:

```json
{
  "message": "用户信息更新成功",
  "user": {
    "id": 123,
    "email": "<EMAIL>",
    "name": "张三丰",
    "nickname": "张大师",
    "avatar": "https://cdn.example.com/avatar/new-123.jpg",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

#### 1.7 修改密码

**接口**: `POST /auth/change-password`

**描述**: 修改用户密码

**请求头**:

```http
Authorization: Bearer <access_token>
```

**请求体**:

```json
{
  "current_password": "OldPassword123!",
  "new_password": "NewPassword456!",
  "confirm_password": "NewPassword456!"
}
```

**响应**:

```json
{
  "message": "密码修改成功"
}
```

### 2. 用户管理 (管理员)

#### 2.1 获取用户列表

**接口**: `GET /users`

**描述**: 获取系统用户列表（需要管理员权限）

**权限要求**: `read:users`

**查询参数**:

- `page`: 页码（默认: 1）
- `size`: 每页数量（默认: 20，最大: 100）
- `search`: 搜索关键词（邮箱、姓名、手机号）
- `is_active`: 是否激活（true/false）
- `role`: 角色筛选
- `sort`: 排序字段（created_at, last_login_at, name）
- `order`: 排序方向（asc/desc）

**请求示例**:

```http
GET /users?page=1&size=20&search=张&is_active=true&sort=created_at&order=desc
```

**响应**:

```json
{
  "items": [
    {
      "id": 123,
      "email": "<EMAIL>",
      "name": "张三",
      "phone": "+86-13812345678",
      "is_active": true,
      "is_blocked": false,
      "roles": ["user"],
      "last_login_at": "2024-01-01T10:00:00Z",
      "created_at": "2024-01-01T08:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "size": 20,
  "pages": 1
}
```

#### 2.2 获取用户详情

**接口**: `GET /users/{user_id}`

**描述**: 获取指定用户的详细信息

**权限要求**: `read:users`

**响应**:

```json
{
  "id": 123,
  "email": "<EMAIL>",
  "name": "张三",
  "nickname": "小张",
  "avatar": "https://cdn.example.com/avatar/123.jpg",
  "phone": "+86-13812345678",
  "is_active": true,
  "is_blocked": false,
  "is_suspended": false,
  "roles": ["user"],
  "permissions": ["read:profile", "update:profile"],
  "login_count": 15,
  "last_login_at": "2024-01-01T10:00:00Z",
  "last_login_ip": "*************",
  "created_at": "2024-01-01T08:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

#### 2.3 激活/停用用户

**接口**: `POST /users/{user_id}/activate` 或 `POST /users/{user_id}/block`

**描述**: 激活或停用指定用户

**权限要求**: `manage:users`

**响应**:

```json
{
  "message": "用户已激活/已停用",
  "user": {
    "id": 123,
    "is_active": true,
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 3. 角色管理

#### 3.1 获取角色列表

**接口**: `GET /roles`

**描述**: 获取系统角色列表

**权限要求**: `read:roles`

**响应**:

```json
{
  "items": [
    {
      "id": 1,
      "code": "admin",
      "name": "系统管理员",
      "description": "拥有系统全部权限",
      "is_active": true,
      "is_system": true,
      "user_count": 2,
      "permission_count": 50,
      "created_at": "2024-01-01T08:00:00Z"
    },
    {
      "id": 2,
      "code": "user",
      "name": "普通用户",
      "description": "普通用户权限",
      "is_active": true,
      "is_system": true,
      "user_count": 100,
      "permission_count": 5,
      "created_at": "2024-01-01T08:00:00Z"
    }
  ]
}
```

#### 3.2 创建角色

**接口**: `POST /roles`

**描述**: 创建新角色

**权限要求**: `create:roles`

**请求体**:

```json
{
  "code": "moderator",
  "name": "内容审核员",
  "description": "负责内容审核的角色",
  "permission_ids": [1, 2, 3, 5, 8]
}
```

**响应**:

```json
{
  "message": "角色创建成功",
  "role": {
    "id": 3,
    "code": "moderator",
    "name": "内容审核员",
    "description": "负责内容审核的角色",
    "is_active": true,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

#### 3.3 分配角色给用户

**接口**: `POST /users/{user_id}/roles`

**描述**: 为用户分配角色

**权限要求**: `assign:roles`

**请求体**:

```json
{
  "role_codes": ["moderator", "user"]
}
```

**响应**:

```json
{
  "message": "角色分配成功",
  "user_roles": ["moderator", "user"]
}
```

### 4. 权限管理

#### 4.1 获取权限列表

**接口**: `GET /permissions`

**描述**: 获取系统权限列表

**权限要求**: `read:permissions`

**查询参数**:

- `group`: 权限组筛选
- `resource`: 资源筛选
- `search`: 搜索关键词

**响应**:

```json
{
  "items": [
    {
      "id": 1,
      "code": "read:users",
      "name": "查看用户",
      "description": "查看用户列表和详情",
      "resource": "users",
      "action": "read",
      "group": "用户管理",
      "category": "管理",
      "is_active": true
    }
  ],
  "groups": ["用户管理", "角色管理", "权限管理"],
  "categories": ["管理", "业务", "系统"]
}
```

#### 4.2 检查用户权限

**接口**: `GET /permissions/check`

**描述**: 检查当前用户是否拥有指定权限

**查询参数**:

- `permission`: 权限代码
- `resource`: 资源（可选）

**请求示例**:

```http
GET /permissions/check?permission=read:users&resource=user_list
```

**响应**:

```json
{
  "has_permission": true,
  "permission": "read:users",
  "resource": "user_list"
}
```

### 5. 会话管理

#### 5.1 获取用户会话列表

**接口**: `GET /auth/sessions`

**描述**: 获取当前用户的活跃会话列表

**响应**:

```json
{
  "sessions": [
    {
      "session_id": "sess_abc123",
      "device_info": {
        "platform": "Web",
        "browser": "Chrome 120.0",
        "os": "Windows 10"
      },
      "ip_address": "*************",
      "location": "北京, 中国",
      "is_current": true,
      "created_at": "2024-01-01T10:00:00Z",
      "last_activity": "2024-01-01T12:00:00Z"
    }
  ]
}
```

#### 5.2 终止会话

**接口**: `DELETE /auth/sessions/{session_id}`

**描述**: 终止指定会话

**响应**:

```json
{
  "message": "会话已终止"
}
```

#### 5.3 终止所有会话

**接口**: `DELETE /auth/sessions`

**描述**: 终止当前用户的所有会话（除当前会话外）

**响应**:

```json
{
  "message": "所有会话已终止",
  "terminated_count": 3
}
```

## 错误处理

### 错误响应格式

所有 API 错误都遵循统一的响应格式：

```json
{
  "detail": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z",
  "path": "/api/v1/auth/login"
}
```

### 常见错误代码

| HTTP 状态码 | 错误代码            | 描述                     |
| ----------- | ------------------- | ------------------------ |
| 400         | VALIDATION_ERROR    | 请求参数验证失败         |
| 401         | UNAUTHORIZED        | 未认证或令牌无效         |
| 403         | FORBIDDEN           | 权限不足                 |
| 404         | NOT_FOUND           | 资源不存在               |
| 409         | CONFLICT            | 资源冲突（如邮箱已存在） |
| 429         | RATE_LIMIT_EXCEEDED | 请求频率限制             |
| 500         | INTERNAL_ERROR      | 服务器内部错误           |

### 字段验证错误

```json
{
  "detail": [
    {
      "field": "email",
      "message": "无效的邮箱格式",
      "code": "INVALID_EMAIL"
    },
    {
      "field": "password",
      "message": "密码长度至少8位",
      "code": "PASSWORD_TOO_SHORT"
    }
  ]
}
```

## 最佳实践

### 1. 认证令牌管理

- **访问令牌**: 有效期较短（默认 15 分钟），用于 API 请求认证
- **刷新令牌**: 有效期较长（默认 7 天），用于获取新的访问令牌
- **令牌存储**: 建议将令牌存储在安全的地方（如 HttpOnly Cookie）
- **令牌刷新**: 在访问令牌即将过期时自动刷新

### 2. 错误处理

```javascript
// 示例：处理API错误
async function callAPI(endpoint, options) {
  try {
    const response = await fetch(endpoint, options);

    if (!response.ok) {
      const error = await response.json();

      // 处理特定错误
      switch (response.status) {
        case 401:
          // 令牌过期，尝试刷新
          await refreshToken();
          break;
        case 403:
          // 权限不足
          showPermissionError();
          break;
        case 429:
          // 请求频率限制
          await delay(1000);
          break;
        default:
          showGenericError(error.detail);
      }

      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
}
```

### 3. 权限检查

```javascript
// 示例：权限检查装饰器
function requirePermission(permission) {
  return function (target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args) {
      const hasPermission = await checkPermission(permission);
      if (!hasPermission) {
        throw new Error('权限不足');
      }
      return originalMethod.apply(this, args);
    };
  };
}

// 使用示例
class UserService {
  @requirePermission('read:users')
  async getUserList() {
    // 获取用户列表
  }

  @requirePermission('manage:users')
  async deleteUser(userId) {
    // 删除用户
  }
}
```

### 4. 请求频率控制

- 合理控制 API 请求频率
- 使用缓存减少重复请求
- 实现请求去重机制
- 处理 429 状态码并实现退避重试

### 5. 安全建议

- 始终使用 HTTPS 传输敏感数据
- 定期轮换 API 密钥和令牌
- 实施强密码策略
- 启用多因素认证（MFA）
- 监控异常登录行为
- 定期安全审计

## 监控和调试

### 请求追踪

每个 API 请求都会包含一个唯一的请求 ID，用于追踪和调试：

```http
X-Request-ID: req_abc123456789
```

### 性能监控

API 响应头包含性能信息：

```http
X-Response-Time: 120ms
X-Rate-Limit-Remaining: 95
X-Rate-Limit-Reset: 1704067200
```

### 调试模式

开发环境下可以启用调试模式获取更详细的错误信息：

```http
X-Debug: true
```

## 版本控制

API 版本控制通过 URL 路径实现：

- `/api/v1/` - 当前稳定版本
- `/api/v2/` - 下一个版本（开发中）

向后兼容性保证：

- 不会删除现有字段
- 新增字段会标明版本
- 重大变更会发布新版本

## 联系我们

如有 API 使用问题或建议，请联系：

- 技术支持：<EMAIL>
- 开发者文档：https://docs.chaiguanjia.com
- GitHub：https://github.com/chaiguanjia/api
