# 监控系统 API 文档

## 概述

监控系统提供完整的系统健康状态、性能指标、告警管理和安全日志等功能的 API 接口。

## 基本信息

- **Base URL**: `https://api.chaiguanjia.com/api/v1/monitoring`
- **认证方式**: <PERSON><PERSON> (JWT)
- **权限要求**: `monitor:read` (查看) / `monitor:manage` (管理)

## API 接口

### 1. 系统健康检查

#### 1.1 获取整体健康状态

**接口**: `GET /health`

**描述**: 获取系统整体健康状态及各组件状态

**权限要求**: `monitor:read`

**响应**:

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "components": {
    "database_connection": {
      "status": "healthy",
      "last_check": "2024-01-01T12:00:00Z",
      "response_time": 25.5,
      "error": null,
      "critical": true,
      "component_type": "database"
    },
    "redis_connection": {
      "status": "healthy",
      "last_check": "2024-01-01T12:00:00Z",
      "response_time": 5.2,
      "error": null,
      "critical": true,
      "component_type": "cache"
    },
    "system_resources": {
      "status": "warning",
      "last_check": "2024-01-01T12:00:00Z",
      "response_time": 10.1,
      "error": "CPU使用率过高: 85.2%",
      "critical": false,
      "component_type": "system"
    }
  }
}
```

**状态值说明**:

- `healthy`: 所有关键组件正常
- `warning`: 存在警告但系统可用
- `unhealthy`: 关键组件异常
- `unknown`: 状态未知

#### 1.2 检查单个组件

**接口**: `GET /health/{component}`

**描述**: 手动检查指定组件的健康状态

**路径参数**:

- `component`: 组件名称（database_connection, redis_connection, system_resources 等）

**响应**:

```json
{
  "status": "success",
  "component": "database_connection",
  "result": {
    "status": "healthy",
    "response_time": 28.3,
    "error": null,
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

#### 1.3 获取组件健康历史

**接口**: `GET /health/{component}/history`

**描述**: 获取指定组件的健康检查历史记录

**查询参数**:

- `hours`: 历史数据小时数 (1-168，默认 24)

**响应**:

```json
{
  "component": "database_connection",
  "history": [
    {
      "timestamp": "2024-01-01T12:00:00Z",
      "status": "healthy",
      "response_time": 25.5,
      "error": null,
      "details": {
        "connection": "active",
        "pool_size": 10,
        "active_connections": 2
      }
    }
  ]
}
```

### 2. 性能指标

#### 2.1 获取系统指标

**接口**: `GET /metrics`

**描述**: 获取系统性能指标数据

**查询参数**:

- `metric_types`: 指标类型，逗号分隔 (counter,gauge,histogram,timer)
- `name_pattern`: 指标名称模式匹配
- `hours`: 历史数据小时数 (1-168，默认 1)

**响应**:

```json
{
  "counters": {
    "api_requests_total": 1250,
    "api_errors_total": 15,
    "database_queries_total": 3420,
    "cache_hits_total": 890,
    "cache_misses_total": 160
  },
  "gauges": {
    "user_sessions_active": 45,
    "system_memory_usage": 75.2,
    "system_cpu_usage": 65.8
  },
  "histograms": {
    "api_request_duration": [
      {
        "timestamp": "2024-01-01T12:00:00Z",
        "count": 120,
        "sum": 15600.5,
        "avg": 130.0,
        "min": 25.2,
        "max": 890.3,
        "p50": 95.5,
        "p90": 250.2,
        "p95": 340.8,
        "p99": 650.1
      }
    ]
  },
  "timers": {
    "database_query_duration": [
      {
        "timestamp": "2024-01-01T12:00:00Z",
        "count": 58,
        "sum": 2850.2,
        "avg": 49.1,
        "min": 5.2,
        "max": 245.8,
        "rate": 0.97
      }
    ]
  }
}
```

#### 2.2 获取系统资源指标

**接口**: `GET /metrics/system`

**描述**: 获取系统资源使用情况

**响应**:

```json
{
  "system": {
    "cpu_percent": 65.8,
    "memory_percent": 75.2,
    "memory_total": 8589934592,
    "memory_available": 2147483648,
    "disk_percent": 45.6,
    "disk_total": 1099511627776,
    "disk_free": 598077333504
  },
  "network": {
    "bytes_sent": 1048576000,
    "bytes_recv": 2097152000,
    "packets_sent": 524288,
    "packets_recv": 1048576
  },
  "process": {
    "memory_rss": 134217728,
    "memory_vms": 268435456,
    "cpu_percent": 12.5,
    "num_threads": 24,
    "num_fds": 156
  }
}
```

#### 2.3 清空指标数据

**接口**: `POST /metrics/clear`

**描述**: 清空指定类型的指标数据

**权限要求**: `monitor:manage`

**请求体**:

```json
{
  "metric_type": "counter" // 可选: counter, gauge, histogram, timer, null(全部)
}
```

**响应**:

```json
{
  "message": "指标数据已清空: counter"
}
```

### 3. 告警管理

#### 3.1 获取告警列表

**接口**: `GET /alerts`

**描述**: 获取系统告警列表

**查询参数**:

- `hours`: 查询小时数 (1-168，默认 24)
- `level`: 告警级别 (info,warning,error,critical)
- `source`: 告警来源 (security,health_monitor,system 等)
- `limit`: 返回数量限制 (1-1000，默认 100)

**响应**:

```json
[
  {
    "id": "alert_abc123",
    "timestamp": "2024-01-01T12:00:00Z",
    "title": "健康检查失败: database_connection",
    "message": "数据库连接失败，连续失败次数: 3",
    "level": "critical",
    "source": "health_monitor",
    "tags": ["health", "database", "database_connection"],
    "status": "active",
    "resolved_at": null
  }
]
```

#### 3.2 创建手动告警

**接口**: `POST /alerts`

**描述**: 手动创建系统告警

**权限要求**: `monitor:manage`

**请求体**:

```json
{
  "title": "系统维护通知",
  "message": "系统将于今晚23:00进行维护升级，预计耗时2小时",
  "level": "warning",
  "source": "manual",
  "tags": ["maintenance", "system"],
  "immediate": false
}
```

**响应**:

```json
{
  "message": "告警创建成功"
}
```

#### 3.3 解决告警

**接口**: `PUT /alerts/{alert_id}/resolve`

**描述**: 标记告警为已解决

**权限要求**: `monitor:manage`

**响应**:

```json
{
  "message": "告警已解决"
}
```

#### 3.4 获取告警统计

**接口**: `GET /alerts/stats`

**描述**: 获取告警统计信息

**响应**:

```json
{
  "total": 156,
  "by_level": {
    "info": 45,
    "warning": 78,
    "error": 28,
    "critical": 5
  },
  "by_source": {
    "health_monitor": 82,
    "security": 35,
    "system": 24,
    "manual": 15
  },
  "resolved": 142,
  "active": 14
}
```

### 4. 安全日志

#### 4.1 获取安全事件

**接口**: `GET /security/events`

**描述**: 获取安全事件列表

**权限要求**: `security:read`

**查询参数**:

- `hours`: 查询小时数 (1-168，默认 24)
- `event_types`: 事件类型，逗号分隔
- `risk_levels`: 风险级别，逗号分隔 (low,medium,high,critical)
- `user_id`: 用户 ID 筛选
- `ip_address`: IP 地址筛选
- `limit`: 返回数量限制 (1-1000，默认 100)

**事件类型**:

- 认证: `login_success`, `login_failed`, `login_blocked`, `logout`
- 授权: `permission_granted`, `permission_denied`, `role_assigned`
- 账户: `account_created`, `account_activated`, `account_blocked`
- 威胁: `brute_force_attack`, `suspicious_activity`, `unauthorized_access`

**响应**:

```json
[
  {
    "event_id": "evt_abc123",
    "timestamp": "2024-01-01T12:00:00Z",
    "event_type": "login_failed",
    "risk_level": "medium",
    "user_id": 123,
    "ip_address": "*************",
    "resource": "auth",
    "action": "login",
    "details": {
      "reason": "invalid_password",
      "attempt_count": 3,
      "user_agent": "Mozilla/5.0..."
    }
  }
]
```

#### 4.2 获取安全指标

**接口**: `GET /security/metrics`

**描述**: 获取安全指标统计

**查询参数**:

- `days`: 统计天数 (1-30，默认 7)

**响应**:

```json
{
  "event_types": {
    "login_success": 1250,
    "login_failed": 45,
    "permission_denied": 12,
    "suspicious_activity": 8,
    "brute_force_attack": 2
  },
  "risk_levels": {
    "low": 890,
    "medium": 320,
    "high": 95,
    "critical": 12
  }
}
```

### 5. 监控仪表板

#### 5.1 获取仪表板数据

**接口**: `GET /dashboard`

**描述**: 获取监控仪表板汇总数据

**响应**:

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "health": {
    "status": "healthy",
    "components": {
      "database_connection": {
        "status": "healthy",
        "response_time": 25.5
      },
      "redis_connection": {
        "status": "healthy",
        "response_time": 5.2
      }
    }
  },
  "system": {
    "cpu_percent": 65.8,
    "memory_percent": 75.2,
    "disk_percent": 45.6,
    "process": {
      "memory_rss": 134217728,
      "cpu_percent": 12.5,
      "num_threads": 24
    }
  },
  "alerts": {
    "total": 156,
    "active": 14,
    "by_level": {
      "critical": 2,
      "error": 5,
      "warning": 7
    }
  },
  "security": {
    "event_types": {
      "login_success": 125,
      "login_failed": 8,
      "permission_denied": 3
    },
    "risk_levels": {
      "high": 2,
      "medium": 15,
      "low": 98
    }
  }
}
```

### 6. 监控配置

#### 6.1 获取监控配置

**接口**: `GET /config`

**描述**: 获取当前监控系统配置

**响应**:

```json
{
  "health_monitoring": {
    "enabled": true,
    "check_interval": 60,
    "alert_threshold": 3,
    "retention_hours": 24
  },
  "metrics_collection": {
    "enabled": true,
    "flush_interval": 60,
    "retention_hours": 24
  },
  "alerting": {
    "enabled_channels": ["email", "dingtalk"],
    "retention_days": 30,
    "rate_limit_window": 300,
    "max_alerts_per_window": 10
  },
  "security_logging": {
    "threat_detection": true,
    "max_login_attempts": 5,
    "suspicious_ip_threshold": 10
  }
}
```

## 实时更新

### WebSocket 连接

监控系统支持 WebSocket 实时推送：

**连接地址**: `wss://api.chaiguanjia.com/monitoring/ws`

**认证**: 通过查询参数传递令牌

```
wss://api.chaiguanjia.com/monitoring/ws?token=<access_token>
```

**订阅主题**:

```json
{
  "action": "subscribe",
  "topics": ["health", "alerts", "security"]
}
```

**实时数据格式**:

```json
{
  "topic": "health",
  "type": "component_status_changed",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "component": "database_connection",
    "old_status": "healthy",
    "new_status": "unhealthy",
    "error": "连接超时"
  }
}
```

```json
{
  "topic": "alerts",
  "type": "new_alert",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "id": "alert_xyz789",
    "title": "高风险安全事件",
    "level": "critical",
    "source": "security"
  }
}
```

## 告警通知渠道

### 邮件告警

配置 SMTP 服务器进行邮件告警：

**环境变量**:

```bash
SMTP_SERVER=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=password
ALERT_FROM_EMAIL=<EMAIL>
ALERT_TO_EMAILS=<EMAIL>,<EMAIL>
```

### 钉钉告警

配置钉钉机器人 Webhook：

**环境变量**:

```bash
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=xxx
DINGTALK_SECRET=SEC...  # 可选，用于签名验证
```

### 企业微信告警

配置企业微信机器人：

**环境变量**:

```bash
WECHAT_WORK_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx
```

### 自定义 Webhook

配置自定义 Webhook 告警：

**环境变量**:

```bash
ALERT_WEBHOOK_URL=https://your-webhook-endpoint.com/alerts
ALERT_WEBHOOK_HEADERS={"Authorization":"Bearer token","X-Source":"chaiguanjia"}
```

## 性能监控装饰器

### 使用示例

```javascript
// 计时器装饰器
@metrics_timer('user_service_get_user')
async function getUser(userId) {
  return await userRepository.findById(userId);
}

// 计数器装饰器
@metrics_counter('user_service_operations', {operation: 'create'})
async function createUser(userData) {
  return await userRepository.create(userData);
}

// 安全事件记录装饰器
@security_event_logger(SecurityEventType.PERMISSION_GRANTED, SecurityRiskLevel.LOW)
async function grantPermission(userId, permission) {
  // 权限授予逻辑
}
```

## 错误码说明

| 错误码                | HTTP 状态 | 描述           |
| --------------------- | --------- | -------------- |
| MONITOR_ACCESS_DENIED | 403       | 监控权限不足   |
| COMPONENT_NOT_FOUND   | 404       | 监控组件不存在 |
| ALERT_NOT_FOUND       | 404       | 告警不存在     |
| INVALID_METRIC_TYPE   | 400       | 无效的指标类型 |
| INVALID_TIME_RANGE    | 400       | 无效的时间范围 |
| MONITORING_DISABLED   | 503       | 监控功能已禁用 |

## 最佳实践

### 1. 健康检查

- 设置合理的检查间隔（推荐 60 秒）
- 关键组件失败立即告警
- 非关键组件失败累计 3 次后告警

### 2. 性能指标

- 定期清理过期指标数据
- 合理设置缓存 TTL
- 避免高频度指标收集

### 3. 告警管理

- 设置告警频率限制
- 重要告警多渠道通知
- 及时处理和解决告警

### 4. 安全监控

- 启用威胁检测
- 监控异常登录行为
- 定期审查安全日志

### 5. 仪表板使用

- 定制化监控面板
- 设置关键指标阈值
- 建立监控值班制度

## 监控指标参考

### 系统指标

- **CPU 使用率**: < 80% (正常), 80-90% (警告), > 90% (严重)
- **内存使用率**: < 85% (正常), 85-95% (警告), > 95% (严重)
- **磁盘使用率**: < 80% (正常), 80-95% (警告), > 95% (严重)

### 应用指标

- **API 响应时间**: < 200ms (优秀), 200-500ms (良好), 500ms-2s (一般), > 2s (差)
- **错误率**: < 1% (正常), 1-5% (警告), > 5% (严重)
- **数据库查询时间**: < 100ms (优秀), 100-500ms (良好), > 500ms (需优化)

### 安全指标

- **登录失败率**: < 5% (正常), 5-10% (关注), > 10% (异常)
- **权限拒绝率**: < 2% (正常), 2-5% (关注), > 5% (异常)
- **可疑活动频率**: 0 (正常), 1-5/天 (关注), > 5/天 (严重)
