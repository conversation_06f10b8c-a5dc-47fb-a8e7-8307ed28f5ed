# API 最佳实践指南

## 目录

1. [认证与授权最佳实践](#认证与授权最佳实践)
2. [API 设计原则](#api设计原则)
3. [错误处理策略](#错误处理策略)
4. [性能优化指南](#性能优化指南)
5. [安全防护措施](#安全防护措施)
6. [监控与日志](#监控与日志)
7. [版本管理](#版本管理)
8. [开发工具推荐](#开发工具推荐)

## 认证与授权最佳实践

### JWT 令牌管理

#### 令牌设计原则

```javascript
// 1. 访问令牌设计 - 短期有效
const accessTokenPayload = {
  sub: 'user_123', // 用户ID
  type: 'access', // 令牌类型
  exp: Date.now() + 900000, // 15分钟过期
  iat: Date.now(), // 签发时间
  iss: 'chaiguanjia', // 签发者
  aud: 'chaiguanjia-users', // 受众
  roles: ['user'], // 用户角色
  permissions: ['read:profile'], // 权限列表
};

// 2. 刷新令牌设计 - 长期有效
const refreshTokenPayload = {
  sub: 'user_123',
  type: 'refresh',
  exp: Date.now() + 604800000, // 7天过期
  iat: Date.now(),
  iss: 'chaiguanjia',
  aud: 'chaiguanjia-users',
  session_id: 'sess_abc123', // 会话ID
};
```

#### 令牌存储策略

```javascript
class TokenManager {
  constructor() {
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenRefreshPromise = null;
  }

  // 安全存储令牌
  storeTokens(tokens) {
    // 访问令牌存储在内存中
    this.accessToken = tokens.access_token;

    // 刷新令牌存储在HttpOnly Cookie中（推荐）
    // 或者存储在安全的本地存储中
    if (typeof document !== 'undefined') {
      // 浏览器环境
      document.cookie = `refresh_token=${tokens.refresh_token}; HttpOnly; Secure; SameSite=Strict; Max-Age=604800`;
    } else {
      // Node.js环境
      this.refreshToken = tokens.refresh_token;
    }
  }

  // 获取访问令牌
  getAccessToken() {
    return this.accessToken;
  }

  // 自动刷新令牌
  async ensureValidToken() {
    if (!this.accessToken) {
      throw new Error('未登录');
    }

    // 检查令牌是否即将过期（提前2分钟刷新）
    const payload = this.parseTokenPayload(this.accessToken);
    const expiresAt = payload.exp * 1000;
    const now = Date.now();
    const shouldRefresh = expiresAt - now < 120000; // 2分钟

    if (shouldRefresh) {
      return await this.refreshAccessToken();
    }

    return this.accessToken;
  }

  // 刷新访问令牌（防止并发刷新）
  async refreshAccessToken() {
    if (this.tokenRefreshPromise) {
      return await this.tokenRefreshPromise;
    }

    this.tokenRefreshPromise = this._doRefreshToken();

    try {
      const result = await this.tokenRefreshPromise;
      return result;
    } finally {
      this.tokenRefreshPromise = null;
    }
  }

  async _doRefreshToken() {
    const refreshToken = this.getRefreshToken();

    const response = await fetch('/api/v1/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    if (!response.ok) {
      // 刷新失败，清除所有令牌
      this.clearTokens();
      throw new Error('令牌刷新失败，请重新登录');
    }

    const tokens = await response.json();
    this.accessToken = tokens.access_token;

    return tokens.access_token;
  }

  // 解析令牌载荷
  parseTokenPayload(token) {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  }

  // 清除所有令牌
  clearTokens() {
    this.accessToken = null;
    this.refreshToken = null;

    // 清除Cookie中的刷新令牌
    if (typeof document !== 'undefined') {
      document.cookie = 'refresh_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    }
  }
}
```

### 权限验证策略

#### 基于角色的访问控制（RBAC）

```javascript
// 权限检查器
class PermissionChecker {
  constructor(userPermissions, userRoles) {
    this.permissions = new Set(userPermissions);
    this.roles = new Set(userRoles);
  }

  // 检查单个权限
  hasPermission(permission) {
    return this.permissions.has(permission);
  }

  // 检查多个权限（需要全部满足）
  hasAllPermissions(permissions) {
    return permissions.every(perm => this.permissions.has(perm));
  }

  // 检查多个权限（满足任一即可）
  hasAnyPermission(permissions) {
    return permissions.some(perm => this.permissions.has(perm));
  }

  // 检查角色
  hasRole(role) {
    return this.roles.has(role);
  }

  // 检查资源权限（支持通配符）
  hasResourcePermission(action, resource) {
    const patterns = [
      `${action}:${resource}`, // 精确匹配
      `${action}:*`, // 操作通配符
      `*:${resource}`, // 资源通配符
      '*:*', // 全局权限
    ];

    return patterns.some(pattern => this.permissions.has(pattern));
  }

  // 层级权限检查
  hasHierarchicalPermission(permission, level = 0) {
    const parts = permission.split(':');

    // 从当前级别开始向上检查
    for (let i = level; i >= 0; i--) {
      const checkPerm = parts.slice(0, parts.length - i).join(':') + (i > 0 ? ':*'.repeat(i) : '');
      if (this.permissions.has(checkPerm)) {
        return true;
      }
    }

    return false;
  }
}

// 权限装饰器
function requirePermissions(...permissions) {
  return function (target, propertyName, descriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args) {
      const checker = this.getPermissionChecker();

      if (!checker.hasAllPermissions(permissions)) {
        throw new Error(`权限不足，需要权限: ${permissions.join(', ')}`);
      }

      return method.apply(this, args);
    };
  };
}

// 使用示例
class UserService {
  @requirePermissions('read:users')
  async getUsers() {
    // 获取用户列表
  }

  @requirePermissions('update:users', 'read:users')
  async updateUser(userId, data) {
    // 更新用户
  }
}
```

## API 设计原则

### RESTful 设计规范

#### 资源命名约定

```
# 正确的资源命名
GET    /api/v1/users              # 获取用户列表
GET    /api/v1/users/123          # 获取特定用户
POST   /api/v1/users              # 创建用户
PUT    /api/v1/users/123          # 更新用户
DELETE /api/v1/users/123          # 删除用户

# 嵌套资源
GET    /api/v1/users/123/roles    # 获取用户的角色
POST   /api/v1/users/123/roles    # 为用户分配角色
DELETE /api/v1/users/123/roles/admin # 移除用户的特定角色

# 特殊操作
POST   /api/v1/users/123/activate    # 激活用户
POST   /api/v1/users/123/reset-password # 重置密码
GET    /api/v1/users/123/sessions     # 获取用户会话
```

#### HTTP 状态码使用规范

```javascript
// 状态码映射
const HTTP_STATUS = {
  // 成功响应
  OK: 200, // 请求成功
  CREATED: 201, // 资源创建成功
  ACCEPTED: 202, // 请求已接受，处理中
  NO_CONTENT: 204, // 请求成功，无返回内容

  // 客户端错误
  BAD_REQUEST: 400, // 请求参数错误
  UNAUTHORIZED: 401, // 未认证
  FORBIDDEN: 403, // 权限不足
  NOT_FOUND: 404, // 资源不存在
  METHOD_NOT_ALLOWED: 405, // 方法不允许
  CONFLICT: 409, // 资源冲突
  UNPROCESSABLE_ENTITY: 422, // 请求格式正确但语义错误
  RATE_LIMITED: 429, // 请求频率限制

  // 服务器错误
  INTERNAL_ERROR: 500, // 服务器内部错误
  BAD_GATEWAY: 502, // 网关错误
  SERVICE_UNAVAILABLE: 503, // 服务不可用
  GATEWAY_TIMEOUT: 504, // 网关超时
};

// 响应格式标准化
class APIResponse {
  static success(data, message = '操作成功') {
    return {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString(),
    };
  }

  static error(message, code = 'UNKNOWN_ERROR', details = null) {
    return {
      success: false,
      message,
      error_code: code,
      details,
      timestamp: new Date().toISOString(),
    };
  }

  static paginated(items, total, page, size) {
    return {
      success: true,
      data: {
        items,
        pagination: {
          total,
          page,
          size,
          pages: Math.ceil(total / size),
        },
      },
      timestamp: new Date().toISOString(),
    };
  }
}
```

### 请求验证与响应处理

#### 输入验证

```javascript
// 使用Joi进行输入验证
const Joi = require('joi');

// 用户注册验证模式
const userRegistrationSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': '请输入有效的邮箱地址',
    'any.required': '邮箱是必填项',
  }),

  password: Joi.string()
    .min(8)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .required()
    .messages({
      'string.min': '密码长度至少8位',
      'string.pattern.base': '密码必须包含大小写字母、数字和特殊字符',
      'any.required': '密码是必填项',
    }),

  name: Joi.string().min(2).max(50).required().messages({
    'string.min': '姓名至少2个字符',
    'string.max': '姓名不能超过50个字符',
    'any.required': '姓名是必填项',
  }),

  phone: Joi.string()
    .pattern(/^(\+86)?1[3-9]\d{9}$/)
    .optional()
    .messages({
      'string.pattern.base': '请输入有效的手机号码',
    }),
});

// 验证中间件
function validateRequest(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      return res
        .status(400)
        .json(APIResponse.error('请求参数验证失败', 'VALIDATION_ERROR', details));
    }

    req.validatedData = value;
    next();
  };
}
```

#### 分页和排序

```javascript
// 分页参数处理
class PaginationHelper {
  static parseParams(query) {
    const page = Math.max(1, parseInt(query.page) || 1);
    const size = Math.min(100, Math.max(1, parseInt(query.size) || 20));
    const offset = (page - 1) * size;

    return { page, size, offset };
  }

  static parseSorting(query, allowedFields = []) {
    const sort = query.sort || 'id';
    const order = ['asc', 'desc'].includes(query.order?.toLowerCase())
      ? query.order.toLowerCase()
      : 'desc';

    // 验证排序字段
    if (allowedFields.length && !allowedFields.includes(sort)) {
      throw new Error(`不支持的排序字段: ${sort}`);
    }

    return { sort, order };
  }

  static parseFilters(query, allowedFilters = {}) {
    const filters = {};

    Object.keys(allowedFilters).forEach(key => {
      if (query[key] !== undefined) {
        const filterConfig = allowedFilters[key];
        filters[key] = this.parseFilterValue(query[key], filterConfig);
      }
    });

    return filters;
  }

  static parseFilterValue(value, config) {
    switch (config.type) {
      case 'boolean':
        return value === 'true';
      case 'number':
        return parseInt(value) || 0;
      case 'date':
        return new Date(value);
      case 'array':
        return value.split(',').map(v => v.trim());
      default:
        return value;
    }
  }
}

// 使用示例
app.get('/api/v1/users', async (req, res) => {
  try {
    const { page, size, offset } = PaginationHelper.parseParams(req.query);
    const { sort, order } = PaginationHelper.parseSorting(req.query, [
      'id',
      'email',
      'name',
      'created_at',
      'last_login_at',
    ]);
    const filters = PaginationHelper.parseFilters(req.query, {
      is_active: { type: 'boolean' },
      role: { type: 'string' },
      created_after: { type: 'date' },
    });

    const result = await userService.getUsers({
      offset,
      limit: size,
      sort,
      order,
      filters,
    });

    res.json(APIResponse.paginated(result.users, result.total, page, size));
  } catch (error) {
    res.status(500).json(APIResponse.error(error.message, 'GET_USERS_ERROR'));
  }
});
```

## 错误处理策略

### 统一错误处理

```javascript
// 自定义错误类
class APIError extends Error {
  constructor(message, statusCode = 500, errorCode = 'INTERNAL_ERROR', details = null) {
    super(message);
    this.name = 'APIError';
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.details = details;
  }
}

class ValidationError extends APIError {
  constructor(message, details) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

class AuthenticationError extends APIError {
  constructor(message = '认证失败') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

class AuthorizationError extends APIError {
  constructor(message = '权限不足') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

class NotFoundError extends APIError {
  constructor(resource = '资源') {
    super(`${resource}不存在`, 404, 'NOT_FOUND');
  }
}

class ConflictError extends APIError {
  constructor(message = '资源冲突') {
    super(message, 409, 'CONFLICT');
  }
}

class RateLimitError extends APIError {
  constructor(message = '请求频率超限') {
    super(message, 429, 'RATE_LIMIT_EXCEEDED');
  }
}

// 全局错误处理中间件
function errorHandler(err, req, res, next) {
  // 记录错误日志
  logger.error({
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    user: req.user?.id,
  });

  // API错误
  if (err instanceof APIError) {
    return res
      .status(err.statusCode)
      .json(APIResponse.error(err.message, err.errorCode, err.details));
  }

  // JWT错误
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json(APIResponse.error('无效的令牌', 'INVALID_TOKEN'));
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json(APIResponse.error('令牌已过期', 'TOKEN_EXPIRED'));
  }

  // 数据库错误
  if (err.name === 'SequelizeValidationError') {
    const details = err.errors.map(error => ({
      field: error.path,
      message: error.message,
      value: error.value,
    }));

    return res
      .status(400)
      .json(APIResponse.error('数据验证失败', 'DATABASE_VALIDATION_ERROR', details));
  }

  // 默认服务器错误
  res
    .status(500)
    .json(
      APIResponse.error(
        process.env.NODE_ENV === 'production' ? '服务器内部错误' : err.message,
        'INTERNAL_ERROR'
      )
    );
}
```

### 客户端错误处理

```javascript
// API客户端
class APIClient {
  constructor(baseURL, tokenManager) {
    this.baseURL = baseURL;
    this.tokenManager = tokenManager;
    this.retryAttempts = 3;
    this.retryDelay = 1000;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    // 添加认证头
    if (this.tokenManager) {
      const token = await this.tokenManager.ensureValidToken();
      config.headers.Authorization = `Bearer ${token}`;
    }

    return this.executeWithRetry(url, config);
  }

  async executeWithRetry(url, config, attempt = 1) {
    try {
      const response = await fetch(url, config);

      // 处理HTTP错误
      if (!response.ok) {
        await this.handleHTTPError(response, url, config, attempt);
      }

      return await response.json();
    } catch (error) {
      if (attempt < this.retryAttempts && this.shouldRetry(error)) {
        await this.delay(this.retryDelay * attempt);
        return this.executeWithRetry(url, config, attempt + 1);
      }

      throw error;
    }
  }

  async handleHTTPError(response, url, config, attempt) {
    const errorData = await response.json().catch(() => ({}));

    switch (response.status) {
      case 401:
        // 令牌无效，尝试刷新
        if (this.tokenManager && errorData.error_code === 'TOKEN_EXPIRED') {
          try {
            await this.tokenManager.refreshAccessToken();
            // 重试原请求
            if (attempt === 1) {
              const token = await this.tokenManager.ensureValidToken();
              config.headers.Authorization = `Bearer ${token}`;
              return this.executeWithRetry(url, config, attempt + 1);
            }
          } catch (refreshError) {
            this.tokenManager.clearTokens();
            throw new AuthenticationError('认证失败，请重新登录');
          }
        }
        throw new AuthenticationError(errorData.message);

      case 403:
        throw new AuthorizationError(errorData.message);

      case 404:
        throw new NotFoundError(errorData.message);

      case 409:
        throw new ConflictError(errorData.message);

      case 429:
        // 处理限流
        const retryAfter = response.headers.get('Retry-After');
        if (retryAfter && attempt < this.retryAttempts) {
          await this.delay(parseInt(retryAfter) * 1000);
          return this.executeWithRetry(url, config, attempt + 1);
        }
        throw new RateLimitError(errorData.message);

      default:
        throw new APIError(errorData.message || '请求失败', response.status, errorData.error_code);
    }
  }

  shouldRetry(error) {
    // 可重试的错误类型
    return (
      error.name === 'TypeError' || // 网络错误
      error.statusCode >= 500 || // 服务器错误
      error.statusCode === 429
    ); // 限流错误
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

## 性能优化指南

### 缓存策略

```javascript
// Redis缓存封装
class CacheManager {
  constructor(redisClient) {
    this.redis = redisClient;
    this.defaultTTL = 3600; // 1小时
  }

  // 设置缓存
  async set(key, value, ttl = this.defaultTTL) {
    const serialized = JSON.stringify(value);
    await this.redis.setex(key, ttl, serialized);
  }

  // 获取缓存
  async get(key) {
    const value = await this.redis.get(key);
    return value ? JSON.parse(value) : null;
  }

  // 删除缓存
  async del(key) {
    await this.redis.del(key);
  }

  // 批量删除（通过模式）
  async delPattern(pattern) {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }

  // 缓存装饰器
  cache(keyGenerator, ttl = this.defaultTTL) {
    return (target, propertyName, descriptor) => {
      const originalMethod = descriptor.value;

      descriptor.value = async function (...args) {
        const key = keyGenerator(...args);

        // 尝试从缓存获取
        let result = await this.cacheManager.get(key);
        if (result !== null) {
          return result;
        }

        // 缓存未命中，调用原方法
        result = await originalMethod.apply(this, args);

        // 存储到缓存
        await this.cacheManager.set(key, result, ttl);

        return result;
      };
    };
  }
}

// 使用示例
class UserService {
  constructor(cacheManager) {
    this.cacheManager = cacheManager;
  }

  @cache(userId => `user:${userId}`, 1800) // 30分钟缓存
  async getUserById(userId) {
    // 从数据库获取用户
    return await this.userRepository.findById(userId);
  }

  async updateUser(userId, data) {
    const user = await this.userRepository.update(userId, data);

    // 清除相关缓存
    await this.cacheManager.del(`user:${userId}`);
    await this.cacheManager.delPattern(`user:${userId}:*`);

    return user;
  }
}
```

### 数据库查询优化

```javascript
// 查询构建器
class QueryBuilder {
  constructor(model) {
    this.model = model;
    this.query = model.findAll || model.find;
    this.options = {};
  }

  select(fields) {
    this.options.attributes = Array.isArray(fields) ? fields : [fields];
    return this;
  }

  where(conditions) {
    this.options.where = { ...this.options.where, ...conditions };
    return this;
  }

  include(associations) {
    this.options.include = this.options.include || [];
    this.options.include.push(...(Array.isArray(associations) ? associations : [associations]));
    return this;
  }

  limit(count) {
    this.options.limit = count;
    return this;
  }

  offset(count) {
    this.options.offset = count;
    return this;
  }

  orderBy(field, direction = 'ASC') {
    this.options.order = this.options.order || [];
    this.options.order.push([field, direction]);
    return this;
  }

  // 执行查询
  async exec() {
    return await this.query(this.options);
  }

  // 执行分页查询
  async paginate(page = 1, size = 20) {
    const offset = (page - 1) * size;
    this.offset(offset).limit(size);

    const { count, rows } = await this.model.findAndCountAll(this.options);

    return {
      items: rows,
      total: count,
      page,
      size,
      pages: Math.ceil(count / size),
    };
  }
}

// 使用示例
class UserRepository {
  async getActiveUsers(page = 1, size = 20) {
    return await new QueryBuilder(User)
      .select(['id', 'email', 'name', 'created_at'])
      .where({ is_active: true })
      .include([
        {
          model: Role,
          attributes: ['code', 'name'],
        },
      ])
      .orderBy('created_at', 'DESC')
      .paginate(page, size);
  }

  async getUsersWithRoles(roleIds) {
    return await new QueryBuilder(User)
      .include([
        {
          model: Role,
          where: { id: roleIds },
          through: { attributes: [] }, // 排除中间表字段
        },
      ])
      .exec();
  }
}
```

### 请求优化

```javascript
// 请求合并器
class RequestBatcher {
  constructor(batchSize = 10, delay = 50) {
    this.batchSize = batchSize;
    this.delay = delay;
    this.batches = new Map();
  }

  // 批量处理请求
  batch(key, request) {
    return new Promise((resolve, reject) => {
      if (!this.batches.has(key)) {
        this.batches.set(key, {
          requests: [],
          timer: null,
        });
      }

      const batch = this.batches.get(key);
      batch.requests.push({ request, resolve, reject });

      // 达到批量大小或设置定时器
      if (batch.requests.length >= this.batchSize) {
        this.executeBatch(key);
      } else if (!batch.timer) {
        batch.timer = setTimeout(() => this.executeBatch(key), this.delay);
      }
    });
  }

  async executeBatch(key) {
    const batch = this.batches.get(key);
    if (!batch || batch.requests.length === 0) return;

    this.batches.delete(key);
    if (batch.timer) {
      clearTimeout(batch.timer);
    }

    try {
      // 执行批量请求
      const requests = batch.requests.map(item => item.request);
      const results = await this.processBatch(key, requests);

      // 分发结果
      batch.requests.forEach((item, index) => {
        item.resolve(results[index]);
      });
    } catch (error) {
      // 批量失败
      batch.requests.forEach(item => {
        item.reject(error);
      });
    }
  }

  async processBatch(key, requests) {
    // 根据key实现不同的批量处理逻辑
    switch (key) {
      case 'users':
        return this.batchGetUsers(requests);
      case 'permissions':
        return this.batchCheckPermissions(requests);
      default:
        throw new Error(`不支持的批量操作: ${key}`);
    }
  }

  async batchGetUsers(userIds) {
    const users = await User.findAll({
      where: { id: userIds },
      include: [Role, Permission],
    });

    // 保持原始顺序
    return userIds.map(id => users.find(user => user.id === id));
  }

  async batchCheckPermissions(requests) {
    // 批量权限检查
    const userIds = [...new Set(requests.map(r => r.userId))];
    const permissions = [...new Set(requests.map(r => r.permission))];

    const userPermissions = await this.getUserPermissions(userIds);

    return requests.map(({ userId, permission }) => {
      return userPermissions[userId]?.includes(permission) || false;
    });
  }
}

// 全局请求批处理器
const requestBatcher = new RequestBatcher();

// 使用示例
async function getUser(userId) {
  return await requestBatcher.batch('users', userId);
}

async function checkPermission(userId, permission) {
  return await requestBatcher.batch('permissions', { userId, permission });
}
```

## 安全防护措施

### 请求频率限制

```javascript
// 基于Redis的分布式限流器
class RateLimiter {
  constructor(redisClient) {
    this.redis = redisClient;
  }

  // 滑动窗口限流
  async checkLimit(key, limit, windowMs) {
    const now = Date.now();
    const window = Math.floor(now / windowMs);
    const redisKey = `rate_limit:${key}:${window}`;

    // 原子操作
    const multi = this.redis.multi();
    multi.incr(redisKey);
    multi.expire(redisKey, Math.ceil(windowMs / 1000));
    const results = await multi.exec();

    const count = results[0][1];
    const remaining = Math.max(0, limit - count);

    return {
      allowed: count <= limit,
      count,
      remaining,
      resetTime: (window + 1) * windowMs,
    };
  }

  // 令牌桶限流
  async checkTokenBucket(key, capacity, refillRate, refillInterval) {
    const now = Date.now();
    const bucketKey = `token_bucket:${key}`;

    // 获取当前桶状态
    const bucketData = await this.redis.hmget(bucketKey, 'tokens', 'lastRefill');
    let tokens = parseInt(bucketData[0]) || capacity;
    let lastRefill = parseInt(bucketData[1]) || now;

    // 计算需要补充的令牌
    const elapsed = now - lastRefill;
    const tokensToAdd = Math.floor(elapsed / refillInterval) * refillRate;
    tokens = Math.min(capacity, tokens + tokensToAdd);

    if (tokens > 0) {
      tokens -= 1;
      await this.redis.hmset(bucketKey, {
        tokens,
        lastRefill: now,
      });
      await this.redis.expire(
        bucketKey,
        Math.ceil((refillInterval * capacity) / refillRate / 1000)
      );

      return { allowed: true, tokens };
    }

    return { allowed: false, tokens: 0 };
  }

  // 限流中间件
  middleware(options) {
    return async (req, res, next) => {
      const {
        keyGenerator = req => req.ip,
        limit = 100,
        windowMs = 60000,
        message = '请求频率超限',
        skipSuccessfulRequests = false,
      } = options;

      const key = keyGenerator(req);
      const result = await this.checkLimit(key, limit, windowMs);

      // 设置响应头
      res.set({
        'X-RateLimit-Limit': limit,
        'X-RateLimit-Remaining': result.remaining,
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
      });

      if (!result.allowed) {
        return res.status(429).json(
          APIResponse.error(message, 'RATE_LIMIT_EXCEEDED', {
            limit,
            resetTime: result.resetTime,
          })
        );
      }

      // 跳过成功请求的计数
      if (skipSuccessfulRequests) {
        res.on('finish', () => {
          if (res.statusCode < 400) {
            // 减少计数（补偿成功请求）
            this.checkLimit(key, limit, windowMs, -1);
          }
        });
      }

      next();
    };
  }
}

// 使用示例
const rateLimiter = new RateLimiter(redisClient);

// 全局限流
app.use(
  rateLimiter.middleware({
    limit: 1000,
    windowMs: 15 * 60 * 1000, // 15分钟
    keyGenerator: req => req.ip,
  })
);

// 登录接口限流
app.post(
  '/api/v1/auth/login',
  rateLimiter.middleware({
    limit: 5,
    windowMs: 15 * 60 * 1000, // 15分钟
    keyGenerator: req => `login:${req.body.email || req.ip}`,
    message: '登录尝试次数过多，请稍后再试',
  }),
  authController.login
);
```

### 输入安全

```javascript
// XSS防护
const xss = require('xss');

class SecurityUtils {
  // 清理HTML内容
  static sanitizeHTML(html) {
    return xss(html, {
      whiteList: {
        p: [],
        br: [],
        strong: [],
        em: [],
        u: [],
        ol: [],
        ul: [],
        li: [],
      },
    });
  }

  // SQL注入防护
  static escapeSQL(value) {
    if (typeof value === 'string') {
      return value.replace(/[\0\x08\x09\x1a\n\r"'\\\%]/g, char => {
        switch (char) {
          case '\0':
            return '\\0';
          case '\x08':
            return '\\b';
          case '\x09':
            return '\\t';
          case '\x1a':
            return '\\z';
          case '\n':
            return '\\n';
          case '\r':
            return '\\r';
          case '"':
          case "'":
          case '\\':
          case '%':
            return '\\' + char;
          default:
            return char;
        }
      });
    }
    return value;
  }

  // 路径遍历防护
  static validatePath(path) {
    const normalizedPath = require('path').normalize(path);

    // 检查路径遍历
    if (normalizedPath.includes('..')) {
      throw new Error('非法路径');
    }

    // 检查绝对路径
    if (require('path').isAbsolute(normalizedPath)) {
      throw new Error('不允许绝对路径');
    }

    return normalizedPath;
  }

  // 内容安全策略
  static setCSP(res) {
    res.set(
      'Content-Security-Policy',
      [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: https:",
        "connect-src 'self'",
        "font-src 'self'",
        "object-src 'none'",
        "media-src 'self'",
        "frame-src 'none'",
      ].join('; ')
    );
  }

  // 安全头设置
  static setSecurityHeaders(res) {
    res.set({
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
    });
  }
}

// 安全中间件
function securityMiddleware(req, res, next) {
  // 设置安全头
  SecurityUtils.setSecurityHeaders(res);
  SecurityUtils.setCSP(res);

  // 清理请求体
  if (req.body && typeof req.body === 'object') {
    req.body = JSON.parse(
      JSON.stringify(req.body, (key, value) => {
        if (typeof value === 'string') {
          // 基础XSS清理
          return SecurityUtils.sanitizeHTML(value);
        }
        return value;
      })
    );
  }

  next();
}
```

## 监控与日志

### 结构化日志

```javascript
// 日志配置
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'chaiguanjia-api' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
    }),
  ],
});

// 请求日志中间件
function requestLogger(req, res, next) {
  const startTime = Date.now();

  // 记录请求
  logger.info('Request started', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    requestId: req.get('X-Request-ID'),
  });

  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - startTime;

    logger.info('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      ip: req.ip,
      userId: req.user?.id,
      requestId: req.get('X-Request-ID'),
    });
  });

  next();
}

// 性能监控
class PerformanceMonitor {
  static trackOperation(name) {
    return (target, propertyName, descriptor) => {
      const originalMethod = descriptor.value;

      descriptor.value = async function (...args) {
        const startTime = Date.now();
        const operationId = `${name}_${Date.now()}_${Math.random()}`;

        logger.info('Operation started', {
          operation: name,
          operationId,
          args: args.length,
        });

        try {
          const result = await originalMethod.apply(this, args);
          const duration = Date.now() - startTime;

          logger.info('Operation completed', {
            operation: name,
            operationId,
            duration,
            success: true,
          });

          return result;
        } catch (error) {
          const duration = Date.now() - startTime;

          logger.error('Operation failed', {
            operation: name,
            operationId,
            duration,
            error: error.message,
            stack: error.stack,
          });

          throw error;
        }
      };
    };
  }
}

// 使用示例
class UserService {
  @PerformanceMonitor.trackOperation('getUserById')
  async getUserById(userId) {
    return await this.userRepository.findById(userId);
  }

  @PerformanceMonitor.trackOperation('updateUser')
  async updateUser(userId, data) {
    return await this.userRepository.update(userId, data);
  }
}
```

### 健康检查

```javascript
// 健康检查端点
class HealthChecker {
  constructor() {
    this.checks = new Map();
    this.registerDefaultChecks();
  }

  registerCheck(name, checkFunction, timeout = 5000) {
    this.checks.set(name, { checkFunction, timeout });
  }

  registerDefaultChecks() {
    // 数据库检查
    this.registerCheck('database', async () => {
      const result = await sequelize.authenticate();
      return { status: 'healthy', details: { connected: true } };
    });

    // Redis检查
    this.registerCheck('redis', async () => {
      const pong = await redisClient.ping();
      return { status: 'healthy', details: { response: pong } };
    });

    // 外部服务检查
    this.registerCheck('authing', async () => {
      const response = await fetch('https://authing-api-url/health');
      if (response.ok) {
        return {
          status: 'healthy',
          details: { responseTime: response.headers.get('x-response-time') },
        };
      }
      throw new Error('Authing service unavailable');
    });
  }

  async runCheck(name) {
    const check = this.checks.get(name);
    if (!check) {
      throw new Error(`健康检查不存在: ${name}`);
    }

    try {
      const result = await Promise.race([
        check.checkFunction(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('检查超时')), check.timeout)),
      ]);

      return { name, ...result, timestamp: new Date().toISOString() };
    } catch (error) {
      return {
        name,
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  async runAllChecks() {
    const results = await Promise.allSettled(
      Array.from(this.checks.keys()).map(name => this.runCheck(name))
    );

    const checks = results.map(result =>
      result.status === 'fulfilled' ? result.value : result.reason
    );

    const allHealthy = checks.every(check => check.status === 'healthy');

    return {
      status: allHealthy ? 'healthy' : 'unhealthy',
      checks,
      timestamp: new Date().toISOString(),
    };
  }
}

// 健康检查路由
const healthChecker = new HealthChecker();

app.get('/health', async (req, res) => {
  try {
    const health = await healthChecker.runAllChecks();
    const statusCode = health.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(health);
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

app.get('/health/:check', async (req, res) => {
  try {
    const result = await healthChecker.runCheck(req.params.check);
    const statusCode = result.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(result);
  } catch (error) {
    res.status(404).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});
```

## 版本管理

### API 版本策略

```javascript
// 版本管理中间件
class VersionManager {
  constructor() {
    this.versions = new Map();
    this.currentVersion = 'v1';
    this.deprecatedVersions = new Set();
  }

  registerVersion(version, routes) {
    this.versions.set(version, routes);
  }

  deprecateVersion(version, sunsetDate) {
    this.deprecatedVersions.add(version);

    // 添加弃用警告中间件
    return (req, res, next) => {
      res.set({
        Deprecation: 'true',
        Sunset: sunsetDate.toISOString(),
        Link: `</api/${this.currentVersion}${req.path}>; rel="successor-version"`,
      });

      logger.warn('Deprecated API version used', {
        version,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      next();
    };
  }

  versionMiddleware(req, res, next) {
    const version = this.extractVersion(req);

    if (!this.versions.has(version)) {
      return res
        .status(400)
        .json(APIResponse.error(`不支持的API版本: ${version}`, 'UNSUPPORTED_VERSION'));
    }

    req.apiVersion = version;
    next();
  }

  extractVersion(req) {
    // 从URL路径提取版本
    const pathMatch = req.path.match(/^\/api\/(v\d+)/);
    if (pathMatch) {
      return pathMatch[1];
    }

    // 从Accept头提取版本
    const acceptHeader = req.get('Accept');
    if (acceptHeader) {
      const versionMatch = acceptHeader.match(/application\/vnd\.chaiguanjia\.(v\d+)\+json/);
      if (versionMatch) {
        return versionMatch[1];
      }
    }

    // 默认版本
    return this.currentVersion;
  }
}

// 使用示例
const versionManager = new VersionManager();

// 注册版本路由
versionManager.registerVersion('v1', require('./routes/v1'));
versionManager.registerVersion('v2', require('./routes/v2'));

// 弃用v1版本
const v1DeprecationMiddleware = versionManager.deprecateVersion('v1', new Date('2024-12-31'));

// 应用中间件
app.use(versionManager.versionMiddleware.bind(versionManager));
app.use('/api/v1', v1DeprecationMiddleware);
```

### 向后兼容性

```javascript
// 字段映射和转换
class FieldMapper {
  constructor() {
    this.mappings = new Map();
  }

  // 注册字段映射
  registerMapping(version, mappings) {
    this.mappings.set(version, mappings);
  }

  // 转换响应数据
  transformResponse(data, fromVersion, toVersion) {
    if (fromVersion === toVersion) {
      return data;
    }

    const mapping = this.mappings.get(`${fromVersion}_to_${toVersion}`);
    if (!mapping) {
      return data;
    }

    return this.applyMapping(data, mapping);
  }

  applyMapping(data, mapping) {
    if (Array.isArray(data)) {
      return data.map(item => this.applyMapping(item, mapping));
    }

    if (typeof data === 'object' && data !== null) {
      const transformed = {};

      for (const [key, value] of Object.entries(data)) {
        const mappingRule = mapping[key];

        if (mappingRule) {
          if (typeof mappingRule === 'string') {
            // 简单重命名
            transformed[mappingRule] = value;
          } else if (typeof mappingRule === 'function') {
            // 自定义转换
            Object.assign(transformed, mappingRule(key, value, data));
          } else if (mappingRule.name) {
            // 重命名并可能转换
            const newValue = mappingRule.transform ? mappingRule.transform(value) : value;
            transformed[mappingRule.name] = newValue;
          }
        } else {
          // 保持原字段
          transformed[key] = this.applyMapping(value, mapping);
        }
      }

      return transformed;
    }

    return data;
  }
}

// 注册字段映射
const fieldMapper = new FieldMapper();

// v1到v2的映射
fieldMapper.registerMapping('v1_to_v2', {
  user_id: 'id',
  full_name: 'name',
  email_address: 'email',
  created_time: {
    name: 'created_at',
    transform: value => new Date(value).toISOString(),
  },
  is_admin: (key, value, data) => ({
    roles: value ? ['admin', 'user'] : ['user'],
  }),
});

// 版本兼容中间件
function versionCompatibility(req, res, next) {
  const originalJson = res.json;

  res.json = function (data) {
    const requestedVersion = req.apiVersion || 'v1';
    const currentVersion = 'v2';

    if (requestedVersion !== currentVersion) {
      data = fieldMapper.transformResponse(data, currentVersion, requestedVersion);
    }

    return originalJson.call(this, data);
  };

  next();
}
```

## 开发工具推荐

### API 文档生成

```javascript
// OpenAPI规范生成
const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: '柴管家API',
      version: '1.0.0',
      description: '柴管家系统API接口文档',
      contact: {
        name: 'API支持',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: 'https://api.chaiguanjia.com/api/v1',
        description: '生产环境',
      },
      {
        url: 'https://staging-api.chaiguanjia.com/api/v1',
        description: '测试环境',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ['./routes/*.js', './models/*.js'],
};

const specs = swaggerJsdoc(options);

// 文档路由
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: 用户登录
 *     tags: [认证]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - identifier
 *               - password
 *             properties:
 *               identifier:
 *                 type: string
 *                 description: 邮箱或手机号
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 description: 密码
 *                 example: SecurePassword123!
 *               remember_me:
 *                 type: boolean
 *                 description: 记住登录状态
 *                 default: false
 *     responses:
 *       200:
 *         description: 登录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 access_token:
 *                   type: string
 *                   description: 访问令牌
 *                 refresh_token:
 *                   type: string
 *                   description: 刷新令牌
 *                 token_type:
 *                   type: string
 *                   example: Bearer
 *                 expires_in:
 *                   type: integer
 *                   description: 令牌过期时间（秒）
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         description: 认证失败
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
```

### 测试工具

```javascript
// Jest API测试
const request = require('supertest');
const app = require('../app');

describe('Auth API', () => {
  let authToken;

  beforeAll(async () => {
    // 创建测试用户
    await request(app).post('/api/v1/auth/register').send({
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: '测试用户',
    });
  });

  describe('POST /auth/login', () => {
    test('成功登录', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          identifier: '<EMAIL>',
          password: 'TestPassword123!',
        })
        .expect(200);

      expect(response.body).toHaveProperty('access_token');
      expect(response.body).toHaveProperty('refresh_token');
      expect(response.body.user.email).toBe('<EMAIL>');

      authToken = response.body.access_token;
    });

    test('密码错误', async () => {
      await request(app)
        .post('/api/v1/auth/login')
        .send({
          identifier: '<EMAIL>',
          password: 'WrongPassword',
        })
        .expect(401);
    });
  });

  describe('GET /auth/me', () => {
    test('获取当前用户信息', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.email).toBe('<EMAIL>');
    });

    test('未认证访问', async () => {
      await request(app).get('/api/v1/auth/me').expect(401);
    });
  });
});

// 性能测试
const loadtest = require('loadtest');

function runLoadTest() {
  const options = {
    url: 'http://localhost:3000/api/v1/auth/login',
    maxRequests: 1000,
    concurrency: 10,
    method: 'POST',
    body: JSON.stringify({
      identifier: '<EMAIL>',
      password: 'TestPassword123!',
    }),
    headers: {
      'Content-Type': 'application/json',
    },
  };

  loadtest.loadTest(options, (error, results) => {
    if (error) {
      console.error('负载测试失败:', error);
    } else {
      console.log('负载测试结果:', results);
    }
  });
}
```

这个最佳实践指南涵盖了 API 开发的各个重要方面，为开发团队提供了完整的指导和代码示例。遵循这些实践可
以确保 API 的安全性、性能和可维护性。
