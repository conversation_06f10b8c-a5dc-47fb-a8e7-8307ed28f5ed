# 错误处理系统文档

## 概述

柴管家系统的错误处理机制提供了完整的错误管理、监控和报告功能。本文档详细介绍了错误码体系、异常处理、
错误监控和最佳实践。

## 错误处理架构

```mermaid
graph TB
    subgraph "错误处理流程"
        A[异常发生] --> B[错误捕获]
        B --> C[错误分类]
        C --> D[错误码映射]
        D --> E[用户友好消息]
        E --> F[标准响应格式]
        F --> G[错误日志记录]
        G --> H[错误监控统计]
        H --> I[告警和报告]
    end

    subgraph "错误监控系统"
        J[错误指标收集]
        K[事件管理]
        L[趋势分析]
        M[健康检查]
        N[报告生成]
    end

    H --> J
    J --> K
    K --> L
    L --> M
    M --> N

    style A fill:#ffcdd2
    style F fill:#c8e6c9
    style I fill:#fff3e0
    style N fill:#e1f5fe
```

## 错误码体系

### 错误码分类

错误码采用 5 位编码体系：`EXXXX`，其中第一位数字表示错误类别：

| 类别             | 编码范围    | 描述                 | 示例                  |
| ---------------- | ----------- | -------------------- | --------------------- |
| **通用错误**     | E1000-E1999 | 系统级错误           | E1001: 内部服务器错误 |
| **请求错误**     | E2000-E2999 | 客户端请求错误       | E2001: 参数验证错误   |
| **认证授权错误** | E3000-E3999 | 身份认证和权限错误   | E3001: 访问令牌无效   |
| **资源错误**     | E4000-E4999 | 资源不存在或访问错误 | E4001: 资源未找到     |
| **业务逻辑错误** | E5000-E5999 | 业务规则违反         | E5004: 配额超限       |
| **限流错误**     | E6000-E6999 | 频率限制和并发控制   | E6000: 请求频率超限   |
| **数据库错误**   | E7000-E7999 | 数据库操作错误       | E7001: 数据库连接错误 |
| **外部服务错误** | E8000-E8999 | 外部依赖服务错误     | E8001: Redis 连接错误 |
| **网络错误**     | E9000-E9999 | 网络和通信错误       | E9001: 连接超时       |

### 常用错误码

#### 通用错误 (E1xxx)

```yaml
E1000: UNKNOWN_ERROR # 未知错误
E1001: INTERNAL_SERVER_ERROR # 服务器内部错误
E1002: SERVICE_UNAVAILABLE # 服务不可用
E1003: TIMEOUT_ERROR # 请求超时
E1004: CONFIGURATION_ERROR # 配置错误
```

#### 请求错误 (E2xxx)

```yaml
E2000: BAD_REQUEST # 错误的请求
E2001: VALIDATION_ERROR # 参数验证错误
E2002: MISSING_PARAMETER # 缺少必需参数
E2003: INVALID_PARAMETER # 无效参数
E2004: REQUEST_TOO_LARGE # 请求体过大
E2005: UNSUPPORTED_MEDIA_TYPE # 不支持的媒体类型
```

#### 认证授权错误 (E3xxx)

```yaml
E3000: UNAUTHORIZED # 未授权访问
E3001: INVALID_TOKEN # 无效的访问令牌
E3002: TOKEN_EXPIRED # 访问令牌已过期
E3003: INSUFFICIENT_PERMISSIONS # 权限不足
E3004: ACCOUNT_DISABLED # 账户已禁用
E3005: LOGIN_REQUIRED # 需要登录
```

## 标准错误响应格式

所有错误响应都遵循统一的 JSON 格式：

```json
{
  "success": false,
  "error_code": "E2001",
  "message": "请求参数验证失败",
  "details": "邮箱格式不正确",
  "timestamp": "2024-01-15T10:30:45.123Z",
  "request_id": "550e8400-e29b-41d4-a716-************",
  "path": "/api/v1/users",
  "method": "POST",
  "severity": "low",
  "validation_errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确",
      "type": "value_error",
      "input": "invalid-email"
    }
  ]
}
```

### 响应字段说明

| 字段                | 类型    | 必需 | 描述                               |
| ------------------- | ------- | ---- | ---------------------------------- |
| `success`           | boolean | ✅   | 请求是否成功（错误时始终为 false） |
| `error_code`        | string  | ✅   | 错误代码                           |
| `message`           | string  | ✅   | 用户友好的错误消息                 |
| `details`           | string  | ❌   | 详细错误信息（仅开发环境）         |
| `timestamp`         | string  | ✅   | 错误发生时间（ISO 8601 格式）      |
| `request_id`        | string  | ❌   | 请求唯一标识                       |
| `path`              | string  | ❌   | 请求路径                           |
| `method`            | string  | ❌   | 请求方法                           |
| `severity`          | string  | ✅   | 错误严重程度                       |
| `validation_errors` | array   | ❌   | 验证错误详情                       |
| `trace_id`          | string  | ❌   | 追踪 ID（仅开发环境）              |
| `stack_trace`       | string  | ❌   | 堆栈信息（仅开发环境）             |

## 错误严重程度

| 级别         | 描述 | 影响范围       | 处理优先级 |
| ------------ | ---- | -------------- | ---------- |
| **critical** | 严重 | 系统不可用     | 立即处理   |
| **high**     | 高   | 影响核心功能   | 优先处理   |
| **medium**   | 中   | 影响部分功能   | 正常处理   |
| **low**      | 低   | 不影响核心功能 | 延后处理   |

## 异常类型

### 基础异常类

```python
from app.core.exceptions import BaseAPIException, ErrorCode, ErrorSeverity

# 抛出自定义异常
raise BaseAPIException(
    message="业务处理失败",
    error_code=ErrorCode.BUSINESS_LOGIC_ERROR,
    status_code=400,
    severity=ErrorSeverity.MEDIUM,
    details="余额不足，无法完成转账"
)
```

### 专用异常类

#### 验证异常

```python
from app.core.exceptions import ValidationException

raise ValidationException(
    message="参数验证失败",
    validation_errors=[
        {"field": "email", "message": "邮箱格式不正确"}
    ]
)
```

#### 资源未找到异常

```python
from app.core.exceptions import ResourceNotFoundException

raise ResourceNotFoundException(
    message="用户不存在",
    resource_type="user",
    resource_id="123"
)
```

#### 认证异常

```python
from app.core.exceptions import AuthenticationException

raise AuthenticationException(
    message="访问令牌无效",
    error_code=ErrorCode.INVALID_TOKEN
)
```

#### 限流异常

```python
from app.core.exceptions import RateLimitException

raise RateLimitException(
    message="请求频率过高",
    retry_after=60
)
```

## 便捷的错误抛出函数

系统提供了一系列便捷函数来抛出常见错误：

```python
from app.core.error_handlers import (
    raise_not_found,
    raise_validation_error,
    raise_business_error,
    raise_unauthorized,
    raise_forbidden,
    raise_rate_limit_exceeded,
)

# 资源未找到
raise_not_found("用户", "123")

# 验证错误
raise_validation_error("email", "邮箱格式不正确")

# 业务逻辑错误
raise_business_error("余额不足")

# 认证失败
raise_unauthorized("访问令牌无效")

# 权限不足
raise_forbidden("需要管理员权限")

# 限流
raise_rate_limit_exceeded(retry_after=60)
```

## 错误监控

### 监控指标

系统自动收集以下错误指标：

```yaml
全局指标:
  - total_errors: 总错误数
  - error_rate: 错误率（百分比）
  - critical_errors: 严重错误数
  - high_errors: 高级错误数
  - medium_errors: 中级错误数
  - low_errors: 低级错误数

趋势指标:
  - error_rate_trend: 错误率趋势
  - recent_errors: 最近错误时间戳
  - peak_requests_per_minute: 峰值每分钟错误数

分类统计:
  - error_code_counts: 按错误码统计
  - top_errors: 最频繁错误
```

### 错误事件管理

每个错误都会创建或更新对应的事件：

```json
{
  "incident_id": "INC_1642680123_001",
  "error_code": "E2001",
  "message": "参数验证失败",
  "severity": "low",
  "first_seen": "2024-01-15T10:30:45.123Z",
  "last_seen": "2024-01-15T10:32:15.456Z",
  "count": 5,
  "affected_users_count": 3,
  "affected_paths_count": 2,
  "resolved": false,
  "resolution_notes": null
}
```

## 监控 API 接口

### 获取错误统计摘要

```http
GET /api/v1/error-monitoring/summary
```

**响应示例**：

```json
{
  "total_errors": 1234,
  "error_rate": 2.5,
  "severity_distribution": {
    "critical": 0,
    "high": 12,
    "medium": 156,
    "low": 1066
  },
  "top_errors": [
    {
      "error_code": "E2001",
      "count": 245,
      "percentage": 19.8
    }
  ],
  "recent_trend": [1.2, 1.8, 2.1, 2.5, 2.3]
}
```

### 获取错误事件列表

```http
GET /api/v1/error-monitoring/incidents?limit=10&severity=high&resolved=false
```

**查询参数**：

- `limit`: 返回数量限制（1-100）
- `severity`: 按严重程度过滤（low/medium/high/critical）
- `resolved`: 按解决状态过滤（true/false）

### 获取错误趋势分析

```http
GET /api/v1/error-monitoring/trends?hours=24
```

**响应示例**：

```json
{
  "time_range": "最近24小时",
  "total_incidents": 45,
  "total_errors": 123,
  "hourly_distribution": {
    "2024-01-15 09:00": 8,
    "2024-01-15 10:00": 12,
    "2024-01-15 11:00": 15
  },
  "error_code_distribution": {
    "E2001": 45,
    "E3001": 23,
    "E4001": 18
  },
  "severity_distribution": {
    "low": 78,
    "medium": 32,
    "high": 12,
    "critical": 1
  }
}
```

### 解决错误事件

```http
POST /api/v1/error-monitoring/incidents/resolve
Content-Type: application/json

{
  "incident_id": "INC_1642680123_001",
  "resolution_notes": "已修复参数验证逻辑"
}
```

### 获取日错误报告

```http
GET /api/v1/error-monitoring/reports/daily
```

### 系统健康检查

```http
GET /api/v1/error-monitoring/health-check
```

**响应示例**：

```json
{
  "status": "warning",
  "timestamp": "2024-01-15T10:30:45.123Z",
  "error_rate": 5.2,
  "total_errors": 1234,
  "issues": ["错误率过高: 5.20%", "高级错误较多: 25 个"],
  "recommendations": [
    "立即检查系统核心功能，错误率过高可能影响用户体验",
    "关注高级错误的处理，防止问题恶化"
  ]
}
```

### 获取错误码列表

```http
GET /api/v1/error-monitoring/error-codes
```

## 开发最佳实践

### 1. 选择合适的错误码

```python
# ✅ 正确：使用具体的错误码
raise BaseAPIException(
    message="用户余额不足",
    error_code=ErrorCode.QUOTA_EXCEEDED
)

# ❌ 错误：使用通用错误码
raise BaseAPIException(
    message="用户余额不足",
    error_code=ErrorCode.BUSINESS_LOGIC_ERROR
)
```

### 2. 提供用户友好的消息

```python
# ✅ 正确：用户友好的消息
raise ValidationException(
    message="邮箱格式不正确，请输入有效的邮箱地址"
)

# ❌ 错误：技术性消息
raise ValidationException(
    message="Invalid email format: regex match failed"
)
```

### 3. 适当使用错误严重程度

```python
# ✅ 正确：根据影响程度设置
raise DatabaseException(
    message="数据库连接失败",
    severity=ErrorSeverity.HIGH  # 影响核心功能
)

raise ValidationException(
    message="参数格式错误",
    severity=ErrorSeverity.LOW   # 不影响核心功能
)
```

### 4. 记录详细的错误上下文

```python
from app.core.error_monitor import record_error_event

await record_error_event(
    error_code=ErrorCode.PAYMENT_FAILED,
    message="支付处理失败",
    severity=ErrorSeverity.HIGH,
    user_id=user_id,
    request_path=request.url.path,
    payment_id=payment_id,
    payment_amount=amount
)
```

### 5. 处理异步错误

```python
import asyncio

async def process_with_error_handling():
    try:
        result = await some_async_operation()
        return result
    except asyncio.TimeoutError:
        raise BaseAPIException(
            message="操作超时",
            error_code=ErrorCode.TIMEOUT_ERROR,
            severity=ErrorSeverity.MEDIUM
        )
    except ConnectionError:
        raise ExternalServiceException(
            message="外部服务连接失败",
            service_name="payment_service"
        )
```

## 配置管理

### 环境配置

```python
# 开发环境配置
DEBUG = True
LOG_LEVEL = "DEBUG"
INCLUDE_ERROR_DETAILS = True
INCLUDE_STACK_TRACE = True

# 生产环境配置
DEBUG = False
LOG_LEVEL = "INFO"
INCLUDE_ERROR_DETAILS = False
INCLUDE_STACK_TRACE = False
```

### 告警阈值配置

```python
# 错误监控告警阈值
ALERT_THRESHOLDS = {
    ErrorSeverity.CRITICAL: 1,   # 1个严重错误就告警
    ErrorSeverity.HIGH: 5,       # 5个高级错误告警
    ErrorSeverity.MEDIUM: 20,    # 20个中级错误告警
    ErrorSeverity.LOW: 100,      # 100个低级错误告警
}

ERROR_RATE_THRESHOLD = 10.0  # 错误率超过10%告警
```

## 故障排除

### 常见问题

1. **错误响应格式不一致**

   - 确保所有错误都通过全局异常处理器处理
   - 检查是否正确设置了 `setup_error_handlers(app)`

2. **错误监控数据缺失**

   - 确认错误监控器已正确初始化
   - 检查 Redis 连接状态（如果使用 Redis 存储监控数据）

3. **错误日志过多**
   - 调整日志级别配置
   - 设置适当的错误严重程度
   - 配置日志轮转策略

### 调试方法

1. **查看错误详情**

```bash
# 开启调试模式
export DEBUG=true

# 查看详细错误日志
tail -f logs/error.log | grep "ERROR"
```

2. **检查错误监控状态**

```bash
curl http://localhost:8000/api/v1/error-monitoring/health-check
```

3. **分析错误趋势**

```bash
curl http://localhost:8000/api/v1/error-monitoring/trends?hours=1
```

## 性能考虑

### 错误处理性能

- 错误处理开销：< 5ms
- 错误监控记录：异步处理，不阻塞请求
- 内存使用：监控数据使用滑动窗口，自动清理

### 监控数据优化

- 使用 Redis 缓存热点数据
- 定期清理过期监控数据
- 异步批量处理错误事件

## 总结

错误处理系统提供了完整的错误管理生命周期，包括：

✅ **标准化错误格式**：统一的错误响应格式，便于前端处理

✅ **完整错误码体系**：分类清晰的错误码，便于问题定位

✅ **智能错误监控**：实时错误统计和趋势分析

✅ **用户友好消息**：针对不同环境的错误信息展示

✅ **开发者友好**：详细的调试信息和便捷的使用接口

通过合理使用错误处理系统，可以显著提升系统的稳定性、可维护性和用户体验。
