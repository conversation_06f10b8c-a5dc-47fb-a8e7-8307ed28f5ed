# 柴管家环境使用操作手册

## 文档概述

本手册详细介绍柴管家基础设施环境的使用方法，包括环境启动、配置、监控和维护等操作，确保团队成员能够独
立操作环境。

## 目录

1. [环境概览](#环境概览)
2. [快速开始](#快速开始)
3. [服务管理](#服务管理)
4. [环境配置](#环境配置)
5. [开发操作](#开发操作)
6. [监控和维护](#监控和维护)
7. [常用命令](#常用命令)
8. [最佳实践](#最佳实践)

---

## 环境概览

### 系统架构图

```mermaid
graph TB
    subgraph "柴管家基础设施环境"
        subgraph "前端层"
            NGINX[Nginx<br/>端口: 80/443]
            FRONTEND[React前端<br/>端口: 3000]
        end

        subgraph "应用层"
            BACKEND[FastAPI后端<br/>端口: 8000]
        end

        subgraph "数据层"
            POSTGRES[(PostgreSQL<br/>端口: 5432)]
            REDIS[(Redis<br/>端口: 6379)]
            ES[(Elasticsearch<br/>端口: 9200)]
        end

        subgraph "消息层"
            RABBITMQ[RabbitMQ<br/>端口: 5672/15672]
        end
    end

    NGINX --> FRONTEND
    NGINX --> BACKEND
    BACKEND --> POSTGRES
    BACKEND --> REDIS
    BACKEND --> ES
    BACKEND --> RABBITMQ

    style NGINX fill:#e3f2fd
    style BACKEND fill:#f3e5f5
    style POSTGRES fill:#e8f5e8
    style REDIS fill:#fff3e0
```

### 服务清单

| 服务名称      | 容器名称                  | 端口        | 用途     | 健康检查            |
| ------------- | ------------------------- | ----------- | -------- | ------------------- |
| PostgreSQL    | chaiguanjia_postgresql    | 5432        | 主数据库 | `/health`           |
| Redis         | chaiguanjia_redis         | 6379        | 缓存系统 | `ping`              |
| RabbitMQ      | chaiguanjia_rabbitmq      | 5672, 15672 | 消息队列 | `node_health_check` |
| Elasticsearch | chaiguanjia_elasticsearch | 9200, 9300  | 搜索引擎 | `/_cluster/health`  |
| Backend API   | chaiguanjia_backend       | 8000        | 后端服务 | `/health`           |
| Frontend      | chaiguanjia_frontend      | 3000        | 前端服务 | `/`                 |
| Nginx         | chaiguanjia_nginx         | 80, 443     | 反向代理 | `nginx -t`          |

---

## 快速开始

### 前置要求

确保您的系统已安装以下软件：

```bash
# 检查 Docker 版本（要求 >= 20.10）
docker --version

# 检查 Docker Compose 版本（要求 >= 2.0）
docker-compose --version

# 检查可用内存（推荐 >= 8GB）
free -h
```

### 15 分钟快速启动

#### 步骤 1：克隆项目并配置环境

```bash
# 1. 进入项目目录
cd /path/to/chaiguanjia8_10

# 2. 复制环境配置文件
cp env.example .env

# 3. 检查配置（可选）
cat .env
```

#### 步骤 2：一键启动所有服务

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

#### 步骤 3：验证环境

```bash
# 运行环境验证脚本
chmod +x scripts/monitoring/environment-validation.sh
./scripts/monitoring/environment-validation.sh
```

### 预期输出

成功启动后，您应该看到：

```
NAME                          STATUS          PORTS
chaiguanjia_postgresql        Up (healthy)    0.0.0.0:5432->5432/tcp
chaiguanjia_redis            Up (healthy)    0.0.0.0:6379->6379/tcp
chaiguanjia_rabbitmq         Up (healthy)    0.0.0.0:5672->5672/tcp, 0.0.0.0:15672->15672/tcp
chaiguanjia_elasticsearch    Up (healthy)    0.0.0.0:9200->9200/tcp, 0.0.0.0:9300->9300/tcp
chaiguanjia_backend          Up (healthy)    0.0.0.0:8000->8000/tcp
chaiguanjia_frontend         Up (healthy)    0.0.0.0:3000->3000/tcp
chaiguanjia_nginx            Up (healthy)    0.0.0.0:80->80/tcp, 0.0.0.0:443->443/tcp
```

---

## 服务管理

### 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 启动特定服务
docker-compose up -d postgresql redis

# 前台启动（可看到日志）
docker-compose up postgresql
```

### 停止服务

```bash
# 停止所有服务
docker-compose down

# 停止特定服务
docker-compose stop postgresql

# 停止并删除数据（⚠️ 慎用）
docker-compose down -v
```

### 重启服务

```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend

# 重新构建并重启
docker-compose up -d --build backend
```

### 服务状态查看

```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend

# 查看所有服务日志
docker-compose logs -f

# 查看最近100行日志
docker-compose logs --tail=100 backend
```

---

## 环境配置

### 配置文件说明

#### 主配置文件 `.env`

```bash
# 数据库配置
POSTGRES_DB=chaiguanjia
POSTGRES_USER=admin
POSTGRES_PASSWORD=chaiguanjia2024
POSTGRES_PORT=5432

# Redis配置
REDIS_PASSWORD=chaiguanjia2024
REDIS_PORT=6379

# RabbitMQ配置
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=chaiguanjia2024
RABBITMQ_VHOST=chaiguanjia
RABBITMQ_PORT=5672
RABBITMQ_MANAGEMENT_PORT=15672

# Elasticsearch配置
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_TRANSPORT_PORT=9300

# 应用配置
BACKEND_PORT=8000
FRONTEND_PORT=3000
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# 安全配置
SECRET_KEY=your-secret-key-here
DEBUG=true
ENVIRONMENT=development
```

#### 开发环境配置 `docker-compose.override.yml`

```yaml
version: '3.8'

services:
  backend:
    volumes:
      - ./backend:/app
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  frontend:
    volumes:
      - ./frontend/src:/app/src
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - CHOKIDAR_USEPOLLING=true
```

### 修改配置的步骤

1. **停止服务**

   ```bash
   docker-compose down
   ```

2. **修改配置文件**

   ```bash
   vim .env  # 或使用您喜欢的编辑器
   ```

3. **重新启动服务**
   ```bash
   docker-compose up -d
   ```

---

## 开发操作

### 代码开发流程

#### 后端开发

```bash
# 1. 进入后端容器
docker exec -it chaiguanjia_backend bash

# 2. 安装新依赖
pip install new-package
pip freeze > requirements.txt

# 3. 运行测试
pytest tests/

# 4. 生成迁移文件
alembic revision --autogenerate -m "add new table"

# 5. 执行迁移
alembic upgrade head
```

#### 前端开发

```bash
# 1. 进入前端容器
docker exec -it chaiguanjia_frontend bash

# 2. 安装新依赖
npm install new-package

# 3. 运行测试
npm test

# 4. 构建生产版本
npm run build
```

### 数据库操作

#### 连接数据库

```bash
# 方法1：通过容器连接
docker exec -it chaiguanjia_postgresql psql -U admin -d chaiguanjia

# 方法2：通过主机连接（需要安装psql）
psql "postgresql://admin:chaiguanjia2024@localhost:5432/chaiguanjia"
```

#### 常用数据库命令

```sql
-- 查看所有表
\dt

-- 查看表结构
\d table_name

-- 执行查询
SELECT * FROM users LIMIT 10;

-- 查看数据库大小
SELECT pg_size_pretty(pg_database_size('chaiguanjia'));
```

#### 数据库备份和恢复

```bash
# 备份数据库
docker exec chaiguanjia_postgresql pg_dump -U admin chaiguanjia > backup_$(date +%Y%m%d).sql

# 恢复数据库
docker exec -i chaiguanjia_postgresql psql -U admin chaiguanjia < backup_20240101.sql
```

### Redis 操作

#### 连接 Redis

```bash
# 连接Redis
docker exec -it chaiguanjia_redis redis-cli -a chaiguanjia2024
```

#### 常用 Redis 命令

```redis
# 查看所有键
KEYS *

# 获取键值
GET key_name

# 设置键值
SET key_name "value"

# 查看Redis信息
INFO

# 清空当前数据库
FLUSHDB

# 监控Redis操作
MONITOR
```

---

## 监控和维护

### 健康检查

#### 自动健康检查

```bash
# 运行完整环境验证
./scripts/monitoring/environment-validation.sh

# 运行性能基准测试
./scripts/monitoring/performance-benchmark.sh

# 运行健康检查
./scripts/monitoring/comprehensive-health-check.sh
```

#### 手动健康检查

```bash
# 检查所有服务状态
docker-compose ps

# 检查服务健康状态
docker inspect chaiguanjia_backend | grep Health -A 10

# 测试API接口
curl http://localhost:8000/health

# 测试数据库连接
docker exec chaiguanjia_postgresql pg_isready -U admin
```

### 日志管理

#### 查看日志

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend

# 实时查看日志
docker-compose logs -f backend

# 查看最近的日志
docker-compose logs --tail=50 backend

# 查看指定时间段的日志
docker-compose logs --since="2024-01-01T10:00:00" backend
```

#### 日志清理

```bash
# 清理Docker日志
docker system prune -f

# 清理特定容器日志
docker logs chaiguanjia_backend --tail 0 &> /dev/null

# 自动清理脚本
./scripts/maintenance/cleanup-logs.sh
```

### 性能监控

#### 资源使用监控

```bash
# 查看容器资源使用
docker stats

# 查看特定容器资源
docker stats chaiguanjia_backend

# 监控系统资源
htop  # 或 top

# 监控磁盘使用
df -h
```

#### 性能分析

```bash
# API性能测试
ab -n 100 -c 10 http://localhost:8000/health

# 数据库性能分析
docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c "
SELECT query, calls, total_time, mean_time
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;"
```

---

## 常用命令

### 容器管理命令

```bash
# 查看运行中的容器
docker ps

# 查看所有容器（包括停止的）
docker ps -a

# 进入容器
docker exec -it <container_name> bash

# 查看容器日志
docker logs <container_name>

# 重启容器
docker restart <container_name>

# 删除容器
docker rm <container_name>
```

### Docker Compose 命令

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重新构建服务
docker-compose build

# 查看服务状态
docker-compose ps

# 查看配置
docker-compose config

# 拉取最新镜像
docker-compose pull
```

### 开发调试命令

```bash
# 查看网络配置
docker network ls
docker network inspect chaiguanjia8_10_chaiguanjia_network

# 查看数据卷
docker volume ls
docker volume inspect chaiguanjia8_10_postgres_data

# 清理未使用的资源
docker system prune

# 查看镜像
docker images

# 构建镜像
docker build -t custom_image .
```

---

## 最佳实践

### 开发环境最佳实践

#### 1. 环境隔离

```bash
# 使用不同的compose文件
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# 使用环境变量区分环境
export ENVIRONMENT=development
docker-compose up -d
```

#### 2. 代码同步

```yaml
# 在docker-compose.override.yml中配置代码挂载
services:
  backend:
    volumes:
      - ./backend:/app
      - /app/__pycache__ # 排除缓存目录
```

#### 3. 依赖管理

```bash
# 使用国内镜像源加速
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
npm config set registry https://registry.npmmirror.com
```

### 生产环境准备

#### 1. 安全配置

```bash
# 生成强密码
openssl rand -base64 32

# 配置环境变量
export SECRET_KEY=$(openssl rand -base64 32)
export DEBUG=false
export ENVIRONMENT=production
```

#### 2. 性能优化

```yaml
# 在生产环境配置中优化
services:
  postgresql:
    environment:
      - shared_preload_libraries=pg_stat_statements
    command: postgres -c max_connections=200 -c shared_buffers=256MB
```

#### 3. 监控配置

```bash
# 启用详细监控
docker-compose -f docker-compose.yml -f docker-compose.monitoring.yml up -d
```

### 故障排查流程

#### 1. 问题定位

```bash
# 检查服务状态
docker-compose ps

# 查看错误日志
docker-compose logs --tail=100 <service_name>

# 检查资源使用
docker stats
```

#### 2. 问题修复

```bash
# 重启问题服务
docker-compose restart <service_name>

# 重新构建服务
docker-compose up -d --build <service_name>

# 检查配置
docker-compose config
```

#### 3. 问题预防

```bash
# 定期运行健康检查
./scripts/monitoring/environment-validation.sh

# 设置监控告警
./scripts/monitoring/setup-alerts.sh

# 定期备份数据
./scripts/maintenance/backup-data.sh
```

---

## 附录

### 端口映射表

| 服务          | 容器端口 | 主机端口 | 协议       | 访问地址               |
| ------------- | -------- | -------- | ---------- | ---------------------- |
| PostgreSQL    | 5432     | 5432     | TCP        | localhost:5432         |
| Redis         | 6379     | 6379     | TCP        | localhost:6379         |
| RabbitMQ      | 5672     | 5672     | TCP        | localhost:5672         |
| RabbitMQ 管理 | 15672    | 15672    | HTTP       | http://localhost:15672 |
| Elasticsearch | 9200     | 9200     | HTTP       | http://localhost:9200  |
| Backend API   | 8000     | 8000     | HTTP       | http://localhost:8000  |
| Frontend      | 80       | 3000     | HTTP       | http://localhost:3000  |
| Nginx         | 80/443   | 80/443   | HTTP/HTTPS | http://localhost       |

### 环境变量参考

| 变量名            | 默认值               | 说明       | 必填 |
| ----------------- | -------------------- | ---------- | ---- |
| POSTGRES_DB       | chaiguanjia          | 数据库名称 | 是   |
| POSTGRES_USER     | admin                | 数据库用户 | 是   |
| POSTGRES_PASSWORD | chaiguanjia2024      | 数据库密码 | 是   |
| REDIS_PASSWORD    | chaiguanjia2024      | Redis 密码 | 是   |
| SECRET_KEY        | your-secret-key-here | 应用密钥   | 是   |
| DEBUG             | true                 | 调试模式   | 否   |
| ENVIRONMENT       | development          | 运行环境   | 否   |

### 常见问题索引

1. [服务启动失败](故障排除指南.md#服务启动失败)
2. [数据库连接错误](故障排除指南.md#数据库连接错误)
3. [API 接口无响应](故障排除指南.md#API接口无响应)
4. [前端页面加载失败](故障排除指南.md#前端页面加载失败)
5. [Redis 连接超时](故障排除指南.md#Redis连接超时)
6. [Elasticsearch 启动失败](故障排除指南.md#Elasticsearch启动失败)

---

_文档版本：v1.0_ _最后更新：2024 年 1 月_ _维护团队：技术架构组_
