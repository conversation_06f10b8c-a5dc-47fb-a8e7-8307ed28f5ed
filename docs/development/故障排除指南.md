# 柴管家环境故障排除指南

## 文档概述

本指南提供柴管家基础设施环境的常见问题诊断和解决方案，帮助团队快速定位和解决环境问题，确保开发工作顺
利进行。

## 目录

1. [故障排查流程](#故障排查流程)
2. [服务启动问题](#服务启动问题)
3. [数据库相关问题](#数据库相关问题)
4. [API 服务问题](#API服务问题)
5. [前端服务问题](#前端服务问题)
6. [缓存服务问题](#缓存服务问题)
7. [消息队列问题](#消息队列问题)
8. [搜索引擎问题](#搜索引擎问题)
9. [网络连接问题](#网络连接问题)
10. [性能问题](#性能问题)
11. [资源不足问题](#资源不足问题)
12. [紧急处理预案](#紧急处理预案)

---

## 故障排查流程

### 标准排查步骤

```mermaid
flowchart TD
    A[发现问题] --> B[收集信息]
    B --> C[检查服务状态]
    C --> D[查看日志]
    D --> E[分析错误]
    E --> F{问题类型}

    F -->|服务启动| G[检查配置]
    F -->|性能问题| H[监控资源]
    F -->|网络问题| I[检查连通性]
    F -->|数据问题| J[检查数据库]

    G --> K[尝试修复]
    H --> K
    I --> K
    J --> K

    K --> L{修复成功?}
    L -->|是| M[验证解决]
    L -->|否| N[升级处理]

    M --> O[记录问题]
    N --> P[联系专家]

    style A fill:#ffcdd2
    style M fill:#c8e6c9
    style N fill:#fff3e0
```

### 快速诊断命令

```bash
# 1. 快速检查所有服务状态
docker-compose ps

# 2. 查看系统资源使用
docker stats --no-stream

# 3. 检查磁盘空间
df -h

# 4. 查看最近的错误日志
docker-compose logs --tail=50 | grep -i error

# 5. 运行健康检查
./scripts/monitoring/environment-validation.sh
```

---

## 服务启动问题

### 问题 1：docker-compose up 失败

#### 症状

```bash
$ docker-compose up -d
ERROR: Service 'postgresql' failed to build: ...
```

#### 诊断步骤

```bash
# 1. 检查Docker状态
docker info

# 2. 检查docker-compose配置
docker-compose config

# 3. 查看具体错误
docker-compose up

# 4. 检查端口占用
netstat -tulpn | grep :5432
```

#### 解决方案

**方案 A：端口冲突**

```bash
# 1. 找到占用端口的进程
lsof -i :5432

# 2. 杀死占用进程
sudo kill -9 <PID>

# 3. 或修改配置使用其他端口
vim .env
# 修改 POSTGRES_PORT=5433
```

**方案 B：权限问题**

```bash
# 1. 检查Docker权限
sudo usermod -aG docker $USER

# 2. 重新登录或刷新权限
newgrp docker

# 3. 检查目录权限
sudo chown -R $USER:$USER ./
```

**方案 C：镜像拉取失败**

```bash
# 1. 配置国内镜像源
sudo vim /etc/docker/daemon.json
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ]
}

# 2. 重启Docker服务
sudo systemctl restart docker

# 3. 手动拉取镜像
docker pull postgres:15-alpine
```

### 问题 2：容器启动后立即退出

#### 症状

```bash
$ docker-compose ps
NAME                    STATUS
chaiguanjia_postgresql  Exited (1) 2 minutes ago
```

#### 诊断步骤

```bash
# 1. 查看退出代码和日志
docker-compose logs postgresql

# 2. 检查容器启动命令
docker inspect chaiguanjia_postgresql | grep -A 10 "Cmd"

# 3. 尝试手动启动容器
docker run -it postgres:15-alpine bash
```

#### 解决方案

**方案 A：环境变量错误**

```bash
# 1. 检查.env文件
cat .env | grep POSTGRES

# 2. 确保必要变量存在
POSTGRES_DB=chaiguanjia
POSTGRES_USER=admin
POSTGRES_PASSWORD=chaiguanjia2024

# 3. 重新启动
docker-compose down && docker-compose up -d
```

**方案 B：数据卷问题**

```bash
# 1. 检查数据卷
docker volume ls | grep postgres

# 2. 删除损坏的数据卷（⚠️ 会丢失数据）
docker volume rm chaiguanjia8_10_postgres_data

# 3. 重新创建
docker-compose up -d postgresql
```

---

## 数据库相关问题

### 问题 3：数据库连接失败

#### 症状

```
psql: error: connection to server at "localhost", port 5432 failed:
Connection refused
```

#### 诊断步骤

```bash
# 1. 检查PostgreSQL容器状态
docker-compose ps postgresql

# 2. 检查容器健康状况
docker inspect chaiguanjia_postgresql | grep -A 10 Health

# 3. 测试端口连通性
telnet localhost 5432

# 4. 检查防火墙
sudo ufw status
```

#### 解决方案

**方案 A：服务未启动**

```bash
# 1. 启动PostgreSQL服务
docker-compose up -d postgresql

# 2. 等待服务完全启动
sleep 30

# 3. 验证连接
docker exec chaiguanjia_postgresql pg_isready -U admin
```

**方案 B：网络配置问题**

```bash
# 1. 检查Docker网络
docker network ls
docker network inspect chaiguanjia8_10_chaiguanjia_network

# 2. 重新创建网络
docker-compose down
docker network prune
docker-compose up -d
```

**方案 C：认证问题**

```bash
# 1. 检查密码配置
docker-compose exec postgresql env | grep POSTGRES

# 2. 使用正确的认证信息
psql "postgresql://admin:chaiguanjia2024@localhost:5432/chaiguanjia"

# 3. 重置密码（如果需要）
docker-compose exec postgresql psql -U admin -c "ALTER USER admin PASSWORD 'new_password';"
```

### 问题 4：数据库性能慢

#### 症状

```
查询响应时间超过5秒
API接口超时
```

#### 诊断步骤

```bash
# 1. 检查数据库连接数
docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c "
SELECT count(*) FROM pg_stat_activity WHERE state = 'active';"

# 2. 查看慢查询
docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c "
SELECT query, query_start, state, wait_event
FROM pg_stat_activity
WHERE state != 'idle' AND query_start < now() - interval '5 seconds';"

# 3. 检查锁等待
docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c "
SELECT blocked_locks.pid AS blocked_pid,
       blocking_locks.pid AS blocking_pid,
       blocked_activity.query AS blocked_statement
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
WHERE NOT blocked_locks.granted;"
```

#### 解决方案

**方案 A：优化查询**

```sql
-- 1. 添加索引
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);

-- 2. 分析查询计划
EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM users WHERE email = '<EMAIL>';

-- 3. 更新统计信息
ANALYZE;
```

**方案 B：调整连接池**

```yaml
# docker-compose.yml
services:
  postgresql:
    command: postgres -c max_connections=200 -c shared_buffers=256MB
    environment:
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --locale=C
```

---

## API 服务问题

### 问题 5：API 接口无响应

#### 症状

```bash
$ curl http://localhost:8000/health
curl: (7) Failed to connect to localhost port 8000: Connection refused
```

#### 诊断步骤

```bash
# 1. 检查Backend容器状态
docker-compose ps backend

# 2. 查看Backend日志
docker-compose logs --tail=100 backend

# 3. 检查端口监听
docker exec chaiguanjia_backend netstat -tlnp | grep :8000

# 4. 测试容器内部连接
docker exec chaiguanjia_backend curl localhost:8000/health
```

#### 解决方案

**方案 A：服务未启动**

```bash
# 1. 重启Backend服务
docker-compose restart backend

# 2. 检查启动日志
docker-compose logs -f backend

# 3. 验证服务启动
sleep 10
curl http://localhost:8000/health
```

**方案 B：代码错误**

```bash
# 1. 检查Python语法错误
docker exec chaiguanjia_backend python -m py_compile /app/main.py

# 2. 查看详细错误日志
docker-compose logs backend | grep -i error

# 3. 进入容器调试
docker exec -it chaiguanjia_backend bash
python -c "import app.main"
```

**方案 C：依赖问题**

```bash
# 1. 检查Python包安装
docker exec chaiguanjia_backend pip list

# 2. 重新安装依赖
docker exec chaiguanjia_backend pip install -r requirements.txt

# 3. 重新构建镜像
docker-compose up -d --build backend
```

### 问题 6：API 响应慢

#### 症状

```
API响应时间超过2秒
频繁超时错误
```

#### 诊断步骤

```bash
# 1. 测试API响应时间
time curl http://localhost:8000/health

# 2. 检查数据库连接
docker exec chaiguanjia_backend python -c "
import psycopg2
conn = psycopg2.connect('**************************************************/chaiguanjia')
print('DB connection OK')
"

# 3. 监控系统资源
docker stats chaiguanjia_backend --no-stream
```

#### 解决方案

**方案 A：数据库优化**

```python
# 1. 启用连接池
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30
)

# 2. 添加查询缓存
from functools import lru_cache

@lru_cache(maxsize=100)
def get_user_by_id(user_id):
    return db.query(User).filter(User.id == user_id).first()
```

**方案 B：增加资源**

```yaml
# docker-compose.yml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

---

## 前端服务问题

### 问题 7：前端页面加载失败

#### 症状

```
浏览器显示"This site can't be reached"
或者页面空白
```

#### 诊断步骤

```bash
# 1. 检查Frontend容器状态
docker-compose ps frontend

# 2. 查看Frontend日志
docker-compose logs frontend

# 3. 测试端口访问
curl http://localhost:3000

# 4. 检查Nginx配置
docker exec chaiguanjia_nginx nginx -t
```

#### 解决方案

**方案 A：重新构建**

```bash
# 1. 停止前端服务
docker-compose stop frontend

# 2. 重新构建
docker-compose build frontend

# 3. 启动服务
docker-compose up -d frontend

# 4. 验证访问
curl -I http://localhost:3000
```

**方案 B：清理缓存**

```bash
# 1. 进入前端容器
docker exec -it chaiguanjia_frontend bash

# 2. 清理node_modules
rm -rf node_modules package-lock.json

# 3. 重新安装依赖
npm install

# 4. 重新启动
npm start
```

### 问题 8：前端 API 调用失败

#### 症状

```javascript
// 浏览器控制台显示
CORS error: Access to fetch at 'http://localhost:8000/api'
from origin 'http://localhost:3000' has been blocked
```

#### 解决方案

**方案 A：配置 CORS**

```python
# backend/app/main.py
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

**方案 B：配置代理**

```javascript
// frontend/package.json
{
  "proxy": "http://localhost:8000"
}
```

---

## 缓存服务问题

### 问题 9：Redis 连接超时

#### 症状

```
redis.exceptions.ConnectionError: Error connecting to Redis
```

#### 诊断步骤

```bash
# 1. 检查Redis容器状态
docker-compose ps redis

# 2. 测试Redis连接
docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 ping

# 3. 查看Redis日志
docker-compose logs redis

# 4. 检查Redis配置
docker exec chaiguanjia_redis cat /usr/local/etc/redis/redis.conf
```

#### 解决方案

**方案 A：重启 Redis**

```bash
# 1. 重启Redis服务
docker-compose restart redis

# 2. 等待服务启动
sleep 10

# 3. 验证连接
docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 ping
```

**方案 B：检查内存使用**

```bash
# 1. 查看Redis内存使用
docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 info memory

# 2. 清理不必要的键
docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 flushdb

# 3. 设置内存限制
docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 config set maxmemory 256mb
```

---

## 消息队列问题

### 问题 10：RabbitMQ 连接失败

#### 症状

```
pika.exceptions.AMQPConnectionError: Connection to localhost:5672 failed
```

#### 诊断步骤

```bash
# 1. 检查RabbitMQ状态
docker-compose ps rabbitmq

# 2. 检查RabbitMQ健康状态
docker exec chaiguanjia_rabbitmq rabbitmqctl node_health_check

# 3. 查看管理界面
curl http://localhost:15672

# 4. 检查用户权限
docker exec chaiguanjia_rabbitmq rabbitmqctl list_users
```

#### 解决方案

**方案 A：重置用户权限**

```bash
# 1. 进入RabbitMQ容器
docker exec -it chaiguanjia_rabbitmq bash

# 2. 添加用户和权限
rabbitmqctl add_user admin chaiguanjia2024
rabbitmqctl set_user_tags admin administrator
rabbitmqctl set_permissions -p chaiguanjia admin ".*" ".*" ".*"

# 3. 验证连接
rabbitmqctl list_connections
```

**方案 B：清理队列**

```bash
# 1. 查看队列状态
docker exec chaiguanjia_rabbitmq rabbitmqctl list_queues

# 2. 清理堆积消息
docker exec chaiguanjia_rabbitmq rabbitmqctl purge_queue queue_name

# 3. 重启服务
docker-compose restart rabbitmq
```

---

## 搜索引擎问题

### 问题 11：Elasticsearch 启动失败

#### 症状

```
Elasticsearch max virtual memory areas vm.max_map_count [65530]
is too low, increase to at least [262144]
```

#### 解决方案

**方案 A：调整系统参数**

```bash
# 1. 临时调整（重启后失效）
sudo sysctl -w vm.max_map_count=262144

# 2. 永久调整
echo 'vm.max_map_count=262144' | sudo tee -a /etc/sysctl.conf

# 3. 重启Elasticsearch
docker-compose restart elasticsearch

# 4. 验证启动
curl http://localhost:9200/_cluster/health
```

**方案 B：调整内存配置**

```yaml
# docker-compose.yml
services:
  elasticsearch:
    environment:
      - 'ES_JAVA_OPTS=-Xms1g -Xmx1g'
    ulimits:
      memlock:
        soft: -1
        hard: -1
```

---

## 网络连接问题

### 问题 12：容器间网络不通

#### 症状

```
容器A无法连接到容器B
getaddrinfo ENOTFOUND postgresql
```

#### 诊断步骤

```bash
# 1. 检查Docker网络
docker network ls

# 2. 检查网络详情
docker network inspect chaiguanjia8_10_chaiguanjia_network

# 3. 测试容器间连通性
docker exec chaiguanjia_backend ping postgresql

# 4. 检查DNS解析
docker exec chaiguanjia_backend nslookup postgresql
```

#### 解决方案

**方案 A：重建网络**

```bash
# 1. 停止所有服务
docker-compose down

# 2. 删除网络
docker network rm chaiguanjia8_10_chaiguanjia_network

# 3. 重新启动
docker-compose up -d

# 4. 验证网络
docker network inspect chaiguanjia8_10_chaiguanjia_network
```

**方案 B：指定网络别名**

```yaml
# docker-compose.yml
services:
  postgresql:
    networks:
      chaiguanjia_network:
        aliases:
          - db
          - database
```

---

## 性能问题

### 问题 13：系统运行缓慢

#### 诊断步骤

```bash
# 1. 检查系统负载
uptime

# 2. 查看CPU使用
top -p $(docker inspect chaiguanjia_backend --format '{{.State.Pid}}')

# 3. 检查内存使用
free -h

# 4. 查看磁盘IO
iostat -x 1 5

# 5. 运行性能测试
./scripts/monitoring/performance-benchmark.sh
```

#### 解决方案

**方案 A：优化资源配置**

```yaml
# docker-compose.yml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
    environment:
      - WORKERS=4
```

**方案 B：数据库优化**

```sql
-- 1. 更新表统计信息
ANALYZE;

-- 2. 重建索引
REINDEX DATABASE chaiguanjia;

-- 3. 清理无用数据
VACUUM FULL;
```

---

## 资源不足问题

### 问题 14：磁盘空间不足

#### 症状

```
No space left on device
docker: Error response from daemon: mkdir: no space left on device
```

#### 解决方案

**方案 A：清理 Docker 资源**

```bash
# 1. 清理未使用的容器
docker container prune -f

# 2. 清理未使用的镜像
docker image prune -a -f

# 3. 清理未使用的数据卷
docker volume prune -f

# 4. 清理构建缓存
docker builder prune -a -f

# 5. 系统清理
docker system prune -a -f --volumes
```

**方案 B：清理日志文件**

```bash
# 1. 清理Docker日志
sudo sh -c "truncate -s 0 /var/lib/docker/containers/*/*-json.log"

# 2. 清理系统日志
sudo journalctl --vacuum-time=7d

# 3. 清理应用日志
find ./logs -name "*.log" -mtime +7 -delete
```

### 问题 15：内存不足

#### 症状

```
Out of memory: Kill process
Container killed due to memory limit
```

#### 解决方案

**方案 A：调整内存限制**

```yaml
# docker-compose.yml
services:
  postgresql:
    mem_limit: 2g
  elasticsearch:
    mem_limit: 2g
    environment:
      - 'ES_JAVA_OPTS=-Xms1g -Xmx1g'
```

**方案 B：优化应用**

```python
# 优化数据库查询
from sqlalchemy import event
from sqlalchemy.pool import Pool

@event.listens_for(Pool, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    if 'postgresql' in str(dbapi_connection):
        with dbapi_connection.cursor() as cursor:
            cursor.execute("SET work_mem = '256MB'")
```

---

## 紧急处理预案

### 服务完全不可用

#### 紧急恢复步骤

```bash
# 1. 停止所有服务
docker-compose down

# 2. 检查系统状态
df -h
free -h
docker system df

# 3. 清理资源（如果空间不足）
docker system prune -f

# 4. 从备份恢复（如果有）
./scripts/maintenance/restore-backup.sh

# 5. 重新启动服务
docker-compose up -d

# 6. 验证服务状态
./scripts/monitoring/environment-validation.sh
```

### 数据丢失紧急恢复

```bash
# 1. 立即停止服务
docker-compose stop

# 2. 检查数据卷状态
docker volume ls | grep chaiguanjia

# 3. 从最近备份恢复
./scripts/maintenance/restore-database.sh backup_20240101.sql

# 4. 验证数据完整性
docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c "SELECT COUNT(*) FROM users;"

# 5. 重启服务
docker-compose up -d
```

### 安全事件响应

```bash
# 1. 立即隔离服务
docker-compose down
iptables -A INPUT -p tcp --dport 80 -j DROP

# 2. 检查入侵痕迹
docker-compose logs | grep -i "attack\|hack\|injection"

# 3. 更改所有密码
./scripts/security/change-all-passwords.sh

# 4. 更新安全配置
./scripts/security/harden-security.sh

# 5. 重新部署
docker-compose up -d
```

---

## 联系方式和升级流程

### 问题升级流程

```mermaid
flowchart TD
    A[发现问题] --> B[自助排查]
    B --> C{问题解决?}
    C -->|是| D[记录解决方案]
    C -->|否| E[联系技术支持]
    E --> F[提供详细信息]
    F --> G[专家远程协助]
    G --> H{问题解决?}
    H -->|是| I[更新文档]
    H -->|否| J[现场支持]

    style A fill:#ffcdd2
    style D fill:#c8e6c9
    style I fill:#c8e6c9
    style J fill:#fff3e0
```

### 技术支持联系方式

- **紧急故障热线**: 400-xxx-xxxx
- **技术支持邮箱**: <EMAIL>
- **企业微信群**: 柴管家技术支持群
- **问题追踪系统**: http://jira.chaiguanjia.com

### 问题报告模板

```markdown
## 问题描述

[详细描述遇到的问题]

## 环境信息

- 操作系统:
- Docker 版本:
- 项目版本:

## 复现步骤

1.
2.
3.

## 期望结果

[描述期望的正常行为]

## 实际结果

[描述实际发生的情况]

## 错误日志
```

[粘贴相关错误日志]

```

## 已尝试的解决方案
[列出已经尝试过的解决方法]
```

---

_文档版本：v1.0_ _最后更新：2024 年 1 月_ _维护团队：技术架构组_
