# 柴管家开发环境配置指南

## 文档概述

本文档详细介绍了柴管家项目的开发环境配置，包括 VS Code 开发工具、代码格式化工具、Git 工作流配置和
pre-commit 钩子设置。通过标准化的开发环境配置，确保所有开发人员使用统一的开发规范和工作流程。

## 目录

1. [快速开始](#快速开始)
2. [VS Code 开发环境](#vs-code-开发环境)
3. [Python 开发工具](#python-开发工具)
4. [前端开发工具](#前端开发工具)
5. [Git 工作流配置](#git-工作流配置)
6. [代码质量保证](#代码质量保证)
7. [故障排除](#故障排除)

---

## 快速开始

### 30 分钟完成环境配置

新加入团队的开发者可以通过以下步骤快速配置完整的开发环境：

```mermaid
flowchart TD
    A[克隆项目] --> B[安装基础工具]
    B --> C[配置VS Code]
    C --> D[安装项目依赖]
    D --> E[配置Git钩子]
    E --> F[验证环境]

    B1[Docker & Docker Compose]
    B2[VS Code]
    B3[Python 3.11+]
    B4[Node.js 18+]
    B5[Git]

    B --> B1
    B --> B2
    B --> B3
    B --> B4
    B --> B5

    style A fill:#e3f2fd
    style F fill:#c8e6c9
```

#### 1. 克隆项目

```bash
git clone <repository-url>
cd chaiguanjia8_10
```

#### 2. 一键环境配置

```bash
# 运行自动配置脚本
./scripts/setup/setup-dev-environment.sh

# 或者手动配置
./scripts/git/setup-git-hooks.sh
./scripts/setup/install-pre-commit.sh
```

#### 3. 启动开发环境

```bash
# 启动容器化环境
./start-dev.sh

# 验证环境
./scripts/docker/test-deployment.sh
```

---

## VS Code 开发环境

### 配置文件说明

项目已包含完整的 VS Code 工作区配置，位于`.vscode/`目录：

| 文件              | 功能       | 自动配置                |
| ----------------- | ---------- | ----------------------- |
| `settings.json`   | 工作区设置 | ✅ 代码格式化、插件配置 |
| `extensions.json` | 推荐插件   | ✅ 自动提示安装         |
| `launch.json`     | 调试配置   | ✅ Python/Node.js 调试  |
| `tasks.json`      | 任务配置   | ✅ 构建、测试、格式化   |

### 推荐插件列表

#### 必备插件（自动安装）

**Python 开发:**

- Python (ms-python.python)
- Black Formatter (ms-python.black-formatter)
- Flake8 (ms-python.flake8)
- MyPy Type Checker (ms-python.mypy-type-checker)
- isort (ms-python.isort)

**前端开发:**

- Prettier (esbenp.prettier-vscode)
- ESLint (dbaeumer.vscode-eslint)
- TypeScript Importer (pmneo.tsimporter)
- Auto Rename Tag (formulahendry.auto-rename-tag)

**容器化和 DevOps:**

- Docker (ms-azuretools.vscode-docker)
- Remote Containers (ms-vscode-remote.remote-containers)
- YAML (redhat.vscode-yaml)

**Git 和版本控制:**

- GitLens (eamodio.gitlens)
- Git Graph (mhutchie.git-graph)
- GitHub Pull Requests (github.vscode-pull-request-github)

#### 配置验证

打开 VS Code 后，会自动提示安装推荐插件：

```bash
# 验证插件安装
code --list-extensions | grep -E "(python|prettier|eslint|docker|gitlens)"
```

### 调试配置

#### Python 后端调试

```json
{
  "name": "Python: FastAPI Backend",
  "type": "python",
  "request": "launch",
  "module": "uvicorn",
  "args": ["app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
  "cwd": "${workspaceFolder}/backend"
}
```

**使用方法:**

1. 在 Python 代码中设置断点
2. 按 `F5` 或点击调试按钮
3. 访问 `http://localhost:8000` 触发断点

#### 容器调试

```json
{
  "name": "Docker: Attach to Backend",
  "type": "python",
  "request": "attach",
  "connect": {
    "host": "localhost",
    "port": 5678
  }
}
```

### 快捷任务

按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (macOS) 打开命令面板：

| 任务       | 命令                     | 快捷键         |
| ---------- | ------------------------ | -------------- |
| 格式化代码 | `Format Document`        | `Shift+Alt+F`  |
| 组织导入   | `Organize Imports`       | `Shift+Alt+O`  |
| 运行测试   | `Python: Run Tests`      | `Ctrl+;`       |
| 启动调试   | `Debug: Start Debugging` | `F5`           |
| 构建任务   | `Tasks: Run Task`        | `Ctrl+Shift+P` |

---

## Python 开发工具

### 代码格式化配置

#### Black - 代码格式化

**配置文件:** `backend/pyproject.toml`

```toml
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
```

**使用方法:**

```bash
# 格式化所有Python文件
black backend/

# 检查格式（不修改）
black --check backend/

# VS Code 自动格式化：保存时自动运行
```

#### isort - 导入排序

**配置特点:**

- 兼容 Black 格式化
- 自动分组：标准库、第三方、本地模块
- 多行导入美化

```bash
# 排序导入
isort backend/

# 检查导入顺序
isort --check-only backend/
```

### 代码质量检查

#### Flake8 - 代码风格检查

**配置文件:** `backend/.flake8`

```ini
[flake8]
max-line-length = 88
ignore = E203, W503, E501
select = E,W,F,C,N,B
```

**检查项目:**

- PEP 8 代码风格
- 语法错误检查
- 复杂度分析
- 命名规范检查

#### MyPy - 类型检查

**配置文件:** `backend/mypy.ini`

**特性:**

- 严格类型检查
- 第三方库类型忽略
- 测试文件宽松配置

```bash
# 运行类型检查
mypy backend/

# 检查特定文件
mypy backend/app/main.py
```

#### Bandit - 安全检查

```bash
# 安全漏洞扫描
bandit -r backend/ -f json -o bandit-report.json
```

### 测试框架配置

#### Pytest 配置

**配置文件:** `backend/pyproject.toml`

```toml
[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = [
    "--cov=app",
    "--cov-report=html",
    "--cov-fail-under=80"
]
```

**运行测试:**

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_api/

# 生成覆盖率报告
pytest --cov=app --cov-report=html
```

---

## 前端开发工具

### Prettier - 代码格式化

**配置文件:** `frontend/.prettierrc`

```json
{
  "printWidth": 80,
  "tabWidth": 2,
  "singleQuote": true,
  "trailingComma": "es5",
  "semi": true,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

### ESLint - 代码检查

**配置文件:** `frontend/.eslintrc.json`

**检查规则:**

- TypeScript 类型检查
- React 最佳实践
- 可访问性检查
- 导入顺序规范

```bash
# 运行ESLint检查
cd frontend
npx eslint src/

# 自动修复问题
npx eslint src/ --fix
```

### TypeScript 配置

**特性:**

- 严格类型检查
- 路径映射配置
- 增量编译支持

```bash
# 类型检查
npx tsc --noEmit

# 构建项目
npm run build
```

---

## Git 工作流配置

### 分支策略

```mermaid
gitgraph
    commit id: "初始提交"
    branch develop
    checkout develop
    commit id: "开发分支"

    branch feature/user-auth
    checkout feature/user-auth
    commit id: "添加用户认证"
    commit id: "完善登录功能"

    checkout develop
    merge feature/user-auth
    commit id: "合并认证功能"

    branch release/v1.0
    checkout release/v1.0
    commit id: "准备发布"

    checkout main
    merge release/v1.0
    commit id: "发布 v1.0"
    tag: "v1.0.0"
```

#### 分支命名规范

| 分支类型   | 命名格式            | 示例                    | 用途         |
| ---------- | ------------------- | ----------------------- | ------------ |
| 主分支     | `main`              | `main`                  | 生产环境代码 |
| 开发分支   | `develop`           | `develop`               | 集成开发代码 |
| 功能分支   | `feature/<功能名>`  | `feature/user-auth`     | 新功能开发   |
| 修复分支   | `fix/<问题描述>`    | `fix/login-bug`         | Bug 修复     |
| 发布分支   | `release/<版本号>`  | `release/v1.0`          | 版本发布准备 |
| 热修复分支 | `hotfix/<问题描述>` | `hotfix/security-patch` | 紧急修复     |

### 提交信息规范

#### 提交格式

```
<类型>(<范围>): <描述>

<详细描述>

<底部信息>
```

#### 类型说明

| 类型       | 说明     | 示例                            |
| ---------- | -------- | ------------------------------- |
| `feat`     | 新功能   | `feat(auth): 添加JWT认证`       |
| `fix`      | 修复 bug | `fix(db): 修复查询性能问题`     |
| `docs`     | 文档更新 | `docs(api): 更新接口文档`       |
| `style`    | 代码格式 | `style: 修复代码格式`           |
| `refactor` | 重构     | `refactor(utils): 优化工具函数` |
| `test`     | 测试     | `test(auth): 添加登录测试`      |
| `chore`    | 构建工具 | `chore: 更新依赖版本`           |

#### 配置 Git 提交模板

```bash
# 配置提交模板
git config commit.template .gitmessage

# 配置Git别名
git config alias.st status
git config alias.co checkout
git config alias.br branch
git config alias.ci commit
```

### Git 钩子配置

运行 Git 钩子配置脚本：

```bash
./scripts/git/setup-git-hooks.sh
```

**自动配置:**

- 提交信息模板
- Git 别名
- pre-commit 钩子
- commit-msg 钩子
- 分支策略

---

## 代码质量保证

### Pre-commit 钩子

#### 安装配置

```bash
# 自动安装和配置
./scripts/setup/install-pre-commit.sh

# 手动安装
pip install pre-commit
pre-commit install
```

#### 检查流程

```mermaid
flowchart TD
    A[git commit] --> B[pre-commit 触发]
    B --> C[文件格式检查]
    C --> D[代码格式化]
    D --> E[代码质量检查]
    E --> F[类型检查]
    F --> G[安全检查]
    G --> H{所有检查通过?}

    H -->|是| I[提交成功]
    H -->|否| J[修复问题]
    J --> K[重新提交]
    K --> B

    style A fill:#e3f2fd
    style I fill:#c8e6c9
    style J fill:#ffcdd2
```

#### 钩子列表

**Python 检查:**

- Black 代码格式化
- isort 导入排序
- Flake8 代码质量
- MyPy 类型检查
- Bandit 安全检查

**JavaScript/TypeScript 检查:**

- Prettier 格式化
- ESLint 代码检查
- TypeScript 类型检查

**通用检查:**

- 文件格式验证
- 大文件检查
- 密钥泄露检查
- 提交信息格式

#### 常用命令

```bash
# 运行所有检查
pre-commit run --all-files

# 运行特定检查
pre-commit run black
pre-commit run eslint

# 跳过检查提交
git commit --no-verify

# 更新钩子版本
pre-commit autoupdate
```

### 持续集成检查

#### GitHub Actions 工作流

```yaml
name: 代码质量检查
on: [push, pull_request]

jobs:
  quality-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: 运行 pre-commit
        uses: pre-commit/action@v3.0.0
```

#### 本地 CI 模拟

```bash
# 模拟CI环境检查
./scripts/ci/run-local-ci.sh
```

---

## 故障排除

### 常见问题

#### 1. VS Code 插件未自动安装

**问题现象:**

- 打开项目后没有插件安装提示
- 代码格式化不工作

**解决方案:**

```bash
# 手动安装推荐插件
code --install-extension ms-python.python
code --install-extension esbenp.prettier-vscode
code --install-extension dbaeumer.vscode-eslint

# 重新加载窗口
# Ctrl+Shift+P -> "Developer: Reload Window"
```

#### 2. Python 虚拟环境问题

**问题现象:**

- 导入错误
- 模块未找到
- 类型检查失败

**解决方案:**

```bash
# 重新创建虚拟环境
cd backend
rm -rf .venv
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements/development.txt

# 在VS Code中选择正确的Python解释器
# Ctrl+Shift+P -> "Python: Select Interpreter"
```

#### 3. Pre-commit 检查失败

**问题现象:**

- 提交被拒绝
- 格式化工具报错
- 类型检查失败

**解决方案:**

```bash
# 查看详细错误信息
pre-commit run --all-files --verbose

# 修复格式问题
black backend/
isort backend/
prettier --write frontend/src/

# 清理缓存重试
pre-commit clean
pre-commit install --install-hooks
```

#### 4. 前端依赖问题

**问题现象:**

- npm 安装失败
- ESLint 检查报错
- TypeScript 类型错误

**解决方案:**

```bash
cd frontend

# 清理node_modules和锁文件
rm -rf node_modules package-lock.json

# 使用国内镜像源重新安装
npm config set registry https://registry.npmmirror.com
npm install

# 验证安装
npm run lint
npm run type-check
```

#### 5. Git 钩子不生效

**问题现象:**

- 提交时没有触发检查
- 钩子脚本权限错误

**解决方案:**

```bash
# 重新安装钩子
pre-commit uninstall
pre-commit install
pre-commit install --hook-type commit-msg

# 检查钩子权限
ls -la .git/hooks/
chmod +x .git/hooks/pre-commit
chmod +x .git/hooks/commit-msg

# 验证钩子配置
pre-commit run --hook-stage manual --all-files
```

### 性能优化

#### VS Code 性能优化

```json
{
  "python.analysis.autoImportCompletions": true,
  "python.analysis.indexing": true,
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "extensions.autoUpdate": false,
  "telemetry.telemetryLevel": "off"
}
```

#### Pre-commit 缓存优化

```bash
# 预安装所有钩子
pre-commit install-hooks

# 启用并行检查
export PRE_COMMIT_COLOR=always
```

### 开发环境重置

如果环境出现严重问题，可以完全重置：

```bash
# 1. 清理所有配置
git clean -fdx
rm -rf backend/.venv
rm -rf frontend/node_modules

# 2. 重新配置环境
./scripts/git/setup-git-hooks.sh
./scripts/setup/install-pre-commit.sh

# 3. 重启VS Code
code .
```

---

## 最佳实践

### 开发工作流

1. **每日开发流程:**

   ```bash
   git pull origin develop
   git checkout -b feature/my-feature
   # 开发代码...
   git add .
   git commit  # 触发pre-commit检查
   git push origin feature/my-feature
   ```

2. **代码审查前:**

   ```bash
   pre-commit run --all-files
   npm test
   pytest
   ```

3. **提交最佳实践:**
   - 每次提交只包含相关变更
   - 提交信息清晰描述变更内容
   - 确保所有检查通过

### 团队协作

1. **新成员入门:**

   - 运行环境配置脚本
   - 阅读开发文档
   - 参与代码审查

2. **代码规范:**

   - 遵循自动格式化规则
   - 编写有意义的注释
   - 保持代码简洁可读

3. **问题处理:**
   - 及时更新依赖
   - 修复 lint 警告
   - 保持测试覆盖率

---

## 附录

### 配置文件清单

| 文件路径                  | 功能               | 说明                |
| ------------------------- | ------------------ | ------------------- |
| `.vscode/settings.json`   | VS Code 工作区设置 | 统一开发环境        |
| `.vscode/extensions.json` | 推荐插件列表       | 自动安装提示        |
| `.vscode/launch.json`     | 调试配置           | Python/Node.js 调试 |
| `.vscode/tasks.json`      | 任务配置           | 构建、测试任务      |
| `backend/pyproject.toml`  | Python 项目配置    | 格式化、测试配置    |
| `backend/.flake8`         | Flake8 配置        | 代码质量检查        |
| `backend/mypy.ini`        | MyPy 配置          | 类型检查            |
| `frontend/.prettierrc`    | Prettier 配置      | 前端格式化          |
| `frontend/.eslintrc.json` | ESLint 配置        | 前端代码检查        |
| `.pre-commit-config.yaml` | Pre-commit 配置    | 代码质量钩子        |
| `.gitignore`              | Git 忽略配置       | 版本控制排除        |
| `.gitmessage`             | 提交信息模板       | 标准化提交格式      |

### 快速命令参考

```bash
# 环境配置
./scripts/git/setup-git-hooks.sh           # 配置Git钩子
./scripts/setup/install-pre-commit.sh      # 安装pre-commit

# 代码检查
pre-commit run --all-files                 # 运行所有检查
black backend/                             # Python格式化
isort backend/                             # 导入排序
flake8 backend/                            # 代码质量检查
mypy backend/                              # 类型检查

# 前端工具
cd frontend && npm run lint                 # ESLint检查
cd frontend && npm run format               # Prettier格式化
cd frontend && npx tsc --noEmit            # TypeScript检查

# 测试运行
pytest                                      # Python测试
cd frontend && npm test                     # 前端测试

# Git操作
git lg                                      # 美化的提交历史
git sync                                    # 同步远程分支
git review                                  # 查看变更文件
```

---

_本文档版本: v1.0_ _最后更新: 2024 年 1 月_ _维护团队: 技术架构组_
