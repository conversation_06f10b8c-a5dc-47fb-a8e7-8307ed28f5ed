{
  "recommendations": [
    // Python 开发必备插件
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.flake8",
    "ms-python.mypy-type-checker",
    "ms-python.isort",
    "ms-toolsai.jupyter",
    "ms-python.pylint",

    // JavaScript/TypeScript/React 开发
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "steoates.autoimport-es6-ts",

    // Web 开发增强
    "formulahendry.auto-rename-tag",
    "ms-vscode.vscode-css-peek",
    "zignd.html-css-class-completion",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-html-preview",

    // Docker 和容器化
    "ms-azuretools.vscode-docker",
    "ms-vscode-remote.remote-containers",
    "ms-kubernetes-tools.vscode-kubernetes-tools",

    // 数据库
    "ms-mssql.mssql",
    "cweijan.vscode-postgresql-client2",
    "cweijan.vscode-redis-client",
    "bradymholt.pgformatter",

    // API 开发和测试
    "humao.rest-client",
    "42Crunch.vscode-openapi",
    "ms-vscode.vscode-swagger-viewer",
    "rangav.vscode-thunder-client",

    // YAML 和配置文件
    "redhat.vscode-yaml",
    "ms-vscode.makefile-tools",
    "mikestead.dotenv",
    "mrmlnc.vscode-duplicate",

    // Git 和版本控制
    "eamodio.gitlens",
    "mhutchie.git-graph",
    "github.vscode-pull-request-github",
    "github.copilot",
    "github.copilot-chat",

    // Markdown 文档
    "yzhang.markdown-all-in-one",
    "shd101wyy.markdown-preview-enhanced",
    "davidanson.vscode-markdownlint",
    "bierner.markdown-mermaid",

    // 代码质量和格式化
    "streetsidesoftware.code-spell-checker",
    "ms-vscode.wordcount",
    "oderwat.indent-rainbow",
    "mechatroner.rainbow-csv",
    "alefragnani.bookmarks",

    // 工作效率插件
    "ms-vscode.vscode-icons",
    "vscode-icons-team.vscode-icons",
    "pkief.material-icon-theme",
    "formulahendry.code-runner",
    "ms-vscode.vscode-multi-cursor",

    // 调试和测试
    "ms-vscode.test-adapter-converter",
    "hbenl.vscode-test-explorer",
    "littlefoxteam.vscode-python-test-adapter",
    "ms-vscode.vscode-coverage-gutters",

    // 服务器和云开发
    "ms-vscode-remote.remote-ssh",
    "ms-vscode-remote.remote-wsl",
    "ms-vscode.remote-explorer",
    "ms-azuretools.vscode-azureresourcegroups",

    // AI 和智能提示
    "ms-toolsai.vscode-ai",
    "visualstudioexptteam.vscodeintellicode",
    "tabnine.tabnine-vscode",

    // 项目管理
    "alefragnani.project-manager",
    "ms-vscode.vscode-todo-highlight",
    "gruntfuggly.todo-tree",
    "aaron-bond.better-comments",

    // 主题和外观
    "github.github-vscode-theme",
    "monokai.theme-monokai-pro-vscode",
    "dracula-theme.theme-dracula",
    "ms-vscode.theme-tomorrow-theme",

    // 实用工具
    "ms-vscode.hexeditor",
    "redhat.vscode-commons",
    "ms-vscode.vscode-node-azure-pack",
    "ms-vscode.vscode-json-schema-viewer"
  ],

  "unwantedRecommendations": [
    // 避免安装可能冲突的插件
    "ms-python.autopep8",
    "ms-python.yapf",
    "hookyqr.beautify",
    "vscode.typescript-language-features"
  ]
}
