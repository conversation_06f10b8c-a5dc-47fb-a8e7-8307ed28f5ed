{
  "version": "2.0.0",
  "tasks": [
    // Docker 相关任务
    {
      "label": "Start Docker Services",
      "type": "shell",
      "command": "./scripts/docker/start.sh",
      "args": ["dev"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": [],
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },

    {
      "label": "Stop Docker Services",
      "type": "shell",
      "command": "./scripts/docker/start.sh",
      "args": ["stop"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },

    {
      "label": "Docker Services Status",
      "type": "shell",
      "command": "./scripts/docker/start.sh",
      "args": ["status"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },

    // Python 后端任务
    {
      "label": "Python: Install Dependencies",
      "type": "shell",
      "command": "pip",
      "args": ["install", "-r", "requirements/development.txt"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "problemMatcher": []
    },

    {
      "label": "Python: Run Tests",
      "type": "shell",
      "command": "pytest",
      "args": ["-v", "--tb=short", "--cov=app", "--cov-report=html", "--cov-report=term"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/backend",
        "env": {
          "PYTHONPATH": "${workspaceFolder}/backend",
          "ENVIRONMENT": "testing"
        }
      },
      "problemMatcher": [
        {
          "owner": "python",
          "fileLocation": ["relative", "${workspaceFolder}/backend"],
          "pattern": {
            "regexp": "^(.*):(\\d+):\\s+(.*)",
            "file": 1,
            "line": 2,
            "message": 3
          }
        }
      ]
    },

    {
      "label": "Python: Format Code (Black)",
      "type": "shell",
      "command": "black",
      "args": [".", "--line-length=88"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "silent",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "problemMatcher": []
    },

    {
      "label": "Python: Sort Imports (isort)",
      "type": "shell",
      "command": "isort",
      "args": [".", "--profile=black"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "silent",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "problemMatcher": []
    },

    {
      "label": "Python: Lint Code (Flake8)",
      "type": "shell",
      "command": "flake8",
      "args": [".", "--max-line-length=88", "--ignore=E203,W503"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "problemMatcher": [
        {
          "owner": "flake8",
          "fileLocation": ["relative", "${workspaceFolder}/backend"],
          "pattern": {
            "regexp": "^(.*):(\\d+):(\\d+):\\s+([EWF]\\d+)\\s+(.*)$",
            "file": 1,
            "line": 2,
            "column": 3,
            "code": 4,
            "message": 5
          }
        }
      ]
    },

    {
      "label": "Python: Type Check (MyPy)",
      "type": "shell",
      "command": "mypy",
      "args": [".", "--ignore-missing-imports"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "problemMatcher": [
        {
          "owner": "mypy",
          "fileLocation": ["relative", "${workspaceFolder}/backend"],
          "pattern": {
            "regexp": "^(.*):(\\d+):\\s+(error|warning|note):\\s+(.*)$",
            "file": 1,
            "line": 2,
            "severity": 3,
            "message": 4
          }
        }
      ]
    },

    // 数据库任务
    {
      "label": "Database: Run Migrations",
      "type": "shell",
      "command": "alembic",
      "args": ["upgrade", "head"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/backend",
        "env": {
          "PYTHONPATH": "${workspaceFolder}/backend"
        }
      },
      "problemMatcher": []
    },

    {
      "label": "Database: Create Migration",
      "type": "shell",
      "command": "alembic",
      "args": ["revision", "--autogenerate", "-m", "${input:migrationMessage}"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/backend",
        "env": {
          "PYTHONPATH": "${workspaceFolder}/backend"
        }
      },
      "problemMatcher": []
    },

    // 前端任务
    {
      "label": "Frontend: Install Dependencies",
      "type": "shell",
      "command": "npm",
      "args": ["install"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "problemMatcher": ["$eslint-stylish"]
    },

    {
      "label": "Frontend: Start Dev Server",
      "type": "shell",
      "command": "npm",
      "args": ["start"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "isBackground": true,
      "problemMatcher": {
        "owner": "webpack",
        "pattern": {
          "regexp": "ERROR in (.*)",
          "file": 1
        },
        "background": {
          "activeOnStart": true,
          "beginsPattern": "webpack building...",
          "endsPattern": "webpack built"
        }
      }
    },

    {
      "label": "Frontend: Build Production",
      "type": "shell",
      "command": "npm",
      "args": ["run", "build"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "problemMatcher": ["$eslint-stylish"]
    },

    {
      "label": "Frontend: Run Tests",
      "type": "shell",
      "command": "npm",
      "args": ["test", "--", "--coverage", "--watchAll=false"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "problemMatcher": ["$eslint-stylish"]
    },

    // 代码质量任务
    {
      "label": "Code Quality: Full Check",
      "dependsOrder": "sequence",
      "dependsOn": [
        "Python: Format Code (Black)",
        "Python: Sort Imports (isort)",
        "Python: Lint Code (Flake8)",
        "Python: Type Check (MyPy)"
      ],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    },

    // 部署任务
    {
      "label": "Deploy: Run Tests",
      "type": "shell",
      "command": "./scripts/docker/test-deployment.sh",
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    }
  ],

  // 输入变量定义
  "inputs": [
    {
      "id": "migrationMessage",
      "description": "Migration message",
      "default": "Auto migration",
      "type": "promptString"
    }
  ]
}
