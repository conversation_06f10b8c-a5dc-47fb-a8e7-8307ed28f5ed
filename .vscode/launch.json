{
  "version": "0.2.0",
  "configurations": [
    // Python 后端调试配置
    {
      "name": "Python: FastAPI Backend",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/backend/app/main.py",
      "module": "uvicorn",
      "args": ["app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/backend",
        "ENVIRONMENT": "development",
        "DEBUG": "true"
      },
      "console": "integratedTerminal",
      "python": "${workspaceFolder}/backend/.venv/bin/python",
      "justMyCode": false,
      "django": false,
      "autoReload": {
        "enable": true
      }
    },

    // Python 测试调试
    {
      "name": "Python: Test Current File",
      "type": "python",
      "request": "launch",
      "module": "pytest",
      "args": ["${file}", "-v", "--tb=short"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/backend",
        "ENVIRONMENT": "testing"
      },
      "console": "integratedTerminal",
      "python": "${workspaceFolder}/backend/.venv/bin/python",
      "justMyCode": false
    },

    // Celery Worker 调试
    {
      "name": "Python: Celery Worker",
      "type": "python",
      "request": "launch",
      "module": "celery",
      "args": ["-A", "app.tasks", "worker", "--loglevel=debug", "--concurrency=1"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/backend",
        "ENVIRONMENT": "development"
      },
      "console": "integratedTerminal",
      "python": "${workspaceFolder}/backend/.venv/bin/python"
    },

    // 数据库迁移调试
    {
      "name": "Python: Alembic Migration",
      "type": "python",
      "request": "launch",
      "module": "alembic",
      "args": ["upgrade", "head"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/backend",
        "ENVIRONMENT": "development"
      },
      "console": "integratedTerminal",
      "python": "${workspaceFolder}/backend/.venv/bin/python"
    },

    // Docker Compose 调试配置
    {
      "name": "Docker: Attach to Backend",
      "type": "python",
      "request": "attach",
      "connect": {
        "host": "localhost",
        "port": 5678
      },
      "pathMappings": [
        {
          "localRoot": "${workspaceFolder}/backend",
          "remoteRoot": "/app"
        }
      ],
      "justMyCode": false
    },

    // Node.js 前端调试（如果需要）
    {
      "name": "Node: Launch Frontend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/frontend/node_modules/.bin/react-scripts",
      "args": ["start"],
      "cwd": "${workspaceFolder}/frontend",
      "env": {
        "NODE_ENV": "development",
        "BROWSER": "none"
      },
      "console": "integratedTerminal",
      "runtimeExecutable": "npm",
      "runtimeArgs": ["start"]
    }
  ],

  "compounds": [
    // 组合启动配置
    {
      "name": "Launch Full Stack",
      "configurations": ["Python: FastAPI Backend", "Node: Launch Frontend"],
      "stopAll": true,
      "preLaunchTask": "Start Docker Services"
    }
  ]
}
