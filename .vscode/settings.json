{
  // 柴管家项目 VS Code 工作区配置
  // 确保所有开发者使用统一的开发环境设置

  // =================================
  // Python 配置
  // =================================
  "python.defaultInterpreter": "./backend/.venv/bin/python",
  "python.pythonPath": "./backend/.venv/bin/python",
  "python.terminal.activateEnvironment": true,
  "python.terminal.activateEnvInCurrentTerminal": true,

  // Python 代码格式化
  "python.formatting.provider": "black",
  "python.formatting.blackArgs": ["--line-length=88", "--target-version=py311"],

  // Python 代码检查
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": true,
  "python.linting.mypyEnabled": true,
  "python.linting.banditEnabled": true,

  // Flake8 配置
  "python.linting.flake8Args": ["--max-line-length=88", "--ignore=E203,W503,E501", "--select=E,W,F,C,N"],

  // MyPy 配置
  "python.linting.mypyArgs": [
    "--ignore-missing-imports",
    "--follow-imports=silent",
    "--show-column-numbers",
    "--strict-optional"
  ],

  // Python 代码排序
  "python.sortImports.args": [
    "--profile=black",
    "--multi-line=3",
    "--trailing-comma",
    "--force-grid-wrap=0",
    "--combine-as",
    "--line-width=88"
  ],

  // =================================
  // JavaScript/TypeScript 配置
  // =================================
  "typescript.preferences.quoteStyle": "single",
  "javascript.preferences.quoteStyle": "single",
  "typescript.suggest.autoImports": true,
  "javascript.suggest.autoImports": true,

  // Prettier 格式化配置
  "prettier.configPath": "./frontend/.prettierrc",
  "prettier.singleQuote": true,
  "prettier.trailingComma": "es5",
  "prettier.tabWidth": 2,
  "prettier.semi": true,
  "prettier.printWidth": 80,

  // =================================
  // 编辑器通用配置
  // =================================
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": false,
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit",
    "source.fixAll": "explicit",
    "source.fixAll.eslint": "explicit"
  },

  // 缩进和空白字符
  "editor.insertSpaces": true,
  "editor.tabSize": 4,
  "editor.detectIndentation": false,
  "editor.trimAutoWhitespace": true,
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,

  // 行尾字符
  "files.eol": "\n",

  // =================================
  // 文件类型关联
  // =================================
  "files.associations": {
    "*.json": "json",
    "*.yml": "yaml",
    "*.yaml": "yaml",
    "Dockerfile*": "dockerfile",
    "docker-compose*.yml": "yaml",
    ".env*": "properties",
    "*.md": "markdown"
  },

  // =================================
  // 语言特定配置
  // =================================
  "[python]": {
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "ms-python.black-formatter",
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    }
  },

  "[javascript]": {
    "editor.tabSize": 2,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  "[typescript]": {
    "editor.tabSize": 2,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  "[typescriptreact]": {
    "editor.tabSize": 2,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  "[json]": {
    "editor.tabSize": 2,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  "[yaml]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "redhat.vscode-yaml"
  },

  "[markdown]": {
    "editor.tabSize": 2,
    "editor.wordWrap": "on",
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  "[sql]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true
  },

  // =================================
  // 搜索和文件排除
  // =================================
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/.venv": true,
    "**/__pycache__": true,
    "**/*.pyc": true,
    "**/coverage": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/logs": true,
    "**/tmp": true,
    "**/*.log": true
  },

  "files.exclude": {
    "**/.git": true,
    "**/.DS_Store": true,
    "**/node_modules": true,
    "**/__pycache__": true,
    "**/*.pyc": true,
    "**/coverage": true,
    "**/.pytest_cache": true,
    "**/.mypy_cache": true
  },

  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/.venv/**": true,
    "**/logs/**": true,
    "**/tmp/**": true
  },

  // =================================
  // 终端配置
  // =================================
  "terminal.integrated.defaultProfile.osx": "zsh",
  "terminal.integrated.defaultProfile.linux": "bash",
  "terminal.integrated.env.osx": {
    "PYTHONPATH": "${workspaceFolder}/backend"
  },
  "terminal.integrated.env.linux": {
    "PYTHONPATH": "${workspaceFolder}/backend"
  },

  // =================================
  // Git 配置
  // =================================
  "git.autofetch": true,
  "git.confirmSync": false,
  "git.enableSmartCommit": true,
  "git.postCommitCommand": "none",
  "git.showPushSuccessNotification": true,

  // =================================
  // 扩展特定配置
  // =================================

  // Docker 扩展
  "docker.showStartPage": false,

  // YAML 扩展
  "yaml.schemas": {
    "https://json.schemastore.org/docker-compose.json": ["docker-compose*.yml", "docker-compose*.yaml"],
    "https://json.schemastore.org/github-workflow.json": [".github/workflows/*.yml", ".github/workflows/*.yaml"]
  },

  // REST Client 扩展
  "rest-client.environmentVariables": {
    "local": {
      "baseUrl": "http://localhost:8000",
      "apiVersion": "v1"
    },
    "dev": {
      "baseUrl": "http://dev.chaiguanjia.com",
      "apiVersion": "v1"
    }
  },

  // 自动保存
  "files.autoSave": "afterDelay",
  "files.autoSaveDelay": 1000,

  // =================================
  // 工作区特定配置
  // =================================
  "workbench.colorTheme": "Quiet Light",
  "workbench.iconTheme": "vscode-icons",
  "workbench.editor.enablePreview": false,
  "workbench.editor.enablePreviewFromQuickOpen": false,

  // 小地图
  "editor.minimap.enabled": true,
  "editor.minimap.maxColumn": 80,

  // 括号配对
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,

  // 代码提示
  "editor.suggestSelection": "first",
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": false
  },

  // =================================
  // 调试配置
  // =================================
  "debug.allowBreakpointsEverywhere": true,
  "debug.showInStatusBar": "always",

  // =================================
  // 性能优化
  // =================================
  "extensions.autoUpdate": false,
  "telemetry.telemetryLevel": "off",
  "update.showReleaseNotes": false,

  // =================================
  // 中文支持
  // =================================
  "files.encoding": "utf8",
  "files.autoGuessEncoding": true
}
