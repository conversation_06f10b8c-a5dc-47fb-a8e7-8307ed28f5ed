# 柴管家应用环境变量配置示例

# 应用基础配置
APP_NAME=柴管家API
APP_DESCRIPTION=多平台聚合智能客服系统后端API
APP_VERSION=1.0.0
DEBUG=true

# 服务器配置
HOST=0.0.0.0
PORT=8000

# API配置
API_V1_PREFIX=/api/v1
API_V2_PREFIX=/api/v2
DOCS_URL=/docs
REDOC_URL=/redoc

# CORS配置
CORS_ORIGINS=*
CORS_CREDENTIALS=true
CORS_METHODS=*
CORS_HEADERS=*

# 数据库配置
DATABASE_URL=postgresql://admin:password@localhost:5432/chaiguanjia
DATABASE_POOL_SIZE=10
DATABASE_POOL_OVERFLOW=20

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_DB=0
REDIS_PASSWORD=

# 消息队列配置
RABBITMQ_URL=amqp://admin:password@localhost:5672/

# 安全配置
SECRET_KEY=your-secret-key-change-in-production-please

# JWT认证配置
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-min-32-chars
JWT_ALGORITHM=HS256
JWT_REFRESH_SECRET_KEY=your-refresh-secret-key-change-in-production-min-32-chars
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=120
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
JWT_ISSUER=chaiguanjia
JWT_AUDIENCE=chaiguanjia-users
JWT_VERIFY_SIGNATURE=true
JWT_VERIFY_EXP=true
JWT_REQUIRE_EXP=true

# Authing配置
AUTHING_USER_POOL_ID=your_authing_user_pool_id
AUTHING_APP_ID=your_authing_app_id
AUTHING_APP_SECRET=your_authing_app_secret
AUTHING_APP_HOST=your_authing_app_host
AUTHING_PROTOCOL=https
AUTHING_API_BASE_URL=https://core.authing.cn
AUTHING_TIMEOUT=30
AUTHING_RETRY_TIMES=3
AUTHING_SYNC_ENABLED=true
AUTHING_SYNC_INTERVAL=3600

# 用户认证配置
PASSWORD_MIN_LENGTH=8
SESSION_TIMEOUT_MINUTES=480
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_MINUTES=30
EMAIL_VERIFICATION_REQUIRED=false
PHONE_VERIFICATION_REQUIRED=false

# 权限系统配置
RBAC_ENABLED=true
DEFAULT_USER_ROLE=user
PERMISSION_CACHE_TTL=3600

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=10
REDIS_RETRY_ON_TIMEOUT=true
REDIS_SOCKET_TIMEOUT=5
REDIS_CONNECTION_TIMEOUT=10

# 监控配置
HEALTH_MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=60
HEALTH_ALERT_THRESHOLD=3
HEALTH_HISTORY_RETENTION_HOURS=24

# 性能指标配置
METRICS_COLLECTION_ENABLED=true
METRICS_FLUSH_INTERVAL=60
METRICS_RETENTION_HOURS=24

# 告警配置
ALERT_CHANNELS=email,dingtalk
ALERT_RETENTION_DAYS=30
ALERT_RATE_LIMIT_WINDOW=300
MAX_ALERTS_PER_WINDOW=10

# 邮件告警配置
SMTP_SERVER=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
ALERT_FROM_EMAIL=<EMAIL>
ALERT_TO_EMAILS=<EMAIL>,<EMAIL>

# 钉钉告警配置
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=xxx
DINGTALK_SECRET=

# 企业微信告警配置
WECHAT_WORK_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx

# Webhook告警配置
ALERT_WEBHOOK_URL=
ALERT_WEBHOOK_HEADERS={}

# 安全日志配置
SECURITY_LOG_DIR=logs
THREAT_DETECTION_ENABLED=true
MAX_LOGIN_ATTEMPTS=5
SUSPICIOUS_IP_THRESHOLD=10

# 日志配置
LOG_LEVEL=info

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# API限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
