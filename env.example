# 柴管家项目环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# =================================
# 应用基础配置
# =================================
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-super-secret-key-change-this-in-production
LOG_LEVEL=DEBUG

# =================================
# 数据库配置 (PostgreSQL)
# =================================
POSTGRES_DB=chaiguanjia
POSTGRES_USER=admin
POSTGRES_PASSWORD=chaiguanjia2024
POSTGRES_HOST=postgresql
POSTGRES_PORT=5432

# =================================
# 缓存配置 (Redis)
# =================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=chaiguanjia2024
REDIS_DB=0

# =================================
# 消息队列配置 (RabbitMQ)
# =================================
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=chaiguanjia2024
RABBITMQ_VHOST=chaiguanjia

# =================================
# 搜索引擎配置 (Elasticsearch)
# =================================
ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=chaiguanjia2024

# =================================
# 服务端口配置
# =================================
BACKEND_PORT=8000
FRONTEND_PORT=3000
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# =================================
# 前端配置
# =================================
REACT_APP_API_URL=http://localhost:8000
REACT_APP_ENVIRONMENT=development
REACT_APP_DEBUG=true

# =================================
# Python 和 Node.js 版本
# =================================
PYTHON_VERSION=3.11
NODE_VERSION=18

# =================================
# JWT 配置
# =================================
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# =================================
# 邮件配置 (开发环境使用 MailHog)
# =================================
MAIL_SERVER=mailhog
MAIL_PORT=1025
MAIL_USE_TLS=false
MAIL_USE_SSL=false
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_FROM=<EMAIL>

# =================================
# 文件存储配置 (MinIO Object Storage)
# =================================
# MinIO 服务配置
MINIO_HOST=minio
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_ACCESS_KEY=chaiguanjia_admin
MINIO_SECRET_KEY=chaiguanjia2024_secret
MINIO_BUCKET_NAME=chaiguanjia-files
MINIO_SECURE=false
MINIO_REGION=us-east-1

# 文件上传限制
UPLOAD_FOLDER=/app/uploads
MAX_CONTENT_LENGTH=104857600  # 100MB
MAX_FILE_SIZE=104857600  # 100MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,bmp,webp,svg,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv,zip,rar,7z,mp4,mp3,avi,mov

# 图片处理配置
IMAGE_QUALITY=85
THUMBNAIL_SIZE=200,200
MAX_IMAGE_DIMENSION=2048

# 文件存储策略
FILE_RETENTION_DAYS=365
FILE_CLEANUP_SCHEDULE=0 3 * * *  # 每天凌晨3点清理

# =================================
# AI 服务配置
# =================================
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=2048

# =================================
# 监控配置
# =================================
SENTRY_DSN=
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# =================================
# 安全配置
# =================================
CORS_ORIGINS=http://localhost:3000,http://localhost:80
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CSRF_TRUSTED_ORIGINS=http://localhost:3000

# =================================
# 缓存配置
# =================================
CACHE_TYPE=redis
CACHE_DEFAULT_TIMEOUT=300
CACHE_KEY_PREFIX=chaiguanjia:

# =================================
# 限流配置
# =================================
RATELIMIT_STORAGE_URL=redis://:chaiguanjia2024@redis:6379/1
RATELIMIT_DEFAULT=100 per hour

# =================================
# 备份配置
# =================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/backups

# =================================
# SSL 证书配置 (生产环境)
# =================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# =================================
# 第三方平台 API 配置
# =================================
# 微信公众号
WECHAT_APP_ID=
WECHAT_APP_SECRET=
WECHAT_TOKEN=
WECHAT_ENCODING_AES_KEY=

# 钉钉
DINGTALK_APP_KEY=
DINGTALK_APP_SECRET=

# 企业微信
WORK_WECHAT_CORP_ID=
WORK_WECHAT_AGENT_ID=
WORK_WECHAT_SECRET=

# 飞书
FEISHU_APP_ID=
FEISHU_APP_SECRET=
