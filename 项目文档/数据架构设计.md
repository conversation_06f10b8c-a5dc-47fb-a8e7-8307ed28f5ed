# 数据架构设计

## 任务清单
- [x] 盘点多平台适配器/服务及其路径
- [x] 识别标准数据结构（消息、会话、用户/联系人、渠道）
- [x] 梳理 Webhook → Job → Service → Model 的转换链路
- [x] 提取关键代码片段（代码路径/类名）
- [x] 绘制数据流程图、类图、时序图（Mermaid）
- [x] 撰写技术决策、错误处理、性能与扩展性分析

---

## 一、项目概述与数据转换架构总览
Chatwoot 通过渠道（`Channel::*`）对接多平台（WhatsApp、Telegram、LINE、Twitter、Facebook/Instagram、Twilio/SMS、Email、WebWidget 等）。各平台入站数据通过 Webhook 控制器进入队列 Job，再由平台级“入站消息服务/构建器”转换为系统标准模型：`Contact/ContactInbox → Conversation → Message (+ Attachments)`。

- Webhook → Job → Service（适配器）→ Builder（可选）→ Model（标准结构）
- 去重与状态：使用 `Message.source_id` 以及 Redis 键保证去重与状态更新
- 统一消息形态：`Message.message_type`（incoming/outgoing/activity/template）、`content_type`（text/image/audio/...）与 `content_attributes`（扩展字段）

关键入口路由（节选）：
```460:506:config/routes.rb
# Routes for channel integrations
mount Facebook::Messenger::Server, at: 'bot'
post 'webhooks/telegram/:bot_token', to: 'webhooks/telegram#process_payload'
post 'webhooks/line/:line_channel_id', to: 'webhooks/line#process_payload'
post 'webhooks/sms/:phone_number', to: 'webhooks/sms#process_payload'
post 'webhooks/whatsapp/:phone_number', to: 'webhooks/whatsapp#process_payload'
post 'webhooks/instagram', to: 'webhooks/instagram#events'
```

---

## 二、标准数据结构定义与说明
- `Contact`（联系人）：访客/顾客实体，支持 email/phone/identifier 去重与校验。
- `ContactInbox`：某 `Contact` 在某个 `Inbox`（渠道实例）下的对应关系，包含平台 `source_id`。
- `Inbox`（渠道实例）：多态关联 `Channel::*`（如 `Channel::Whatsapp`、`Channel::Telegram` 等）。
- `Conversation`（会话）：绑定 `Account / Inbox / Contact / ContactInbox`，记录状态（open/resolved/...）、优先级等。
- `Message`（消息）：统一格式，含 `message_type`、`content_type`、`content`、`content_attributes`、`source_id` 等；支持附件与回调事件。

示例：
```41:121:app/models/Message.rb
class Message < ApplicationRecord
  enum message_type: { incoming: 0, outgoing: 1, activity: 2, template: 3 }
  enum content_type: { text: 0, input_text: 1, input_textarea: 2, input_email: 3, input_select: 4,
                       cards: 5, form: 6, article: 7, incoming_email: 8, input_csat: 9,
                       integrations: 10, sticker: 11 }
  # 附加字段如: submitted_values, in_reply_to, translations, external_error, ...
end
```
```52:123:app/models/Conversation.rb
class Conversation < ApplicationRecord
  belongs_to :account; belongs_to :inbox; belongs_to :contact; belongs_to :contact_inbox
  enum status: { open: 0, resolved: 1, pending: 2, snoozed: 3 }
  has_many :messages, dependent: :destroy_async
end
```

---

## 三、各平台适配器实现分析

### 1) WhatsApp（Cloud/360dialog）
- Webhook 控制器 → 事件 Job → 入站服务（按 provider 分流）
```1:23:app/controllers/webhooks/whatsapp_controller.rb
class Webhooks::WhatsappController < ActionController::API
  def process_payload
    Webhooks::WhatsappEventsJob.perform_later(params.to_unsafe_hash)
    head :ok
  end
end
```
```1:18:app/jobs/webhooks/whatsapp_events_job.rb
class Webhooks::WhatsappEventsJob < ApplicationJob
  def perform(params = {})
    channel = find_channel_from_whatsapp_business_payload(params)
    case channel.provider
    when 'whatsapp_cloud'
      Whatsapp::IncomingMessageWhatsappCloudService.new(inbox: channel.inbox, params: params).perform
    else
      Whatsapp::IncomingMessageService.new(inbox: channel.inbox, params: params).perform
    end
  end
end
```
- 入站服务（基类 + Helpers）
```1:17:app/services/whatsapp/incoming_message_base_service.rb
class Whatsapp::IncomingMessageBaseService
  def perform
    processed_params
    if processed_params[:statuses].present?; process_statuses
    elsif processed_params[:messages].present?; process_messages
    end
  end
end
```
```1:116:app/services/whatsapp/incoming_message_service_helpers.rb
module Whatsapp::IncomingMessageServiceHelpers
  def unprocessable_message_type?(t); %w[reaction ephemeral unsupported request_welcome].include?(t); end
  def find_message_by_source_id(id); @message = Message.find_by(source_id: id) if id; end
  def message_under_process?; Redis::Alfred.get(format(Redis::RedisKeys::MESSAGE_SOURCE_KEY, id: @processed_params[:messages].first[:id])); end
  def cache_message_source_id_in_redis; ::Redis::Alfred.setex(format(Redis::RedisKeys::MESSAGE_SOURCE_KEY, id: @processed_params[:messages].first[:id]), true); end
  def clear_message_source_id_from_redis; ::Redis::Alfred.delete(format(Redis::RedisKeys::MESSAGE_SOURCE_KEY, id: @processed_params[:messages].first[:id])); end
end
```
- 要点：
  - 去重：`source_id` + Redis 键避免重复消息
  - 状态同步：`statuses` 更新 `Message.status`，失败时写 `external_error`
  - 联系人与会话：`ContactInboxWithContactBuilder` + `lock_to_single_conversation` 策略

### 2) Telegram
```1:44:app/controllers/webhooks/telegram_controller.rb
class Webhooks::TelegramController < ActionController::API
  def process_payload
    Webhooks::TelegramEventsJob.perform_later(params.to_unsafe_hash)
    head :ok
  end
end
```
```1:44:app/jobs/webhooks/telegram_events_job.rb
class Webhooks::TelegramEventsJob < ApplicationJob
  def perform(params = {})
    channel = Channel::Telegram.find_by(bot_token: params[:bot_token])
    process_event_params(channel, params)
  end
end
```
```1:36:app/services/telegram/incoming_message_service.rb
class Telegram::IncomingMessageService
  def perform
    transform_business_message!; return unless private_message?
    set_contact; update_contact_avatar; set_conversation
    @message = @conversation.messages.build(content: telegram_params_message_content,
      account_id: @inbox.account_id, inbox_id: @inbox.id, message_type: message_type,
      sender: message_sender, content_attributes: telegram_params_content_attributes,
      source_id: telegram_params_message_id.to_s)
    process_message_attachments if message_params?
    @message.save!
  end
end
```
- 要点：私聊过滤、头像更新、附件处理、`source_id` 追踪。

### 3) LINE
```1:24:app/jobs/webhooks/line_events_job.rb
Line::IncomingMessageService.new(inbox: @channel.inbox, params: @params['line'].with_indifferent_access).perform
```
```1:45:app/services/line/incoming_message_service.rb
@message = @conversation.messages.build(content: message_content(event), content_type: message_content_type(event),
  account_id: @inbox.account_id, inbox_id: @inbox.id, message_type: :incoming, sender: @contact, source_id: event['message']['id'].to_s)
```
- 要点：HMAC 校验、sticker 转图片占位、附件处理。

### 4) Twilio（SMS / WhatsApp via Twilio）
```1:17:app/jobs/webhooks/twilio_events_job.rb
return if params[:Body].blank? && params[:MediaUrl0].blank? && !valid_location_message?(params)
::Twilio::IncomingMessageService.new(params: params).perform
```
```1:22:app/services/twilio/incoming_message_service.rb
@message = @conversation.messages.build(content: message_body, account_id: @inbox.account_id, inbox_id: @inbox.id,
  message_type: :incoming, sender: @contact, source_id: params[:SmsSid])
```
- 要点：区分投递回执与真实消息、地理位置消息单独判定。

### 5) SMS（自建短信通道）
```1:22:app/jobs/webhooks/sms_events_job.rb
if delivery_event?(params)
  Sms::DeliveryStatusService.new(...).perform
else
  Sms::IncomingMessageService.new(inbox: channel.inbox, params: params[:message].with_indifferent_access).perform
end
```
```1:19:app/services/sms/incoming_message_service.rb
@message = @conversation.messages.create!(content: params[:text], account_id: @inbox.account_id, inbox_id: @inbox.id,
  message_type: :incoming, sender: @contact, source_id: params[:id])
```

### 6) Facebook / Instagram（Messenger/IG）
- Facebook：
```1:60:app/builders/messages/facebook/message_builder.rb
class Messages::Facebook::MessageBuilder < Messages::Messenger::MessageBuilder
  def perform
    build_contact_inbox; build_message; # 处理附件/异常
  end
end
```
- Instagram：
```1:181:app/builders/messages/instagram/base_message_builder.rb
class Messages::Instagram::BaseMessageBuilder < Messages::Messenger::MessageBuilder
  def build_message
    return if message_already_exists? || (message_content.blank? && all_unsupported_files?)
    @message = conversation.messages.create!(message_params)
    attachments.each { |a| process_attachment(a) }
  end
end
```
- 解析辅助：
```1:70:lib/integrations/facebook/message_parser.rb
class Integrations::Facebook::MessageParser
  def content; @messaging.dig('message', 'text'); end
  def identifier; @messaging.dig('message', 'mid'); end
end
```

### 7) Email（IMAP / ActionMailbox）
```1:24:app/mailboxes/imap/imap_mailbox.rb
ActiveRecord::Base.transaction do
  find_or_create_contact; find_or_create_conversation; create_message; add_attachments_to_message
end
```
```1:35:app/mailboxes/mailbox_helper.rb
@message = @conversation.messages.create!(message_type: 'incoming', content_type: 'incoming_email',
  content: mail_content&.truncate(150_000), source_id: processed_mail.message_id,
  content_attributes: { email: processed_mail.serialized_data, cc_email: processed_mail.cc, bcc_email: processed_mail.bcc })
```

### 8) WebWidget / Public API
```1:29:app/controllers/api/v1/widget/messages_controller.rb
@message = conversation.messages.new(message_params); build_attachment; @message.save!
```

---

## 四、数据转换流程详解

### 标准入站转换流水线
1) Webhook 控制器接收第三方回调（可能含签名/Token 校验）
2) 入队 Job（队列：`low`/`default` 等）
3) Job 解析参数并定位 `Channel::*` 与 `Inbox`
4) 调用平台入站服务/构建器：
   - 去重（`source_id` + Redis 键）
   - `ContactInboxWithContactBuilder` 定位/创建 `Contact` & `ContactInbox`
   - 依据 `lock_to_single_conversation` 策略选择/创建 `Conversation`
   - 构建 `Message`（含 `content_type`、`content_attributes`、附件）
5) `Message` 回调触发：ActionCable 事件广播、邮件通知、自动化/模板钩子

消息创建后的事件广播：
```298:321:app/models/Message.rb
Rails.configuration.dispatcher.dispatch(MESSAGE_CREATED, ...)
attachments.blank? ? SendReplyJob.perform_later(id) : ...
```
```33:39:app/listeners/action_cable_listener.rb
broadcast(account, tokens, MESSAGE_CREATED, message.push_event_data)
```

---

## 五、Mermaid 图表

### 5.1 数据流程图（graph TD）
```mermaid
graph TD
  A[外部平台 Webhook\nWhatsApp/Telegram/LINE/Twilio/SMS/FB/IG/Email] --> B[Webhook Controller]
  B --> C[Webhooks::*EventsJob\n(队列: low/default)]
  C --> D[Incoming Service / MessageBuilder\n平台适配器]
  D --> E[ContactInboxWithContactBuilder]
  E --> F[Conversation]
  D --> G[Message (+Attachments)]
  G --> H[Message Callbacks]
  H --> I[Sidekiq Jobs\nSendReplyJob等]
  H --> J[ActionCable Listener\n广播标准事件]
  J --> K[前端 Dashboard/Widget]
```

### 5.2 类图（classDiagram）
```mermaid
classDiagram
  class Webhooks::WhatsappController
  class Webhooks::TelegramController
  class Webhooks::LineController
  class Webhooks::InstagramController
  class Api::V1::WebhooksController
  class Webhooks::WhatsappEventsJob
  class Webhooks::TelegramEventsJob
  class Webhooks::LineEventsJob
  class Webhooks::TwilioEventsJob
  class Whatsapp::IncomingMessageBaseService
  class Whatsapp::IncomingMessageWhatsappCloudService
  class Telegram::IncomingMessageService
  class Line::IncomingMessageService
  class Twilio::IncomingMessageService
  class Sms::IncomingMessageService
  class Messages::Facebook::MessageBuilder
  class Messages::Instagram::BaseMessageBuilder
  class ContactInboxWithContactBuilder
  class Channel::Whatsapp
  class Channel::Telegram
  class Channel::Line
  class Channel::TwilioSms
  class Channel::Sms
  class Channel::FacebookPage
  class Channel::Instagram
  class Inbox
  class Contact
  class ContactInbox
  class Conversation
  class Message

  Webhooks::WhatsappController --> Webhooks::WhatsappEventsJob
  Webhooks::TelegramController --> Webhooks::TelegramEventsJob
  Webhooks::LineController --> Webhooks::LineEventsJob
  Api::V1::WebhooksController --> Webhooks::TwilioEventsJob

  Webhooks::WhatsappEventsJob --> Whatsapp::IncomingMessageBaseService
  Webhooks::WhatsappEventsJob --> Whatsapp::IncomingMessageWhatsappCloudService
  Webhooks::TelegramEventsJob --> Telegram::IncomingMessageService
  Webhooks::LineEventsJob --> Line::IncomingMessageService
  Webhooks::TwilioEventsJob --> Twilio::IncomingMessageService

  Whatsapp::IncomingMessageBaseService --> ContactInboxWithContactBuilder
  Telegram::IncomingMessageService --> ContactInboxWithContactBuilder
  Line::IncomingMessageService --> ContactInboxWithContactBuilder
  Twilio::IncomingMessageService --> ContactInboxWithContactBuilder
  Sms::IncomingMessageService --> ContactInboxWithContactBuilder
  Messages::Facebook::MessageBuilder --> Conversation
  Messages::Instagram::BaseMessageBuilder --> Conversation

  Channel::Whatsapp --> Inbox
  Channel::Telegram --> Inbox
  Channel::Line --> Inbox
  Channel::TwilioSms --> Inbox
  Channel::Sms --> Inbox
  Channel::FacebookPage --> Inbox
  Channel::Instagram --> Inbox

  ContactInboxWithContactBuilder --> Contact
  ContactInboxWithContactBuilder --> ContactInbox
  ContactInbox --> Inbox
  Conversation --> Inbox
  Conversation --> Contact
  Conversation --> ContactInbox
  Conversation --> Message
```

### 5.3 时序图（sequenceDiagram）——以 WhatsApp 入站为例
```mermaid
sequenceDiagram
  participant Meta as WhatsApp Cloud/360
  participant WC as Webhooks::WhatsappController
  participant J as Webhooks::WhatsappEventsJob
  participant S as Whatsapp::IncomingMessageBaseService
  participant B as ContactInboxWithContactBuilder
  participant Conv as Conversation
  participant Msg as Message
  participant AC as ActionCableListener

  Meta->>WC: POST /webhooks/whatsapp/:phone_number
  WC->>J: perform_later(params)
  J->>S: perform(inbox, params)
  S->>S: processed_params / 去重(Redis+source_id)
  S->>B: find_or_create(Contact, ContactInbox)
  B-->>S: ContactInbox
  S->>Conv: set_conversation(lock_to_single_conversation)
  S->>Msg: build + attachments + status
  Msg-->>AC: MESSAGE_CREATED 事件（push_event_data）
  AC-->>Frontend: WebSocket 广播
```

---

## 六、关键代码示例与技术决策说明
- 统一标准落点：所有平台最终在本地标准模型落地 `Conversation` + `Message`，并以 `source_id` 链接外部消息 ID，同时通过 `content_attributes` 承载平台特有元数据（如 in_reply_to_external_id、email headers、media_url 等）。
- 适配器模式：各平台入站服务（`Whatsapp::IncomingMessage*Service`、`Telegram::IncomingMessageService`、`Line::IncomingMessageService`、`Twilio::IncomingMessageService`、`Sms::IncomingMessageService`）承担“外部载荷 → 标准模型”的映射与边界校验。
- 构建器复用：
  - 通用附件处理：`Messages::Messenger::MessageBuilder#process_attachment`
  - 联系人与渠道绑定：`ContactInboxWithContactBuilder`（避免重复、处理头像异步）
- 事件与回调：消息保存后触发广播与异步任务（`SendReplyJob`、模板钩子），实现与前端/外部系统的解耦与实时性。

---

## 七、错误处理与异常情况
- 渠道不可用/未激活：Job 层进行早期返回与日志警告（如 WhatsApp/Telegram/SMS Jobs）。
- 鉴权/校验失败：
  - LINE 签名校验失败直接丢弃（HMAC 校验）。
  - WhatsApp 校验 Token（`MetaTokenVerifyConcern`）与“停用号码”过滤。
- 重复事件：`Message.source_id` + Redis 键（`MESSAGE_SOURCE_KEY`）防重。
- 不支持的消息类型：适配器层显式过滤（如 WhatsApp `reaction/ephemeral/unsupported`）。
- 外部错误透传：WhatsApp `statuses` 失败写入 `external_error`，便于排查。
- 邮件边界：`incoming_email_from_valid_email?` 过滤异常邮件，`content` 截断（150,000 字符）与附件上限约束。

---

## 八、性能考虑与扩展性设计
- 性能：
  - Sidekiq 队列分级（`critical`/`default`/`low`），压测路径短路（Job 层过滤非消息事件）。
  - Redis 去重避免重复写放大；附件下载与 ActiveStorage 异步处理；消息内容截断与附件数量限制。
- 扩展性：
  - 新平台接入模式：新增 Webhook 控制器 + 事件 Job + 入站 Service/Builder；重用 `ContactInboxWithContactBuilder` 与标准模型。
  - `Channelable` 抽象统一渠道能力；`content_attributes` 承载差异化元数据。
- 技术权衡：
  - 适配器在各自服务中分散实现，贴近平台细节，快速落地；但通用逻辑（如去重、入会话策略、附件归一）已抽象在 helpers/builder 中，控制重复与复杂度。

---

## 九、潜在改进点
- 统一入站适配器接口：定义 `IncomingMessageAdapter` 抽象基类（perform/normalize/ensure_contact/ensure_conversation/build_message），固化流程，减少平台差异代码分散。
- 适配器注册表：按 `Channel`/`provider` 注册对应适配器，实现更清晰的分发与可观测（metrics）。
- 失败重试与死信队列：对外部 API/附件下载失败场景增加幂等重试与DLQ监控。
- 规范化 `content_attributes`：定义 schema 与校验，提高前后端/外部消费者一致性。
- 统一状态同步：对各平台 delivery/read 状态做统一映射与回放策略。

---

## 十、参考代码路径索引
- Webhooks Controllers：`app/controllers/webhooks/*`
- Webhooks Jobs：`app/jobs/webhooks/*`
- Incoming Services：
  - WhatsApp：`app/services/whatsapp/*`
  - Telegram：`app/services/telegram/incoming_message_service.rb`
  - LINE：`app/services/line/incoming_message_service.rb`
  - Twilio：`app/services/twilio/incoming_message_service.rb`
  - SMS：`app/services/sms/incoming_message_service.rb`
  - Facebook/Instagram Builders：`app/builders/messages/{facebook,instagram}/*`
- 通用构建器：`app/builders/contact_inbox_with_contact_builder.rb`, `app/builders/messages/message_builder.rb`
- 标准模型：`app/models/{message,conversation,contact,inbox}.rb`, `app/models/channel/*`
- 邮件处理：`app/mailboxes/*`
- 广播与事件：`app/listeners/action_cable_listener.rb`, `app/jobs/action_cable_broadcast_job.rb`
