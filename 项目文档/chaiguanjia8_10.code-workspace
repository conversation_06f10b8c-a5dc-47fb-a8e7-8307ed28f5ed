{"folders": [{"path": ".."}], "settings": {"files.associations": {"*.json": "json", "*.yml": "yaml", "*.yaml": "yaml", "Dockerfile*": "dockerfile", "docker-compose*.yml": "yaml", ".env*": "properties", "*.md": "markdown"}, "terminal.integrated.defaultProfile.osx": "zsh", "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.env.osx": {"PYTHONPATH": "${workspaceFolder}/backend"}, "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}/backend"}, "git.confirmSync": false, "git.showPushSuccessNotification": true, "workbench.colorTheme": "Quiet Light", "workbench.iconTheme": "vscode-icons", "workbench.editor.enablePreview": false, "workbench.editor.enablePreviewFromQuickOpen": false, "debug.allowBreakpointsEverywhere": true, "debug.showInStatusBar": "always"}}