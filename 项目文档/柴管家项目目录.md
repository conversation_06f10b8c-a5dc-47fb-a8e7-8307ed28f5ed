# 柴管家项目目录结构设计

## 📋 文档概述

基于**Chatwoot成熟架构**和**AI原生设计**理念，本文档详细设计了柴管家智能客服系统的项目目录结构，体现了模块化单体架构、事件驱动、平台适配器模式和人机协作等核心设计原则。

## 🏗️ 整体架构设计原则

### 核心设计理念

1. **Chatwoot架构继承**：借鉴经过生产验证的多渠道聚合架构模式
2. **AI原生设计**：数据结构和处理流程针对AI场景优化
3. **平台适配器模式**：统一的多平台接入和消息处理
4. **事件驱动架构**：通过RabbitMQ实现模块间异步解耦
5. **渐进式演进**：支持从MVP到完整功能的平滑过渡
6. **人机协作机制**：内置AI与人工的智能协作流程

### 技术栈对应

- **后端技术栈**：Python 3.11+ + FastAPI + SQLAlchemy + PostgreSQL
- **前端技术栈**：React 18 + TypeScript + shadcn/ui + Tailwind CSS
- **消息队列**：RabbitMQ 3.12+
- **缓存存储**：Redis 7+
- **AI服务集成**：通义千问 + GPT-4 + Claude
- **容器化部署**：Docker + Docker Compose

## 📁 项目总体目录结构

```text
chaiguanjia/
├── frontend/                      # 前端应用层
├── backend/                       # 后端服务层
├── infrastructure/                # 基础设施层
├── docs/                         # 项目文档
├── tests/                        # 端到端测试
├── scripts/                      # 部署和工具脚本
├── .github/                      # CI/CD配置
├── README.md                     # 项目说明
├── .gitignore                    # Git忽略文件
├── .env.example                  # 环境变量示例
└── docker-compose.yml            # 开发环境配置
```

## 🎨 前端应用层结构

```text
frontend/
├── src/
│   ├── components/               # 通用UI组件
│   │   ├── ui/                  # 基础UI组件(shadcn/ui)
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── dialog.tsx
│   │   │   └── ...
│   │   ├── layout/              # 布局组件
│   │   │   ├── Header.tsx
│   │   │   ├── Sidebar.tsx
│   │   │   ├── Footer.tsx
│   │   │   └── MainLayout.tsx
│   │   ├── chat/                # 聊天界面组件
│   │   │   ├── ChatWindow.tsx
│   │   │   ├── MessageBubble.tsx
│   │   │   ├── MessageInput.tsx
│   │   │   ├── ConversationList.tsx
│   │   │   └── TypingIndicator.tsx
│   │   ├── channels/            # 渠道管理组件
│   │   │   ├── ChannelList.tsx
│   │   │   ├── ChannelConfig.tsx
│   │   │   ├── PlatformConnector.tsx
│   │   │   └── AdapterStatus.tsx
│   │   ├── ai/                  # AI功能组件
│   │   │   ├── AIAssistant.tsx
│   │   │   ├── ConfidenceIndicator.tsx
│   │   │   ├── SuggestionPanel.tsx
│   │   │   ├── HumanTakeoverPanel.tsx
│   │   │   └── ModelSelector.tsx
│   │   ├── knowledge/           # 知识库组件
│   │   │   ├── KnowledgeBase.tsx
│   │   │   ├── QuestionEditor.tsx
│   │   │   ├── AnswerEditor.tsx
│   │   │   └── SearchInterface.tsx
│   │   ├── workflow/            # 工作流组件
│   │   │   ├── WorkflowDesigner.tsx
│   │   │   ├── RuleEditor.tsx
│   │   │   ├── ActionBuilder.tsx
│   │   │   └── FlowVisualizer.tsx
│   │   └── dashboard/           # 仪表板组件
│   │       ├── Analytics.tsx
│   │       ├── Performance.tsx
│   │       ├── AIMetrics.tsx
│   │       └── SystemHealth.tsx
│   ├── pages/                   # 页面组件
│   │   ├── auth/                # 认证页面
│   │   │   ├── Login.tsx
│   │   │   ├── Register.tsx
│   │   │   └── ForgotPassword.tsx
│   │   ├── dashboard/           # 仪表板页面
│   │   │   ├── Overview.tsx
│   │   │   ├── Conversations.tsx
│   │   │   ├── Analytics.tsx
│   │   │   └── Settings.tsx
│   │   ├── channels/            # 渠道管理页面
│   │   │   ├── index.tsx
│   │   │   ├── create.tsx
│   │   │   └── [id]/edit.tsx
│   │   ├── knowledge/           # 知识库页面
│   │   │   ├── index.tsx
│   │   │   ├── create.tsx
│   │   │   └── [id]/edit.tsx
│   │   ├── workflow/            # 工作流页面
│   │   │   ├── index.tsx
│   │   │   ├── create.tsx
│   │   │   └── [id]/edit.tsx
│   │   └── admin/               # 管理页面
│   │       ├── users.tsx
│   │       ├── roles.tsx
│   │       └── system.tsx
│   ├── hooks/                   # 自定义Hooks
│   │   ├── useAuth.ts
│   │   ├── useWebSocket.ts
│   │   ├── useChat.ts
│   │   ├── useAI.ts
│   │   ├── useKnowledge.ts
│   │   └── useWorkflow.ts
│   ├── services/                # API服务
│   │   ├── api.ts               # API基础配置
│   │   ├── auth.ts              # 认证服务
│   │   ├── channels.ts          # 渠道管理服务
│   │   ├── messages.ts          # 消息服务
│   │   ├── ai.ts                # AI服务
│   │   ├── knowledge.ts         # 知识库服务
│   │   ├── workflow.ts          # 工作流服务
│   │   └── websocket.ts         # WebSocket服务
│   ├── stores/                  # 状态管理(Zustand)
│   │   ├── authStore.ts
│   │   ├── chatStore.ts
│   │   ├── channelStore.ts
│   │   ├── aiStore.ts
│   │   ├── knowledgeStore.ts
│   │   └── workflowStore.ts
│   ├── types/                   # TypeScript类型定义
│   │   ├── auth.ts
│   │   ├── channel.ts
│   │   ├── message.ts
│   │   ├── ai.ts
│   │   ├── knowledge.ts
│   │   ├── workflow.ts
│   │   └── api.ts
│   ├── utils/                   # 工具函数
│   │   ├── format.ts
│   │   ├── validation.ts
│   │   ├── constants.ts
│   │   └── helpers.ts
│   ├── contexts/                # React Context
│   │   ├── AuthContext.tsx
│   │   ├── ThemeContext.tsx
│   │   └── WebSocketContext.tsx
│   └── assets/                  # 静态资源
│       ├── images/
│       ├── icons/
│       └── styles/
├── public/                      # 公共静态资源
├── package.json
├── tsconfig.json
├── tailwind.config.js
├── vite.config.ts
└── Dockerfile
```

## 🔧 后端服务层结构

### 核心架构图

```mermaid
graph TB
    subgraph "后端服务架构"
        subgraph "API入口层"
            MAIN[main.py<br/>FastAPI应用入口]
            GATEWAY[API Gateway<br/>路由聚合]
        end
        
        subgraph "核心业务模块层"
            ADAPTERS[Platform Adapters<br/>平台适配器模块]
            MESSAGES[Message Processing<br/>消息处理模块]
            AI_SERVICES[AI Services<br/>AI智能服务模块]
            KNOWLEDGE[Knowledge Management<br/>知识管理模块]
            WORKFLOW[Workflow Engine<br/>工作流引擎模块]
            COLLAB[Human Collaboration<br/>人机协作模块]
        end
        
        subgraph "共享基础层"
            DATABASE[Database<br/>数据库层]
            CACHE[Cache<br/>缓存层]
            MESSAGING[Messaging<br/>消息队列层]
            SECURITY[Security<br/>安全认证层]
        end
        
        MAIN --> GATEWAY
        GATEWAY --> ADAPTERS
        GATEWAY --> MESSAGES
        GATEWAY --> AI_SERVICES
        GATEWAY --> KNOWLEDGE
        GATEWAY --> WORKFLOW
        GATEWAY --> COLLAB
        
        ADAPTERS --> DATABASE
        MESSAGES --> DATABASE
        AI_SERVICES --> CACHE
        KNOWLEDGE --> DATABASE
        WORKFLOW --> DATABASE
        COLLAB --> DATABASE
        
        ADAPTERS --> MESSAGING
        MESSAGES --> MESSAGING
        AI_SERVICES --> MESSAGING
        WORKFLOW --> MESSAGING
    end
    
    style MAIN fill:#e3f2fd
    style ADAPTERS fill:#f3e5f5
    style AI_SERVICES fill:#e8f5e8
    style DATABASE fill:#fff3e0
```

### 详细目录结构

```text
backend/
├── app/                        # 主应用目录
│   ├── main.py                 # FastAPI应用入口
│   ├── config/                 # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py         # 基础配置
│   │   ├── environments/       # 环境配置
│   │   │   ├── __init__.py
│   │   │   ├── development.py
│   │   │   ├── testing.py
│   │   │   ├── staging.py
│   │   │   └── production.py
│   │   ├── database.py         # 数据库配置
│   │   ├── redis.py           # Redis配置
│   │   ├── rabbitmq.py        # RabbitMQ配置
│   │   ├── ai_config.py       # AI服务配置
│   │   ├── security.py        # 安全配置
│   │   └── logging.py         # 日志配置
│   ├── core/                  # 核心基础组件
│   │   ├── __init__.py
│   │   ├── security.py        # 安全认证核心
│   │   ├── middleware.py      # 核心中间件
│   │   ├── exceptions.py      # 异常处理
│   │   ├── events.py          # 事件系统
│   │   └── dependencies.py    # 全局依赖注入
│   ├── modules/               # 核心业务模块
│   │   ├── __init__.py
│   │   ├── platform_adapters/ # 平台适配器模块
│   │   │   ├── __init__.py
│   │   │   ├── api/           # API层
│   │   │   │   ├── __init__.py
│   │   │   │   ├── routers.py
│   │   │   │   ├── schemas.py
│   │   │   │   └── dependencies.py
│   │   │   ├── services/      # 业务层
│   │   │   │   ├── __init__.py
│   │   │   │   ├── adapter_factory.py      # 适配器工厂
│   │   │   │   ├── channel_manager.py      # 渠道管理器
│   │   │   │   └── webhook_processor.py    # Webhook处理器
│   │   │   ├── adapters/      # 平台适配器实现
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base.py                 # 基础适配器
│   │   │   │   ├── wechat.py              # 微信适配器
│   │   │   │   ├── douyin.py              # 抖音适配器
│   │   │   │   ├── xiaohongshu.py         # 小红书适配器
│   │   │   │   ├── xianyu.py              # 闲鱼适配器
│   │   │   │   ├── zhishixingqiu.py       # 知识星球适配器
│   │   │   │   └── adapter_registry.py    # 适配器注册表
│   │   │   ├── models/        # 数据层
│   │   │   │   ├── __init__.py
│   │   │   │   ├── channel.py
│   │   │   │   ├── webhook.py
│   │   │   │   └── adapter_config.py
│   │   │   └── tests/
│   │   │       ├── __init__.py
│   │   │       ├── test_adapters.py
│   │   │       ├── test_services.py
│   │   │       └── features/
│   │   ├── message_processing/ # 消息处理模块
│   │   │   ├── __init__.py
│   │   │   ├── api/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── routers.py
│   │   │   │   ├── schemas.py
│   │   │   │   └── dependencies.py
│   │   │   ├── services/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── message_processor.py   # 消息处理器
│   │   │   │   ├── contact_builder.py     # 联系人构建器
│   │   │   │   ├── conversation_builder.py # 会话构建器
│   │   │   │   ├── message_builder.py     # 消息构建器
│   │   │   │   └── deduplication.py       # 消息去重
│   │   │   ├── models/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── message.py
│   │   │   │   ├── conversation.py
│   │   │   │   ├── contact.py
│   │   │   │   └── contact_inbox.py
│   │   │   └── tests/
│   │   │       ├── __init__.py
│   │   │       ├── test_processing.py
│   │   │       ├── test_builders.py
│   │   │       └── features/
│   │   ├── ai_services/       # AI智能服务模块
│   │   │   ├── __init__.py
│   │   │   ├── api/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── routers.py
│   │   │   │   ├── schemas.py
│   │   │   │   └── dependencies.py
│   │   │   ├── services/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── ai_router.py           # AI路由器
│   │   │   │   ├── model_balancer.py      # 模型负载均衡器
│   │   │   │   └── cache_manager.py       # AI缓存管理器
│   │   │   ├── engines/       # AI引擎实现
│   │   │   │   ├── __init__.py
│   │   │   │   ├── nlu/                   # 自然语言理解
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── intent_classifier.py
│   │   │   │   │   ├── entity_extractor.py
│   │   │   │   │   └── emotion_analyzer.py
│   │   │   │   ├── generation/            # 回复生成
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── response_generator.py
│   │   │   │   │   ├── context_manager.py
│   │   │   │   │   └── prompt_builder.py
│   │   │   │   ├── confidence/            # 置信度评估
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── evaluator.py
│   │   │   │   │   ├── metrics.py
│   │   │   │   │   └── threshold_manager.py
│   │   │   │   └── providers/             # AI提供商接口
│   │   │   │       ├── __init__.py
│   │   │   │       ├── qwen_provider.py   # 通义千问
│   │   │   │       ├── openai_provider.py # GPT-4
│   │   │   │       ├── claude_provider.py # Claude
│   │   │   │       └── base_provider.py
│   │   │   ├── models/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── ai_processing_log.py
│   │   │   │   ├── ai_config.py
│   │   │   │   └── model_metrics.py
│   │   │   └── tests/
│   │   │       ├── __init__.py
│   │   │       ├── test_engines.py
│   │   │       ├── test_providers.py
│   │   │       └── features/
│   │   ├── knowledge_management/ # 知识管理模块
│   │   │   ├── __init__.py
│   │   │   ├── api/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── routers.py
│   │   │   │   ├── schemas.py
│   │   │   │   └── dependencies.py
│   │   │   ├── services/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── knowledge_service.py
│   │   │   │   ├── search_service.py      # 知识检索服务
│   │   │   │   ├── embedding_service.py   # 向量嵌入服务
│   │   │   │   └── rag_service.py         # RAG服务
│   │   │   ├── engines/       # 知识引擎
│   │   │   │   ├── __init__.py
│   │   │   │   ├── retrieval/             # 检索引擎
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── vector_search.py
│   │   │   │   │   ├── keyword_search.py
│   │   │   │   │   └── hybrid_search.py
│   │   │   │   ├── indexing/              # 索引引擎
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── document_processor.py
│   │   │   │   │   └── index_manager.py
│   │   │   │   └── ranking/               # 排序引擎
│   │   │   │       ├── __init__.py
│   │   │   │       ├── relevance_scorer.py
│   │   │   │       └── result_ranker.py
│   │   │   ├── models/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── knowledge_base.py
│   │   │   │   ├── document.py
│   │   │   │   └── search_log.py
│   │   │   └── tests/
│   │   │       ├── __init__.py
│   │   │       ├── test_search.py
│   │   │       ├── test_embedding.py
│   │   │       └── features/
│   │   ├── workflow_engine/   # 工作流引擎模块
│   │   │   ├── __init__.py
│   │   │   ├── api/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── routers.py
│   │   │   │   ├── schemas.py
│   │   │   │   └── dependencies.py
│   │   │   ├── services/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── workflow_service.py
│   │   │   │   ├── rule_engine.py         # 规则引擎
│   │   │   │   ├── execution_engine.py    # 执行引擎
│   │   │   │   └── automation_service.py  # 自动化服务
│   │   │   ├── engines/       # 工作流引擎
│   │   │   │   ├── __init__.py
│   │   │   │   ├── rule_engine/           # 规则引擎
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── condition_evaluator.py
│   │   │   │   │   ├── action_executor.py
│   │   │   │   │   └── rule_parser.py
│   │   │   │   ├── execution/             # 执行引擎
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── workflow_executor.py
│   │   │   │   │   ├── task_scheduler.py
│   │   │   │   │   └── state_manager.py
│   │   │   │   └── triggers/              # 触发器
│   │   │   │       ├── __init__.py
│   │   │   │       ├── event_triggers.py
│   │   │   │       ├── time_triggers.py
│   │   │   │       └── condition_triggers.py
│   │   │   ├── models/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── workflow.py
│   │   │   │   ├── automation_rule.py
│   │   │   │   └── execution_log.py
│   │   │   └── tests/
│   │   │       ├── __init__.py
│   │   │       ├── test_rules.py
│   │   │       ├── test_execution.py
│   │   │       └── features/
│   │   ├── human_collaboration/ # 人机协作模块
│   │   │   ├── __init__.py
│   │   │   ├── api/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── routers.py
│   │   │   │   ├── schemas.py
│   │   │   │   └── dependencies.py
│   │   │   ├── services/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── collaboration_service.py
│   │   │   │   ├── takeover_service.py     # 人工接管服务
│   │   │   │   ├── suggestion_service.py   # 建议服务
│   │   │   │   └── handover_service.py     # 交接服务
│   │   │   ├── engines/       # 协作引擎
│   │   │   │   ├── __init__.py
│   │   │   │   ├── mode_selector.py       # 模式选择器
│   │   │   │   ├── threshold_manager.py   # 阈值管理器
│   │   │   │   ├── escalation_engine.py   # 升级引擎
│   │   │   │   └── quality_checker.py     # 质量检查器
│   │   │   ├── models/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── takeover_log.py
│   │   │   │   ├── collaboration_config.py
│   │   │   │   └── quality_metrics.py
│   │   │   └── tests/
│   │   │       ├── __init__.py
│   │   │       ├── test_takeover.py
│   │   │       ├── test_collaboration.py
│   │   │       └── features/
│   │   └── user_management/   # 用户管理模块
│   │       ├── __init__.py
│   │       ├── api/
│   │       │   ├── __init__.py
│   │       │   ├── routers.py
│   │       │   ├── schemas.py
│   │       │   └── dependencies.py
│   │       ├── services/
│   │       │   ├── __init__.py
│   │       │   ├── user_service.py
│   │       │   ├── auth_service.py
│   │       │   └── permission_service.py
│   │       ├── models/
│   │       │   ├── __init__.py
│   │       │   ├── user.py
│   │       │   ├── role.py
│   │       │   └── permission.py
│   │       └── tests/
│   │           ├── __init__.py
│   │           ├── test_auth.py
│   │           ├── test_permissions.py
│   │           └── features/
│   ├── shared/                # 共享基础组件
│   │   ├── __init__.py
│   │   ├── database/          # 数据库组件
│   │   │   ├── __init__.py
│   │   │   ├── base.py        # 基础模型
│   │   │   ├── session.py     # 数据库会话
│   │   │   └── migrations/    # 数据库迁移
│   │   ├── cache/             # 缓存组件
│   │   │   ├── __init__.py
│   │   │   ├── redis_client.py
│   │   │   ├── cache_manager.py
│   │   │   └── cache_strategies.py
│   │   ├── messaging/         # 消息队列组件
│   │   │   ├── __init__.py
│   │   │   ├── rabbitmq_client.py
│   │   │   ├── event_bus.py
│   │   │   ├── publishers.py
│   │   │   └── subscribers.py
│   │   ├── security/          # 安全组件
│   │   │   ├── __init__.py
│   │   │   ├── jwt_handler.py
│   │   │   ├── oauth2/        # OAuth2认证
│   │   │   │   ├── __init__.py
│   │   │   │   ├── providers/
│   │   │   │   └── callbacks/
│   │   │   ├── rbac/          # 基于角色的权限控制
│   │   │   │   ├── __init__.py
│   │   │   │   ├── roles.py
│   │   │   │   └── permissions.py
│   │   │   └── encryption/    # 加密工具
│   │   │       ├── __init__.py
│   │   │       └── utils.py
│   │   ├── monitoring/        # 监控组件
│   │   │   ├── __init__.py
│   │   │   ├── metrics.py
│   │   │   ├── logging.py
│   │   │   ├── health_check.py
│   │   │   └── performance.py
│   │   └── utils/             # 工具函数
│   │       ├── __init__.py
│   │       ├── validators.py
│   │       ├── formatters.py
│   │       ├── helpers.py
│   │       └── constants.py
│   ├── api/                   # API路由聚合
│   │   ├── __init__.py
│   │   ├── v1/                # API版本管理v1
│   │   │   ├── __init__.py
│   │   │   ├── api.py         # 路由聚合
│   │   │   └── endpoints/     # 端点定义
│   │   │       ├── __init__.py
│   │   │       ├── webhooks.py
│   │   │       ├── channels.py
│   │   │       ├── messages.py
│   │   │       ├── ai.py
│   │   │       ├── knowledge.py
│   │   │       ├── workflow.py
│   │   │       └── auth.py
│   │   └── v2/                # API版本管理v2（预留）
│   │       └── __init__.py
│   └── tasks/                 # 异步任务
│       ├── __init__.py
│       ├── celery_app.py     # Celery应用配置
│       ├── webhook_tasks.py   # Webhook处理任务
│       ├── ai_tasks.py       # AI处理任务
│       ├── notification_tasks.py # 通知任务
│       └── maintenance_tasks.py  # 维护任务
├── database/                  # 数据库结构设计
│   ├── migrations/            # 数据库迁移文件（Alembic）
│   │   ├── env.py
│   │   ├── script.py.mako
│   │   └── versions/
│   │       ├── 001_initial_schema.py
│   │       ├── 002_add_channels.py
│   │       ├── 003_add_messages.py
│   │       ├── 004_add_ai_features.py
│   │       ├── 005_add_knowledge_base.py
│   │       ├── 006_add_workflows.py
│   │       └── 007_add_collaboration.py
│   ├── schemas/               # 数据库Schema设计
│   │   ├── accounts.sql       # 账户相关表
│   │   ├── channels.sql       # 渠道相关表
│   │   ├── messages.sql       # 消息相关表
│   │   ├── ai_services.sql    # AI服务相关表
│   │   ├── knowledge.sql      # 知识库相关表
│   │   ├── workflows.sql      # 工作流相关表
│   │   ├── collaboration.sql  # 人机协作相关表
│   │   └── users.sql          # 用户相关表
│   └── seeds/                 # 初始数据
│       ├── development/       # 开发环境初始数据
│       ├── testing/          # 测试环境初始数据
│       └── production/       # 生产环境初始数据
├── tests/                     # 测试文件
│   ├── conftest.py           # 测试配置
│   ├── unit/                 # 单元测试
│   ├── integration/          # 集成测试
│   ├── e2e/                  # 端到端测试
│   └── performance/          # 性能测试
├── requirements/              # 依赖管理
│   ├── base.txt              # 基础依赖
│   ├── development.txt       # 开发依赖
│   ├── testing.txt           # 测试依赖
│   └── production.txt        # 生产依赖
├── Dockerfile                # Docker镜像构建
├── docker-compose.yml        # 开发环境配置
├── .env.example             # 环境变量示例
└── README.md                # 后端说明文档
```

## 🏗️ 基础设施层结构

```text
infrastructure/
├── docker/                   # Docker配置
│   ├── docker-compose.yml    # 标准环境配置
│   ├── docker-compose.dev.yml # 开发环境配置
│   ├── docker-compose.prod.yml # 生产环境配置
│   ├── nginx/               # Nginx配置
│   │   ├── nginx.conf       # 主配置文件
│   │   ├── conf.d/         # 虚拟主机配置
│   │   │   ├── default.conf
│   │   │   ├── api.conf
│   │   │   └── ssl.conf
│   │   ├── ssl/            # SSL证书
│   │   └── logs/           # 日志文件
│   ├── rabbitmq/           # RabbitMQ配置
│   │   ├── rabbitmq.conf
│   │   ├── definitions.json # 队列定义
│   │   └── enabled_plugins
│   ├── redis/              # Redis配置
│   │   └── redis.conf
│   └── postgresql/         # PostgreSQL配置
│       ├── postgresql.conf
│       ├── pg_hba.conf
│       └── init/           # 初始化脚本
├── kubernetes/             # K8s配置（预留）
│   ├── namespace.yaml
│   ├── configmap.yaml
│   ├── deployment.yaml
│   ├── service.yaml
│   └── ingress.yaml
├── monitoring/             # 监控配置
│   ├── health-checks/      # 健康检查脚本
│   │   ├── app-health.sh
│   │   ├── db-health.sh
│   │   ├── redis-health.sh
│   │   └── rabbitmq-health.sh
│   ├── logging/            # 日志配置
│   │   ├── logrotate.conf
│   │   ├── rsyslog.conf
│   │   └── fluentd/        # 日志收集配置
│   ├── metrics/            # 指标收集（MVP后扩展）
│   │   ├── prometheus/
│   │   ├── grafana/
│   │   └── alertmanager/
│   └── alerting/           # 告警配置
│       ├── email-alerts.py
│       ├── wechat-alerts.py
│       └── slack-alerts.py
├── security/               # 安全配置
│   ├── certificates/       # 证书管理
│   ├── secrets/           # 密钥管理
│   └── policies/          # 安全策略
├── backup/                # 备份策略
│   ├── database/          # 数据库备份
│   ├── files/            # 文件备份
│   └── scripts/          # 备份脚本
└── scripts/               # 运维脚本
    ├── deploy.sh          # 部署脚本
    ├── backup.sh          # 备份脚本
    ├── restore.sh         # 恢复脚本
    ├── migrate.sh         # 迁移脚本
    └── monitor.sh         # 监控脚本
```

## 📚 项目文档结构

```text
docs/
├── README.md              # 文档索引
├── architecture/          # 架构文档
│   ├── overview.md        # 架构概览
│   ├── design-principles.md # 设计原则
│   ├── technology-stack.md   # 技术栈说明
│   ├── data-flow.md       # 数据流设计
│   ├── security.md        # 安全架构
│   └── performance.md     # 性能设计
├── api/                   # API文档
│   ├── openapi.yaml       # OpenAPI规范
│   ├── authentication.md  # 认证说明
│   ├── channels.md        # 渠道API
│   ├── messages.md        # 消息API
│   ├── ai-services.md     # AI服务API
│   ├── knowledge.md       # 知识库API
│   ├── workflows.md       # 工作流API
│   └── webhooks.md        # Webhook API
├── platform-adapters/     # 平台适配器文档
│   ├── overview.md        # 适配器概览
│   ├── wechat.md         # 微信适配器
│   ├── douyin.md         # 抖音适配器
│   ├── xiaohongshu.md    # 小红书适配器
│   ├── xianyu.md         # 闲鱼适配器
│   ├── zhishixingqiu.md  # 知识星球适配器
│   └── custom-adapter.md  # 自定义适配器开发
├── ai-integration/        # AI集成文档
│   ├── providers.md       # AI提供商集成
│   ├── model-routing.md   # 模型路由策略
│   ├── confidence.md      # 置信度评估
│   ├── knowledge-rag.md   # 知识库RAG
│   └── human-collaboration.md # 人机协作
├── deployment/            # 部署文档
│   ├── quick-start.md     # 快速开始
│   ├── docker.md          # Docker部署
│   ├── kubernetes.md      # K8s部署
│   ├── monitoring.md      # 监控配置
│   ├── backup.md          # 备份策略
│   └── troubleshooting.md # 故障排除
├── development/           # 开发文档
│   ├── setup.md           # 开发环境搭建
│   ├── coding-standards.md # 编码规范
│   ├── testing.md         # 测试指南
│   ├── database.md        # 数据库设计
│   ├── contributing.md    # 贡献指南
│   └── modules/           # 模块开发指南
│       ├── platform-adapters.md
│       ├── ai-services.md
│       ├── knowledge.md
│       └── workflows.md
├── user-guide/            # 用户指南
│   ├── getting-started.md # 快速入门
│   ├── channel-setup.md   # 渠道配置
│   ├── ai-configuration.md # AI配置
│   ├── knowledge-base.md  # 知识库管理
│   ├── workflow-design.md # 工作流设计
│   └── troubleshooting.md # 常见问题
└── changelog/             # 变更日志
    ├── CHANGELOG.md
    └── migration-guides/  # 迁移指南
```

## 🧪 测试结构

```text
tests/
├── conftest.py            # 全局测试配置
├── unit/                  # 单元测试
│   ├── adapters/          # 适配器单元测试
│   ├── ai_services/       # AI服务单元测试
│   ├── knowledge/         # 知识库单元测试
│   ├── workflows/         # 工作流单元测试
│   └── shared/            # 共享组件测试
├── integration/           # 集成测试
│   ├── api/               # API集成测试
│   ├── database/          # 数据库集成测试
│   ├── messaging/         # 消息队列集成测试
│   └── external/          # 外部服务集成测试
├── e2e/                   # 端到端测试
│   ├── user-flows/        # 用户流程测试
│   ├── api-workflows/     # API工作流测试
│   └── platform-integration/ # 平台集成测试
├── performance/           # 性能测试
│   ├── load-testing/      # 负载测试
│   ├── stress-testing/    # 压力测试
│   └── benchmarks/        # 基准测试
├── security/              # 安全测试
│   ├── auth-testing/      # 认证测试
│   ├── permission-testing/ # 权限测试
│   └── vulnerability-scanning/ # 漏洞扫描
└── fixtures/              # 测试数据
    ├── api-responses/     # API响应数据
    ├── database-seeds/    # 数据库种子数据
    └── mock-data/         # 模拟数据
```

## 🔧 工具脚本结构

```text
scripts/
├── setup/                 # 环境搭建脚本
│   ├── install-deps.sh    # 依赖安装
│   ├── init-database.sh   # 数据库初始化
│   ├── setup-dev.sh       # 开发环境设置
│   └── setup-prod.sh      # 生产环境设置
├── deployment/            # 部署脚本
│   ├── deploy.sh          # 主部署脚本
│   ├── rollback.sh        # 回滚脚本
│   ├── health-check.sh    # 健康检查
│   └── update.sh          # 更新脚本
├── database/              # 数据库脚本
│   ├── migrate.sh         # 数据库迁移
│   ├── backup.sh          # 数据库备份
│   ├── restore.sh         # 数据库恢复
│   └── seed.sh            # 数据初始化
├── monitoring/            # 监控脚本
│   ├── system-monitor.sh  # 系统监控
│   ├── log-analyzer.sh    # 日志分析
│   └── alert-sender.sh    # 告警发送
├── maintenance/           # 维护脚本
│   ├── cleanup.sh         # 清理脚本
│   ├── optimize.sh        # 优化脚本
│   └── vacuum.sh          # 数据库维护
└── development/           # 开发工具脚本
    ├── code-gen.sh        # 代码生成
    ├── test-runner.sh     # 测试运行
    ├── lint-check.sh      # 代码检查
    └── doc-gen.sh         # 文档生成
```

## 🚀 CI/CD配置结构

```text
.github/
├── workflows/             # GitHub Actions工作流
│   ├── backend-ci.yml     # 后端CI
│   ├── frontend-ci.yml    # 前端CI
│   ├── integration-test.yml # 集成测试
│   ├── security-scan.yml  # 安全扫描
│   ├── deploy-staging.yml # 预发布部署
│   └── deploy-production.yml # 生产部署
├── ISSUE_TEMPLATE/        # Issue模板
│   ├── bug_report.md
│   ├── feature_request.md
│   └── documentation.md
├── PULL_REQUEST_TEMPLATE.md # PR模板
└── SECURITY.md            # 安全政策
```

## 📊 核心设计亮点

### 1. Chatwoot架构继承

```mermaid
graph LR
    subgraph "Chatwoot架构继承"
        A[多平台适配器<br/>Platform Adapters] --> B[消息处理流水线<br/>Message Processing]
        B --> C[联系人管理<br/>Contact Management]
        C --> D[会话管理<br/>Conversation Management]
    end
    
    subgraph "AI原生增强"
        E[AI智能服务<br/>AI Services] --> F[知识库RAG<br/>Knowledge RAG]
        F --> G[人机协作<br/>Human-AI Collaboration]
        G --> H[工作流自动化<br/>Workflow Automation]
    end
    
    B --> E
    D --> G
    
    style A fill:#e3f2fd
    style E fill:#f3e5f5
    style G fill:#e8f5e8
```

### 2. AI原生设计特性

- **多模型路由**: 智能选择最优AI模型处理不同类型请求
- **置信度评估**: 多维度评估AI回复质量，决定是否需要人工介入
- **知识增强RAG**: 结合知识库的检索增强生成，提升回复准确性
- **人机协作**: 无缝的AI与人工切换机制

### 3. 渐进式演进能力

- **MVP优先**: 核心功能快速上线验证
- **模块化扩展**: 独立模块便于功能迭代
- **版本管理**: API版本管理支持平滑升级
- **监控预留**: 预留监控扩展接口，支持从简单到复杂的监控方案

## 🎯 下一步实施建议

### 第一阶段：基础架构（2-3周）

1. **项目结构搭建**: 按照设计创建目录结构
2. **核心框架集成**: FastAPI + SQLAlchemy + Redis + RabbitMQ
3. **基础适配器**: 实现微信、闲鱼等重点平台适配器
4. **简单AI集成**: 集成通义千问API

### 第二阶段：AI服务完善（3-4周）

1. **AI服务架构**: 完善AI路由、置信度评估等核心功能
2. **知识库系统**: 实现知识检索和RAG功能
3. **人机协作**: 实现基础的人工接管机制
4. **前端界面**: 开发核心管理界面

### 第三阶段：功能扩展（2-3周）

1. **工作流引擎**: 实现自动化规则和工作流
2. **高级AI功能**: 多模型路由、缓存优化等
3. **监控告警**: 完善监控和告警机制
4. **性能优化**: 系统性能调优

这个目录结构设计充分体现了Chatwoot架构的成熟性和AI原生的创新性，为快速构建生产级智能客服系统提供了清晰的实施路径。
