#!/bin/bash

# Git 钩子配置脚本
# 自动配置 Git 提交规范和钩子

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🔧 配置 Git 工作环境...${NC}"

# ==========================================
# 配置 Git 用户信息
# ==========================================
configure_git_user() {
    echo -e "${YELLOW}📝 配置 Git 用户信息${NC}"

    # 检查是否已设置用户信息
    if ! git config user.name >/dev/null 2>&1; then
        echo "请输入您的姓名:"
        read -r user_name
        git config user.name "$user_name"
        echo -e "${GREEN}✅ 已设置用户名: $user_name${NC}"
    else
        echo -e "${GREEN}✅ 用户名已设置: $(git config user.name)${NC}"
    fi

    if ! git config user.email >/dev/null 2>&1; then
        echo "请输入您的邮箱:"
        read -r user_email
        git config user.email "$user_email"
        echo -e "${GREEN}✅ 已设置邮箱: $user_email${NC}"
    else
        echo -e "${GREEN}✅ 邮箱已设置: $(git config user.email)${NC}"
    fi
}

# ==========================================
# 配置 Git 提交模板
# ==========================================
configure_commit_template() {
    echo -e "${YELLOW}📋 配置提交信息模板${NC}"

    if [ -f ".gitmessage" ]; then
        git config commit.template .gitmessage
        echo -e "${GREEN}✅ 已设置提交信息模板${NC}"
    else
        echo -e "${RED}❌ 提交模板文件 .gitmessage 不存在${NC}"
    fi
}

# ==========================================
# 配置 Git 别名
# ==========================================
configure_git_aliases() {
    echo -e "${YELLOW}🔗 配置 Git 别名${NC}"

    # 常用别名
    git config alias.st status
    git config alias.co checkout
    git config alias.br branch
    git config alias.ci commit
    git config alias.unstage 'reset HEAD --'
    git config alias.last 'log -1 HEAD'
    git config alias.visual '!gitk'

    # 美化日志
    git config alias.lg "log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit"
    git config alias.ll "log --oneline --graph --decorate --all"

    # 分支操作
    git config alias.switch-to-branch '!f() { git checkout -b "$1" 2>/dev/null || git checkout "$1"; }; f'
    git config alias.delete-merged-branches '!f() { git branch --merged | grep -v "\*\|main\|master\|develop" | xargs -n 1 git branch -d; }; f'

    # 同步操作
    git config alias.sync '!f() { git fetch origin && git rebase origin/$(git branch --show-current); }; f'
    git config alias.pushnew '!f() { git push -u origin $(git branch --show-current); }; f'

    # 代码审查
    git config alias.review 'diff --name-only --diff-filter=M HEAD~1'
    git config alias.contributors 'shortlog -sn'

    echo -e "${GREEN}✅ Git 别名配置完成${NC}"
}

# ==========================================
# 配置分支策略
# ==========================================
configure_branch_strategy() {
    echo -e "${YELLOW}🌿 配置分支策略${NC}"

    # 设置默认分支为 main
    git config init.defaultBranch main

    # 配置合并策略
    git config merge.ours.driver true

    # 配置 rebase 策略
    git config pull.rebase true
    git config rebase.autoStash true

    # 配置推送策略
    git config push.default simple
    git config push.followTags true

    echo -e "${GREEN}✅ 分支策略配置完成${NC}"
}

# ==========================================
# 创建 pre-commit 钩子
# ==========================================
create_pre_commit_hook() {
    echo -e "${YELLOW}🎣 创建 pre-commit 钩子${NC}"

    hook_file=".git/hooks/pre-commit"

    cat > "$hook_file" << 'EOF'
#!/bin/bash

# 柴管家项目 pre-commit 钩子
# 在提交前执行代码质量检查

set -e

echo "🔍 执行 pre-commit 检查..."

# 检查是否有暂存的文件
if ! git diff --cached --name-only | grep -qE '\.(py|js|jsx|ts|tsx|json|md)$'; then
    echo "✅ 没有需要检查的文件"
    exit 0
fi

# Python 文件检查
if git diff --cached --name-only | grep -qE '\.py$'; then
    echo "🐍 检查 Python 代码..."

    # 检查是否在虚拟环境中
    if [ -f "backend/.venv/bin/activate" ]; then
        source backend/.venv/bin/activate
    fi

    # Black 格式化检查
    if command -v black >/dev/null 2>&1; then
        echo "  - 运行 Black 格式化检查..."
        black --check --diff backend/ || {
            echo "❌ Black 格式化检查失败，请运行: black backend/"
            exit 1
        }
    fi

    # isort 导入排序检查
    if command -v isort >/dev/null 2>&1; then
        echo "  - 运行 isort 导入排序检查..."
        isort --check-only --diff backend/ || {
            echo "❌ isort 检查失败，请运行: isort backend/"
            exit 1
        }
    fi

    # Flake8 代码质量检查
    if command -v flake8 >/dev/null 2>&1; then
        echo "  - 运行 Flake8 代码质量检查..."
        flake8 backend/ || {
            echo "❌ Flake8 检查失败，请修复代码质量问题"
            exit 1
        }
    fi
fi

# TypeScript/JavaScript 文件检查
if git diff --cached --name-only | grep -qE '\.(js|jsx|ts|tsx)$'; then
    echo "📱 检查 TypeScript/JavaScript 代码..."

    # ESLint 检查
    if [ -f "frontend/node_modules/.bin/eslint" ]; then
        echo "  - 运行 ESLint 检查..."
        cd frontend
        npx eslint $(git diff --cached --name-only --relative | grep -E '\.(js|jsx|ts|tsx)$' | tr '\n' ' ') || {
            echo "❌ ESLint 检查失败，请修复代码质量问题"
            exit 1
        }
        cd ..
    fi

    # Prettier 格式化检查
    if [ -f "frontend/node_modules/.bin/prettier" ]; then
        echo "  - 运行 Prettier 格式化检查..."
        cd frontend
        npx prettier --check $(git diff --cached --name-only --relative | grep -E '\.(js|jsx|ts|tsx|json|md)$' | tr '\n' ' ') || {
            echo "❌ Prettier 格式化检查失败，请运行: npx prettier --write ."
            exit 1
        }
        cd ..
    fi
fi

# 检查提交信息格式（如果有的话）
if [ -f ".gitmessage" ]; then
    echo "📝 提交信息将使用模板格式"
fi

echo "✅ 所有 pre-commit 检查通过！"
EOF

    chmod +x "$hook_file"
    echo -e "${GREEN}✅ pre-commit 钩子创建完成${NC}"
}

# ==========================================
# 创建 commit-msg 钩子
# ==========================================
create_commit_msg_hook() {
    echo -e "${YELLOW}💬 创建 commit-msg 钩子${NC}"

    hook_file=".git/hooks/commit-msg"

    cat > "$hook_file" << 'EOF'
#!/bin/bash

# 柴管家项目 commit-msg 钩子
# 验证提交信息格式

commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .{1,50}'

error_msg="
❌ 提交信息格式不正确！

正确格式: <类型>(<范围>): <描述>

类型包括:
  feat:     新功能
  fix:      修复bug
  docs:     文档更新
  style:    代码格式调整
  refactor: 重构代码
  test:     测试相关
  chore:    构建工具、依赖管理等
  perf:     性能优化
  ci:       CI/CD 相关
  build:    构建系统相关
  revert:   回滚提交

示例:
  feat(auth): 添加用户登录功能
  fix(db): 修复查询性能问题
  docs(api): 更新API文档
"

if ! grep -qE "$commit_regex" "$1"; then
    echo "$error_msg" >&2
    exit 1
fi

# 检查提交信息长度
if [ $(head -n 1 "$1" | wc -c) -gt 72 ]; then
    echo "❌ 提交信息第一行过长，请保持在72个字符以内" >&2
    exit 1
fi

echo "✅ 提交信息格式正确"
EOF

    chmod +x "$hook_file"
    echo -e "${GREEN}✅ commit-msg 钩子创建完成${NC}"
}

# ==========================================
# 配置忽略文件权限变更
# ==========================================
configure_file_permissions() {
    echo -e "${YELLOW}🔒 配置文件权限设置${NC}"

    # 忽略文件权限变更（在某些系统上很有用）
    git config core.filemode false

    # 配置换行符处理
    git config core.autocrlf input
    git config core.safecrlf true

    echo -e "${GREEN}✅ 文件权限配置完成${NC}"
}

# ==========================================
# 主执行函数
# ==========================================
main() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  柴管家 Git 环境配置脚本${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""

    # 检查是否在 Git 仓库中
    if ! git rev-parse --git-dir >/dev/null 2>&1; then
        echo -e "${RED}❌ 当前目录不是 Git 仓库${NC}"
        exit 1
    fi

    configure_git_user
    echo ""

    configure_commit_template
    echo ""

    configure_git_aliases
    echo ""

    configure_branch_strategy
    echo ""

    create_pre_commit_hook
    echo ""

    create_commit_msg_hook
    echo ""

    configure_file_permissions
    echo ""

    echo -e "${GREEN}🎉 Git 环境配置完成！${NC}"
    echo ""
    echo -e "${YELLOW}📋 接下来你可以:${NC}"
    echo "  • 使用 'git ci' 代替 'git commit'"
    echo "  • 使用 'git lg' 查看美化的提交历史"
    echo "  • 使用 'git sync' 同步远程分支"
    echo "  • 提交时会自动进行代码质量检查"
    echo "  • 提交信息格式会自动验证"
    echo ""
    echo -e "${BLUE}💡 提示: 使用模板格式提交信息可获得更好的项目管理体验${NC}"
}

# 执行主函数
main "$@"
