#!/bin/bash

# 柴管家容器化环境启动脚本
# 作者: 技术架构团队
# 版本: v1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "柴管家容器化环境管理脚本"
    echo ""
    echo "使用方式:"
    echo "  $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  dev       启动开发环境 (默认)"
    echo "  prod      启动生产环境"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  clean     清理容器和数据卷"
    echo "  setup     初始化环境配置"
    echo "  help      显示此帮助信息"
    echo ""
    echo "选项:"
    echo "  --force   强制执行操作"
    echo "  --quiet   静默模式"
    echo "  --build   重新构建镜像"
    echo ""
    echo "示例:"
    echo "  $0 dev              # 启动开发环境"
    echo "  $0 prod --build     # 重新构建并启动生产环境"
    echo "  $0 logs backend     # 查看后端服务日志"
    echo "  $0 clean --force    # 强制清理所有数据"
}

# 检查 Docker 和 Docker Compose
check_requirements() {
    log_info "检查运行环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi
    
    log_success "运行环境检查通过"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，将从 env.example 复制"
        if [ -f "env.example" ]; then
            cp env.example .env
            log_success "已创建 .env 文件，请根据需要修改配置"
        else
            log_error "env.example 文件不存在，请先创建环境配置"
            exit 1
        fi
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录结构..."
    
    directories=(
        "database/init"
        "database/seeds/development"
        "infrastructure/nginx/conf.d"
        "infrastructure/nginx/ssl"
        "infrastructure/redis"
        "infrastructure/rabbitmq"
        "infrastructure/elasticsearch"
        "logs"
        "backups"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
    done
    
    log_success "目录结构创建完成"
}

# 启动开发环境
start_development() {
    log_info "启动开发环境..."
    
    COMPOSE_FILES="-f docker-compose.yml -f docker-compose.override.yml"
    
    if [ "$BUILD" = true ]; then
        log_info "重新构建镜像..."
        docker-compose $COMPOSE_FILES build --parallel
    fi
    
    log_info "启动服务容器..."
    docker-compose $COMPOSE_FILES up -d
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务健康状态
    check_services_health
    
    log_success "开发环境启动完成！"
    show_service_urls
}

# 启动生产环境
start_production() {
    log_info "启动生产环境..."
    
    COMPOSE_FILES="-f docker-compose.yml -f docker-compose.prod.yml"
    
    if [ "$BUILD" = true ]; then
        log_info "重新构建镜像..."
        docker-compose $COMPOSE_FILES build --parallel
    fi
    
    log_info "启动服务容器..."
    docker-compose $COMPOSE_FILES up -d
    
    log_info "等待服务启动..."
    sleep 15
    
    # 检查服务健康状态
    check_services_health
    
    log_success "生产环境启动完成！"
}

# 检查服务健康状态
check_services_health() {
    log_info "检查服务健康状态..."
    
    services=("postgresql" "redis" "rabbitmq" "elasticsearch")
    
    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "$service.*Up.*healthy"; then
            log_success "$service 服务运行正常"
        elif docker-compose ps | grep -q "$service.*Up"; then
            log_warning "$service 服务正在启动中..."
        else
            log_error "$service 服务启动失败"
        fi
    done
}

# 显示服务访问地址
show_service_urls() {
    echo ""
    log_info "服务访问地址："
    echo "  前端应用:          http://localhost:3000"
    echo "  后端API:          http://localhost:8000"
    echo "  API文档:          http://localhost:8000/docs"
    echo "  PostgreSQL:       localhost:5432"
    echo "  Redis:            localhost:6379"
    echo "  RabbitMQ管理:     http://localhost:15672"
    echo "  Elasticsearch:    http://localhost:9200"
    echo "  pgAdmin:          http://localhost:5050"
    echo "  Redis Commander:  http://localhost:8081"
    echo "  MailHog:          http://localhost:8025"
    echo ""
    log_info "默认账号密码请查看 .env 文件"
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启所有服务..."
    docker-compose restart
    log_success "服务已重启"
}

# 查看服务状态
show_status() {
    log_info "服务运行状态："
    docker-compose ps
}

# 查看服务日志
show_logs() {
    if [ -n "$2" ]; then
        log_info "查看 $2 服务日志："
        docker-compose logs -f "$2"
    else
        log_info "查看所有服务日志："
        docker-compose logs -f
    fi
}

# 清理环境
clean_environment() {
    if [ "$FORCE" = true ]; then
        log_warning "强制清理所有容器和数据..."
        docker-compose down -v --remove-orphans
        docker system prune -f
        log_success "环境清理完成"
    else
        log_warning "此操作将删除所有容器和数据卷，请确认 (y/N):"
        read -r confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            docker-compose down -v --remove-orphans
            log_success "环境清理完成"
        else
            log_info "操作已取消"
        fi
    fi
}

# 初始化环境
setup_environment() {
    log_info "初始化环境配置..."
    
    check_requirements
    check_env_file
    create_directories
    
    log_success "环境初始化完成"
}

# 解析命令行参数
COMMAND=${1:-dev}
BUILD=false
FORCE=false
QUIET=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --build)
            BUILD=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --quiet)
            QUIET=true
            shift
            ;;
        *)
            shift
            ;;
    esac
done

# 静默模式处理
if [ "$QUIET" = true ]; then
    exec 1>/dev/null 2>&1
fi

# 主执行逻辑
case $COMMAND in
    dev|development)
        check_requirements
        check_env_file
        create_directories
        start_development
        ;;
    prod|production)
        check_requirements
        check_env_file
        create_directories
        start_production
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$@"
        ;;
    clean)
        clean_environment
        ;;
    setup)
        setup_environment
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "未知命令: $COMMAND"
        show_help
        exit 1
        ;;
esac
