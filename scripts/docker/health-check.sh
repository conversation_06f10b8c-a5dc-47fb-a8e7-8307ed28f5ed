#!/bin/bash
# 柴管家基础服务健康检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 全局变量
HEALTH_REPORT=""
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 添加检查结果
add_check_result() {
    local service=$1
    local status=$2
    local message=$3

    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

    if [ "$status" = "PASS" ]; then
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        HEALTH_REPORT="${HEALTH_REPORT}✅ ${service}: ${message}\n"
    else
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        HEALTH_REPORT="${HEALTH_REPORT}❌ ${service}: ${message}\n"
    fi
}

# 检查Docker Compose服务状态
check_compose_services() {
    log_info "检查Docker Compose服务状态..."

    local services=("postgresql" "redis" "rabbitmq" "elasticsearch" "backend" "frontend" "nginx")

    for service in "${services[@]}"; do
        if docker-compose ps "$service" | grep -q "Up"; then
            add_check_result "$service" "PASS" "服务运行中"
        else
            add_check_result "$service" "FAIL" "服务未运行"
        fi
    done
}

# 检查PostgreSQL健康状态
check_postgresql() {
    log_info "检查PostgreSQL数据库..."

    if docker-compose exec -T postgresql pg_isready -U admin -d chaiguanjia >/dev/null 2>&1; then
        # 检查数据库连接
        local db_result
        db_result=$(docker-compose exec -T postgresql psql -U admin -d chaiguanjia -c "SELECT 1;" 2>/dev/null | grep -c "1 row" || echo "0")

        if [ "$db_result" = "1" ]; then
            # 获取数据库大小
            local db_size
            db_size=$(docker-compose exec -T postgresql psql -U admin -d chaiguanjia -c "SELECT pg_size_pretty(pg_database_size('chaiguanjia'));" -t | tr -d ' ')
            add_check_result "PostgreSQL" "PASS" "数据库连接正常，大小: $db_size"
        else
            add_check_result "PostgreSQL" "FAIL" "数据库查询失败"
        fi
    else
        add_check_result "PostgreSQL" "FAIL" "数据库连接失败"
    fi
}

# 检查Redis健康状态
check_redis() {
    log_info "检查Redis缓存..."

    if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
        # 检查Redis内存使用
        local memory_info
        memory_info=$(docker-compose exec -T redis redis-cli info memory | grep "used_memory_human" | cut -d: -f2 | tr -d '\r')

        # 检查键数量
        local key_count
        key_count=$(docker-compose exec -T redis redis-cli dbsize | tr -d '\r')

        add_check_result "Redis" "PASS" "连接正常，内存使用: $memory_info，键数量: $key_count"
    else
        add_check_result "Redis" "FAIL" "Redis连接失败"
    fi
}

# 检查RabbitMQ健康状态
check_rabbitmq() {
    log_info "检查RabbitMQ消息队列..."

    if docker-compose exec -T rabbitmq rabbitmqctl node_health_check >/dev/null 2>&1; then
        # 检查队列状态
        local queue_info
        queue_info=$(docker-compose exec -T rabbitmq rabbitmqctl list_queues -p chaiguanjia --formatter=table 2>/dev/null | wc -l || echo "1")

        if [ "$queue_info" -gt 1 ]; then
            add_check_result "RabbitMQ" "PASS" "服务正常，队列数量: $((queue_info - 1))"
        else
            add_check_result "RabbitMQ" "PASS" "服务正常，无队列"
        fi
    else
        add_check_result "RabbitMQ" "FAIL" "RabbitMQ健康检查失败"
    fi
}

# 检查Elasticsearch健康状态
check_elasticsearch() {
    log_info "检查Elasticsearch搜索引擎..."

    local es_health
    es_health=$(curl -s http://localhost:9200/_cluster/health 2>/dev/null | jq -r '.status' 2>/dev/null || echo "unreachable")

    case "$es_health" in
        "green")
            add_check_result "Elasticsearch" "PASS" "集群状态: 绿色 (健康)"
            ;;
        "yellow")
            add_check_result "Elasticsearch" "PASS" "集群状态: 黄色 (可用但有警告)"
            ;;
        "red")
            add_check_result "Elasticsearch" "FAIL" "集群状态: 红色 (有问题)"
            ;;
        *)
            add_check_result "Elasticsearch" "FAIL" "服务不可达"
            ;;
    esac
}

# 检查后端API服务
check_backend_api() {
    log_info "检查后端API服务..."

    local api_response
    api_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health 2>/dev/null || echo "000")

    if [ "$api_response" = "200" ]; then
        # 获取API版本信息
        local api_info
        api_info=$(curl -s http://localhost:8000/health 2>/dev/null | jq -r '.version' 2>/dev/null || echo "unknown")
        add_check_result "Backend API" "PASS" "HTTP 200，版本: $api_info"
    else
        add_check_result "Backend API" "FAIL" "HTTP状态码: $api_response"
    fi
}

# 检查前端服务
check_frontend() {
    log_info "检查前端服务..."

    local frontend_response
    frontend_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null || echo "000")

    if [ "$frontend_response" = "200" ]; then
        add_check_result "Frontend" "PASS" "HTTP 200，前端服务可访问"
    else
        add_check_result "Frontend" "FAIL" "HTTP状态码: $frontend_response"
    fi
}

# 检查Nginx代理
check_nginx() {
    log_info "检查Nginx反向代理..."

    # 检查Nginx状态
    local nginx_response
    nginx_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:80/health 2>/dev/null || echo "000")

    if [ "$nginx_response" = "200" ]; then
        add_check_result "Nginx" "PASS" "HTTP 200，代理服务正常"
    else
        add_check_result "Nginx" "FAIL" "HTTP状态码: $nginx_response"
    fi

    # 检查Nginx配置
    if docker-compose exec -T nginx nginx -t >/dev/null 2>&1; then
        add_check_result "Nginx Config" "PASS" "配置文件语法正确"
    else
        add_check_result "Nginx Config" "FAIL" "配置文件语法错误"
    fi
}

# 检查网络连接
check_network_connectivity() {
    log_info "检查服务间网络连接..."

    # 检查后端到数据库的连接
    if docker-compose exec -T backend python -c "import psycopg2; psycopg2.connect('**************************************************/chaiguanjia').close()" >/dev/null 2>&1; then
        add_check_result "Backend-DB" "PASS" "后端到数据库连接正常"
    else
        add_check_result "Backend-DB" "FAIL" "后端到数据库连接失败"
    fi

    # 检查后端到Redis的连接
    if docker-compose exec -T backend python -c "import redis; redis.Redis(host='redis', port=6379, password='chaiguanjia2024').ping()" >/dev/null 2>&1; then
        add_check_result "Backend-Redis" "PASS" "后端到Redis连接正常"
    else
        add_check_result "Backend-Redis" "FAIL" "后端到Redis连接失败"
    fi
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."

    local disk_usage
    disk_usage=$(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')

    if [ "$disk_usage" -lt 80 ]; then
        add_check_result "Disk Space" "PASS" "磁盘使用率: ${disk_usage}%"
    elif [ "$disk_usage" -lt 90 ]; then
        add_check_result "Disk Space" "WARN" "磁盘使用率: ${disk_usage}% (警告)"
    else
        add_check_result "Disk Space" "FAIL" "磁盘使用率: ${disk_usage}% (严重)"
    fi
}

# 检查Docker资源使用
check_docker_resources() {
    log_info "检查Docker资源使用..."

    # 获取容器资源使用情况
    local container_stats
    container_stats=$(docker stats --no-stream --format "{{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" $(docker ps --filter "name=chaiguanjia" -q) 2>/dev/null)

    if [ -n "$container_stats" ]; then
        local high_cpu_containers
        high_cpu_containers=$(echo "$container_stats" | awk -F'\t' '$2+0 > 80 {print $1}' | wc -l)

        if [ "$high_cpu_containers" -eq 0 ]; then
            add_check_result "CPU Usage" "PASS" "所有容器CPU使用率正常"
        else
            add_check_result "CPU Usage" "WARN" "$high_cpu_containers 个容器CPU使用率过高"
        fi

        add_check_result "Container Stats" "PASS" "资源监控数据可用"
    else
        add_check_result "Container Stats" "FAIL" "无法获取容器资源数据"
    fi
}

# 生成健康报告
generate_health_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    echo ""
    echo "======================================="
    echo "        柴管家系统健康检查报告"
    echo "======================================="
    echo "检查时间: $timestamp"
    echo ""

    echo -e "$HEALTH_REPORT"

    echo "======================================="
    echo "检查汇总:"
    echo "  总检查项: $TOTAL_CHECKS"
    echo "  通过: $PASSED_CHECKS"
    echo "  失败: $FAILED_CHECKS"
    echo "  成功率: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%"
    echo "======================================="

    # 根据失败数量返回退出码
    if [ "$FAILED_CHECKS" -eq 0 ]; then
        log_success "所有健康检查通过！"
        return 0
    elif [ "$FAILED_CHECKS" -le 2 ]; then
        log_warning "存在少量问题，建议检查"
        return 1
    else
        log_error "存在多个严重问题，需要立即处理"
        return 2
    fi
}

# 保存健康报告到文件
save_health_report() {
    local report_dir="logs/health-checks"
    local report_file="$report_dir/health-report-$(date +%Y%m%d-%H%M%S).txt"

    mkdir -p "$report_dir"

    {
        echo "柴管家系统健康检查报告"
        echo "检查时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo ""
        echo -e "$HEALTH_REPORT"
        echo ""
        echo "检查汇总:"
        echo "  总检查项: $TOTAL_CHECKS"
        echo "  通过: $PASSED_CHECKS"
        echo "  失败: $FAILED_CHECKS"
        echo "  成功率: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%"
    } > "$report_file"

    log_info "健康报告已保存到: $report_file"
}

# 主函数
main() {
    echo ""
    log_info "开始系统健康检查..."
    echo ""

    # 检查所有组件
    check_compose_services
    check_postgresql
    check_redis
    check_rabbitmq
    check_elasticsearch
    check_backend_api
    check_frontend
    check_nginx
    check_network_connectivity
    check_disk_space
    check_docker_resources

    # 生成报告
    generate_health_report

    # 保存报告
    if [ "$1" = "--save" ] || [ "$1" = "-s" ]; then
        save_health_report
    fi
}

# 帮助信息
show_help() {
    echo "柴管家基础服务健康检查脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -s, --save      保存健康报告到文件"
    echo "  -h, --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0              # 执行健康检查"
    echo "  $0 --save       # 执行健康检查并保存报告"
    echo ""
}

# 处理命令行参数
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
