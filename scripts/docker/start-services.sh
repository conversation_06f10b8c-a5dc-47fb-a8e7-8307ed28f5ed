#!/bin/bash
# 柴管家基础服务启动脚本
# 使用国内镜像源优化的Docker Compose配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装和运行
check_docker() {
    log_info "检查Docker环境..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi

    log_success "Docker环境检查通过"
}

# 检查环境变量文件
check_env_file() {
    log_info "检查环境变量文件..."

    if [ ! -f ".env" ]; then
        log_warning ".env文件不存在，复制env.example到.env"
        cp env.example .env
        log_info "请检查.env文件中的配置并根据需要修改"
    fi

    log_success "环境变量文件检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."

    # 创建数据目录
    mkdir -p data/postgres
    mkdir -p data/redis
    mkdir -p data/rabbitmq
    mkdir -p data/elasticsearch
    mkdir -p logs/nginx
    mkdir -p logs/backend

    # 设置权限
    chmod 755 data/postgres
    chmod 755 data/redis
    chmod 755 data/rabbitmq
    chmod 755 data/elasticsearch
    chmod 755 logs/nginx
    chmod 755 logs/backend

    log_success "目录创建完成"
}

# 拉取Docker镜像（使用国内镜像源）
pull_images() {
    log_info "拉取Docker镜像（使用国内镜像源）..."

    # 配置Docker镜像源
    if [ ! -f /etc/docker/daemon.json ]; then
        log_info "配置Docker国内镜像源..."
        sudo mkdir -p /etc/docker
        sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
    "registry-mirrors": [
        "https://docker.mirrors.ustc.edu.cn",
        "https://hub-mirror.c.163.com",
        "https://mirror.ccs.tencentyun.com",
        "https://registry.docker-cn.com"
    ]
}
EOF
        log_warning "Docker镜像源配置已更新，请重启Docker服务：sudo systemctl restart docker"
    fi

    # 拉取基础镜像
    docker-compose pull

    log_success "Docker镜像拉取完成"
}

# 启动基础服务
start_basic_services() {
    log_info "启动基础数据服务..."

    # 先启动数据库相关服务
    docker-compose up -d postgresql redis rabbitmq elasticsearch

    log_info "等待服务启动..."
    sleep 30

    # 检查服务状态
    check_service_health "postgresql" 5432
    check_service_health "redis" 6379
    check_service_health "rabbitmq" 5672
    check_service_health "elasticsearch" 9200

    log_success "基础数据服务启动完成"
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1

    log_info "检查${service_name}服务健康状态..."

    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T $service_name sh -c "nc -z localhost $port" 2>/dev/null; then
            log_success "${service_name}服务健康检查通过"
            return 0
        fi

        log_info "等待${service_name}服务启动 (${attempt}/${max_attempts})..."
        sleep 5
        ((attempt++))
    done

    log_error "${service_name}服务健康检查失败"
    return 1
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."

    # 等待PostgreSQL完全启动
    sleep 10

    # 执行数据库初始化脚本
    if [ -f "database/init/01-init-database.sql" ]; then
        docker-compose exec -T postgresql psql -U admin -d chaiguanjia -f /docker-entrypoint-initdb.d/01-init-database.sql
        log_success "数据库初始化完成"
    else
        log_warning "数据库初始化脚本不存在"
    fi
}

# 启动应用服务
start_app_services() {
    log_info "启动应用服务..."

    # 启动后端服务
    docker-compose up -d backend
    sleep 20

    # 检查后端服务
    check_app_health "backend" 8000 "/health"

    # 启动前端服务
    docker-compose up -d frontend
    sleep 15

    # 启动Nginx
    docker-compose up -d nginx
    sleep 10

    # 检查服务状态
    check_app_health "frontend" 3000 "/"
    check_app_health "nginx" 80 "/health"

    log_success "应用服务启动完成"
}

# 检查应用健康状态
check_app_health() {
    local service_name=$1
    local port=$2
    local path=$3
    local max_attempts=20
    local attempt=1

    log_info "检查${service_name}应用健康状态..."

    while [ $attempt -le $max_attempts ]; do
        if curl -f "http://localhost:${port}${path}" &>/dev/null; then
            log_success "${service_name}应用健康检查通过"
            return 0
        fi

        log_info "等待${service_name}应用启动 (${attempt}/${max_attempts})..."
        sleep 5
        ((attempt++))
    done

    log_warning "${service_name}应用健康检查失败，但继续启动流程"
    return 1
}

# 显示服务状态
show_service_status() {
    log_info "显示服务状态..."

    echo ""
    echo "=== 柴管家服务状态 ==="
    docker-compose ps

    echo ""
    echo "=== 服务访问地址 ==="
    echo "前端应用: http://localhost:3000"
    echo "后端API: http://localhost:8000"
    echo "API文档: http://localhost:8000/docs"
    echo "Nginx代理: http://localhost:80"
    echo "RabbitMQ管理: http://localhost:15672 (admin/chaiguanjia2024)"
    echo "Elasticsearch: http://localhost:9200"
    echo ""

    echo "=== 数据库连接信息 ==="
    echo "PostgreSQL: localhost:5432/chaiguanjia (admin/chaiguanjia2024)"
    echo "Redis: localhost:6379 (密码: chaiguanjia2024)"
    echo ""
}

# 显示日志监控命令
show_log_commands() {
    echo "=== 日志监控命令 ==="
    echo "查看所有服务日志: docker-compose logs -f"
    echo "查看特定服务日志: docker-compose logs -f <service_name>"
    echo "查看PostgreSQL日志: docker-compose logs -f postgresql"
    echo "查看Redis日志: docker-compose logs -f redis"
    echo "查看RabbitMQ日志: docker-compose logs -f rabbitmq"
    echo "查看后端日志: docker-compose logs -f backend"
    echo "查看Nginx日志: docker-compose logs -f nginx"
    echo ""
}

# 主函数
main() {
    echo ""
    log_info "======================================="
    log_info "    柴管家基础服务启动脚本"
    log_info "======================================="
    echo ""

    # 检查环境
    check_docker
    check_env_file
    create_directories

    # 拉取镜像
    if [ "$1" = "--pull" ] || [ "$1" = "-p" ]; then
        pull_images
    fi

    # 启动服务
    start_basic_services

    # 初始化数据库
    if [ "$1" = "--init-db" ] || [ "$2" = "--init-db" ]; then
        init_database
    fi

    # 启动应用服务
    start_app_services

    # 显示状态
    show_service_status
    show_log_commands

    log_success "柴管家服务启动完成！"
    log_info "使用 'docker-compose logs -f' 查看服务日志"
    log_info "使用 'docker-compose down' 停止所有服务"
}

# 帮助信息
show_help() {
    echo "柴管家基础服务启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -p, --pull      拉取最新的Docker镜像"
    echo "  --init-db       初始化数据库"
    echo "  -h, --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0              # 启动所有服务"
    echo "  $0 --pull       # 拉取镜像并启动服务"
    echo "  $0 --init-db    # 启动服务并初始化数据库"
    echo ""
}

# 处理命令行参数
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
