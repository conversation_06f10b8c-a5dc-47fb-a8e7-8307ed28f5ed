#!/bin/bash
# 柴管家基础服务停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止应用服务
stop_app_services() {
    log_info "停止应用服务..."

    # 停止前端和Nginx
    docker-compose stop nginx frontend

    # 停止后端服务
    docker-compose stop backend

    log_success "应用服务已停止"
}

# 停止基础服务
stop_basic_services() {
    log_info "停止基础数据服务..."

    # 停止数据库服务
    docker-compose stop postgresql redis rabbitmq elasticsearch

    log_success "基础数据服务已停止"
}

# 完全停止所有服务
stop_all_services() {
    log_info "停止所有服务..."

    docker-compose down

    log_success "所有服务已停止"
}

# 强制停止并清理
force_stop() {
    log_warning "强制停止所有服务并清理容器..."

    # 强制停止所有容器
    docker-compose down --remove-orphans

    # 清理停止的容器
    docker container prune -f

    log_success "强制停止和清理完成"
}

# 停止并清理数据
clean_all() {
    log_warning "停止服务并清理所有数据..."
    echo "这将删除所有数据库数据、缓存数据等，是否确认？ (y/N)"
    read -r response

    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        # 停止所有服务并删除卷
        docker-compose down -v --remove-orphans

        # 清理Docker资源
        docker system prune -f

        # 删除数据目录
        if [ -d "data" ]; then
            rm -rf data
            log_info "已删除数据目录"
        fi

        log_success "数据清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 显示服务状态
show_status() {
    log_info "显示服务状态..."

    echo ""
    echo "=== Docker Compose 服务状态 ==="
    docker-compose ps

    echo ""
    echo "=== 运行中的容器 ==="
    docker ps --filter "name=chaiguanjia" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

    echo ""
    echo "=== 资源使用情况 ==="
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" $(docker ps --filter "name=chaiguanjia" -q)
}

# 备份数据
backup_data() {
    log_info "备份数据..."

    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"

    # 备份PostgreSQL
    if docker-compose ps postgresql | grep -q "Up"; then
        log_info "备份PostgreSQL数据..."
        docker-compose exec -T postgresql pg_dump -U admin chaiguanjia > "$backup_dir/postgres_backup.sql"
        log_success "PostgreSQL备份完成"
    fi

    # 备份Redis（如果有持久化数据）
    if docker-compose ps redis | grep -q "Up"; then
        log_info "备份Redis数据..."
        docker-compose exec -T redis redis-cli --rdb /data/dump.rdb
        docker cp $(docker-compose ps -q redis):/data/dump.rdb "$backup_dir/redis_dump.rdb"
        log_success "Redis备份完成"
    fi

    # 压缩备份
    tar -czf "backup_$(date +%Y%m%d_%H%M%S).tar.gz" -C backups "$(basename "$backup_dir")"
    rm -rf "$backup_dir"

    log_success "数据备份完成: backup_$(date +%Y%m%d_%H%M%S).tar.gz"
}

# 重启服务
restart_services() {
    log_info "重启服务..."

    # 先停止
    docker-compose down

    # 等待清理完成
    sleep 5

    # 重新启动
    docker-compose up -d

    log_success "服务重启完成"
}

# 查看服务日志
show_logs() {
    local service=${1:-""}

    if [ -n "$service" ]; then
        log_info "显示 $service 服务日志..."
        docker-compose logs -f "$service"
    else
        log_info "显示所有服务日志..."
        docker-compose logs -f
    fi
}

# 主函数
main() {
    echo ""
    log_info "======================================="
    log_info "    柴管家基础服务停止脚本"
    log_info "======================================="
    echo ""

    case "$1" in
        "app")
            stop_app_services
            ;;
        "basic"|"data")
            stop_basic_services
            ;;
        "all"|"")
            stop_all_services
            ;;
        "force")
            force_stop
            ;;
        "clean")
            clean_all
            ;;
        "status")
            show_status
            ;;
        "backup")
            backup_data
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs "$2"
            ;;
        *)
            show_help
            exit 1
            ;;
    esac
}

# 帮助信息
show_help() {
    echo "柴管家基础服务停止脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  app             只停止应用服务 (nginx, frontend, backend)"
    echo "  basic, data     只停止基础数据服务 (postgresql, redis, rabbitmq, elasticsearch)"
    echo "  all             停止所有服务 (默认)"
    echo "  force           强制停止并清理容器"
    echo "  clean           停止服务并清理所有数据 (危险操作)"
    echo "  status          显示服务状态"
    echo "  backup          备份数据"
    echo "  restart         重启所有服务"
    echo "  logs [service]  查看服务日志"
    echo ""
    echo "示例:"
    echo "  $0              # 停止所有服务"
    echo "  $0 app          # 只停止应用服务"
    echo "  $0 force        # 强制停止并清理"
    echo "  $0 backup       # 备份数据"
    echo "  $0 logs backend # 查看后端服务日志"
    echo ""
}

# 处理命令行参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

main "$@"
