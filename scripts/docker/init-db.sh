#!/bin/bash

# 数据库初始化脚本
# 创建数据库、用户和基础表结构

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${YELLOW}[DB-INIT]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[DB-INIT]${NC} $1"
}

log_error() {
    echo -e "${RED}[DB-INIT]${NC} $1"
}

# 检查环境变量
if [ -z "$POSTGRES_DB" ] || [ -z "$POSTGRES_USER" ] || [ -z "$POSTGRES_PASSWORD" ]; then
    log_error "缺少必要的环境变量"
    exit 1
fi

log_info "开始初始化数据库..."

# 等待 PostgreSQL 服务启动
log_info "等待 PostgreSQL 服务启动..."
until docker-compose exec postgresql pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB"; do
    sleep 2
done

log_success "PostgreSQL 服务已启动"

# 执行数据库初始化
log_info "执行数据库迁移..."
docker-compose exec backend python -m alembic upgrade head

log_info "创建默认用户和权限..."
docker-compose exec backend python -c "
from app.core.database import get_db
from app.modules.user_management.services.user_service import create_default_admin
from sqlalchemy.orm import Session

db = next(get_db())
try:
    create_default_admin(db)
    print('默认管理员用户创建成功')
except Exception as e:
    print(f'创建默认用户失败: {e}')
finally:
    db.close()
"

# 如果是开发环境，加载测试数据
if [ "$ENVIRONMENT" = "development" ]; then
    log_info "加载开发环境测试数据..."
    docker-compose exec backend python -c "
from app.scripts.seed_dev_data import seed_development_data
seed_development_data()
print('开发数据加载完成')
"
fi

log_success "数据库初始化完成"
