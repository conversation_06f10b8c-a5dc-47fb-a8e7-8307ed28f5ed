#!/bin/bash
# 柴管家环境验证脚本 - Task I-1.4
# 进行全面的环境连通性测试和基础性能测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置参数
TIMEOUT=30
RETRY_COUNT=3
REPORT_FILE="./environment-validation-report-$(date +%Y%m%d_%H%M%S).md"

# 服务端口配置
POSTGRES_PORT=5432
REDIS_PORT=6379
RABBITMQ_PORT=5672
RABBITMQ_MGMT_PORT=15672
ELASTICSEARCH_PORT=9200
BACKEND_PORT=8000
FRONTEND_PORT=3000
NGINX_PORT=80

# 验收结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
declare -a FAILED_ITEMS=()

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "**INFO:** $1" >> "$REPORT_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    echo "✅ **SUCCESS:** $1" >> "$REPORT_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "⚠️ **WARNING:** $1" >> "$REPORT_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    echo "❌ **ERROR:** $1" >> "$REPORT_FILE"
}

log_section() {
    echo -e "${CYAN}[SECTION]${NC} $1"
    echo "" >> "$REPORT_FILE"
    echo "## $1" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
}

# 验收标准检查函数
check_ac() {
    local test_name="$1"
    local test_func="$2"

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_info "验收测试: $test_name"

    if $test_func; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "$test_name"
        return 0
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        FAILED_ITEMS+=("$test_name")
        log_error "$test_name"
        return 1
    fi
}

# 初始化报告文件
init_report() {
    cat > "$REPORT_FILE" << EOF
# 柴管家环境验证测试报告

**测试时间:** $(date '+%Y-%m-%d %H:%M:%S')
**测试版本:** Task I-1.4
**测试环境:** $(uname -s) $(uname -r)

EOF
}

# 1. 环境连通性测试
test_service_connectivity() {
    log_section "环境连通性测试"

    # AC1: 所有服务间连通性测试通过
    check_ac "PostgreSQL数据库连接测试" "test_postgres_connection"
    check_ac "Redis缓存连接测试" "test_redis_connection"
    check_ac "RabbitMQ消息队列连接测试" "test_rabbitmq_connection"
    check_ac "Elasticsearch搜索引擎连接测试" "test_elasticsearch_connection"
    check_ac "Backend API服务连接测试" "test_backend_connection"
    check_ac "Frontend前端服务连接测试" "test_frontend_connection"
    check_ac "Nginx代理服务连接测试" "test_nginx_connection"
    check_ac "服务间网络互通测试" "test_service_network"
}

# PostgreSQL连接测试
test_postgres_connection() {
    if docker exec chaiguanjia_postgresql pg_isready -U admin -d chaiguanjia >/dev/null 2>&1; then
        local version=$(docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -t -c "SELECT version();" 2>/dev/null | head -1)
        log_info "PostgreSQL版本: ${version// /}"

        # 测试中文支持
        local encoding=$(docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -t -c "SHOW server_encoding;" 2>/dev/null | tr -d ' ')
        if [[ "$encoding" == "UTF8" ]]; then
            log_info "数据库编码: UTF8 (支持中文)"
            return 0
        else
            log_warning "数据库编码: $encoding (可能不支持中文)"
            return 1
        fi
    else
        return 1
    fi
}

# Redis连接测试
test_redis_connection() {
    if docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 ping >/dev/null 2>&1; then
        local info=$(docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 info server 2>/dev/null | grep redis_version)
        log_info "Redis版本: ${info#*:}"

        # 测试读写性能
        local start_time=$(date +%s%N)
        docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 set test_key "test_value" >/dev/null 2>&1
        docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 get test_key >/dev/null 2>&1
        docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 del test_key >/dev/null 2>&1
        local end_time=$(date +%s%N)
        local duration=$(( (end_time - start_time) / 1000000 ))

        log_info "Redis读写响应时间: ${duration}ms"
        if [[ $duration -lt 50 ]]; then
            return 0
        else
            log_warning "Redis响应时间过长: ${duration}ms"
            return 1
        fi
    else
        return 1
    fi
}

# RabbitMQ连接测试
test_rabbitmq_connection() {
    if docker exec chaiguanjia_rabbitmq rabbitmqctl node_health_check >/dev/null 2>&1; then
        local version=$(docker exec chaiguanjia_rabbitmq rabbitmqctl version 2>/dev/null | head -1)
        log_info "RabbitMQ版本: $version"

        # 测试管理界面连接
        if curl -s -f "http://localhost:$RABBITMQ_MGMT_PORT" >/dev/null 2>&1; then
            log_info "RabbitMQ管理界面可访问"
            return 0
        else
            log_warning "RabbitMQ管理界面无法访问"
            return 1
        fi
    else
        return 1
    fi
}

# Elasticsearch连接测试
test_elasticsearch_connection() {
    if curl -s -f "http://localhost:$ELASTICSEARCH_PORT/_cluster/health" >/dev/null 2>&1; then
        local health=$(curl -s "http://localhost:$ELASTICSEARCH_PORT/_cluster/health" | python3 -c "import sys, json; print(json.load(sys.stdin)['status'])" 2>/dev/null)
        local version=$(curl -s "http://localhost:$ELASTICSEARCH_PORT" | python3 -c "import sys, json; print(json.load(sys.stdin)['version']['number'])" 2>/dev/null)

        log_info "Elasticsearch版本: $version"
        log_info "集群状态: $health"

        if [[ "$health" == "green" || "$health" == "yellow" ]]; then
            return 0
        else
            log_warning "Elasticsearch集群状态异常: $health"
            return 1
        fi
    else
        return 1
    fi
}

# Backend API连接测试
test_backend_connection() {
    if curl -s -f "http://localhost:$BACKEND_PORT/health" >/dev/null 2>&1; then
        local response=$(curl -s "http://localhost:$BACKEND_PORT/health")
        log_info "Backend健康检查响应: $response"

        # 测试API响应时间
        local start_time=$(date +%s%N)
        curl -s "http://localhost:$BACKEND_PORT/health" >/dev/null 2>&1
        local end_time=$(date +%s%N)
        local duration=$(( (end_time - start_time) / 1000000 ))

        log_info "Backend API响应时间: ${duration}ms"
        if [[ $duration -lt 500 ]]; then
            return 0
        else
            log_warning "Backend API响应时间过长: ${duration}ms"
            return 1
        fi
    else
        return 1
    fi
}

# Frontend连接测试
test_frontend_connection() {
    if curl -s -f "http://localhost:$FRONTEND_PORT" >/dev/null 2>&1; then
        log_info "Frontend服务可访问"
        return 0
    else
        return 1
    fi
}

# Nginx连接测试
test_nginx_connection() {
    if curl -s -f "http://localhost:$NGINX_PORT" >/dev/null 2>&1; then
        log_info "Nginx代理服务可访问"

        # 测试配置有效性
        if docker exec chaiguanjia_nginx nginx -t >/dev/null 2>&1; then
            log_info "Nginx配置有效"
            return 0
        else
            log_warning "Nginx配置可能有问题"
            return 1
        fi
    else
        return 1
    fi
}

# 服务间网络测试
test_service_network() {
    # 测试Backend到数据库的连接
    if docker exec chaiguanjia_backend python3 -c "
import psycopg2
try:
    conn = psycopg2.connect('**************************************************/chaiguanjia')
    conn.close()
    print('Backend->PostgreSQL: OK')
except Exception as e:
    print(f'Backend->PostgreSQL: FAILED - {e}')
    exit(1)
" 2>/dev/null; then
        log_info "Backend到PostgreSQL连接正常"
    else
        log_warning "Backend到PostgreSQL连接异常"
        return 1
    fi

    # 测试Backend到Redis的连接
    if docker exec chaiguanjia_backend python3 -c "
import redis
try:
    r = redis.Redis(host='redis', port=6379, password='chaiguanjia2024', db=0)
    r.ping()
    print('Backend->Redis: OK')
except Exception as e:
    print(f'Backend->Redis: FAILED - {e}')
    exit(1)
" 2>/dev/null; then
        log_info "Backend到Redis连接正常"
        return 0
    else
        log_warning "Backend到Redis连接异常"
        return 1
    fi
}

# 2. 基础性能测试
test_performance() {
    log_section "基础性能测试"

    # AC2: 基础性能指标达到预期要求
    check_ac "API响应时间性能测试" "test_api_performance"
    check_ac "数据库查询性能测试" "test_database_performance"
    check_ac "缓存读写性能测试" "test_cache_performance"
    check_ac "系统资源使用测试" "test_resource_usage"
}

# API性能测试
test_api_performance() {
    log_info "进行API性能测试..."

    # 使用ab进行简单的并发测试
    if command -v ab >/dev/null 2>&1; then
        local result=$(ab -n 100 -c 10 "http://localhost:$BACKEND_PORT/health" 2>/dev/null | grep "Time per request")
        if [[ -n "$result" ]]; then
            log_info "API性能测试结果: $result"
            local avg_time=$(echo "$result" | head -1 | awk '{print $4}')
            if (( $(echo "$avg_time < 500" | bc -l) )); then
                return 0
            else
                log_warning "API平均响应时间过长: ${avg_time}ms"
                return 1
            fi
        else
            log_warning "无法获取API性能测试结果"
            return 1
        fi
    else
        log_warning "Apache Bench (ab)未安装，跳过详细性能测试"
        return 0
    fi
}

# 数据库性能测试
test_database_performance() {
    # 简单的查询性能测试
    local start_time=$(date +%s%N)
    docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c "SELECT COUNT(*) FROM information_schema.tables;" >/dev/null 2>&1
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 ))

    log_info "数据库查询响应时间: ${duration}ms"
    if [[ $duration -lt 100 ]]; then
        return 0
    else
        log_warning "数据库查询响应时间过长: ${duration}ms"
        return 1
    fi
}

# 缓存性能测试
test_cache_performance() {
    # 批量读写测试
    local start_time=$(date +%s%N)
    for i in {1..10}; do
        docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 set "test_key_$i" "test_value_$i" >/dev/null 2>&1
        docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 get "test_key_$i" >/dev/null 2>&1
        docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 del "test_key_$i" >/dev/null 2>&1
    done
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 ))

    log_info "缓存批量操作响应时间: ${duration}ms (10次读写)"
    if [[ $duration -lt 500 ]]; then
        return 0
    else
        log_warning "缓存操作响应时间过长: ${duration}ms"
        return 1
    fi
}

# 系统资源使用测试
test_resource_usage() {
    log_info "检查系统资源使用情况..."

    # 检查容器资源使用
    local stats=$(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep chaiguanjia)

    echo "| 容器名称 | CPU使用率 | 内存使用 |" >> "$REPORT_FILE"
    echo "|----------|-----------|----------|" >> "$REPORT_FILE"

    while IFS= read -r line; do
        if [[ -n "$line" && "$line" != *"CONTAINER"* ]]; then
            echo "| $line |" >> "$REPORT_FILE"
            log_info "容器资源: $line"
        fi
    done <<< "$stats"

    # 检查磁盘使用
    local disk_usage=$(df -h / | tail -1 | awk '{print $5}' | tr -d '%')
    log_info "磁盘使用率: ${disk_usage}%"

    if [[ $disk_usage -lt 90 ]]; then
        return 0
    else
        log_warning "磁盘使用率过高: ${disk_usage}%"
        return 1
    fi
}

# 3. 故障恢复测试
test_fault_recovery() {
    log_section "故障恢复测试"

    # AC5: 环境故障恢复流程测试通过
    check_ac "服务重启恢复测试" "test_service_restart"
    check_ac "数据持久化验证测试" "test_data_persistence"
}

# 服务重启测试
test_service_restart() {
    log_info "测试服务重启恢复..."

    # 重启Redis服务测试
    docker restart chaiguanjia_redis >/dev/null 2>&1
    sleep 10

    # 检查服务是否正常恢复
    local retry=0
    while [[ $retry -lt $RETRY_COUNT ]]; do
        if docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 ping >/dev/null 2>&1; then
            log_info "Redis服务重启后恢复正常"
            return 0
        fi
        retry=$((retry + 1))
        sleep 5
    done

    log_warning "Redis服务重启后未能正常恢复"
    return 1
}

# 数据持久化测试
test_data_persistence() {
    # 写入测试数据
    docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 set persistence_test "data_persistence_test_value" >/dev/null 2>&1

    # 重启服务
    docker restart chaiguanjia_redis >/dev/null 2>&1
    sleep 10

    # 验证数据是否持久化
    local retry=0
    while [[ $retry -lt $RETRY_COUNT ]]; do
        if docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 get persistence_test 2>/dev/null | grep -q "data_persistence_test_value"; then
            log_info "数据持久化验证成功"
            docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 del persistence_test >/dev/null 2>&1
            return 0
        fi
        retry=$((retry + 1))
        sleep 5
    done

    log_warning "数据持久化验证失败"
    return 1
}

# 生成测试报告
generate_report() {
    log_section "测试总结"

    local success_rate=$(( PASSED_TESTS * 100 / TOTAL_TESTS ))

    cat >> "$REPORT_FILE" << EOF

## 测试结果统计

- **总测试项:** $TOTAL_TESTS
- **通过测试:** $PASSED_TESTS
- **失败测试:** $FAILED_TESTS
- **成功率:** $success_rate%

EOF

    if [[ $FAILED_TESTS -gt 0 ]]; then
        echo "## 失败项目详情" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        for item in "${FAILED_ITEMS[@]}"; do
            echo "- ❌ $item" >> "$REPORT_FILE"
        done
        echo "" >> "$REPORT_FILE"
    fi

    cat >> "$REPORT_FILE" << EOF
## 验收标准检查

| 验收标准 | 状态 | 说明 |
|----------|------|------|
| AC1: 所有服务间连通性测试通过 | $(if [[ $PASSED_TESTS -ge 7 ]]; then echo "✅ 通过"; else echo "❌ 失败"; fi) | 基础服务连接测试 |
| AC2: 基础性能指标达到预期要求 | $(if [[ $success_rate -ge 80 ]]; then echo "✅ 通过"; else echo "❌ 失败"; fi) | 性能指标测试 |
| AC3: 环境文档完整，操作步骤清晰 | ⏳ 待完成 | 需要编写环境使用文档 |
| AC4: 团队成员都能独立操作环境 | ⏳ 待完成 | 需要进行团队培训 |
| AC5: 环境故障恢复流程测试通过 | $(if [[ $FAILED_TESTS -eq 0 ]]; then echo "✅ 通过"; else echo "❌ 失败"; fi) | 故障恢复测试 |

## 建议和改进

1. 如有测试失败，请检查对应服务的配置和日志
2. 定期执行此验证脚本，确保环境稳定性
3. 在生产环境部署前，建议增加更详细的性能测试
4. 建立监控告警机制，实时监控服务状态

---
*报告生成时间: $(date '+%Y-%m-%d %H:%M:%S')*
EOF

    echo ""
    echo "===== 柴管家环境验证测试完成 ====="
    echo "总测试项: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $success_rate%"
    echo ""
    echo "详细报告已保存至: $REPORT_FILE"

    if [[ $FAILED_TESTS -gt 0 ]]; then
        echo ""
        log_error "以下测试项失败:"
        for item in "${FAILED_ITEMS[@]}"; do
            echo "  - $item"
        done
        return 1
    else
        log_success "所有测试项均通过!"
        return 0
    fi
}

# 主函数
main() {
    echo "===== 柴管家环境验证测试开始 ====="
    echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""

    # 初始化报告
    init_report

    # 检查Docker环境
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker未安装或不可用"
        exit 1
    fi

    # 检查服务是否启动
    log_info "检查Docker服务状态..."
    if ! docker-compose ps | grep -q "Up"; then
        log_error "Docker服务未启动，请先运行: docker-compose up -d"
        exit 1
    fi

    # 执行测试
    test_service_connectivity
    test_performance
    test_fault_recovery

    # 生成报告
    generate_report
}

# 执行主函数
main "$@"
