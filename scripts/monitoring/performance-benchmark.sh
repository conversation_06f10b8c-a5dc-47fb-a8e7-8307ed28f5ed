#!/bin/bash
# 柴管家基础性能基准测试脚本
# 为 Task I-1.4 提供详细的性能基准测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置参数
REPORT_FILE="./performance-benchmark-$(date +%Y%m%d_%H%M%S).md"
TEST_DURATION=60  # 测试持续时间（秒）
CONCURRENT_USERS=50  # 并发用户数
API_REQUESTS=1000   # API请求总数
DB_QUERIES=100      # 数据库查询次数

# 目标性能指标
TARGET_API_RESPONSE=500    # API响应时间目标（ms）
TARGET_DB_QUERY=100        # 数据库查询目标（ms）
TARGET_CACHE_RESPONSE=10   # 缓存响应目标（ms）
TARGET_CPU_USAGE=70        # CPU使用率目标（%）
TARGET_MEMORY_USAGE=80     # 内存使用率目标（%）

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "**INFO:** $1" >> "$REPORT_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    echo "✅ **SUCCESS:** $1" >> "$REPORT_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "⚠️ **WARNING:** $1" >> "$REPORT_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    echo "❌ **ERROR:** $1" >> "$REPORT_FILE"
}

log_section() {
    echo -e "${CYAN}[SECTION]${NC} $1"
    echo "" >> "$REPORT_FILE"
    echo "## $1" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
}

# 初始化报告
init_report() {
    cat > "$REPORT_FILE" << EOF
# 柴管家基础性能基准测试报告

**测试时间:** $(date '+%Y-%m-%d %H:%M:%S')
**测试版本:** Task I-1.4
**测试环境:** $(uname -s) $(uname -r)
**测试配置:**
- 并发用户数: $CONCURRENT_USERS
- API请求总数: $API_REQUESTS
- 数据库查询次数: $DB_QUERIES
- 测试持续时间: ${TEST_DURATION}秒

EOF
}

# 检查测试工具
check_tools() {
    local tools=("curl" "ab" "wrk" "bc")
    local missing_tools=()

    for tool in "${tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_warning "以下工具未安装，将使用基础测试: ${missing_tools[*]}"

        # 在国内镜像源安装缺失工具
        if command -v apt-get >/dev/null 2>&1; then
            log_info "尝试安装缺失的测试工具..."
            for tool in "${missing_tools[@]}"; do
                case $tool in
                    "ab")
                        sudo apt-get update && sudo apt-get install -y apache2-utils
                        ;;
                    "wrk")
                        sudo apt-get update && sudo apt-get install -y wrk
                        ;;
                    "bc")
                        sudo apt-get update && sudo apt-get install -y bc
                        ;;
                esac
            done
        elif command -v brew >/dev/null 2>&1; then
            log_info "尝试使用Homebrew安装缺失工具..."
            for tool in "${missing_tools[@]}"; do
                case $tool in
                    "ab")
                        brew install httpd
                        ;;
                    "wrk")
                        brew install wrk
                        ;;
                    "bc")
                        brew install bc
                        ;;
                esac
            done
        fi
    fi
}

# API性能基准测试
test_api_performance() {
    log_section "API性能基准测试"

    local api_url="http://localhost:8000/health"

    # 使用Apache Bench进行测试
    if command -v ab >/dev/null 2>&1; then
        log_info "使用Apache Bench进行API压力测试..."

        local ab_result=$(ab -n $API_REQUESTS -c $CONCURRENT_USERS "$api_url" 2>/dev/null)

        # 解析结果
        local requests_per_sec=$(echo "$ab_result" | grep "Requests per second" | awk '{print $4}')
        local time_per_request=$(echo "$ab_result" | grep "Time per request" | head -1 | awk '{print $4}')
        local failed_requests=$(echo "$ab_result" | grep "Failed requests" | awk '{print $3}')
        local transfer_rate=$(echo "$ab_result" | grep "Transfer rate" | awk '{print $3}')

        cat >> "$REPORT_FILE" << EOF
### Apache Bench 测试结果

| 指标 | 值 | 目标 | 状态 |
|------|----|----- |----- |
| 每秒请求数 | ${requests_per_sec} req/sec | > 100 req/sec | $(if (( $(echo "$requests_per_sec > 100" | bc -l) )); then echo "✅ 通过"; else echo "❌ 未达标"; fi) |
| 平均响应时间 | ${time_per_request} ms | < ${TARGET_API_RESPONSE} ms | $(if (( $(echo "$time_per_request < $TARGET_API_RESPONSE" | bc -l) )); then echo "✅ 通过"; else echo "❌ 未达标"; fi) |
| 失败请求数 | ${failed_requests} | 0 | $(if [[ "$failed_requests" == "0" ]]; then echo "✅ 通过"; else echo "❌ 有失败"; fi) |
| 传输速率 | ${transfer_rate} KB/sec | > 50 KB/sec | $(if (( $(echo "$transfer_rate > 50" | bc -l) )); then echo "✅ 通过"; else echo "❌ 未达标"; fi) |

EOF

        log_info "API性能测试完成 - 每秒请求数: $requests_per_sec, 平均响应时间: ${time_per_request}ms"

    # 使用wrk进行高级测试
    elif command -v wrk >/dev/null 2>&1; then
        log_info "使用wrk进行API压力测试..."

        local wrk_result=$(wrk -t12 -c$CONCURRENT_USERS -d${TEST_DURATION}s "$api_url" 2>/dev/null)

        local requests_per_sec=$(echo "$wrk_result" | grep "Requests/sec" | awk '{print $2}')
        local avg_latency=$(echo "$wrk_result" | grep "Latency" | awk '{print $2}')

        cat >> "$REPORT_FILE" << EOF
### wrk 测试结果

| 指标 | 值 |
|------|----|
| 每秒请求数 | ${requests_per_sec} |
| 平均延迟 | ${avg_latency} |

EOF

        log_info "API性能测试完成 - 每秒请求数: $requests_per_sec, 平均延迟: $avg_latency"

    else
        # 基础curl测试
        log_info "使用curl进行基础API测试..."

        local total_time=0
        local successful_requests=0

        for ((i=1; i<=10; i++)); do
            local start_time=$(date +%s%N)
            if curl -s -f "$api_url" >/dev/null 2>&1; then
                local end_time=$(date +%s%N)
                local duration=$(( (end_time - start_time) / 1000000 ))
                total_time=$((total_time + duration))
                successful_requests=$((successful_requests + 1))
            fi
        done

        local avg_response_time=$((total_time / successful_requests))

        cat >> "$REPORT_FILE" << EOF
### 基础 curl 测试结果

| 指标 | 值 | 目标 | 状态 |
|------|----|----- |----- |
| 成功请求数 | ${successful_requests}/10 | 10/10 | $(if [[ $successful_requests -eq 10 ]]; then echo "✅ 通过"; else echo "❌ 未达标"; fi) |
| 平均响应时间 | ${avg_response_time} ms | < ${TARGET_API_RESPONSE} ms | $(if [[ $avg_response_time -lt $TARGET_API_RESPONSE ]]; then echo "✅ 通过"; else echo "❌ 未达标"; fi) |

EOF

        log_info "基础API测试完成 - 成功率: ${successful_requests}/10, 平均响应时间: ${avg_response_time}ms"
    fi
}

# 数据库性能基准测试
test_database_performance() {
    log_section "数据库性能基准测试"

    log_info "执行数据库性能测试..."

    # 创建测试表
    docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c "
        DROP TABLE IF EXISTS performance_test;
        CREATE TABLE performance_test (
            id SERIAL PRIMARY KEY,
            data TEXT,
            created_at TIMESTAMP DEFAULT NOW()
        );
    " >/dev/null 2>&1

    # 插入测试数据
    log_info "插入测试数据..."
    local insert_start=$(date +%s%N)
    for ((i=1; i<=1000; i++)); do
        docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c "
            INSERT INTO performance_test (data) VALUES ('test_data_$i');
        " >/dev/null 2>&1
    done
    local insert_end=$(date +%s%N)
    local insert_duration=$(( (insert_end - insert_start) / 1000000 ))

    # 查询测试
    log_info "执行查询测试..."
    local query_times=()

    for ((i=1; i<=10; i++)); do
        local query_start=$(date +%s%N)
        docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c "
            SELECT COUNT(*) FROM performance_test WHERE id <= $((i*100));
        " >/dev/null 2>&1
        local query_end=$(date +%s%N)
        local query_duration=$(( (query_end - query_start) / 1000000 ))
        query_times+=($query_duration)
    done

    # 计算平均查询时间
    local total_query_time=0
    for time in "${query_times[@]}"; do
        total_query_time=$((total_query_time + time))
    done
    local avg_query_time=$((total_query_time / ${#query_times[@]}))

    # 复杂查询测试
    log_info "执行复杂查询测试..."
    local complex_start=$(date +%s%N)
    docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c "
        SELECT
            DATE_TRUNC('hour', created_at) as hour,
            COUNT(*) as count,
            AVG(id) as avg_id
        FROM performance_test
        GROUP BY DATE_TRUNC('hour', created_at)
        ORDER BY hour;
    " >/dev/null 2>&1
    local complex_end=$(date +%s%N)
    local complex_duration=$(( (complex_end - complex_start) / 1000000 ))

    cat >> "$REPORT_FILE" << EOF
### 数据库性能测试结果

| 测试项 | 值 | 目标 | 状态 |
|--------|----|----- |----- |
| 1000条记录插入时间 | ${insert_duration} ms | < 5000 ms | $(if [[ $insert_duration -lt 5000 ]]; then echo "✅ 通过"; else echo "❌ 未达标"; fi) |
| 平均简单查询时间 | ${avg_query_time} ms | < ${TARGET_DB_QUERY} ms | $(if [[ $avg_query_time -lt $TARGET_DB_QUERY ]]; then echo "✅ 通过"; else echo "❌ 未达标"; fi) |
| 复杂查询时间 | ${complex_duration} ms | < 1000 ms | $(if [[ $complex_duration -lt 1000 ]]; then echo "✅ 通过"; else echo "❌ 未达标"; fi) |

EOF

    # 清理测试数据
    docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c "DROP TABLE performance_test;" >/dev/null 2>&1

    log_info "数据库性能测试完成"
}

# 缓存性能基准测试
test_cache_performance() {
    log_section "缓存性能基准测试"

    log_info "执行Redis缓存性能测试..."

    # 单次操作性能测试
    local set_times=()
    local get_times=()

    for ((i=1; i<=100; i++)); do
        # SET操作
        local set_start=$(date +%s%N)
        docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 set "perf_test_$i" "performance_test_value_$i" >/dev/null 2>&1
        local set_end=$(date +%s%N)
        local set_duration=$(( (set_end - set_start) / 1000000 ))
        set_times+=($set_duration)

        # GET操作
        local get_start=$(date +%s%N)
        docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 get "perf_test_$i" >/dev/null 2>&1
        local get_end=$(date +%s%N)
        local get_duration=$(( (get_end - get_start) / 1000000 ))
        get_times+=($get_duration)
    done

    # 计算平均时间
    local total_set_time=0
    local total_get_time=0

    for time in "${set_times[@]}"; do
        total_set_time=$((total_set_time + time))
    done

    for time in "${get_times[@]}"; do
        total_get_time=$((total_get_time + time))
    done

    local avg_set_time=$((total_set_time / ${#set_times[@]}))
    local avg_get_time=$((total_get_time / ${#get_times[@]}))

    # 批量操作测试
    log_info "执行批量操作测试..."
    local batch_start=$(date +%s%N)

    # 创建批量命令
    local batch_commands=""
    for ((i=1; i<=1000; i++)); do
        batch_commands+="set batch_test_$i batch_value_$i\n"
    done

    echo -e "$batch_commands" | docker exec -i chaiguanjia_redis redis-cli -a chaiguanjia2024 --pipe >/dev/null 2>&1

    local batch_end=$(date +%s%N)
    local batch_duration=$(( (batch_end - batch_start) / 1000000 ))

    # 测试连接池性能
    log_info "测试并发连接性能..."
    local concurrent_start=$(date +%s%N)

    for ((i=1; i<=10; i++)); do
        {
            docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 set "concurrent_test_$i" "concurrent_value_$i" >/dev/null 2>&1
            docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 get "concurrent_test_$i" >/dev/null 2>&1
        } &
    done
    wait

    local concurrent_end=$(date +%s%N)
    local concurrent_duration=$(( (concurrent_end - concurrent_start) / 1000000 ))

    cat >> "$REPORT_FILE" << EOF
### 缓存性能测试结果

| 测试项 | 值 | 目标 | 状态 |
|--------|----|----- |----- |
| 平均SET操作时间 | ${avg_set_time} ms | < ${TARGET_CACHE_RESPONSE} ms | $(if [[ $avg_set_time -lt $TARGET_CACHE_RESPONSE ]]; then echo "✅ 通过"; else echo "❌ 未达标"; fi) |
| 平均GET操作时间 | ${avg_get_time} ms | < ${TARGET_CACHE_RESPONSE} ms | $(if [[ $avg_get_time -lt $TARGET_CACHE_RESPONSE ]]; then echo "✅ 通过"; else echo "❌ 未达标"; fi) |
| 1000条批量写入时间 | ${batch_duration} ms | < 1000 ms | $(if [[ $batch_duration -lt 1000 ]]; then echo "✅ 通过"; else echo "❌ 未达标"; fi) |
| 10个并发操作时间 | ${concurrent_duration} ms | < 500 ms | $(if [[ $concurrent_duration -lt 500 ]]; then echo "✅ 通过"; else echo "❌ 未达标"; fi) |

EOF

    # 清理测试数据
    for ((i=1; i<=100; i++)); do
        docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 del "perf_test_$i" >/dev/null 2>&1
        docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 del "concurrent_test_$i" >/dev/null 2>&1
    done

    for ((i=1; i<=1000; i++)); do
        docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 del "batch_test_$i" >/dev/null 2>&1
    done

    log_info "缓存性能测试完成"
}

# 系统资源监控
test_system_resources() {
    log_section "系统资源使用监控"

    log_info "监控系统资源使用情况..."

    # 获取Docker容器统计信息
    local stats_output=$(docker stats --no-stream --format "json" | jq -s '.')

    cat >> "$REPORT_FILE" << EOF
### 容器资源使用情况

| 容器名称 | CPU使用率 | 内存使用 | 内存限制 | 网络IO | 磁盘IO |
|----------|-----------|----------|----------|--------|---------|
EOF

    # 解析并格式化统计信息
    echo "$stats_output" | jq -r '.[] | [.Name, .CPUPerc, .MemUsage, .MemPerc, .NetIO, .BlockIO] | @tsv' | while IFS=$'\t' read -r name cpu mem_usage mem_perc net_io block_io; do
        if [[ "$name" == chaiguanjia_* ]]; then
            echo "| $name | $cpu | $mem_usage | $mem_perc | $net_io | $block_io |" >> "$REPORT_FILE"
        fi
    done

    # 检查主机资源
    local host_cpu=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    local host_memory=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    local host_disk=$(df -h / | awk 'NR==2{printf "%s", $5}' | tr -d '%')

    cat >> "$REPORT_FILE" << EOF

### 主机资源使用情况

| 资源类型 | 使用率 | 目标 | 状态 |
|----------|--------|------|------|
| CPU使用率 | ${host_cpu}% | < ${TARGET_CPU_USAGE}% | $(if (( $(echo "$host_cpu < $TARGET_CPU_USAGE" | bc -l) )); then echo "✅ 正常"; else echo "⚠️ 偏高"; fi) |
| 内存使用率 | ${host_memory}% | < ${TARGET_MEMORY_USAGE}% | $(if (( $(echo "$host_memory < $TARGET_MEMORY_USAGE" | bc -l) )); then echo "✅ 正常"; else echo "⚠️ 偏高"; fi) |
| 磁盘使用率 | ${host_disk}% | < 90% | $(if [[ $host_disk -lt 90 ]]; then echo "✅ 正常"; else echo "⚠️ 偏高"; fi) |

EOF

    log_info "资源监控完成 - CPU: ${host_cpu}%, 内存: ${host_memory}%, 磁盘: ${host_disk}%"
}

# 生成性能报告
generate_performance_report() {
    log_section "性能测试总结"

    cat >> "$REPORT_FILE" << EOF

## 性能基准总结

本次性能测试涵盖了以下关键方面：

1. **API性能测试** - 验证REST API的响应时间和吞吐量
2. **数据库性能测试** - 验证PostgreSQL的查询和写入性能
3. **缓存性能测试** - 验证Redis的读写性能和并发能力
4. **系统资源监控** - 监控CPU、内存、磁盘等资源使用情况

## 性能优化建议

1. **API优化建议:**
   - 如响应时间超过目标，考虑增加缓存层
   - 优化数据库查询，添加适当的索引
   - 考虑使用连接池优化数据库连接

2. **数据库优化建议:**
   - 对高频查询字段建立索引
   - 定期进行数据库维护和统计信息更新
   - 考虑数据分区策略提升大表查询性能

3. **缓存优化建议:**
   - 合理设置缓存过期时间
   - 实施缓存预热策略
   - 监控缓存命中率并优化缓存键设计

4. **系统资源优化建议:**
   - 如资源使用率持续偏高，考虑扩容
   - 实施资源监控告警机制
   - 定期清理日志和临时文件

## 持续监控建议

1. 建议每周执行一次完整的性能基准测试
2. 在生产环境部署前进行压力测试
3. 建立性能监控面板，实时监控关键指标
4. 设置性能告警阈值，及时发现性能问题

---
*测试完成时间: $(date '+%Y-%m-%d %H:%M:%S')*
EOF

    log_success "性能基准测试完成，报告已保存至: $REPORT_FILE"
}

# 主函数
main() {
    echo "===== 柴管家基础性能基准测试开始 ====="
    echo "测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""

    # 初始化报告
    init_report

    # 检查工具
    check_tools

    # 检查Docker环境
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker未安装或不可用"
        exit 1
    fi

    # 检查服务状态
    if ! docker-compose ps | grep -q "Up"; then
        log_error "Docker服务未启动，请先运行: docker-compose up -d"
        exit 1
    fi

    # 执行性能测试
    test_api_performance
    test_database_performance
    test_cache_performance
    test_system_resources

    # 生成报告
    generate_performance_report

    echo ""
    echo "===== 基础性能基准测试完成 ====="
    echo "详细报告: $REPORT_FILE"
}

# 执行主函数
main "$@"
