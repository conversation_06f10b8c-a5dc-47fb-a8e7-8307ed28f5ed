#!/bin/bash

# 柴管家服务监控脚本
# 持续监控服务状态并发送报警

set -e

# 配置
MONITOR_INTERVAL=30  # 监控间隔（秒）
LOG_FILE="logs/service-monitor.log"
MAX_FAILURES=3      # 最大失败次数

# 创建日志目录
mkdir -p logs

# 服务失败计数器
declare -A failure_counts

# 日志函数
log() {
    echo "[$(date)] $1" | tee -a "$LOG_FILE"
}

# 检查单个服务
check_service() {
    local service_name="$1"
    local check_command="$2"

    if eval "$check_command" >/dev/null 2>&1; then
        failure_counts["$service_name"]=0
        return 0
    else
        failure_counts["$service_name"]=$((${failure_counts["$service_name"]:-0} + 1))
        return 1
    fi
}

# 发送报警
send_alert() {
    local service="$1"
    local message="$2"

    log "🚨 ALERT: $service - $message"

    # 这里可以集成邮件、短信、钉钉等报警通道
    echo "服务报警: $service - $message" >> alerts.log
}

# 主监控循环
monitor_loop() {
    while true; do
        log "🔍 开始服务监控检查..."

        # 定义要监控的服务
        declare -A services=(
            ["PostgreSQL"]="docker exec chaiguanjia_postgresql pg_isready"
            ["Redis"]="docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 ping"
            ["RabbitMQ"]="curl -s http://localhost:15672/api/overview >/dev/null"
            ["Elasticsearch"]="curl -s http://localhost:9200/_cluster/health >/dev/null"
            ["Backend"]="curl -s --noproxy localhost http://localhost:8000/health >/dev/null"
            ["Frontend"]="curl -s --noproxy localhost http://localhost:3000/health >/dev/null"
        )

        local all_healthy=true

        for service in "${!services[@]}"; do
            if check_service "$service" "${services[$service]}"; then
                log "✅ $service - 健康"
            else
                log "❌ $service - 失败 (${failure_counts[$service]}/$MAX_FAILURES)"
                all_healthy=false

                # 检查是否需要报警
                if [ "${failure_counts[$service]}" -ge "$MAX_FAILURES" ]; then
                    send_alert "$service" "服务连续失败 ${failure_counts[$service]} 次"
                    failure_counts["$service"]=0  # 重置计数器避免重复报警
                fi
            fi
        done

        if [ "$all_healthy" = true ]; then
            log "🎉 所有服务运行正常"
        fi

        log "⏰ 等待 $MONITOR_INTERVAL 秒..."
        sleep "$MONITOR_INTERVAL"
    done
}

# 信号处理
cleanup() {
    log "🛑 监控服务停止"
    exit 0
}

trap cleanup SIGINT SIGTERM

# 启动监控
log "🚀 启动柴管家服务监控..."
log "📊 监控间隔: $MONITOR_INTERVAL 秒"
log "⚠️  最大失败次数: $MAX_FAILURES"

monitor_loop
