#!/bin/bash

# 柴管家基础设施综合健康检查脚本
# 作者: 柴管家开发团队
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 执行检查并记录结果
run_check() {
    local check_name="$1"
    local check_command="$2"
    local expected_result="$3"

    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

    log_info "检查: $check_name"

    if eval "$check_command" >/dev/null 2>&1; then
        log_success "✅ $check_name - 通过"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "❌ $check_name - 失败"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 主函数
main() {
    echo "=================================================="
    echo "🏥 柴管家基础设施健康检查"
    echo "📅 执行时间: $(date)"
    echo "=================================================="

    # 1. Docker服务检查
    log_info "🐳 检查Docker服务状态..."
    run_check "Docker守护进程运行状态" "docker info" ""

    # 2. 容器状态检查
    log_info "📦 检查容器运行状态..."

    # 检查PostgreSQL
    run_check "PostgreSQL容器运行状态" "docker ps | grep chaiguanjia_postgresql | grep -q 'Up'" ""
    run_check "PostgreSQL连接测试" "docker exec chaiguanjia_postgresql pg_isready -U admin -d chaiguanjia" ""

    # 检查Redis
    run_check "Redis容器运行状态" "docker ps | grep chaiguanjia_redis | grep -q 'Up'" ""
    run_check "Redis连接测试" "docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 ping | grep -q 'PONG'" ""

    # 检查RabbitMQ
    run_check "RabbitMQ容器运行状态" "docker ps | grep chaiguanjia_rabbitmq | grep -q 'Up'" ""
    run_check "RabbitMQ健康检查" "docker exec chaiguanjia_rabbitmq rabbitmqctl node_health_check" ""

    # 检查Elasticsearch
    run_check "Elasticsearch容器运行状态" "docker ps | grep chaiguanjia_elasticsearch | grep -q 'Up'" ""
    run_check "Elasticsearch集群健康" "curl -s --noproxy localhost http://localhost:9200/_cluster/health | grep -q 'green\\|yellow'" ""

    # 检查Backend
    run_check "Backend容器运行状态" "docker ps | grep chaiguanjia_backend | grep -q 'Up'" ""
    run_check "Backend API健康检查" "curl -s --noproxy localhost http://localhost:8000/health | grep -q 'healthy'" ""

    # 检查Frontend
    run_check "Frontend容器运行状态" "docker ps | grep chaiguanjia_frontend | grep -q 'Up'" ""
    run_check "Frontend健康检查" "curl -s --noproxy localhost http://localhost:3000/health | grep -q 'frontend healthy'" ""

    # 3. 端口连通性检查
    log_info "🔌 检查端口连通性..."
    run_check "PostgreSQL端口5432" "nc -z localhost 5432" ""
    run_check "Redis端口6379" "nc -z localhost 6379" ""
    run_check "RabbitMQ端口5672" "nc -z localhost 5672" ""
    run_check "RabbitMQ管理端口15672" "nc -z localhost 15672" ""
    run_check "Elasticsearch端口9200" "nc -z localhost 9200" ""
    run_check "Backend端口8000" "nc -z localhost 8000" ""
    run_check "Frontend端口3000" "nc -z localhost 3000" ""

    # 4. 数据卷检查
    log_info "💾 检查数据卷状态..."
    run_check "PostgreSQL数据卷" "docker volume inspect chaiguanjia8_10_postgres_data" ""
    run_check "Redis数据卷" "docker volume inspect chaiguanjia8_10_redis_data" ""
    run_check "Elasticsearch数据卷" "docker volume inspect chaiguanjia8_10_es_data" ""
    run_check "RabbitMQ数据卷" "docker volume inspect chaiguanjia8_10_rabbitmq_data" ""

    # 5. 网络检查
    log_info "🌐 检查Docker网络..."
    run_check "Docker网络存在" "docker network inspect chaiguanjia8_10_chaiguanjia_network" ""

    # 6. 资源使用检查
    log_info "📊 检查资源使用情况..."
    echo "=== 容器资源使用情况 ==="
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -10

    # 结果汇总
    echo ""
    echo "=================================================="
    echo "📋 健康检查汇总报告"
    echo "=================================================="
    echo "总检查项: $TOTAL_CHECKS"
    echo "通过: $PASSED_CHECKS"
    echo "失败: $FAILED_CHECKS"
    echo "成功率: $(echo "scale=2; $PASSED_CHECKS * 100 / $TOTAL_CHECKS" | bc)%"

    if [ $FAILED_CHECKS -eq 0 ]; then
        log_success "🎉 所有健康检查通过！系统运行正常。"
        exit 0
    else
        log_warning "⚠️  存在 $FAILED_CHECKS 个问题需要关注。"
        exit 1
    fi
}

# 检查依赖工具
check_dependencies() {
    local deps=("docker" "curl" "nc" "bc")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "缺少依赖工具: $dep"
            log_info "请安装后重试。"
            exit 1
        fi
    done
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi
