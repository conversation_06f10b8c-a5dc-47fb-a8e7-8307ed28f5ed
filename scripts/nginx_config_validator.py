#!/usr/bin/env python3
"""
Nginx配置文件语法验证器
用于基本的Nginx配置语法检查
"""

import os
import re
import sys


def validate_nginx_config(config_path):
    """验证Nginx配置文件的基本语法"""
    errors = []
    warnings = []

    try:
        with open(config_path, "r", encoding="utf-8") as f:
            content = f.read()
    except FileNotFoundError:
        return [f"配置文件未找到: {config_path}"], []
    except Exception as e:
        return [f"读取配置文件失败: {e}"], []

    # 移除注释和空行进行分析
    lines = content.split("\n")
    cleaned_lines = []
    for line in lines:
        # 如果整行都是注释，跳过
        if line.strip().startswith("#"):
            continue
        # 移除行末注释（但保留字符串中的#）
        line = re.sub(r'#[^"\']*$', "", line)
        if line.strip():
            cleaned_lines.append(line.strip())

    # 检查基本语法
    brace_count = 0
    in_location = False
    in_server = False

    for i, line in enumerate(cleaned_lines, 1):
        # 检查大括号平衡
        brace_count += line.count("{") - line.count("}")

        # 检查if指令位置
        if re.match(r"\s*if\s*\(", line):
            if not (in_location or in_server):
                errors.append(f"第{i}行: if指令应该在location或server块中")

        # 检查server和location块
        if "server" in line and "{" in line:
            in_server = True
        elif "location" in line and "{" in line:
            in_location = True
        elif "}" in line:
            if in_location:
                in_location = False
            elif in_server and "server" not in line:
                in_server = False

        # 检查常见错误
        if line.endswith(","):
            warnings.append(f"第{i}行: 可能缺少分号")

        if (
            re.search(r"[^;{}]\s*$", line)
            and not line.endswith("{")
            and not line.endswith("}")
        ):
            if not any(
                keyword in line
                for keyword in ["#", "include", "upstream", "server", "location", "if"]
            ):
                warnings.append(f"第{i}行: 可能缺少分号")

    # 检查大括号平衡
    if brace_count != 0:
        errors.append(
            f"大括号不平衡: 多了 {brace_count} 个开括号"
            if brace_count > 0
            else f"大括号不平衡: 多了 {-brace_count} 个闭括号"
        )

    return errors, warnings


def main():
    """主函数"""
    base_path = "/Users/<USER>/Documents/Augment/chaiguanjia8_10"
    nginx_configs = [
        "infrastructure/nginx/nginx.conf",
        "infrastructure/nginx/conf.d/default.conf",
        "infrastructure/nginx/conf.d/security.conf",
    ]

    total_errors = 0
    total_warnings = 0

    print("=== Nginx配置文件语法验证 ===\n")

    for config in nginx_configs:
        config_path = os.path.join(base_path, config)
        print(f"检查配置文件: {config}")

        errors, warnings = validate_nginx_config(config_path)

        if errors:
            print(f"  ❌ 错误 ({len(errors)}个):")
            for error in errors:
                print(f"    - {error}")
            total_errors += len(errors)

        if warnings:
            print(f"  ⚠️  警告 ({len(warnings)}个):")
            for warning in warnings:
                print(f"    - {warning}")
            total_warnings += len(warnings)

        if not errors and not warnings:
            print("  ✅ 配置语法正确")

        print()

    print(f"=== 验证结果 ===")
    print(f"总错误数: {total_errors}")
    print(f"总警告数: {total_warnings}")

    if total_errors == 0:
        print("✅ 所有配置文件语法验证通过!")
        return 0
    else:
        print("❌ 配置文件存在语法错误，请修复后重试")
        return 1


if __name__ == "__main__":
    sys.exit(main())
