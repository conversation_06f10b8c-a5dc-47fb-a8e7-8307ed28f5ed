#!/bin/bash

# 柴管家智能客服系统 - 部署脚本
# 支持开发、测试、生产环境的自动化部署

set -e

# 配置参数
ENVIRONMENT=${1:-development}
VERSION=${2:-latest}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-""}
PROJECT_NAME="chaiguanjia"

echo "🚀 开始部署柴管家智能客服系统"
echo "📋 环境: $ENVIRONMENT"
echo "🏷️ 版本: $VERSION"

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker build -t ${PROJECT_NAME}-backend:${VERSION} ./backend/
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker build -t ${PROJECT_NAME}-frontend:${VERSION} ./frontend/
    
    # 如果有镜像仓库，推送镜像
    if [ ! -z "$DOCKER_REGISTRY" ]; then
        log_info "推送镜像到仓库..."
        docker tag ${PROJECT_NAME}-backend:${VERSION} ${DOCKER_REGISTRY}/${PROJECT_NAME}-backend:${VERSION}
        docker tag ${PROJECT_NAME}-frontend:${VERSION} ${DOCKER_REGISTRY}/${PROJECT_NAME}-frontend:${VERSION}
        docker push ${DOCKER_REGISTRY}/${PROJECT_NAME}-backend:${VERSION}
        docker push ${DOCKER_REGISTRY}/${PROJECT_NAME}-frontend:${VERSION}
    fi
    
    log_success "镜像构建完成"
}

# 部署应用
deploy_application() {
    log_info "部署应用..."
    
    # 选择环境配置文件
    case $ENVIRONMENT in
        "development")
            COMPOSE_FILE="infrastructure/docker/docker-compose.yml"
            ;;
        "staging")
            COMPOSE_FILE="infrastructure/docker/docker-compose.dev.yml"
            ;;
        "production")
            COMPOSE_FILE="infrastructure/docker/docker-compose.prod.yml"
            ;;
        *)
            log_error "不支持的环境: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    # 设置环境变量
    export VERSION=$VERSION
    export ENVIRONMENT=$ENVIRONMENT
    
    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose -f $COMPOSE_FILE down --remove-orphans
    
    # 清理旧镜像（可选）
    if [ "$ENVIRONMENT" = "production" ]; then
        log_info "清理旧镜像..."
        docker image prune -f
    fi
    
    # 启动新服务
    log_info "启动新服务..."
    docker-compose -f $COMPOSE_FILE up -d
    
    log_success "应用部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 30
    
    # 检查后端健康状态
    if curl -f http://localhost:8000/health &> /dev/null; then
        log_success "后端服务健康检查通过"
    else
        log_error "后端服务健康检查失败"
        exit 1
    fi
    
    # 检查前端可访问性
    if curl -f http://localhost:3000 &> /dev/null; then
        log_success "前端服务健康检查通过"
    else
        log_error "前端服务健康检查失败"
        exit 1
    fi
    
    log_success "所有服务健康检查通过"
}

# 部署后处理
post_deploy() {
    log_info "执行部署后处理..."
    
    # 数据库迁移
    log_info "执行数据库迁移..."
    docker-compose exec backend alembic upgrade head
    
    # 清理构建缓存
    docker builder prune -f
    
    log_success "部署后处理完成"
}

# 回滚函数
rollback() {
    log_warning "执行回滚操作..."
    
    PREVIOUS_VERSION=${1:-"previous"}
    
    # 这里应该实现回滚逻辑
    # 可以从备份恢复或切换到上一个版本
    
    log_success "回滚完成"
}

# 显示部署信息
show_deploy_info() {
    log_success "🎉 部署完成！"
    echo ""
    echo "📋 部署信息:"
    echo "   环境: $ENVIRONMENT"
    echo "   版本: $VERSION"
    echo "   时间: $(date)"
    echo ""
    echo "🔗 访问地址:"
    echo "   前端: http://localhost:3000"
    echo "   后端API: http://localhost:8000"
    echo "   API文档: http://localhost:8000/docs"
    echo ""
    echo "📊 服务状态:"
    docker-compose ps
    echo ""
    echo "📝 查看日志: docker-compose logs -f"
    echo "🛑 停止服务: docker-compose down"
}

# 主执行流程
main() {
    case "${1:-deploy}" in
        "deploy")
            check_dependencies
            build_images
            deploy_application
            health_check
            post_deploy
            show_deploy_info
            ;;
        "rollback")
            rollback $2
            ;;
        "health")
            health_check
            ;;
        *)
            echo "用法: $0 {deploy|rollback|health} [环境] [版本]"
            echo "环境: development|staging|production"
            echo "示例: $0 deploy production v1.0.0"
            exit 1
            ;;
    esac
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
