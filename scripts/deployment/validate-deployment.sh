#!/bin/bash

# 柴管家完整部署验证脚本
# 验证所有服务是否正确部署并可用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_header() {
    echo -e "\n${PURPLE}=================================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}=================================================${NC}"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

log_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 执行测试并记录结果
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_info "测试: $test_name"

    if output=$(eval "$test_command" 2>&1); then
        if [[ -z "$expected_pattern" ]] || echo "$output" | grep -q "$expected_pattern"; then
            log_success "$test_name"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            log_fail "$test_name - 输出不匹配预期模式: $expected_pattern"
            echo "实际输出: $output"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        log_fail "$test_name - 命令执行失败"
        echo "错误输出: $output"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    local service_name="$1"
    local check_command="$2"
    local max_wait=60
    local waited=0

    log_info "等待 $service_name 启动..."

    while [ $waited -lt $max_wait ]; do
        if eval "$check_command" >/dev/null 2>&1; then
            log_success "$service_name 已启动"
            return 0
        fi
        sleep 2
        waited=$((waited + 2))
        echo -n "."
    done

    log_fail "$service_name 启动超时"
    return 1
}

# 主验证函数
main() {
    log_header "🚀 柴管家基础设施部署验证"
    echo "📅 验证时间: $(date)"
    echo "🏗️  验证环境: Docker"
    echo ""

    # 1. 验证Docker环境
    log_header "🐳 Docker环境验证"
    run_test "Docker服务运行状态" "docker info" "Server:"
    run_test "Docker Compose可用性" "docker-compose --version" "Docker Compose version"

    # 2. 验证容器状态
    log_header "📦 容器状态验证"

    # 基础设施容器
    run_test "PostgreSQL容器运行" "docker ps | grep chaiguanjia_postgresql" "Up"
    run_test "Redis容器运行" "docker ps | grep chaiguanjia_redis" "Up"
    run_test "RabbitMQ容器运行" "docker ps | grep chaiguanjia_rabbitmq" "Up"
    run_test "Elasticsearch容器运行" "docker ps | grep chaiguanjia_elasticsearch" "Up"
    run_test "Backend容器运行" "docker ps | grep chaiguanjia_backend" "Up"
    run_test "Frontend容器运行" "docker ps | grep chaiguanjia_frontend" "Up"

    # 3. 验证端口监听
    log_header "🔌 端口监听验证"
    run_test "PostgreSQL端口5432" "nc -z localhost 5432" ""
    run_test "Redis端口6379" "nc -z localhost 6379" ""
    run_test "RabbitMQ端口5672" "nc -z localhost 5672" ""
    run_test "RabbitMQ管理端口15672" "nc -z localhost 15672" ""
    run_test "Elasticsearch端口9200" "nc -z localhost 9200" ""
    run_test "Backend API端口8000" "nc -z localhost 8000" ""
    run_test "Frontend端口3000" "nc -z localhost 3000" ""

    # 4. 验证服务功能
    log_header "⚡ 服务功能验证"

    # 等待服务完全启动
    wait_for_service "PostgreSQL" "docker exec chaiguanjia_postgresql pg_isready"
    wait_for_service "Redis" "docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 ping"
    wait_for_service "Backend API" "curl -s --noproxy localhost http://localhost:8000/health"

    # PostgreSQL功能测试
    run_test "PostgreSQL连接测试" "docker exec chaiguanjia_postgresql pg_isready -U admin -d chaiguanjia" "accepting connections"
    run_test "PostgreSQL数据库存在" "docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c '\l'" "chaiguanjia"

    # Redis功能测试
    run_test "Redis PING测试" "docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 ping 2>/dev/null" "PONG"
    run_test "Redis信息查询" "docker exec chaiguanjia_redis redis-cli -a chaiguanjia2024 info server 2>/dev/null" "redis_version"

    # RabbitMQ功能测试
    run_test "RabbitMQ节点健康检查" "docker exec chaiguanjia_rabbitmq rabbitmqctl node_health_check" "Health check passed"
    run_test "RabbitMQ管理界面" "curl -s -u admin:chaiguanjia2024 http://localhost:15672/api/overview" "management_version"

    # Elasticsearch功能测试
    run_test "Elasticsearch集群健康" "curl -s --noproxy localhost http://localhost:9200/_cluster/health" "cluster_name"
    run_test "Elasticsearch节点信息" "curl -s --noproxy localhost http://localhost:9200/_nodes" "nodes"

    # Backend API功能测试
    run_test "Backend健康检查" "curl -s --noproxy localhost http://localhost:8000/health" "healthy"
    run_test "Backend API文档" "curl -s --noproxy localhost http://localhost:8000/docs" "FastAPI"
    run_test "Backend版本信息" "curl -s --noproxy localhost http://localhost:8000/api/version" "version"
    run_test "Backend状态信息" "curl -s --noproxy localhost http://localhost:8000/api/status" "uptime"

    # Frontend功能测试
    run_test "Frontend健康检查" "curl -s --noproxy localhost http://localhost:3000/health" "frontend healthy"
    run_test "Frontend主页访问" "curl -s --noproxy localhost http://localhost:3000/" "<!DOCTYPE html>"

    # 5. 验证数据持久化
    log_header "💾 数据持久化验证"
    run_test "PostgreSQL数据卷" "docker volume inspect chaiguanjia8_10_postgres_data" "Name"
    run_test "Redis数据卷" "docker volume inspect chaiguanjia8_10_redis_data" "Name"
    run_test "Elasticsearch数据卷" "docker volume inspect chaiguanjia8_10_es_data" "Name"
    run_test "RabbitMQ数据卷" "docker volume inspect chaiguanjia8_10_rabbitmq_data" "Name"

    # 6. 验证网络配置
    log_header "🌐 网络配置验证"
    run_test "Docker网络存在" "docker network inspect chaiguanjia8_10_chaiguanjia_network" "Name"
    run_test "容器网络连通性" "docker exec chaiguanjia_backend ping -c 1 chaiguanjia_postgresql" "1 packets transmitted, 1 received"

    # 7. 验证配置文件
    log_header "⚙️  配置文件验证"
    run_test "Docker Compose配置" "docker-compose config" "services:"
    run_test "环境变量文件" "test -f .env" ""

    # 8. 安全性验证
    log_header "🔒 安全配置验证"
    run_test "Redis密码保护" "docker exec chaiguanjia_redis redis-cli ping 2>&1" "NOAUTH Authentication required"
    run_test "PostgreSQL用户权限" "docker exec chaiguanjia_postgresql psql -U admin -d chaiguanjia -c 'SELECT current_user;'" "admin"

    # 9. 性能验证
    log_header "📊 性能验证"
    echo "=== 容器资源使用情况 ==="
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" 2>/dev/null | head -8

    echo ""
    echo "=== 镜像大小统计 ==="
    docker images | grep chaiguanjia | awk '{print $1 "\t" $2 "\t" $7 $8}'

    # 结果汇总
    log_header "📋 验证结果汇总"
    echo "总测试项: $TOTAL_TESTS"
    echo "通过: $PASSED_TESTS"
    echo "失败: $FAILED_TESTS"

    success_rate=$(echo "scale=2; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc 2>/dev/null || echo "N/A")
    echo "成功率: $success_rate%"

    if [ $FAILED_TESTS -eq 0 ]; then
        log_success "🎉 所有验证测试通过！部署成功！"
        echo ""
        echo "✅ 柴管家基础设施已完全部署并验证通过"
        echo "🌐 前端监控面板: http://localhost:3000"
        echo "🚀 后端API文档: http://localhost:8000/docs"
        echo "🐰 RabbitMQ管理: http://localhost:15672 (admin/chaiguanjia2024)"
        echo "🔍 Elasticsearch: http://localhost:9200"
        echo ""
        exit 0
    else
        log_fail "❌ 发现 $FAILED_TESTS 个问题需要解决"
        echo ""
        echo "请检查失败的测试项并修复相关问题"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    local deps=("docker" "docker-compose" "curl" "nc" "bc")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_fail "缺少依赖工具: $dep"
            echo "请安装 $dep 后重试"
            exit 1
        fi
    done
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi
