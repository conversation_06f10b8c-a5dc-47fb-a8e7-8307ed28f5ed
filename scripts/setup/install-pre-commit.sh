#!/bin/bash

# Pre-commit 安装和配置脚本
# 自动安装和配置 pre-commit 钩子

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🔧 安装和配置 Pre-commit 钩子...${NC}"

# ==========================================
# 检查 Python 环境
# ==========================================
check_python() {
    echo -e "${YELLOW}🐍 检查 Python 环境${NC}"

    if ! command -v python3 >/dev/null 2>&1; then
        echo -e "${RED}❌ Python3 未安装${NC}"
        exit 1
    fi

    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    required_version="3.11"

    if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
        echo -e "${RED}❌ Python 版本过低，需要 $required_version+，当前版本: $python_version${NC}"
        exit 1
    fi

    echo -e "${GREEN}✅ Python 版本检查通过: $python_version${NC}"
}

# ==========================================
# 安装 Pre-commit
# ==========================================
install_pre_commit() {
    echo -e "${YELLOW}📦 安装 Pre-commit${NC}"

    # 检查是否已安装
    if command -v pre-commit >/dev/null 2>&1; then
        current_version=$(pre-commit --version | cut -d' ' -f2)
        echo -e "${GREEN}✅ Pre-commit 已安装，版本: $current_version${NC}"
    else
        echo "正在安装 Pre-commit..."

        # 使用 pip 安装
        if command -v pip3 >/dev/null 2>&1; then
            pip3 install pre-commit
        elif command -v pip >/dev/null 2>&1; then
            pip install pre-commit
        else
            echo -e "${RED}❌ 未找到 pip，请先安装 pip${NC}"
            exit 1
        fi

        echo -e "${GREEN}✅ Pre-commit 安装完成${NC}"
    fi
}

# ==========================================
# 安装 Node.js 依赖（前端）
# ==========================================
install_node_dependencies() {
    echo -e "${YELLOW}📱 安装 Node.js 依赖${NC}"

    if [ -f "frontend/package.json" ]; then
        cd frontend

        # 检查 npm 是否可用
        if command -v npm >/dev/null 2>&1; then
            echo "正在安装前端依赖..."
            npm install
            echo -e "${GREEN}✅ 前端依赖安装完成${NC}"
        else
            echo -e "${RED}❌ npm 未安装，跳过前端依赖安装${NC}"
        fi

        cd ..
    else
        echo -e "${YELLOW}⚠️ frontend/package.json 不存在，跳过前端依赖安装${NC}"
    fi
}

# ==========================================
# 安装 Python 依赖（后端）
# ==========================================
install_python_dependencies() {
    echo -e "${YELLOW}🐍 安装 Python 依赖${NC}"

    if [ -f "backend/requirements/development.txt" ]; then
        cd backend

        # 创建虚拟环境（如果不存在）
        if [ ! -d ".venv" ]; then
            echo "创建虚拟环境..."
            python3 -m venv .venv
        fi

        # 激活虚拟环境
        source .venv/bin/activate

        # 升级 pip
        pip install --upgrade pip

        # 安装依赖
        echo "正在安装后端依赖..."
        pip install -r requirements/development.txt

        echo -e "${GREEN}✅ 后端依赖安装完成${NC}"

        # 退出虚拟环境
        deactivate

        cd ..
    else
        echo -e "${YELLOW}⚠️ backend/requirements/development.txt 不存在，跳过后端依赖安装${NC}"
    fi
}

# ==========================================
# 配置 Pre-commit
# ==========================================
configure_pre_commit() {
    echo -e "${YELLOW}⚙️ 配置 Pre-commit${NC}"

    # 检查配置文件是否存在
    if [ ! -f ".pre-commit-config.yaml" ]; then
        echo -e "${RED}❌ .pre-commit-config.yaml 配置文件不存在${NC}"
        exit 1
    fi

    # 安装 pre-commit 钩子
    echo "正在安装 pre-commit 钩子..."
    pre-commit install

    # 安装 commit-msg 钩子
    echo "正在安装 commit-msg 钩子..."
    pre-commit install --hook-type commit-msg

    # 安装 pre-push 钩子
    echo "正在安装 pre-push 钩子..."
    pre-commit install --hook-type pre-push

    echo -e "${GREEN}✅ Pre-commit 钩子安装完成${NC}"
}

# ==========================================
# 初始化检查
# ==========================================
run_initial_check() {
    echo -e "${YELLOW}🔍 运行初始检查${NC}"

    echo "正在运行 pre-commit 初始检查..."

    # 运行所有文件的检查
    if pre-commit run --all-files; then
        echo -e "${GREEN}✅ 初始检查全部通过${NC}"
    else
        echo -e "${YELLOW}⚠️ 部分检查未通过，这是正常的${NC}"
        echo -e "${YELLOW}   Pre-commit 已自动修复部分问题${NC}"
        echo -e "${YELLOW}   请查看修改并提交这些改动${NC}"
    fi
}

# ==========================================
# 创建密钥检测基线
# ==========================================
create_secrets_baseline() {
    echo -e "${YELLOW}🔐 创建密钥检测基线${NC}"

    # 运行 detect-secrets 创建基线
    if command -v detect-secrets >/dev/null 2>&1; then
        detect-secrets scan --baseline .secrets.baseline
        echo -e "${GREEN}✅ 密钥检测基线创建完成${NC}"
    else
        echo "正在安装 detect-secrets..."
        pip install detect-secrets
        detect-secrets scan --baseline .secrets.baseline
        echo -e "${GREEN}✅ 密钥检测基线创建完成${NC}"
    fi
}

# ==========================================
# 配置自动更新
# ==========================================
configure_auto_update() {
    echo -e "${YELLOW}🔄 配置自动更新${NC}"

    # 创建自动更新脚本
    cat > scripts/maintenance/update-pre-commit.sh << 'EOF'
#!/bin/bash

# Pre-commit 钩子自动更新脚本

echo "🔄 更新 Pre-commit 钩子..."

# 更新钩子版本
pre-commit autoupdate

# 运行检查确保更新正常
echo "🔍 运行检查验证更新..."
pre-commit run --all-files

echo "✅ Pre-commit 钩子更新完成"
EOF

    chmod +x scripts/maintenance/update-pre-commit.sh

    echo -e "${GREEN}✅ 自动更新脚本创建完成${NC}"
}

# ==========================================
# 显示使用说明
# ==========================================
show_usage() {
    echo ""
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Pre-commit 配置完成！${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
    echo -e "${YELLOW}📋 使用说明:${NC}"
    echo ""
    echo -e "${GREEN}日常使用:${NC}"
    echo "  • 正常提交代码，pre-commit 会自动运行检查"
    echo "  • 如果检查失败，修复问题后重新提交"
    echo "  • 某些问题会被自动修复，需要重新添加文件"
    echo ""
    echo -e "${GREEN}手动运行:${NC}"
    echo "  • 运行所有检查: pre-commit run --all-files"
    echo "  • 运行特定检查: pre-commit run <hook-id>"
    echo "  • 跳过检查提交: git commit --no-verify"
    echo ""
    echo -e "${GREEN}维护命令:${NC}"
    echo "  • 更新钩子: pre-commit autoupdate"
    echo "  • 清理缓存: pre-commit clean"
    echo "  • 卸载钩子: pre-commit uninstall"
    echo ""
    echo -e "${GREEN}自动更新:${NC}"
    echo "  • 运行更新脚本: ./scripts/maintenance/update-pre-commit.sh"
    echo ""
    echo -e "${YELLOW}💡 提示:${NC}"
    echo "  • 首次运行可能较慢，后续会使用缓存"
    echo "  • 某些检查只在 push 时运行（如测试）"
    echo "  • 可以在 .pre-commit-config.yaml 中自定义配置"
    echo ""
}

# ==========================================
# 主执行函数
# ==========================================
main() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  柴管家 Pre-commit 安装脚本${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""

    # 检查是否在 Git 仓库中
    if ! git rev-parse --git-dir >/dev/null 2>&1; then
        echo -e "${RED}❌ 当前目录不是 Git 仓库${NC}"
        exit 1
    fi

    # 创建必要目录
    mkdir -p scripts/maintenance

    check_python
    echo ""

    install_pre_commit
    echo ""

    install_python_dependencies
    echo ""

    install_node_dependencies
    echo ""

    configure_pre_commit
    echo ""

    create_secrets_baseline
    echo ""

    configure_auto_update
    echo ""

    run_initial_check
    echo ""

    show_usage
}

# 执行主函数
main "$@"
