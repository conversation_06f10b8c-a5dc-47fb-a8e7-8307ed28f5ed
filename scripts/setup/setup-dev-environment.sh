#!/bin/bash

# 柴管家开发环境一键配置脚本
# 自动配置完整的开发环境，包括工具、钩子和依赖

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 进度跟踪
TOTAL_STEPS=8
CURRENT_STEP=0

# 进度显示函数
show_progress() {
    ((CURRENT_STEP++))
    local progress=$((CURRENT_STEP * 100 / TOTAL_STEPS))
    echo -e "${BLUE}[${CURRENT_STEP}/${TOTAL_STEPS}] (${progress}%) $1${NC}"
}

# 成功消息
show_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 警告消息
show_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

# 错误消息
show_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 信息消息
show_info() {
    echo -e "${PURPLE}ℹ️ $1${NC}"
}

# 检查系统要求
check_system_requirements() {
    show_progress "检查系统要求"

    local missing_tools=()

    # 检查必需工具
    if ! command -v python3 >/dev/null 2>&1; then
        missing_tools+=("Python 3.11+")
    else
        python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        if [ "$(printf '%s\n' "3.11" "$python_version" | sort -V | head -n1)" != "3.11" ]; then
            missing_tools+=("Python 3.11+ (当前版本: $python_version)")
        fi
    fi

    if ! command -v node >/dev/null 2>&1; then
        missing_tools+=("Node.js 18+")
    else
        node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$node_version" -lt 18 ]; then
            missing_tools+=("Node.js 18+ (当前版本: v$node_version)")
        fi
    fi

    if ! command -v docker >/dev/null 2>&1; then
        missing_tools+=("Docker")
    fi

    if ! command -v docker-compose >/dev/null 2>&1; then
        missing_tools+=("Docker Compose")
    fi

    if ! command -v git >/dev/null 2>&1; then
        missing_tools+=("Git")
    fi

    if [ ${#missing_tools[@]} -gt 0 ]; then
        show_error "缺少必需工具："
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        echo ""
        echo "请安装缺少的工具后重新运行此脚本"
        exit 1
    fi

    show_success "系统要求检查通过"
}

# 检查VS Code
check_vscode() {
    if command -v code >/dev/null 2>&1; then
        show_success "VS Code 已安装"
        return 0
    else
        show_warning "VS Code 未安装，将跳过相关配置"
        return 1
    fi
}

# 配置npm镜像源
configure_npm_registry() {
    show_progress "配置npm国内镜像源"

    # 设置npm镜像源
    npm config set registry https://registry.npmmirror.com
    npm config set disturl https://npmmirror.com/dist
    npm config set sass_binary_site https://npmmirror.com/mirrors/node-sass
    npm config set electron_mirror https://npmmirror.com/mirrors/electron/
    npm config set puppeteer_download_host https://npmmirror.com/mirrors
    npm config set chromedriver_cdnurl https://npmmirror.com/mirrors/chromedriver
    npm config set operadriver_cdnurl https://npmmirror.com/mirrors/operadriver
    npm config set phantomjs_cdnurl https://npmmirror.com/mirrors/phantomjs
    npm config set selenium_cdnurl https://npmmirror.com/mirrors/selenium
    npm config set node_inspector_cdnurl https://npmmirror.com/mirrors/node-inspector

    show_success "npm镜像源配置完成"
}

# 创建Python虚拟环境
setup_python_environment() {
    show_progress "配置Python开发环境"

    cd backend

    # 创建虚拟环境
    if [ ! -d ".venv" ]; then
        echo "创建Python虚拟环境..."
        python3 -m venv .venv
        show_success "虚拟环境创建完成"
    else
        show_info "虚拟环境已存在"
    fi

    # 激活虚拟环境并安装依赖
    source .venv/bin/activate

    # 升级pip
    pip install --upgrade pip

    # 安装开发依赖
    if [ -f "requirements/development.txt" ]; then
        echo "安装Python开发依赖..."
        pip install -r requirements/development.txt
        show_success "Python依赖安装完成"
    elif [ -f "requirements.txt" ]; then
        echo "安装Python依赖..."
        pip install -r requirements.txt
        show_success "Python依赖安装完成"
    else
        show_warning "未找到requirements文件，跳过Python依赖安装"
    fi

    deactivate
    cd ..
}

# 安装前端依赖
setup_frontend_environment() {
    show_progress "配置前端开发环境"

    if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then
        cd frontend

        echo "安装前端依赖..."
        npm install

        show_success "前端依赖安装完成"
        cd ..
    else
        show_warning "未找到frontend目录，跳过前端依赖安装"
    fi
}

# 配置Git环境
setup_git_environment() {
    show_progress "配置Git开发环境"

    if [ -f "scripts/git/setup-git-hooks.sh" ]; then
        echo "配置Git钩子和工作流..."
        ./scripts/git/setup-git-hooks.sh
        show_success "Git环境配置完成"
    else
        show_warning "Git配置脚本不存在，跳过Git环境配置"
    fi
}

# 安装pre-commit钩子
setup_pre_commit() {
    show_progress "配置代码质量检查钩子"

    if [ -f "scripts/setup/install-pre-commit.sh" ]; then
        echo "安装pre-commit钩子..."
        ./scripts/setup/install-pre-commit.sh
        show_success "Pre-commit钩子配置完成"
    else
        show_warning "Pre-commit配置脚本不存在，跳过钩子安装"
    fi
}

# 配置VS Code
setup_vscode_environment() {
    show_progress "配置VS Code开发环境"

    if check_vscode; then
        # 安装推荐插件
        if [ -f ".vscode/extensions.json" ]; then
            echo "安装VS Code推荐插件..."

            # 读取推荐插件列表并安装
            extensions=$(cat .vscode/extensions.json | jq -r '.recommendations[]' 2>/dev/null || echo "")

            if [ -n "$extensions" ]; then
                echo "$extensions" | while read -r extension; do
                    if [ -n "$extension" ]; then
                        echo "  安装插件: $extension"
                        code --install-extension "$extension" --force >/dev/null 2>&1 || true
                    fi
                done
                show_success "VS Code插件安装完成"
            else
                show_warning "无法读取插件列表，请手动安装推荐插件"
            fi
        else
            show_warning "未找到VS Code插件配置，跳过插件安装"
        fi
    fi
}

# 验证环境配置
verify_environment() {
    show_progress "验证开发环境配置"

    local verification_passed=true

    echo "验证环境配置..."

    # 检查Python虚拟环境
    if [ -f "backend/.venv/bin/python" ]; then
        show_success "Python虚拟环境正常"
    else
        show_error "Python虚拟环境配置失败"
        verification_passed=false
    fi

    # 检查前端环境
    if [ -d "frontend/node_modules" ]; then
        show_success "前端依赖安装正常"
    else
        show_warning "前端依赖可能未正确安装"
    fi

    # 检查Git钩子
    if [ -f ".git/hooks/pre-commit" ]; then
        show_success "Git钩子配置正常"
    else
        show_warning "Git钩子可能未正确配置"
    fi

    # 检查pre-commit
    if command -v pre-commit >/dev/null 2>&1; then
        show_success "Pre-commit工具可用"
    else
        show_warning "Pre-commit工具可能未正确安装"
    fi

    # 测试代码格式化工具
    if [ -f "backend/.venv/bin/black" ]; then
        show_success "Python格式化工具可用"
    else
        show_warning "Python格式化工具可能未安装"
    fi

    if $verification_passed; then
        show_success "环境验证通过"
    else
        show_error "环境验证发现问题，请检查配置"
    fi
}

# 显示使用指南
show_usage_guide() {
    echo ""
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  🎉 开发环境配置完成！${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
    echo -e "${GREEN}📋 下一步操作:${NC}"
    echo ""
    echo -e "${YELLOW}1. 启动开发环境:${NC}"
    echo "   ./start-dev.sh"
    echo ""
    echo -e "${YELLOW}2. 验证环境:${NC}"
    echo "   ./scripts/docker/test-deployment.sh"
    echo ""
    echo -e "${YELLOW}3. 打开VS Code:${NC}"
    echo "   code ."
    echo ""
    echo -e "${GREEN}🛠️ 常用开发命令:${NC}"
    echo ""
    echo -e "${YELLOW}Git操作:${NC}"
    echo "   git st                    # 查看状态"
    echo "   git lg                    # 美化的提交历史"
    echo "   git sync                  # 同步远程分支"
    echo ""
    echo -e "${YELLOW}代码检查:${NC}"
    echo "   pre-commit run --all-files  # 运行所有检查"
    echo "   black backend/              # Python格式化"
    echo "   cd frontend && npm run lint # 前端代码检查"
    echo ""
    echo -e "${YELLOW}测试运行:${NC}"
    echo "   pytest                      # Python测试"
    echo "   cd frontend && npm test     # 前端测试"
    echo ""
    echo -e "${GREEN}📚 文档参考:${NC}"
    echo "   docs/development/开发环境配置指南.md"
    echo "   docs/deployment/容器化部署指南.md"
    echo ""
    echo -e "${PURPLE}💡 提示:${NC}"
    echo "   • 每次提交代码时会自动运行代码质量检查"
    echo "   • 使用提交信息模板可以获得更好的版本管理体验"
    echo "   • 遇到问题请查看故障排除文档或联系团队"
    echo ""
}

# 错误处理
handle_error() {
    echo ""
    show_error "环境配置过程中发生错误！"
    echo ""
    echo -e "${YELLOW}可能的解决方案:${NC}"
    echo "1. 检查网络连接"
    echo "2. 确保所有必需工具已正确安装"
    echo "3. 检查磁盘空间是否足够"
    echo "4. 重新运行此脚本"
    echo ""
    echo -e "${YELLOW}如需帮助，请联系技术团队${NC}"
    exit 1
}

# 主执行函数
main() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  柴管家开发环境配置脚本${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
    echo -e "${GREEN}这个脚本将为您配置完整的开发环境${NC}"
    echo ""

    # 设置错误处理
    trap handle_error ERR

    # 检查是否在Git仓库中
    if ! git rev-parse --git-dir >/dev/null 2>&1; then
        show_error "当前目录不是Git仓库"
        exit 1
    fi

    # 执行配置步骤
    check_system_requirements
    configure_npm_registry
    setup_python_environment
    setup_frontend_environment
    setup_git_environment
    setup_pre_commit
    setup_vscode_environment
    verify_environment

    # 显示使用指南
    show_usage_guide
}

# 执行主函数
main "$@"
