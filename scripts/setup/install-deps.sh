#!/bin/bash

# 柴管家智能客服系统 - 依赖安装脚本
# 自动安装项目所需的所有依赖包（使用国内镜像源）

set -e

echo "🚀 开始安装柴管家智能客服系统依赖..."

# 检查操作系统
if [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
else
    echo "❌ 不支持的操作系统: $OSTYPE"
    exit 1
fi

echo "📋 检测到操作系统: $OS"

# 检查并安装Homebrew (macOS)
if [[ "$OS" == "macos" ]]; then
    if ! command -v brew &> /dev/null; then
        echo "📦 正在安装Homebrew..."
        /bin/bash -c "$(curl -fsSL https://install.brew.sh/)"
    else
        echo "✅ Homebrew已安装"
    fi
fi

# 安装Python 3.11+
echo "🐍 检查Python安装..."
if ! command -v python3 &> /dev/null; then
    if [[ "$OS" == "macos" ]]; then
        brew install python@3.11
    elif [[ "$OS" == "linux" ]]; then
        sudo apt-get update
        sudo apt-get install -y python3.11 python3.11-venv python3.11-pip
    fi
else
    PYTHON_VERSION=$(python3 --version | grep -o '[0-9]\+\.[0-9]\+')
    echo "✅ Python版本: $PYTHON_VERSION"
fi

# 安装Node.js 18+
echo "📦 检查Node.js安装..."
if ! command -v node &> /dev/null; then
    if [[ "$OS" == "macos" ]]; then
        brew install node@18
    elif [[ "$OS" == "linux" ]]; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    fi
else
    NODE_VERSION=$(node --version)
    echo "✅ Node.js版本: $NODE_VERSION"
fi

# 设置npm国内镜像源
echo "🔧 配置npm国内镜像源..."
npm config set registry https://registry.npmmirror.com
echo "✅ npm镜像源配置完成"

# 安装Docker
echo "🐳 检查Docker安装..."
if ! command -v docker &> /dev/null; then
    if [[ "$OS" == "macos" ]]; then
        echo "请手动安装Docker Desktop for Mac: https://www.docker.com/products/docker-desktop"
    elif [[ "$OS" == "linux" ]]; then
        sudo apt-get install -y docker.io docker-compose
        sudo systemctl start docker
        sudo systemctl enable docker
        sudo usermod -aG docker $USER
    fi
else
    DOCKER_VERSION=$(docker --version)
    echo "✅ Docker版本: $DOCKER_VERSION"
fi

# 安装PostgreSQL
echo "🗄️ 检查PostgreSQL安装..."
if ! command -v psql &> /dev/null; then
    if [[ "$OS" == "macos" ]]; then
        brew install postgresql@15
        brew services start postgresql@15
    elif [[ "$OS" == "linux" ]]; then
        sudo apt-get install -y postgresql-15 postgresql-client-15
        sudo systemctl start postgresql
        sudo systemctl enable postgresql
    fi
else
    POSTGRES_VERSION=$(psql --version)
    echo "✅ PostgreSQL版本: $POSTGRES_VERSION"
fi

# 安装Redis
echo "📦 检查Redis安装..."
if ! command -v redis-server &> /dev/null; then
    if [[ "$OS" == "macos" ]]; then
        brew install redis
        brew services start redis
    elif [[ "$OS" == "linux" ]]; then
        sudo apt-get install -y redis-server
        sudo systemctl start redis
        sudo systemctl enable redis
    fi
else
    REDIS_VERSION=$(redis-server --version)
    echo "✅ Redis版本: $REDIS_VERSION"
fi

# 安装后端Python依赖
echo "🐍 安装后端Python依赖..."
cd backend

# 设置pip国内镜像源
pip3 config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 创建虚拟环境
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# 激活虚拟环境并安装依赖
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements/development.txt

echo "✅ 后端依赖安装完成"

# 安装前端依赖
echo "📦 安装前端依赖..."
cd ../frontend

# 安装依赖
npm install

echo "✅ 前端依赖安装完成"

# 返回项目根目录
cd ..

echo "🎉 所有依赖安装完成！"
echo ""
echo "📝 下一步操作："
echo "1. 启动数据库: ./scripts/setup/init-database.sh"
echo "2. 启动后端: cd backend && source venv/bin/activate && python app/main.py"
echo "3. 启动前端: cd frontend && npm run dev"
echo "4. 或者使用Docker: docker-compose up -d"
echo ""
echo "📖 更多信息请查看文档: docs/development/setup.md"
