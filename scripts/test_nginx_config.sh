#!/bin/bash
# Nginx配置测试脚本

set -e

echo "=== Nginx配置文件语法测试 ==="

# 创建临时测试目录
TEST_DIR="/tmp/nginx_test_$$"
mkdir -p "$TEST_DIR/conf.d"

# 复制配置文件
cp infrastructure/nginx/nginx.conf "$TEST_DIR/"
cp infrastructure/nginx/conf.d/*.conf "$TEST_DIR/conf.d/"

# 修改upstream配置，使用可解析的地址进行语法测试
sed -i.bak 's/chaiguanjia_backend:8000/127.0.0.1:8000/g' "$TEST_DIR/nginx.conf"
sed -i.bak 's/chaiguanjia_frontend:80/127.0.0.1:80/g' "$TEST_DIR/nginx.conf"
sed -i.bak 's/rabbitmq:15672/127.0.0.1:15672/g' "$TEST_DIR/conf.d/default.conf"

# 创建基本的mime.types文件
cat > "$TEST_DIR/mime.types" << 'EOF'
types {
    text/html                             html htm shtml;
    text/css                              css;
    text/xml                              xml;
    image/gif                             gif;
    image/jpeg                            jpeg jpg;
    application/javascript                js;
    application/atom+xml                  atom;
    application/rss+xml                   rss;
    application/json                      json;
}
EOF

# 修改nginx.conf的mime.types路径
sed -i.bak "s|/etc/nginx/mime.types|/etc/nginx/mime.types|g" "$TEST_DIR/nginx.conf"

echo "测试配置文件位置: $TEST_DIR"

# 使用Docker测试语法
echo "正在使用Docker测试Nginx配置语法..."

if docker run --rm -v "$TEST_DIR:/etc/nginx:ro" -v "$TEST_DIR/mime.types:/etc/nginx/mime.types:ro" nginx:alpine nginx -t; then
    echo "✅ Nginx配置语法验证通过!"
    result=0
else
    echo "❌ Nginx配置语法验证失败!"
    result=1
fi

# 清理临时文件
rm -rf "$TEST_DIR"

exit $result
