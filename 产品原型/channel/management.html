<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道管理 - 柴管家</title>
    <link rel="stylesheet" href="../assets/css/material-design.css">
    <link rel="stylesheet" href="../assets/css/common.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
        }

        .header-info h1 {
            margin: 0 0 8px 0;
            color: var(--md-sys-color-primary);
        }

        .header-info p {
            margin: 0;
            color: var(--md-sys-color-on-surface-variant);
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .stat-card {
            background-color: var(--md-sys-color-surface-container);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: var(--md-sys-color-primary);
            margin-bottom: 8px;
        }

        .stat-label {
            color: var(--md-sys-color-on-surface-variant);
            font-size: 14px;
        }

        .channels-section {
            background-color: var(--md-sys-color-surface);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--md-elevation-1);
        }

        .section-header {
            padding: 20px 24px;
            background-color: var(--md-sys-color-surface-container);
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 18px;
            font-weight: 500;
            margin: 0;
        }

        .filter-controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid var(--md-sys-color-outline);
            border-radius: 8px;
            background-color: var(--md-sys-color-surface);
            color: var(--md-sys-color-on-surface);
        }

        .channels-table {
            width: 100%;
            border-collapse: collapse;
        }

        .channels-table th {
            background-color: var(--md-sys-color-surface-container-high);
            padding: 16px;
            text-align: left;
            font-weight: 500;
            color: var(--md-sys-color-on-surface);
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
        }

        .channels-table td {
            padding: 16px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
        }

        .channels-table tr:hover {
            background-color: var(--md-sys-color-surface-container);
        }

        .channel-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .channel-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--md-sys-color-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-secondary);
            font-weight: 500;
            position: relative;
        }

        .platform-icon {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--md-sys-color-surface);
        }

        .platform-wechat {
            background-color: #07C160;
            color: white;
        }

        .platform-qq {
            background-color: #12B7F5;
            color: white;
        }

        .platform-xianyu {
            background-color: #FF6A00;
            color: white;
        }

        .channel-details h4 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 500;
        }

        .channel-details p {
            margin: 0;
            font-size: 14px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-online {
            background-color: var(--md-sys-color-success-container);
            color: var(--md-sys-color-on-success-container);
        }

        .status-offline {
            background-color: var(--md-sys-color-error-container);
            color: var(--md-sys-color-on-error-container);
        }

        .status-paused {
            background-color: var(--md-sys-color-warning-container);
            color: var(--md-sys-color-on-warning-container);
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s ease-in-out;
        }

        .btn-edit {
            background-color: var(--md-sys-color-primary-container);
            color: var(--md-sys-color-on-primary-container);
        }

        .btn-edit:hover {
            background-color: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
        }

        .btn-pause {
            background-color: var(--md-sys-color-warning-container);
            color: var(--md-sys-color-on-warning-container);
        }

        .btn-pause:hover {
            background-color: var(--md-sys-color-warning);
            color: var(--md-sys-color-on-warning);
        }

        .btn-delete {
            background-color: var(--md-sys-color-error-container);
            color: var(--md-sys-color-on-error-container);
        }

        .btn-delete:hover {
            background-color: var(--md-sys-color-error);
            color: var(--md-sys-color-on-error);
        }

        .empty-state {
            text-align: center;
            padding: 48px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .empty-icon {
            font-size: 64px !important;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .fab {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            border-radius: 16px;
            background-color: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--md-elevation-3);
            transition: all 0.2s ease-in-out;
        }

        .fab:hover {
            box-shadow: var(--md-elevation-4);
            transform: translateY(-2px);
        }

        .nav-breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 24px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .nav-breadcrumb a {
            color: var(--md-sys-color-primary);
            text-decoration: none;
        }

        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .page-container {
                padding: 16px;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .channels-table {
                font-size: 14px;
            }

            .channels-table th,
            .channels-table td {
                padding: 12px 8px;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body class="md-theme-light">
    <div class="page-container">
        <!-- Breadcrumb Navigation -->
        <nav class="nav-breadcrumb">
            <a href="../dashboard/main.html">工作台</a>
            <span class="material-icons">chevron_right</span>
            <span>渠道管理</span>
        </nav>

        <!-- Page Header -->
        <header class="page-header">
            <div class="header-info">
                <h1 class="md-headline-medium">渠道管理</h1>
                <p class="md-body-medium">管理您的社交平台账号连接</p>
            </div>
            <div class="header-actions">
                <button class="md-button md-button-outlined" onclick="showHistoryChannels()">
                    <span class="material-icons">history</span>
                    历史渠道
                </button>
                <button class="md-button md-button-filled" onclick="addNewChannel()">
                    <span class="material-icons">add</span>
                    添加渠道
                </button>
            </div>
        </header>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">5</div>
                <div class="stat-label">已连接渠道</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">3</div>
                <div class="stat-label">在线渠道</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">128</div>
                <div class="stat-label">今日消息</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">95%</div>
                <div class="stat-label">连接稳定性</div>
            </div>
        </div>

        <!-- Channels Table -->
        <div class="channels-section">
            <div class="section-header">
                <h2 class="section-title">渠道列表</h2>
                <div class="filter-controls">
                    <select class="filter-select" id="platformFilter">
                        <option value="all">全部平台</option>
                        <option value="wechat">微信</option>
                        <option value="qq">QQ</option>
                        <option value="xianyu">闲鱼</option>
                    </select>
                    <select class="filter-select" id="statusFilter">
                        <option value="all">全部状态</option>
                        <option value="online">在线</option>
                        <option value="offline">离线</option>
                        <option value="paused">已暂停</option>
                    </select>
                </div>
            </div>
            <table class="channels-table" id="channelsTable">
                <thead>
                    <tr>
                        <th>账号信息</th>
                        <th>平台</th>
                        <th>状态</th>
                        <th>连接时间</th>
                        <th>最后活跃</th>
                        <th>今日消息</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="channelsTableBody">
                    <!-- 表格内容将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button class="fab" onclick="addNewChannel()" title="添加新渠道">
        <span class="material-icons">add</span>
    </button>

    <script src="../assets/js/common.js"></script>
    <script>
        class ChannelManager {
            constructor() {
                this.channels = [];
                this.filteredChannels = [];
                this.init();
            }

            init() {
                this.loadMockData();
                this.bindEvents();
                this.renderChannels();
            }

            loadMockData() {
                this.channels = [
                    {
                        id: '1',
                        name: '主店铺',
                        accountId: 'xianyu_shop_123',
                        avatar: '主',
                        platform: 'xianyu',
                        platformName: '闲鱼',
                        status: 'online',
                        connectTime: new Date('2025-08-01T10:30:00'),
                        lastActive: new Date(Date.now() - 300000), // 5分钟前
                        todayMessages: 25,
                        cookieStatus: 'valid'
                    },
                    {
                        id: '2',
                        name: '客服店铺',
                        accountId: 'xianyu_shop_456',
                        avatar: '客',
                        platform: 'xianyu',
                        platformName: '闲鱼',
                        status: 'online',
                        connectTime: new Date('2025-08-02T14:20:00'),
                        lastActive: new Date(Date.now() - 600000), // 10分钟前
                        todayMessages: 18,
                        cookieStatus: 'valid'
                    },
                    {
                        id: '3',
                        name: '官方号',
                        accountId: 'dy_789',
                        avatar: '官',
                        platform: 'wechat',
                        platformName: '微信',
                        status: 'online',
                        connectTime: new Date('2025-08-03T09:15:00'),
                        lastActive: new Date(Date.now() - 180000), // 3分钟前
                        todayMessages: 42,
                        cookieStatus: 'valid'
                    },
                    {
                        id: '4',
                        name: 'QQ群助手',
                        accountId: 'qq_12345',
                        avatar: 'Q',
                        platform: 'qq',
                        platformName: 'QQ',
                        status: 'paused',
                        connectTime: new Date('2025-07-28T16:45:00'),
                        lastActive: new Date(Date.now() - 7200000), // 2小时前
                        todayMessages: 0,
                        cookieStatus: 'expired'
                    },
                    {
                        id: '5',
                        name: '测试账号',
                        accountId: 'test_account',
                        avatar: '测',
                        platform: 'wechat',
                        platformName: '微信',
                        status: 'offline',
                        connectTime: new Date('2025-07-25T11:30:00'),
                        lastActive: new Date(Date.now() - ********), // 1天前
                        todayMessages: 0,
                        cookieStatus: 'invalid'
                    }
                ];
                this.filteredChannels = [...this.channels];
            }

            bindEvents() {
                // 筛选器事件
                document.getElementById('platformFilter').addEventListener('change', () => {
                    this.applyFilters();
                });

                document.getElementById('statusFilter').addEventListener('change', () => {
                    this.applyFilters();
                });

                // 全局函数
                window.addNewChannel = () => {
                    window.location.href = 'connection.html';
                };

                window.showHistoryChannels = () => {
                    toast.info('历史渠道功能开发中...');
                };

                window.editChannel = (id) => {
                    this.editChannel(id);
                };

                window.toggleChannelStatus = (id) => {
                    this.toggleChannelStatus(id);
                };

                window.deleteChannel = (id) => {
                    this.deleteChannel(id);
                };
            }

            applyFilters() {
                const platformFilter = document.getElementById('platformFilter').value;
                const statusFilter = document.getElementById('statusFilter').value;

                this.filteredChannels = this.channels.filter(channel => {
                    const platformMatch = platformFilter === 'all' || channel.platform === platformFilter;
                    const statusMatch = statusFilter === 'all' || channel.status === statusFilter;
                    return platformMatch && statusMatch;
                });

                this.renderChannels();
            }

            renderChannels() {
                const tbody = document.getElementById('channelsTableBody');

                if (this.filteredChannels.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7">
                                <div class="empty-state">
                                    <span class="material-icons empty-icon">account_tree</span>
                                    <h3>暂无渠道</h3>
                                    <p>点击"添加渠道"按钮开始连接您的社交平台账号</p>
                                </div>
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = this.filteredChannels.map(channel => this.renderChannelRow(channel)).join('');
            }

            renderChannelRow(channel) {
                const statusClass = `status-${channel.status}`;
                const statusText = {
                    online: '在线',
                    offline: '离线',
                    paused: '已暂停'
                }[channel.status];

                const platformClass = `platform-${channel.platform}`;
                const platformIcon = {
                    wechat: '微',
                    qq: 'Q',
                    xianyu: '闲'
                }[channel.platform];

                return `
                    <tr>
                        <td>
                            <div class="channel-info">
                                <div class="channel-avatar">
                                    ${channel.avatar}
                                    <div class="platform-icon ${platformClass}">
                                        ${platformIcon}
                                    </div>
                                </div>
                                <div class="channel-details">
                                    <h4>${channel.name}</h4>
                                    <p>${channel.accountId}</p>
                                </div>
                            </div>
                        </td>
                        <td>${channel.platformName}</td>
                        <td>
                            <span class="status-badge ${statusClass}">
                                ${statusText}
                            </span>
                        </td>
                        <td>${formatTime(channel.connectTime)}</td>
                        <td>${formatTime(channel.lastActive)}</td>
                        <td>${channel.todayMessages}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn btn-edit" onclick="editChannel('${channel.id}')" title="编辑">
                                    <span class="material-icons" style="font-size: 16px;">edit</span>
                                </button>
                                <button class="action-btn btn-pause" onclick="toggleChannelStatus('${channel.id}')"
                                        title="${channel.status === 'paused' ? '启用' : '暂停'}">
                                    <span class="material-icons" style="font-size: 16px;">
                                        ${channel.status === 'paused' ? 'play_arrow' : 'pause'}
                                    </span>
                                </button>
                                <button class="action-btn btn-delete" onclick="deleteChannel('${channel.id}')" title="删除">
                                    <span class="material-icons" style="font-size: 16px;">delete</span>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }

            async editChannel(id) {
                const channel = this.channels.find(c => c.id === id);
                if (!channel) return;

                const newAlias = await this.showEditDialog(channel);
                if (newAlias && newAlias !== channel.name) {
                    channel.name = newAlias;
                    this.renderChannels();
                    toast.success('渠道别名已更新');
                }
            }

            showEditDialog(channel) {
                return new Promise((resolve) => {
                    modal.show({
                        title: '编辑渠道',
                        content: `
                            <div class="md-text-field">
                                <label class="form-label">渠道别名</label>
                                <input type="text" id="channelAlias" value="${channel.name}"
                                       placeholder="请输入渠道别名" style="width: 100%; padding: 12px; border: 1px solid var(--md-sys-color-outline); border-radius: 8px;">
                            </div>
                            <div style="margin-top: 16px; padding: 12px; background-color: var(--md-sys-color-surface-container); border-radius: 8px;">
                                <p style="margin: 0; font-size: 14px; color: var(--md-sys-color-on-surface-variant);">
                                    <strong>账号ID:</strong> ${channel.accountId}<br>
                                    <strong>平台:</strong> ${channel.platformName}<br>
                                    <strong>连接时间:</strong> ${formatTime(channel.connectTime)}
                                </p>
                            </div>
                        `,
                        confirmText: '保存',
                        cancelText: '取消',
                        onConfirm: () => {
                            const alias = document.getElementById('channelAlias').value.trim();
                            resolve(alias);
                        },
                        onCancel: () => {
                            resolve(null);
                        }
                    });
                });
            }

            async toggleChannelStatus(id) {
                const channel = this.channels.find(c => c.id === id);
                if (!channel) return;

                const newStatus = channel.status === 'paused' ? 'online' : 'paused';
                const action = newStatus === 'paused' ? '暂停' : '启用';

                const confirmed = await modal.confirm({
                    title: `${action}渠道`,
                    content: `确定要${action}渠道"${channel.name}"吗？`,
                    confirmText: action,
                    type: newStatus === 'paused' ? 'default' : 'default'
                });

                if (confirmed) {
                    loading.show(`${action}中...`);

                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    channel.status = newStatus;
                    if (newStatus === 'online') {
                        channel.lastActive = new Date();
                    }

                    loading.hide();
                    this.renderChannels();
                    toast.success(`渠道已${action}`);
                }
            }

            async deleteChannel(id) {
                const channel = this.channels.find(c => c.id === id);
                if (!channel) return;

                const confirmed = await modal.confirm({
                    title: '删除渠道',
                    content: `
                        <p>确定要删除渠道"${channel.name}"吗？</p>
                        <p style="color: var(--md-sys-color-error); font-size: 14px; margin-top: 12px;">
                            ⚠️ 删除后该渠道将从主列表中移除，但数据会保留在历史记录中，可以随时恢复。
                        </p>
                    `,
                    confirmText: '删除',
                    type: 'danger'
                });

                if (confirmed) {
                    loading.show('删除中...');

                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 1500));

                    // 软删除：从当前列表移除，但保留数据
                    this.channels = this.channels.filter(c => c.id !== id);
                    this.applyFilters();

                    loading.hide();
                    toast.success('渠道已删除，可在历史渠道中恢复');
                }
            }
        }

        // 初始化渠道管理器
        const channelManager = new ChannelManager();
    </script>
</body>
</html>
