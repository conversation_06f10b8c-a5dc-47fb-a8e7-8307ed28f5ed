// Common JavaScript for 柴管家 Prototype

// ===== Utility Functions =====

/**
 * 格式化时间戳
 * @param {Date|string|number} timestamp 
 * @returns {string}
 */
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    // 小于1分钟
    if (diff < 60000) {
        return '刚刚';
    }
    
    // 小于1小时
    if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`;
    }
    
    // 小于24小时
    if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`;
    }
    
    // 小于7天
    if (diff < 604800000) {
        return `${Math.floor(diff / 86400000)}天前`;
    }
    
    // 超过7天显示具体日期
    return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * 生成随机ID
 * @returns {string}
 */
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

/**
 * 防抖函数
 * @param {Function} func 
 * @param {number} wait 
 * @returns {Function}
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 * @param {Function} func 
 * @param {number} limit 
 * @returns {Function}
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// ===== Toast Notification System =====

class ToastManager {
    constructor() {
        this.container = this.createContainer();
    }
    
    createContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                display: flex;
                flex-direction: column;
                gap: 8px;
            `;
            document.body.appendChild(container);
        }
        return container;
    }
    
    show(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        const colors = {
            success: 'var(--md-sys-color-success-container)',
            error: 'var(--md-sys-color-error-container)',
            warning: 'var(--md-sys-color-warning-container)',
            info: 'var(--md-sys-color-surface-container-high)'
        };
        
        toast.style.cssText = `
            background-color: ${colors[type]};
            color: var(--md-sys-color-on-surface);
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: var(--md-elevation-3);
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
            cursor: pointer;
        `;
        
        toast.textContent = message;
        
        // 点击关闭
        toast.addEventListener('click', () => {
            this.remove(toast);
        });
        
        this.container.appendChild(toast);
        
        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                this.remove(toast);
            }, duration);
        }
        
        return toast;
    }
    
    remove(toast) {
        toast.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
    
    success(message, duration) {
        return this.show(message, 'success', duration);
    }
    
    error(message, duration) {
        return this.show(message, 'error', duration);
    }
    
    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }
    
    info(message, duration) {
        return this.show(message, 'info', duration);
    }
}

// 全局Toast实例
const toast = new ToastManager();

// ===== Modal Dialog System =====

class ModalManager {
    constructor() {
        this.activeModal = null;
    }
    
    show(options) {
        const {
            title = '确认',
            content = '',
            confirmText = '确认',
            cancelText = '取消',
            onConfirm = () => {},
            onCancel = () => {},
            type = 'default'
        } = options;
        
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10001;
            animation: fadeIn 0.3s ease-out;
        `;
        
        const dialog = document.createElement('div');
        dialog.className = 'modal-dialog';
        dialog.style.cssText = `
            background-color: var(--md-sys-color-surface);
            border-radius: 12px;
            padding: 24px;
            max-width: 400px;
            width: 90%;
            box-shadow: var(--md-elevation-5);
            animation: scaleIn 0.3s ease-out;
        `;
        
        dialog.innerHTML = `
            <h3 class="modal-title md-title-large" style="margin-bottom: 16px; color: var(--md-sys-color-on-surface);">${title}</h3>
            <div class="modal-content md-body-medium" style="margin-bottom: 24px; color: var(--md-sys-color-on-surface-variant);">${content}</div>
            <div class="modal-actions" style="display: flex; gap: 12px; justify-content: flex-end;">
                <button class="md-button md-button-text modal-cancel">${cancelText}</button>
                <button class="md-button md-button-filled modal-confirm">${confirmText}</button>
            </div>
        `;
        
        // 根据类型设置样式
        if (type === 'danger') {
            const confirmBtn = dialog.querySelector('.modal-confirm');
            confirmBtn.style.backgroundColor = 'var(--md-sys-color-error)';
            confirmBtn.style.color = 'var(--md-sys-color-on-error)';
        }
        
        modal.appendChild(dialog);
        document.body.appendChild(modal);
        
        // 事件处理
        const confirmBtn = dialog.querySelector('.modal-confirm');
        const cancelBtn = dialog.querySelector('.modal-cancel');
        
        confirmBtn.addEventListener('click', () => {
            onConfirm();
            this.close(modal);
        });
        
        cancelBtn.addEventListener('click', () => {
            onCancel();
            this.close(modal);
        });
        
        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                onCancel();
                this.close(modal);
            }
        });
        
        this.activeModal = modal;
        return modal;
    }
    
    close(modal) {
        if (modal) {
            modal.style.animation = 'fadeOut 0.3s ease-in';
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
            
            if (this.activeModal === modal) {
                this.activeModal = null;
            }
        }
    }
    
    confirm(options) {
        return new Promise((resolve) => {
            this.show({
                ...options,
                onConfirm: () => resolve(true),
                onCancel: () => resolve(false)
            });
        });
    }
}

// 全局Modal实例
const modal = new ModalManager();

// ===== Loading Manager =====

class LoadingManager {
    constructor() {
        this.loadingCount = 0;
        this.overlay = null;
    }
    
    show(message = '加载中...') {
        this.loadingCount++;
        
        if (!this.overlay) {
            this.overlay = document.createElement('div');
            this.overlay.className = 'loading-overlay';
            this.overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(255, 255, 255, 0.8);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                animation: fadeIn 0.3s ease-out;
            `;
            
            this.overlay.innerHTML = `
                <div class="loading-spinner" style="
                    width: 40px;
                    height: 40px;
                    border: 4px solid var(--md-sys-color-outline-variant);
                    border-top: 4px solid var(--md-sys-color-primary);
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin-bottom: 16px;
                "></div>
                <div class="loading-text md-body-medium" style="color: var(--md-sys-color-on-surface);">${message}</div>
            `;
            
            document.body.appendChild(this.overlay);
        }
    }
    
    hide() {
        this.loadingCount = Math.max(0, this.loadingCount - 1);
        
        if (this.loadingCount === 0 && this.overlay) {
            this.overlay.style.animation = 'fadeOut 0.3s ease-in';
            setTimeout(() => {
                if (this.overlay && this.overlay.parentNode) {
                    this.overlay.parentNode.removeChild(this.overlay);
                    this.overlay = null;
                }
            }, 300);
        }
    }
}

// 全局Loading实例
const loading = new LoadingManager();

// ===== Animation Styles =====

// 添加动画样式
const animationStyles = document.createElement('style');
animationStyles.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
    
    @keyframes scaleIn {
        from {
            transform: scale(0.8);
            opacity: 0;
        }
        to {
            transform: scale(1);
            opacity: 1;
        }
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(animationStyles);

// ===== Global Event Handlers =====

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('柴管家原型系统已加载');
    
    // 初始化所有按钮的点击效果
    document.querySelectorAll('.md-button').forEach(button => {
        button.addEventListener('click', function(e) {
            // 添加点击波纹效果
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// 导出全局对象
window.ChaiGuanJia = {
    toast,
    modal,
    loading,
    utils: {
        formatTime,
        generateId,
        debounce,
        throttle
    }
};
