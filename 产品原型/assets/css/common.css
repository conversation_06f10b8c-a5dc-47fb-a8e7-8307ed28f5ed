/* Common Styles for 柴管家 Prototype */

/* ===== Reset & Base Styles ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Roboto', sans-serif;
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== Layout Components ===== */

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.container-fluid {
  width: 100%;
  padding: 0 16px;
}

/* Header */
header {
  padding: 24px 0;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.header-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.header-content h1 {
  color: var(--md-sys-color-primary);
  margin-bottom: 8px;
}

.header-content p {
  color: var(--md-sys-color-on-surface-variant);
}

/* Navigation */
.nav-bar {
  background-color: var(--md-sys-color-surface-container);
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  font-size: 20px;
  color: var(--md-sys-color-primary);
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 24px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  color: var(--md-sys-color-on-surface);
}

.nav-item:hover {
  background-color: var(--md-sys-color-primary-container);
}

.nav-item.active {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

/* Main Content */
main {
  padding: 32px 0;
  min-height: calc(100vh - 200px);
}

/* Grid Systems */
.prototype-grid {
  display: grid;
  gap: 48px;
  margin: 32px 0;
}

.prototype-section {
  margin-bottom: 48px;
}

.prototype-section h2 {
  color: var(--md-sys-color-primary);
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--md-sys-color-primary-container);
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.card-content {
  text-align: center;
}

.card-icon {
  font-size: 48px !important;
  color: var(--md-sys-color-primary);
  margin-bottom: 16px;
  display: block;
}

.card-content h3 {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 8px;
}

.card-content p {
  color: var(--md-sys-color-on-surface-variant);
}

/* Dashboard Layout */
.dashboard-layout {
  display: grid;
  grid-template-columns: 300px 1fr 320px;
  grid-template-rows: 64px 1fr;
  grid-template-areas: 
    "nav nav nav"
    "sidebar main ai-panel";
  height: 100vh;
  gap: 1px;
  background-color: var(--md-sys-color-outline-variant);
}

.dashboard-nav {
  grid-area: nav;
  background-color: var(--md-sys-color-surface-container);
}

.dashboard-sidebar {
  grid-area: sidebar;
  background-color: var(--md-sys-color-surface);
  overflow-y: auto;
}

.dashboard-main {
  grid-area: main;
  background-color: var(--md-sys-color-surface);
  overflow-y: auto;
}

.dashboard-ai-panel {
  grid-area: ai-panel;
  background-color: var(--md-sys-color-surface-container);
  overflow-y: auto;
}

/* Conversation List */
.conversation-list {
  padding: 16px;
}

.conversation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  margin-bottom: 8px;
}

.conversation-item:hover {
  background-color: var(--md-sys-color-surface-container);
}

.conversation-item.active {
  background-color: var(--md-sys-color-primary-container);
}

.conversation-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--md-sys-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--md-sys-color-on-primary);
  font-weight: 500;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-name {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-preview {
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.conversation-time {
  font-size: 12px;
  color: var(--md-sys-color-on-surface-variant);
}

.conversation-badge {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
}

/* Chat Interface */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  background-color: var(--md-sys-color-surface-container);
}

.chat-messages {
  flex: 1;
  padding: 16px 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chat-input-area {
  padding: 16px 24px;
  border-top: 1px solid var(--md-sys-color-outline-variant);
  background-color: var(--md-sys-color-surface-container);
}

.message {
  display: flex;
  gap: 12px;
  max-width: 70%;
}

.message.sent {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message.received {
  align-self: flex-start;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--md-sys-color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--md-sys-color-on-secondary);
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
}

.message-content {
  background-color: var(--md-sys-color-surface-container);
  padding: 12px 16px;
  border-radius: 16px;
  position: relative;
}

.message.sent .message-content {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

.message-text {
  margin-bottom: 4px;
}

.message-time {
  font-size: 12px;
  color: var(--md-sys-color-on-surface-variant);
}

/* AI Panel */
.ai-panel {
  padding: 16px;
}

.ai-section {
  margin-bottom: 24px;
}

.ai-section-title {
  font-weight: 500;
  margin-bottom: 12px;
  color: var(--md-sys-color-primary);
}

.ai-intent-card {
  background-color: var(--md-sys-color-ai-container);
  color: var(--md-sys-color-on-ai-container);
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 12px;
}

.ai-suggestion {
  background-color: var(--md-sys-color-surface-container);
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.ai-suggestion:hover {
  background-color: var(--md-sys-color-surface-container-high);
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-online {
  background-color: var(--md-sys-color-success-container);
  color: var(--md-sys-color-on-success-container);
}

.status-offline {
  background-color: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
}

.status-warning {
  background-color: var(--md-sys-color-warning-container);
  color: var(--md-sys-color-on-warning-container);
}

.status-ai {
  background-color: var(--md-sys-color-ai-container);
  color: var(--md-sys-color-on-ai-container);
}

/* Footer */
footer {
  padding: 24px 0;
  border-top: 1px solid var(--md-sys-color-outline-variant);
  margin-top: 48px;
}

.footer-content {
  text-align: center;
  color: var(--md-sys-color-on-surface-variant);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-layout {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "nav"
      "main";
  }
  
  .dashboard-sidebar,
  .dashboard-ai-panel {
    display: none;
  }
  
  .card-grid {
    grid-template-columns: 1fr;
  }
  
  .container {
    padding: 0 12px;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Loading States */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid var(--md-sys-color-outline-variant);
  border-radius: 50%;
  border-top-color: var(--md-sys-color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
