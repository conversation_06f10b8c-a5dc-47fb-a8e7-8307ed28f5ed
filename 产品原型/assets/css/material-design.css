/* Material Design 3.0 Design System */

/* ===== Color System ===== */
:root {
  /* Primary Colors */
  --md-sys-color-primary: #6750A4;
  --md-sys-color-on-primary: #FFFFFF;
  --md-sys-color-primary-container: #EADDFF;
  --md-sys-color-on-primary-container: #21005D;

  /* Secondary Colors */
  --md-sys-color-secondary: #625B71;
  --md-sys-color-on-secondary: #FFFFFF;
  --md-sys-color-secondary-container: #E8DEF8;
  --md-sys-color-on-secondary-container: #1D192B;

  /* Tertiary Colors */
  --md-sys-color-tertiary: #7D5260;
  --md-sys-color-on-tertiary: #FFFFFF;
  --md-sys-color-tertiary-container: #FFD8E4;
  --md-sys-color-on-tertiary-container: #31111D;

  /* Error Colors */
  --md-sys-color-error: #BA1A1A;
  --md-sys-color-on-error: #FFFFFF;
  --md-sys-color-error-container: #FFDAD6;
  --md-sys-color-on-error-container: #410002;

  /* Surface Colors */
  --md-sys-color-surface: #FEF7FF;
  --md-sys-color-on-surface: #1C1B1F;
  --md-sys-color-surface-variant: #E7E0EC;
  --md-sys-color-on-surface-variant: #49454F;
  --md-sys-color-surface-container: #F3EDF7;
  --md-sys-color-surface-container-high: #ECE6F0;
  --md-sys-color-surface-container-highest: #E6E0E9;

  /* Outline Colors */
  --md-sys-color-outline: #79747E;
  --md-sys-color-outline-variant: #CAC4D0;

  /* Success Colors (Custom) */
  --md-sys-color-success: #4CAF50;
  --md-sys-color-on-success: #FFFFFF;
  --md-sys-color-success-container: #E8F5E8;
  --md-sys-color-on-success-container: #1B5E20;

  /* Warning Colors (Custom) */
  --md-sys-color-warning: #FF9800;
  --md-sys-color-on-warning: #FFFFFF;
  --md-sys-color-warning-container: #FFF3E0;
  --md-sys-color-on-warning-container: #E65100;

  /* AI Colors (Custom) */
  --md-sys-color-ai: #2196F3;
  --md-sys-color-on-ai: #FFFFFF;
  --md-sys-color-ai-container: #E3F2FD;
  --md-sys-color-on-ai-container: #0D47A1;
}

/* ===== Typography System ===== */
.md-display-large {
  font-family: 'Roboto', sans-serif;
  font-size: 57px;
  font-weight: 400;
  line-height: 64px;
  letter-spacing: -0.25px;
}

.md-display-medium {
  font-family: 'Roboto', sans-serif;
  font-size: 45px;
  font-weight: 400;
  line-height: 52px;
  letter-spacing: 0px;
}

.md-display-small {
  font-family: 'Roboto', sans-serif;
  font-size: 36px;
  font-weight: 400;
  line-height: 44px;
  letter-spacing: 0px;
}

.md-headline-large {
  font-family: 'Roboto', sans-serif;
  font-size: 32px;
  font-weight: 400;
  line-height: 40px;
  letter-spacing: 0px;
}

.md-headline-medium {
  font-family: 'Roboto', sans-serif;
  font-size: 28px;
  font-weight: 400;
  line-height: 36px;
  letter-spacing: 0px;
}

.md-headline-small {
  font-family: 'Roboto', sans-serif;
  font-size: 24px;
  font-weight: 400;
  line-height: 32px;
  letter-spacing: 0px;
}

.md-title-large {
  font-family: 'Roboto', sans-serif;
  font-size: 22px;
  font-weight: 400;
  line-height: 28px;
  letter-spacing: 0px;
}

.md-title-medium {
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0.15px;
}

.md-title-small {
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
}

.md-body-large {
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0.5px;
}

.md-body-medium {
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.25px;
}

.md-body-small {
  font-family: 'Roboto', sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0.4px;
}

.md-label-large {
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
}

.md-label-medium {
  font-family: 'Roboto', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0.5px;
}

.md-label-small {
  font-family: 'Roboto', sans-serif;
  font-size: 11px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0.5px;
}

/* ===== Elevation System ===== */
.md-elevation-0 {
  box-shadow: none;
}

.md-elevation-1 {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
}

.md-elevation-2 {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
}

.md-elevation-3 {
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
}

.md-elevation-4 {
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
}

.md-elevation-5 {
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);
}

/* ===== Surface Colors ===== */
.md-surface {
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
}

.md-surface-container {
  background-color: var(--md-sys-color-surface-container);
  color: var(--md-sys-color-on-surface);
}

.md-surface-container-high {
  background-color: var(--md-sys-color-surface-container-high);
  color: var(--md-sys-color-on-surface);
}

.md-surface-container-highest {
  background-color: var(--md-sys-color-surface-container-highest);
  color: var(--md-sys-color-on-surface);
}

/* ===== Component Styles ===== */

/* Cards */
.md-card {
  background-color: var(--md-sys-color-surface-container);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.md-card:hover {
  background-color: var(--md-sys-color-surface-container-high);
  transform: translateY(-2px);
}

/* Buttons */
.md-button {
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
  border-radius: 20px;
  padding: 10px 24px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.md-button-filled {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.md-button-filled:hover {
  background-color: var(--md-sys-color-primary);
  box-shadow: var(--md-elevation-1);
}

.md-button-outlined {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: 1px solid var(--md-sys-color-outline);
}

.md-button-outlined:hover {
  background-color: var(--md-sys-color-primary-container);
}

.md-button-text {
  background-color: transparent;
  color: var(--md-sys-color-primary);
}

.md-button-text:hover {
  background-color: var(--md-sys-color-primary-container);
}

/* Input Fields */
.md-text-field {
  position: relative;
  margin: 16px 0;
}

.md-text-field input {
  width: 100%;
  padding: 16px;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 4px;
  font-size: 16px;
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  transition: border-color 0.2s ease-in-out;
}

.md-text-field input:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
  border-width: 2px;
}

.md-text-field label {
  position: absolute;
  top: -8px;
  left: 12px;
  background-color: var(--md-sys-color-surface);
  padding: 0 4px;
  font-size: 12px;
  color: var(--md-sys-color-primary);
  font-weight: 500;
}

/* Theme Support */
.md-theme-light {
  color-scheme: light;
}

.md-theme-dark {
  color-scheme: dark;
  /* Dark theme colors would be defined here */
}

/* Status Colors */
.status-online {
  color: var(--md-sys-color-success);
}

.status-offline {
  color: var(--md-sys-color-error);
}

.status-warning {
  color: var(--md-sys-color-warning);
}

.status-ai {
  color: var(--md-sys-color-ai);
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.align-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.gap-8 {
  gap: 8px;
}

.gap-16 {
  gap: 16px;
}

.gap-24 {
  gap: 24px;
}

.margin-16 {
  margin: 16px;
}

.padding-16 {
  padding: 16px;
}

.padding-24 {
  padding: 24px;
}
