<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作台 - 柴管家</title>
    <link rel="stylesheet" href="../assets/css/material-design.css">
    <link rel="stylesheet" href="../assets/css/common.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        .dashboard-layout {
            display: grid;
            grid-template-columns: 300px 1fr 320px;
            grid-template-rows: 64px 1fr;
            grid-template-areas:
                "nav nav nav"
                "sidebar main ai-panel";
            height: 100vh;
            gap: 1px;
            background-color: var(--md-sys-color-outline-variant);
        }

        .dashboard-nav {
            grid-area: nav;
            background-color: var(--md-sys-color-surface-container);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .nav-brand {
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            font-size: 20px;
            color: var(--md-sys-color-primary);
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 8px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out;
            color: var(--md-sys-color-on-surface);
            text-decoration: none;
        }

        .nav-item:hover {
            background-color: var(--md-sys-color-primary-container);
        }

        .nav-item.active {
            background-color: var(--md-sys-color-primary-container);
            color: var(--md-sys-color-on-primary-container);
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out;
        }

        .user-menu:hover {
            background-color: var(--md-sys-color-surface-container-high);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--md-sys-color-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-primary);
            font-weight: 500;
            font-size: 14px;
        }

        .dashboard-sidebar {
            grid-area: sidebar;
            background-color: var(--md-sys-color-surface);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
            background-color: var(--md-sys-color-surface-container);
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            border: 1px solid var(--md-sys-color-outline);
            border-radius: 20px;
            font-size: 14px;
            background-color: var(--md-sys-color-surface);
            color: var(--md-sys-color-on-surface);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--md-sys-color-primary);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--md-sys-color-on-surface-variant);
        }

        .filter-tabs {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .filter-tab {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out;
            border: 1px solid var(--md-sys-color-outline);
        }

        .filter-tab.active {
            background-color: var(--md-sys-color-primary-container);
            color: var(--md-sys-color-on-primary-container);
            border-color: var(--md-sys-color-primary);
        }

        .conversation-list {
            flex: 1;
            padding: 8px;
        }

        .conversation-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out;
            margin-bottom: 4px;
            position: relative;
        }

        .conversation-item:hover {
            background-color: var(--md-sys-color-surface-container);
        }

        .conversation-item.active {
            background-color: var(--md-sys-color-primary-container);
        }

        .conversation-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--md-sys-color-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-secondary);
            font-weight: 500;
            position: relative;
        }

        .platform-badge {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--md-sys-color-surface);
        }

        .platform-wechat {
            background-color: #07C160;
            color: white;
        }

        .platform-qq {
            background-color: #12B7F5;
            color: white;
        }

        .platform-xianyu {
            background-color: #FF6A00;
            color: white;
        }

        .conversation-info {
            flex: 1;
            min-width: 0;
        }

        .conversation-name {
            font-weight: 500;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .mode-indicator {
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
        }

        .mode-manual {
            background-color: var(--md-sys-color-surface-container);
            color: var(--md-sys-color-on-surface-variant);
        }

        .mode-ai {
            background-color: var(--md-sys-color-ai-container);
            color: var(--md-sys-color-on-ai-container);
        }

        .mode-pending {
            background-color: var(--md-sys-color-warning-container);
            color: var(--md-sys-color-on-warning-container);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        .conversation-preview {
            font-size: 14px;
            color: var(--md-sys-color-on-surface-variant);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-meta {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
        }

        .conversation-time {
            font-size: 12px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .conversation-badge {
            background-color: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 12px;
            font-weight: 500;
            min-width: 20px;
            text-align: center;
        }

        .dashboard-main {
            grid-area: main;
            background-color: var(--md-sys-color-surface);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--md-sys-color-on-surface-variant);
            text-align: center;
            padding: 48px;
        }

        .empty-icon {
            font-size: 64px !important;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .dashboard-ai-panel {
            grid-area: ai-panel;
            background-color: var(--md-sys-color-surface-container);
            overflow-y: auto;
            padding: 16px;
        }

        .ai-section {
            margin-bottom: 24px;
        }

        .ai-section-title {
            font-weight: 500;
            margin-bottom: 12px;
            color: var(--md-sys-color-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .dashboard-layout {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "nav"
                    "main";
            }

            .dashboard-sidebar,
            .dashboard-ai-panel {
                display: none;
            }
        }
    </style>
</head>
<body class="md-theme-light">
    <div class="dashboard-layout">
        <!-- Navigation Bar -->
        <nav class="dashboard-nav">
            <div class="nav-left">
                <div class="nav-brand">
                    <span class="material-icons">chat</span>
                    柴管家
                </div>
                <ul class="nav-menu">
                    <li><a href="#" class="nav-item active">
                        <span class="material-icons">dashboard</span>
                        工作台
                    </a></li>
                    <li><a href="../channel/management.html" class="nav-item">
                        <span class="material-icons">account_tree</span>
                        渠道管理
                    </a></li>
                    <li><a href="../ai/knowledge-base.html" class="nav-item">
                        <span class="material-icons">psychology</span>
                        知识库
                    </a></li>
                    <li><a href="../settings/profile.html" class="nav-item">
                        <span class="material-icons">settings</span>
                        设置
                    </a></li>
                </ul>
            </div>
            <div class="nav-right">
                <div class="status-indicator status-online">
                    <span class="material-icons" style="font-size: 16px;">wifi</span>
                    在线
                </div>
                <div class="user-menu" onclick="showUserMenu()">
                    <div class="user-avatar">张</div>
                    <span>张三</span>
                    <span class="material-icons">expand_more</span>
                </div>
            </div>
        </nav>

        <!-- Sidebar - Conversation List -->
        <aside class="dashboard-sidebar">
            <div class="sidebar-header">
                <div class="search-box">
                    <span class="material-icons search-icon">search</span>
                    <input type="text" class="search-input" placeholder="搜索会话..." id="searchInput">
                </div>
                <div class="filter-tabs">
                    <div class="filter-tab active" data-filter="all">全部</div>
                    <div class="filter-tab" data-filter="unread">未读</div>
                    <div class="filter-tab" data-filter="ai">AI托管</div>
                    <div class="filter-tab" data-filter="pending">待接管</div>
                </div>
            </div>
            <div class="conversation-list" id="conversationList">
                <!-- 会话列表项将通过JavaScript动态生成 -->
            </div>
        </aside>

        <!-- Main Chat Area -->
        <main class="dashboard-main" id="chatMain">
            <div class="empty-state">
                <span class="material-icons empty-icon">chat_bubble_outline</span>
                <h3 class="md-headline-small">选择一个会话开始聊天</h3>
                <p class="md-body-medium">从左侧选择一个会话，或等待新消息到达</p>
            </div>
        </main>

        <!-- AI Assistant Panel -->
        <aside class="dashboard-ai-panel">
            <div class="ai-section">
                <div class="ai-section-title">
                    <span class="material-icons">psychology</span>
                    AI副驾驶
                </div>
                <div class="ai-intent-card">
                    <div class="intent-label">当前意图</div>
                    <div class="intent-value">等待分析...</div>
                </div>
            </div>

            <div class="ai-section">
                <div class="ai-section-title">
                    <span class="material-icons">lightbulb</span>
                    回复建议
                </div>
                <div class="ai-suggestions" id="aiSuggestions">
                    <div style="text-align: center; color: var(--md-sys-color-on-surface-variant); padding: 24px;">
                        <span class="material-icons" style="font-size: 48px; opacity: 0.5;">smart_toy</span>
                        <p>选择会话后AI将提供回复建议</p>
                    </div>
                </div>
            </div>

            <div class="ai-section">
                <div class="ai-section-title">
                    <span class="material-icons">analytics</span>
                    今日统计
                </div>
                <div style="background-color: var(--md-sys-color-surface); padding: 12px; border-radius: 8px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>收到消息</span>
                        <strong>42</strong>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>AI自动回复</span>
                        <strong>28</strong>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>人工处理</span>
                        <strong>14</strong>
                    </div>
                </div>
            </div>
        </aside>
    </div>

    <script src="../assets/js/common.js"></script>
    <script>
        class Dashboard {
            constructor() {
                this.conversations = [];
                this.currentConversation = null;
                this.currentFilter = 'all';
                this.init();
            }

            init() {
                this.loadMockData();
                this.bindEvents();
                this.renderConversations();
                this.startMessagePolling();
            }

            loadMockData() {
                this.conversations = [
                    {
                        id: '1',
                        name: '李小明',
                        avatar: '李',
                        platform: 'wechat',
                        platformName: '微信',
                        lastMessage: '你好，请问有什么产品推荐吗？',
                        time: new Date(Date.now() - 300000), // 5分钟前
                        unreadCount: 2,
                        mode: 'manual',
                        status: 'online',
                        messages: [
                            {
                                id: 'm1',
                                type: 'received',
                                content: '你好，请问有什么产品推荐吗？',
                                time: new Date(Date.now() - 300000),
                                sender: '李小明'
                            }
                        ]
                    },
                    {
                        id: '2',
                        name: '王大华',
                        avatar: '王',
                        platform: 'qq',
                        platformName: 'QQ',
                        lastMessage: 'AI: 我们有多款优质产品，您可以看看...',
                        time: new Date(Date.now() - 600000), // 10分钟前
                        unreadCount: 0,
                        mode: 'ai',
                        status: 'online',
                        messages: [
                            {
                                id: 'm2',
                                type: 'received',
                                content: '有什么优惠活动吗？',
                                time: new Date(Date.now() - 700000),
                                sender: '王大华'
                            },
                            {
                                id: 'm3',
                                type: 'sent',
                                content: '我们有多款优质产品，您可以看看我们的最新优惠活动...',
                                time: new Date(Date.now() - 600000),
                                sender: 'AI助手',
                                isAI: true
                            }
                        ]
                    },
                    {
                        id: '3',
                        name: '闲鱼买家001',
                        avatar: '闲',
                        platform: 'xianyu',
                        platformName: '闲鱼',
                        lastMessage: '这个价格能便宜点吗？',
                        time: new Date(Date.now() - 900000), // 15分钟前
                        unreadCount: 1,
                        mode: 'pending',
                        status: 'online',
                        messages: [
                            {
                                id: 'm4',
                                type: 'received',
                                content: '这个价格能便宜点吗？',
                                time: new Date(Date.now() - 900000),
                                sender: '闲鱼买家001'
                            }
                        ]
                    }
                ];
            }

            bindEvents() {
                // 搜索功能
                const searchInput = document.getElementById('searchInput');
                searchInput.addEventListener('input', debounce(() => {
                    this.filterConversations();
                }, 300));

                // 筛选标签
                document.querySelectorAll('.filter-tab').forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        this.setFilter(e.target.dataset.filter);
                    });
                });

                // 用户菜单
                window.showUserMenu = () => {
                    modal.show({
                        title: '用户菜单',
                        content: `
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <button class="md-button md-button-text" onclick="window.location.href='../settings/profile.html'">
                                    <span class="material-icons">person</span>
                                    个人设置
                                </button>
                                <button class="md-button md-button-text" onclick="logout()">
                                    <span class="material-icons">logout</span>
                                    退出登录
                                </button>
                            </div>
                        `,
                        confirmText: '关闭',
                        onConfirm: () => {}
                    });
                };

                window.logout = async () => {
                    const confirmed = await modal.confirm({
                        title: '确认退出',
                        content: '确定要退出登录吗？',
                        type: 'default'
                    });

                    if (confirmed) {
                        toast.info('正在退出...');
                        setTimeout(() => {
                            window.location.href = '../auth/login.html';
                        }, 1000);
                    }
                };
            }

            setFilter(filter) {
                this.currentFilter = filter;

                // 更新标签样式
                document.querySelectorAll('.filter-tab').forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.filter === filter);
                });

                this.renderConversations();
            }

            filterConversations() {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                this.renderConversations(searchTerm);
            }

            renderConversations(searchTerm = '') {
                const container = document.getElementById('conversationList');
                let filteredConversations = this.conversations;

                // 应用搜索过滤
                if (searchTerm) {
                    filteredConversations = filteredConversations.filter(conv =>
                        conv.name.toLowerCase().includes(searchTerm) ||
                        conv.lastMessage.toLowerCase().includes(searchTerm)
                    );
                }

                // 应用状态过滤
                if (this.currentFilter !== 'all') {
                    filteredConversations = filteredConversations.filter(conv => {
                        switch (this.currentFilter) {
                            case 'unread':
                                return conv.unreadCount > 0;
                            case 'ai':
                                return conv.mode === 'ai';
                            case 'pending':
                                return conv.mode === 'pending';
                            default:
                                return true;
                        }
                    });
                }

                container.innerHTML = filteredConversations.map(conv => this.renderConversationItem(conv)).join('');

                // 绑定点击事件
                container.querySelectorAll('.conversation-item').forEach(item => {
                    item.addEventListener('click', () => {
                        this.selectConversation(item.dataset.id);
                    });
                });
            }

            renderConversationItem(conversation) {
                const platformClass = `platform-${conversation.platform}`;
                const modeClass = `mode-${conversation.mode}`;
                const modeText = {
                    manual: '人工',
                    ai: 'AI',
                    pending: '待接管'
                }[conversation.mode];

                return `
                    <div class="conversation-item ${this.currentConversation?.id === conversation.id ? 'active' : ''}"
                         data-id="${conversation.id}">
                        <div class="conversation-avatar">
                            ${conversation.avatar}
                            <div class="platform-badge ${platformClass}">
                                ${conversation.platform === 'wechat' ? '微' :
                                  conversation.platform === 'qq' ? 'Q' : '闲'}
                            </div>
                        </div>
                        <div class="conversation-info">
                            <div class="conversation-name">
                                ${conversation.name}
                                <span class="mode-indicator ${modeClass}">${modeText}</span>
                            </div>
                            <div class="conversation-preview">${conversation.lastMessage}</div>
                        </div>
                        <div class="conversation-meta">
                            <div class="conversation-time">${formatTime(conversation.time)}</div>
                            ${conversation.unreadCount > 0 ?
                                `<div class="conversation-badge">${conversation.unreadCount}</div>` : ''}
                        </div>
                    </div>
                `;
            }

            selectConversation(id) {
                this.currentConversation = this.conversations.find(conv => conv.id === id);
                if (!this.currentConversation) return;

                // 清除未读计数
                this.currentConversation.unreadCount = 0;

                // 重新渲染会话列表
                this.renderConversations();

                // 渲染聊天界面
                this.renderChatInterface();

                // 更新AI面板
                this.updateAIPanel();
            }

            renderChatInterface() {
                const chatMain = document.getElementById('chatMain');
                const conv = this.currentConversation;

                chatMain.innerHTML = `
                    <div class="chat-header">
                        <div class="chat-info">
                            <div class="chat-avatar">${conv.avatar}</div>
                            <div class="chat-details">
                                <h3>${conv.name}</h3>
                                <p>${conv.platformName} • ${conv.status === 'online' ? '在线' : '离线'}</p>
                            </div>
                        </div>
                        <div class="chat-actions">
                            <div class="mode-toggle">
                                <div class="mode-option ${conv.mode === 'manual' ? 'active' : ''}"
                                     onclick="dashboard.switchMode('manual')">人工模式</div>
                                <div class="mode-option ${conv.mode === 'ai' ? 'active' : ''}"
                                     onclick="dashboard.switchMode('ai')">AI托管</div>
                            </div>
                        </div>
                    </div>
                    <div class="chat-messages" id="chatMessages">
                        ${conv.messages.map(msg => this.renderMessage(msg)).join('')}
                    </div>
                    <div class="chat-input-area">
                        <div class="input-container">
                            <textarea class="input-field" id="messageInput"
                                      placeholder="输入消息..." rows="1"></textarea>
                            <button class="send-button" onclick="dashboard.sendMessage()">
                                <span class="material-icons">send</span>
                            </button>
                        </div>
                    </div>
                `;

                // 自动调整输入框高度
                const messageInput = document.getElementById('messageInput');
                messageInput.addEventListener('input', () => {
                    messageInput.style.height = 'auto';
                    messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
                });

                // 滚动到底部
                setTimeout(() => {
                    const messagesContainer = document.getElementById('chatMessages');
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }, 100);
            }

            renderMessage(message) {
                return `
                    <div class="message ${message.type}">
                        <div class="message-avatar">
                            ${message.type === 'sent' ? (message.isAI ? 'AI' : '我') :
                              this.currentConversation.avatar}
                        </div>
                        <div class="message-content">
                            <div class="message-text">${message.content}</div>
                            <div class="message-time">${formatTime(message.time)}</div>
                        </div>
                    </div>
                `;
            }

            switchMode(mode) {
                if (!this.currentConversation) return;

                this.currentConversation.mode = mode;
                this.renderChatInterface();
                this.renderConversations();

                toast.success(`已切换到${mode === 'manual' ? '人工' : 'AI托管'}模式`);
            }

            sendMessage() {
                const input = document.getElementById('messageInput');
                const content = input.value.trim();

                if (!content || !this.currentConversation) return;

                const message = {
                    id: generateId(),
                    type: 'sent',
                    content: content,
                    time: new Date(),
                    sender: '我'
                };

                this.currentConversation.messages.push(message);
                this.currentConversation.lastMessage = content;
                this.currentConversation.time = new Date();

                input.value = '';
                input.style.height = 'auto';

                this.renderChatInterface();
                this.renderConversations();

                // 模拟对方回复
                setTimeout(() => {
                    this.simulateReply();
                }, 2000);
            }

            simulateReply() {
                if (!this.currentConversation) return;

                const replies = [
                    '好的，我了解了',
                    '谢谢您的回复',
                    '请问还有其他问题吗？',
                    '这个价格可以接受',
                    '什么时候可以发货？'
                ];

                const reply = {
                    id: generateId(),
                    type: 'received',
                    content: replies[Math.floor(Math.random() * replies.length)],
                    time: new Date(),
                    sender: this.currentConversation.name
                };

                this.currentConversation.messages.push(reply);
                this.currentConversation.lastMessage = reply.content;
                this.currentConversation.time = new Date();
                this.currentConversation.unreadCount = 1;

                this.renderChatInterface();
                this.renderConversations();
                this.updateAIPanel();

                // 播放通知音效（模拟）
                toast.info('收到新消息');
            }

            updateAIPanel() {
                if (!this.currentConversation) return;

                const lastMessage = this.currentConversation.messages[this.currentConversation.messages.length - 1];

                if (lastMessage && lastMessage.type === 'received') {
                    // 模拟AI意图识别
                    const intents = ['咨询价格', '询问活动', '产品介绍', '物流咨询', '售后服务'];
                    const intent = intents[Math.floor(Math.random() * intents.length)];

                    document.querySelector('.intent-value').textContent = intent;

                    // 模拟AI回复建议
                    const suggestions = [
                        '我们的产品价格为999元，现在有优惠活动。',
                        '感谢您的咨询，我们有多款优质产品可供选择。',
                        '我们提供全国包邮服务，一般3-5个工作日到达。',
                        '我们有专业的售后团队，7*24小时为您服务。'
                    ];

                    const suggestionsHtml = suggestions.slice(0, 2).map(suggestion => `
                        <div class="ai-suggestion" onclick="dashboard.useSuggestion('${suggestion}')">
                            <div class="suggestion-text">${suggestion}</div>
                            <div class="suggestion-meta">
                                <span>置信度: 0.85</span>
                                <span>点击使用</span>
                            </div>
                        </div>
                    `).join('');

                    document.getElementById('aiSuggestions').innerHTML = suggestionsHtml;
                }
            }

            useSuggestion(suggestion) {
                const input = document.getElementById('messageInput');
                if (input) {
                    input.value = suggestion;
                    input.focus();
                    toast.success('已填入AI建议');
                }
            }

            startMessagePolling() {
                // 模拟定期接收新消息
                setInterval(() => {
                    if (Math.random() < 0.1) { // 10% 概率收到新消息
                        this.simulateNewMessage();
                    }
                }, 30000); // 每30秒检查一次
            }

            simulateNewMessage() {
                const randomConv = this.conversations[Math.floor(Math.random() * this.conversations.length)];
                const messages = [
                    '你好，在吗？',
                    '请问这个产品还有库存吗？',
                    '能发个详细介绍吗？',
                    '什么时候有优惠活动？'
                ];

                const newMessage = {
                    id: generateId(),
                    type: 'received',
                    content: messages[Math.floor(Math.random() * messages.length)],
                    time: new Date(),
                    sender: randomConv.name
                };

                randomConv.messages.push(newMessage);
                randomConv.lastMessage = newMessage.content;
                randomConv.time = new Date();
                randomConv.unreadCount++;

                this.renderConversations();

                if (this.currentConversation?.id === randomConv.id) {
                    this.renderChatInterface();
                    this.updateAIPanel();
                }

                toast.info(`${randomConv.name} 发来新消息`);
            }
        }

        // 初始化仪表板
        const dashboard = new Dashboard();
    </script>
</body>
</html>
