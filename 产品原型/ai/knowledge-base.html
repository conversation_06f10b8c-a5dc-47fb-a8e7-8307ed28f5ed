<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库管理 - 柴管家</title>
    <link rel="stylesheet" href="../assets/css/material-design.css">
    <link rel="stylesheet" href="../assets/css/common.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
        }

        .header-info h1 {
            margin: 0 0 8px 0;
            color: var(--md-sys-color-primary);
        }

        .header-info p {
            margin: 0;
            color: var(--md-sys-color-on-surface-variant);
        }

        .search-section {
            background-color: var(--md-sys-color-surface-container);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 24px;
        }

        .search-box {
            position: relative;
            max-width: 500px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 48px;
            border: 1px solid var(--md-sys-color-outline);
            border-radius: 24px;
            font-size: 16px;
            background-color: var(--md-sys-color-surface);
            color: var(--md-sys-color-on-surface);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--md-sys-color-primary);
            border-width: 2px;
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--md-sys-color-on-surface-variant);
        }

        .knowledge-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .knowledge-card {
            background-color: var(--md-sys-color-surface);
            border-radius: 12px;
            padding: 20px;
            box-shadow: var(--md-elevation-1);
            transition: all 0.2s ease-in-out;
            border: 1px solid var(--md-sys-color-outline-variant);
        }

        .knowledge-card:hover {
            box-shadow: var(--md-elevation-2);
            transform: translateY(-2px);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .card-question {
            font-weight: 500;
            color: var(--md-sys-color-on-surface);
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .card-answer {
            color: var(--md-sys-color-on-surface-variant);
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .card-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-edit {
            background-color: var(--md-sys-color-primary-container);
            color: var(--md-sys-color-on-primary-container);
        }

        .btn-edit:hover {
            background-color: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
        }

        .btn-delete {
            background-color: var(--md-sys-color-error-container);
            color: var(--md-sys-color-on-error-container);
        }

        .btn-delete:hover {
            background-color: var(--md-sys-color-error);
            color: var(--md-sys-color-on-error);
        }

        .fab {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            border-radius: 16px;
            background-color: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--md-elevation-3);
            transition: all 0.2s ease-in-out;
        }

        .fab:hover {
            box-shadow: var(--md-elevation-4);
            transform: translateY(-2px);
        }

        .empty-state {
            text-align: center;
            padding: 64px 24px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .empty-icon {
            font-size: 64px !important;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .nav-breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 24px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .nav-breadcrumb a {
            color: var(--md-sys-color-primary);
            text-decoration: none;
        }

        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }

        .stats-bar {
            display: flex;
            gap: 24px;
            margin-bottom: 24px;
            padding: 16px 20px;
            background-color: var(--md-sys-color-surface-container);
            border-radius: 12px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: var(--md-sys-color-primary);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--md-sys-color-on-surface-variant);
        }

        /* Modal Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--md-sys-color-on-surface);
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--md-sys-color-outline);
            border-radius: 8px;
            font-size: 14px;
            background-color: var(--md-sys-color-surface);
            color: var(--md-sys-color-on-surface);
            font-family: inherit;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--md-sys-color-primary);
            border-width: 2px;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .page-container {
                padding: 16px;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .knowledge-grid {
                grid-template-columns: 1fr;
            }

            .stats-bar {
                flex-wrap: wrap;
                gap: 16px;
            }
        }
    </style>
</head>
<body class="md-theme-light">
    <div class="page-container">
        <!-- Breadcrumb Navigation -->
        <nav class="nav-breadcrumb">
            <a href="../dashboard/main.html">工作台</a>
            <span class="material-icons">chevron_right</span>
            <span>知识库管理</span>
        </nav>

        <!-- Page Header -->
        <header class="page-header">
            <div class="header-info">
                <h1 class="md-headline-medium">知识库管理</h1>
                <p class="md-body-medium">管理AI回复的问答对内容</p>
            </div>
            <div class="header-actions">
                <button class="md-button md-button-outlined" onclick="importKnowledge()">
                    <span class="material-icons">upload</span>
                    导入
                </button>
                <button class="md-button md-button-filled" onclick="addKnowledge()">
                    <span class="material-icons">add</span>
                    添加问答对
                </button>
            </div>
        </header>

        <!-- Statistics -->
        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-number" id="totalCount">0</div>
                <div class="stat-label">总问答对</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="usedCount">0</div>
                <div class="stat-label">本周使用</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="hitRate">0%</div>
                <div class="stat-label">命中率</div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <div class="search-box">
                <span class="material-icons search-icon">search</span>
                <input type="text" class="search-input" id="searchInput" placeholder="搜索问题或答案...">
            </div>
        </div>

        <!-- Knowledge Grid -->
        <div class="knowledge-grid" id="knowledgeGrid">
            <!-- 知识库卡片将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- Floating Action Button -->
    <button class="fab" onclick="addKnowledge()" title="添加问答对">
        <span class="material-icons">add</span>
    </button>

    <script src="../assets/js/common.js"></script>
    <script>
        class KnowledgeBase {
            constructor() {
                this.knowledgeItems = [];
                this.filteredItems = [];
                this.init();
            }

            init() {
                this.loadMockData();
                this.bindEvents();
                this.renderKnowledge();
                this.updateStats();
            }

            loadMockData() {
                this.knowledgeItems = [
                    {
                        id: '1',
                        question: '产品价格是多少？',
                        answer: '我们的产品价格为999元，现在有限时优惠活动，可以享受8.5折优惠，优惠后价格为849元。活动截止到本月底，欢迎咨询详情。',
                        category: '价格咨询',
                        createTime: new Date('2025-08-01T10:30:00'),
                        updateTime: new Date('2025-08-10T15:20:00'),
                        usageCount: 25,
                        lastUsed: new Date(Date.now() - 3600000) // 1小时前
                    },
                    {
                        id: '2',
                        question: '有什么优惠活动吗？',
                        answer: '目前我们有以下优惠活动：\n1. 新用户首单8.5折\n2. 满500元包邮\n3. 买二送一活动（限指定商品）\n4. 会员专享9折优惠\n详情请咨询客服。',
                        category: '活动咨询',
                        createTime: new Date('2025-08-02T14:20:00'),
                        updateTime: new Date('2025-08-09T11:30:00'),
                        usageCount: 18,
                        lastUsed: new Date(Date.now() - 7200000) // 2小时前
                    },
                    {
                        id: '3',
                        question: '什么时候发货？',
                        answer: '我们的发货时间如下：\n- 现货商品：当天下单，次日发货\n- 预售商品：按预售时间发货\n- 定制商品：7-15个工作日发货\n所有商品均提供物流跟踪服务。',
                        category: '物流咨询',
                        createTime: new Date('2025-08-03T09:15:00'),
                        updateTime: new Date('2025-08-08T16:45:00'),
                        usageCount: 32,
                        lastUsed: new Date(Date.now() - 1800000) // 30分钟前
                    },
                    {
                        id: '4',
                        question: '支持退换货吗？',
                        answer: '我们支持7天无理由退换货：\n1. 商品需保持原包装完整\n2. 不影响二次销售\n3. 退货运费由买家承担\n4. 质量问题免费退换\n具体政策请查看商品详情页。',
                        category: '售后服务',
                        createTime: new Date('2025-08-04T11:45:00'),
                        updateTime: new Date('2025-08-07T13:20:00'),
                        usageCount: 15,
                        lastUsed: new Date(Date.now() - 5400000) // 1.5小时前
                    },
                    {
                        id: '5',
                        question: '产品质量怎么样？',
                        answer: '我们的产品具有以下特点：\n1. 严格的质量控制体系\n2. 通过国家相关认证\n3. 提供质量保证书\n4. 支持第三方检测\n我们对产品质量非常有信心，欢迎了解详情。',
                        category: '产品介绍',
                        createTime: new Date('2025-08-05T16:30:00'),
                        updateTime: new Date('2025-08-06T10:15:00'),
                        usageCount: 22,
                        lastUsed: new Date(Date.now() - 10800000) // 3小时前
                    }
                ];
                this.filteredItems = [...this.knowledgeItems];
            }

            bindEvents() {
                // 搜索功能
                const searchInput = document.getElementById('searchInput');
                searchInput.addEventListener('input', debounce(() => {
                    this.filterKnowledge();
                }, 300));

                // 全局函数
                window.addKnowledge = () => {
                    this.showKnowledgeDialog();
                };

                window.editKnowledge = (id) => {
                    this.editKnowledge(id);
                };

                window.deleteKnowledge = (id) => {
                    this.deleteKnowledge(id);
                };

                window.importKnowledge = () => {
                    toast.info('导入功能开发中...');
                };
            }

            filterKnowledge() {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();

                if (!searchTerm) {
                    this.filteredItems = [...this.knowledgeItems];
                } else {
                    this.filteredItems = this.knowledgeItems.filter(item =>
                        item.question.toLowerCase().includes(searchTerm) ||
                        item.answer.toLowerCase().includes(searchTerm) ||
                        item.category.toLowerCase().includes(searchTerm)
                    );
                }

                this.renderKnowledge();
            }

            renderKnowledge() {
                const container = document.getElementById('knowledgeGrid');

                if (this.filteredItems.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state" style="grid-column: 1 / -1;">
                            <span class="material-icons empty-icon">psychology</span>
                            <h3>暂无知识库内容</h3>
                            <p>点击"添加问答对"按钮开始构建您的知识库</p>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = this.filteredItems.map(item => this.renderKnowledgeCard(item)).join('');
            }

            renderKnowledgeCard(item) {
                return `
                    <div class="knowledge-card">
                        <div class="card-header">
                            <div class="card-question">${item.question}</div>
                            <div class="card-actions">
                                <button class="action-btn btn-edit" onclick="editKnowledge('${item.id}')" title="编辑">
                                    <span class="material-icons" style="font-size: 16px;">edit</span>
                                </button>
                                <button class="action-btn btn-delete" onclick="deleteKnowledge('${item.id}')" title="删除">
                                    <span class="material-icons" style="font-size: 16px;">delete</span>
                                </button>
                            </div>
                        </div>
                        <div class="card-answer">${item.answer}</div>
                        <div class="card-meta">
                            <span>分类: ${item.category}</span>
                            <span>使用 ${item.usageCount} 次</span>
                        </div>
                    </div>
                `;
            }

            updateStats() {
                const totalCount = this.knowledgeItems.length;
                const usedCount = this.knowledgeItems.filter(item =>
                    item.lastUsed && item.lastUsed > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                ).length;
                const hitRate = totalCount > 0 ? Math.round((usedCount / totalCount) * 100) : 0;

                document.getElementById('totalCount').textContent = totalCount;
                document.getElementById('usedCount').textContent = usedCount;
                document.getElementById('hitRate').textContent = `${hitRate}%`;
            }

            showKnowledgeDialog(item = null) {
                const isEdit = !!item;
                const title = isEdit ? '编辑问答对' : '添加问答对';

                modal.show({
                    title: title,
                    content: `
                        <div class="form-group">
                            <label class="form-label">问题</label>
                            <input type="text" class="form-input" id="questionInput"
                                   value="${item ? item.question : ''}" placeholder="请输入问题">
                        </div>
                        <div class="form-group">
                            <label class="form-label">答案</label>
                            <textarea class="form-input form-textarea" id="answerInput"
                                      placeholder="请输入答案">${item ? item.answer : ''}</textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">分类</label>
                            <input type="text" class="form-input" id="categoryInput"
                                   value="${item ? item.category : ''}" placeholder="请输入分类">
                        </div>
                    `,
                    confirmText: isEdit ? '保存' : '添加',
                    cancelText: '取消',
                    onConfirm: () => {
                        this.saveKnowledge(item);
                    }
                });
            }

            saveKnowledge(existingItem) {
                const question = document.getElementById('questionInput').value.trim();
                const answer = document.getElementById('answerInput').value.trim();
                const category = document.getElementById('categoryInput').value.trim();

                if (!question || !answer) {
                    toast.error('问题和答案不能为空');
                    return;
                }

                if (existingItem) {
                    // 编辑现有项
                    existingItem.question = question;
                    existingItem.answer = answer;
                    existingItem.category = category || '未分类';
                    existingItem.updateTime = new Date();
                    toast.success('问答对已更新');
                } else {
                    // 添加新项
                    const newItem = {
                        id: generateId(),
                        question: question,
                        answer: answer,
                        category: category || '未分类',
                        createTime: new Date(),
                        updateTime: new Date(),
                        usageCount: 0,
                        lastUsed: null
                    };
                    this.knowledgeItems.unshift(newItem);
                    toast.success('问答对已添加');
                }

                this.filteredItems = [...this.knowledgeItems];
                this.renderKnowledge();
                this.updateStats();
            }

            editKnowledge(id) {
                const item = this.knowledgeItems.find(k => k.id === id);
                if (item) {
                    this.showKnowledgeDialog(item);
                }
            }

            async deleteKnowledge(id) {
                const item = this.knowledgeItems.find(k => k.id === id);
                if (!item) return;

                const confirmed = await modal.confirm({
                    title: '删除问答对',
                    content: `确定要删除问题"${item.question}"吗？此操作不可恢复。`,
                    confirmText: '删除',
                    type: 'danger'
                });

                if (confirmed) {
                    this.knowledgeItems = this.knowledgeItems.filter(k => k.id !== id);
                    this.filterKnowledge();
                    this.updateStats();
                    toast.success('问答对已删除');
                }
            }
        }

        // 初始化知识库管理器
        const knowledgeBase = new KnowledgeBase();
    </script>
</body>
</html>
