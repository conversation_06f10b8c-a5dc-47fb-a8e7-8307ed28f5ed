<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>柴管家产品原型 - 导航索引</title>
    <link rel="stylesheet" href="assets/css/material-design.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="md-theme-light">
    <div class="container">
        <!-- Header -->
        <header class="md-surface-container">
            <div class="header-content">
                <h1 class="md-display-large">柴管家产品原型</h1>
                <p class="md-body-large">基于Material Design 3.0的IP运营者工作台</p>
            </div>
        </header>

        <!-- Navigation Cards -->
        <main class="prototype-grid">
            <!-- 认证模块 -->
            <section class="prototype-section">
                <h2 class="md-headline-medium">认证模块</h2>
                <div class="card-grid">
                    <div class="md-card md-elevation-1" onclick="navigateTo('auth/login.html')">
                        <div class="card-content">
                            <span class="material-icons card-icon">login</span>
                            <h3 class="md-title-large">用户登录</h3>
                            <p class="md-body-medium">验证码/密码登录，状态管理</p>
                        </div>
                    </div>
                    <div class="md-card md-elevation-1" onclick="navigateTo('auth/register.html')">
                        <div class="card-content">
                            <span class="material-icons card-icon">person_add</span>
                            <h3 class="md-title-large">用户注册</h3>
                            <p class="md-body-medium">手机号验证，密码设置</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 工作台模块 -->
            <section class="prototype-section">
                <h2 class="md-headline-medium">工作台模块</h2>
                <div class="card-grid">
                    <div class="md-card md-elevation-1" onclick="navigateTo('dashboard/main.html')">
                        <div class="card-content">
                            <span class="material-icons card-icon">dashboard</span>
                            <h3 class="md-title-large">主工作台</h3>
                            <p class="md-body-medium">统一消息处理，AI辅助回复</p>
                        </div>
                    </div>
                    <div class="md-card md-elevation-1" onclick="navigateTo('dashboard/message-center.html')">
                        <div class="card-content">
                            <span class="material-icons card-icon">chat</span>
                            <h3 class="md-title-large">消息中心</h3>
                            <p class="md-body-medium">会话列表，对话窗口</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 渠道管理模块 -->
            <section class="prototype-section">
                <h2 class="md-headline-medium">渠道管理模块</h2>
                <div class="card-grid">
                    <div class="md-card md-elevation-1" onclick="navigateTo('channel/management.html')">
                        <div class="card-content">
                            <span class="material-icons card-icon">account_tree</span>
                            <h3 class="md-title-large">渠道管理</h3>
                            <p class="md-body-medium">渠道列表，状态监控</p>
                        </div>
                    </div>
                    <div class="md-card md-elevation-1" onclick="navigateTo('channel/connection.html')">
                        <div class="card-content">
                            <span class="material-icons card-icon">link</span>
                            <h3 class="md-title-large">渠道接入</h3>
                            <p class="md-body-medium">平台授权，账号配置</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- AI助手模块 -->
            <section class="prototype-section">
                <h2 class="md-headline-medium">AI助手模块</h2>
                <div class="card-grid">
                    <div class="md-card md-elevation-1" onclick="navigateTo('ai/knowledge-base.html')">
                        <div class="card-content">
                            <span class="material-icons card-icon">psychology</span>
                            <h3 class="md-title-large">知识库管理</h3>
                            <p class="md-body-medium">问答对管理，内容维护</p>
                        </div>
                    </div>
                    <div class="md-card md-elevation-1" onclick="navigateTo('ai/assistant-panel.html')">
                        <div class="card-content">
                            <span class="material-icons card-icon">smart_toy</span>
                            <h3 class="md-title-large">AI副驾面板</h3>
                            <p class="md-body-medium">意图识别，回复建议</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 设置模块 -->
            <section class="prototype-section">
                <h2 class="md-headline-medium">设置模块</h2>
                <div class="card-grid">
                    <div class="md-card md-elevation-1" onclick="navigateTo('settings/profile.html')">
                        <div class="card-content">
                            <span class="material-icons card-icon">settings</span>
                            <h3 class="md-title-large">账户设置</h3>
                            <p class="md-body-medium">个人信息，安全设置</p>
                        </div>
                    </div>
                    <div class="md-card md-elevation-1" onclick="navigateTo('settings/notifications.html')">
                        <div class="card-content">
                            <span class="material-icons card-icon">notifications</span>
                            <h3 class="md-title-large">通知设置</h3>
                            <p class="md-body-medium">消息提醒，AI配置</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="md-surface-container">
            <div class="footer-content">
                <p class="md-body-small">© 2025 柴管家产品原型 - 基于Material Design 3.0设计规范</p>
                <p class="md-body-small">本原型展示了完整的用户流程和交互设计</p>
            </div>
        </footer>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        function navigateTo(path) {
            window.open(path, '_blank');
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            console.log('柴管家产品原型导航页面已加载');
        });
    </script>
</body>
</html>
