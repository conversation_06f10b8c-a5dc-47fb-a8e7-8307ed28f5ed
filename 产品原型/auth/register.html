<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 柴管家</title>
    <link rel="stylesheet" href="../assets/css/material-design.css">
    <link rel="stylesheet" href="../assets/css/common.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--md-sys-color-secondary-container) 0%, var(--md-sys-color-surface) 100%);
            padding: 20px 0;
        }

        .register-card {
            width: 100%;
            max-width: 450px;
            padding: 32px;
            background-color: var(--md-sys-color-surface);
            border-radius: 16px;
            box-shadow: var(--md-elevation-3);
        }

        .register-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .register-logo {
            width: 64px;
            height: 64px;
            background-color: var(--md-sys-color-secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
        }

        .register-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 16px;
            border: 1px solid var(--md-sys-color-outline);
            border-radius: 8px;
            font-size: 16px;
            background-color: var(--md-sys-color-surface);
            color: var(--md-sys-color-on-surface);
            transition: border-color 0.2s ease-in-out;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--md-sys-color-primary);
            border-width: 2px;
        }

        .form-label {
            position: absolute;
            top: -8px;
            left: 12px;
            background-color: var(--md-sys-color-surface);
            padding: 0 4px;
            font-size: 12px;
            color: var(--md-sys-color-primary);
            font-weight: 500;
        }

        .verification-group {
            display: flex;
            gap: 12px;
        }

        .verification-input {
            flex: 1;
        }

        .verification-btn {
            padding: 16px 20px;
            white-space: nowrap;
        }

        .verification-btn:disabled {
            background-color: var(--md-sys-color-surface-container);
            color: var(--md-sys-color-on-surface-variant);
            cursor: not-allowed;
        }

        .password-strength {
            margin-top: 8px;
            font-size: 12px;
        }

        .strength-bar {
            height: 4px;
            background-color: var(--md-sys-color-surface-container);
            border-radius: 2px;
            margin: 4px 0;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: width 0.3s ease-in-out;
            border-radius: 2px;
        }

        .strength-weak .strength-fill {
            width: 33%;
            background-color: var(--md-sys-color-error);
        }

        .strength-medium .strength-fill {
            width: 66%;
            background-color: var(--md-sys-color-warning);
        }

        .strength-strong .strength-fill {
            width: 100%;
            background-color: var(--md-sys-color-success);
        }

        .register-actions {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-top: 24px;
        }

        .register-links {
            text-align: center;
            margin-top: 16px;
        }

        .register-link {
            color: var(--md-sys-color-primary);
            text-decoration: none;
            font-size: 14px;
        }

        .register-link:hover {
            text-decoration: underline;
        }

        .error-message {
            color: var(--md-sys-color-error);
            font-size: 14px;
            margin-top: 8px;
            display: none;
        }

        .success-message {
            color: var(--md-sys-color-success);
            font-size: 14px;
            margin-top: 8px;
            display: none;
        }

        .terms-checkbox {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin: 16px 0;
        }

        .terms-checkbox input[type="checkbox"] {
            margin-top: 2px;
        }

        .terms-text {
            font-size: 14px;
            color: var(--md-sys-color-on-surface-variant);
            line-height: 1.4;
        }

        .terms-link {
            color: var(--md-sys-color-primary);
            text-decoration: none;
        }

        .terms-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body class="md-theme-light">
    <div class="register-container">
        <div class="register-card">
            <!-- Header -->
            <div class="register-header">
                <div class="register-logo">
                    <span class="material-icons" style="color: var(--md-sys-color-on-secondary); font-size: 32px;">person_add</span>
                </div>
                <h1 class="md-headline-small">创建账户</h1>
                <p class="md-body-medium" style="color: var(--md-sys-color-on-surface-variant);">注册您的柴管家账户</p>
            </div>

            <!-- Register Form -->
            <form class="register-form" id="registerForm">
                <!-- Phone Number -->
                <div class="form-group">
                    <label class="form-label">手机号</label>
                    <input type="tel" class="form-input" id="phoneNumber" placeholder="请输入手机号" maxlength="11">
                    <div class="error-message" id="phoneError"></div>
                </div>

                <!-- Verification Code -->
                <div class="form-group">
                    <div class="verification-group">
                        <div class="verification-input">
                            <label class="form-label">验证码</label>
                            <input type="text" class="form-input" id="verificationCode" placeholder="请输入验证码" maxlength="6">
                        </div>
                        <button type="button" class="md-button md-button-outlined verification-btn" id="getCodeBtn">
                            获取验证码
                        </button>
                    </div>
                    <div class="error-message" id="codeError"></div>
                </div>

                <!-- Password -->
                <div class="form-group">
                    <label class="form-label">设置密码</label>
                    <input type="password" class="form-input" id="password" placeholder="请设置密码">
                    <div class="password-strength" id="passwordStrength">
                        <div class="strength-bar">
                            <div class="strength-fill"></div>
                        </div>
                        <div class="strength-text">密码强度：<span id="strengthText">请输入密码</span></div>
                    </div>
                    <div class="error-message" id="passwordError"></div>
                </div>

                <!-- Confirm Password -->
                <div class="form-group">
                    <label class="form-label">确认密码</label>
                    <input type="password" class="form-input" id="confirmPassword" placeholder="请再次输入密码">
                    <div class="error-message" id="confirmPasswordError"></div>
                    <div class="success-message" id="confirmPasswordSuccess">密码匹配</div>
                </div>

                <!-- Terms Agreement -->
                <div class="terms-checkbox">
                    <input type="checkbox" id="agreeTerms">
                    <div class="terms-text">
                        我已阅读并同意
                        <a href="#" class="terms-link">《用户协议》</a>
                        和
                        <a href="#" class="terms-link">《隐私政策》</a>
                    </div>
                </div>

                <!-- Actions -->
                <div class="register-actions">
                    <button type="submit" class="md-button md-button-filled" style="padding: 16px;">
                        <span class="material-icons">person_add</span>
                        注册账户
                    </button>
                </div>

                <!-- Links -->
                <div class="register-links">
                    <span style="color: var(--md-sys-color-on-surface-variant);">已有账户？</span>
                    <a href="login.html" class="register-link">立即登录</a>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/js/common.js"></script>
    <script>
        class RegisterPage {
            constructor() {
                this.countdownTimer = null;
                this.init();
            }

            init() {
                this.bindEvents();
            }

            bindEvents() {
                // 获取验证码
                document.getElementById('getCodeBtn').addEventListener('click', () => {
                    this.getVerificationCode();
                });

                // 表单提交
                document.getElementById('registerForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleRegister();
                });

                // 实时验证
                document.getElementById('phoneNumber').addEventListener('input', () => {
                    this.validatePhone();
                });

                document.getElementById('verificationCode').addEventListener('input', () => {
                    this.validateCode();
                });

                document.getElementById('password').addEventListener('input', () => {
                    this.validatePassword();
                    this.checkPasswordStrength();
                });

                document.getElementById('confirmPassword').addEventListener('input', () => {
                    this.validateConfirmPassword();
                });

                document.getElementById('agreeTerms').addEventListener('change', () => {
                    this.validateTerms();
                });
            }

            validatePhone() {
                const phone = document.getElementById('phoneNumber').value;
                // const phoneRegex = /^1[3-9]\d{9}$/; // 已注释用于开发调试
                const errorEl = document.getElementById('phoneError');

                if (!phone) {
                    this.showError(errorEl, '请输入手机号');
                    return false;
                }

                // 注释掉格式验证，方便开发调试
                // if (!phoneRegex.test(phone)) {
                //     this.showError(errorEl, '手机号格式不正确，请输入11位数字');
                //     return false;
                // }

                this.hideError(errorEl);
                return true;
            }

            validateCode() {
                const code = document.getElementById('verificationCode').value;
                const errorEl = document.getElementById('codeError');

                if (!code) {
                    this.showError(errorEl, '请输入验证码');
                    return false;
                }

                // 注释掉格式验证，方便开发调试
                // if (!/^\d{6}$/.test(code)) {
                //     this.showError(errorEl, '验证码格式不正确');
                //     return false;
                // }

                this.hideError(errorEl);
                return true;
            }

            validatePassword() {
                const password = document.getElementById('password').value;
                const errorEl = document.getElementById('passwordError');

                if (!password) {
                    this.showError(errorEl, '请设置密码');
                    return false;
                }

                if (password.length < 8) {
                    this.showError(errorEl, '密码长度至少8位');
                    return false;
                }

                if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(password)) {
                    this.showError(errorEl, '密码必须包含字母和数字');
                    return false;
                }

                this.hideError(errorEl);
                return true;
            }

            validateConfirmPassword() {
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                const errorEl = document.getElementById('confirmPasswordError');
                const successEl = document.getElementById('confirmPasswordSuccess');

                if (!confirmPassword) {
                    this.showError(errorEl, '请确认密码');
                    this.hideSuccess(successEl);
                    return false;
                }

                if (password !== confirmPassword) {
                    this.showError(errorEl, '两次输入密码不一致');
                    this.hideSuccess(successEl);
                    return false;
                }

                this.hideError(errorEl);
                this.showSuccess(successEl, '密码匹配');
                return true;
            }

            validateTerms() {
                const agreed = document.getElementById('agreeTerms').checked;
                return agreed;
            }

            checkPasswordStrength() {
                const password = document.getElementById('password').value;
                const strengthEl = document.getElementById('passwordStrength');
                const textEl = document.getElementById('strengthText');

                if (!password) {
                    strengthEl.className = 'password-strength';
                    textEl.textContent = '请输入密码';
                    return;
                }

                let score = 0;

                // 长度检查
                if (password.length >= 8) score++;
                if (password.length >= 12) score++;

                // 字符类型检查
                if (/[a-z]/.test(password)) score++;
                if (/[A-Z]/.test(password)) score++;
                if (/\d/.test(password)) score++;
                if (/[^a-zA-Z\d]/.test(password)) score++;

                if (score <= 2) {
                    strengthEl.className = 'password-strength strength-weak';
                    textEl.textContent = '弱';
                } else if (score <= 4) {
                    strengthEl.className = 'password-strength strength-medium';
                    textEl.textContent = '中等';
                } else {
                    strengthEl.className = 'password-strength strength-strong';
                    textEl.textContent = '强';
                }
            }

            validateForm() {
                const phoneValid = this.validatePhone();
                const codeValid = this.validateCode();
                const passwordValid = this.validatePassword();
                const confirmPasswordValid = this.validateConfirmPassword();
                const termsValid = this.validateTerms();

                if (!termsValid) {
                    toast.warning('请同意用户协议和隐私政策');
                    return false;
                }

                return phoneValid && codeValid && passwordValid && confirmPasswordValid;
            }

            async getVerificationCode() {
                if (!this.validatePhone()) return;

                const btn = document.getElementById('getCodeBtn');
                const phone = document.getElementById('phoneNumber').value;

                try {
                    loading.show('发送验证码中...');

                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // 模拟检查手机号是否已注册 - 已注释用于开发调试
                    // if (phone === '13800138000') {
                    //     throw new Error('该手机号已注册，请直接登录');
                    // }

                    loading.hide();
                    toast.success('验证码已发送');
                    this.startCountdown(btn);

                } catch (error) {
                    loading.hide();
                    toast.error(error.message);
                }
            }

            startCountdown(btn) {
                let count = 60;
                btn.disabled = true;
                btn.textContent = `${count}秒后重试`;

                this.countdownTimer = setInterval(() => {
                    count--;
                    btn.textContent = `${count}秒后重试`;

                    if (count <= 0) {
                        clearInterval(this.countdownTimer);
                        btn.disabled = false;
                        btn.textContent = '获取验证码';
                    }
                }, 1000);
            }

            async handleRegister() {
                if (!this.validateForm()) return;

                const formData = {
                    phone: document.getElementById('phoneNumber').value,
                    code: document.getElementById('verificationCode').value,
                    password: document.getElementById('password').value,
                    confirmPassword: document.getElementById('confirmPassword').value
                };

                try {
                    loading.show('注册中...');

                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 模拟注册验证 - 已注释用于开发调试
                    // if (formData.code !== '123456') {
                    //     throw new Error('验证码错误，请重新输入');
                    // }

                    loading.hide();
                    toast.success('注册成功！正在为您登录...');

                    // 跳转到工作台
                    setTimeout(() => {
                        window.location.href = '../dashboard/main.html';
                    }, 1500);

                } catch (error) {
                    loading.hide();
                    toast.error(error.message);
                }
            }

            showError(element, message) {
                element.textContent = message;
                element.style.display = 'block';
            }

            hideError(element) {
                element.style.display = 'none';
            }

            showSuccess(element, message) {
                element.textContent = message;
                element.style.display = 'block';
            }

            hideSuccess(element) {
                element.style.display = 'none';
            }
        }

        // 初始化注册页面
        new RegisterPage();
    </script>
</body>
</html>
