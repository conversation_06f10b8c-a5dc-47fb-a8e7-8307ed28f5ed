# 生产环境配置文件
# 使用方式: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: "3.8"

services:
  # PostgreSQL 生产环境配置
  postgresql:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS="--encoding=UTF-8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
      # 生产环境不映射种子数据
    # 生产环境不暴露端口到主机
    networks:
      - chaiguanjia_network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: "1.0"
        reservations:
          memory: 1G
          cpus: "0.5"
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # Redis 生产环境配置
  redis:
    image: redis:7-alpine
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - redis_data:/data
      - ./infrastructure/redis/redis.prod.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - chaiguanjia_network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "0.5"
        reservations:
          memory: 512M
          cpus: "0.25"
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # RabbitMQ 生产环境配置
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
      - RABBITMQ_DEFAULT_VHOST=${RABBITMQ_VHOST}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./infrastructure/rabbitmq/rabbitmq.prod.conf:/etc/rabbitmq/rabbitmq.conf:ro
    networks:
      - chaiguanjia_network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "0.5"
        reservations:
          memory: 512M
          cpus: "0.25"
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # Elasticsearch 生产环境配置
  elasticsearch:
    image: elasticsearch:8.10.4
    environment:
      - node.name=chaiguanjia-prod-es-node
      - cluster.name=chaiguanjia-prod-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g" # 生产环境使用更多内存
      - xpack.security.enabled=true
      - xpack.security.enrollment.enabled=true
      - ELASTIC_PASSWORD=${ELASTICSEARCH_PASSWORD}
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - es_data:/usr/share/elasticsearch/data
      - ./infrastructure/elasticsearch/elasticsearch.prod.yml:/usr/share/elasticsearch/config/elasticsearch.yml:ro
    networks:
      - chaiguanjia_network
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: "2.0"
        reservations:
          memory: 2G
          cpus: "1.0"
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # Backend 生产环境配置
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      args:
        - PYTHON_VERSION=3.11
        - HTTP_PROXY=http://host.docker.internal:7897
        - HTTPS_PROXY=http://host.docker.internal:7897
        - NO_PROXY=localhost,127.0.0.1,postgresql,redis,rabbitmq,elasticsearch,minio,frontend,nginx,**********/16
    # 生产环境不设置container_name，允许Docker自动生成（与replicas冲突）
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgresql:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - RABBITMQ_URL=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/${RABBITMQ_VHOST}
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ENVIRONMENT=production
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=false
      - LOG_LEVEL=INFO
      # 生产环境运行时代理配置（仅用于访问外部API）
      - HTTP_PROXY=http://host.docker.internal:7897
      - HTTPS_PROXY=http://host.docker.internal:7897
      - NO_PROXY=localhost,127.0.0.1,postgresql,redis,rabbitmq,elasticsearch,minio,frontend,nginx,**********/16
    volumes:
      - backend_logs:/app/logs
      - backend_static:/app/static
    networks:
      - chaiguanjia_network
    depends_on:
      - postgresql
      - redis
      - rabbitmq
      - elasticsearch
    deploy:
      replicas: 2 # 生产环境运行多个实例
      resources:
        limits:
          memory: 1G
          cpus: "1.0"
        reservations:
          memory: 512M
          cpus: "0.5"
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "5"

  # Frontend 生产环境配置
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
      args:
        - NODE_VERSION=18
        - HTTP_PROXY=http://host.docker.internal:7897
        - HTTPS_PROXY=http://host.docker.internal:7897
        - NO_PROXY=localhost,127.0.0.1,backend,nginx,**********/16
        - REACT_APP_API_URL=${REACT_APP_API_URL}
        - REACT_APP_ENVIRONMENT=production
    # 生产环境不设置container_name，允许Docker自动生成（与replicas冲突）
    volumes:
      - frontend_static:/app/build
    networks:
      - chaiguanjia_network
    depends_on:
      - backend
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # Nginx 生产环境配置
  nginx:
    image: nginx:alpine
    volumes:
      - ./infrastructure/nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./infrastructure/nginx/conf.d/prod.conf:/etc/nginx/conf.d/default.conf:ro
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl:ro
      - frontend_static:/var/www/html:ro
      - backend_static:/var/www/static:ro
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - chaiguanjia_network
    depends_on:
      - backend
      - frontend
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # Celery Worker 生产环境
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      args:
        - HTTP_PROXY=http://host.docker.internal:7897
        - HTTPS_PROXY=http://host.docker.internal:7897
        - NO_PROXY=localhost,127.0.0.1,postgresql,redis,rabbitmq,elasticsearch,minio,**********/16
    # 生产环境不设置container_name（与replicas冲突）
    command: celery -A app.tasks worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgresql:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - RABBITMQ_URL=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/${RABBITMQ_VHOST}
      - ENVIRONMENT=production
      - SECRET_KEY=${SECRET_KEY}
      - LOG_LEVEL=INFO
      # Celery Worker运行时代理配置
      - HTTP_PROXY=http://host.docker.internal:7897
      - HTTPS_PROXY=http://host.docker.internal:7897
      - NO_PROXY=localhost,127.0.0.1,postgresql,redis,rabbitmq,elasticsearch,minio,**********/16
    volumes:
      - backend_logs:/app/logs
    networks:
      - chaiguanjia_network
    depends_on:
      - postgresql
      - redis
      - rabbitmq
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: "1.0"
        reservations:
          memory: 512M
          cpus: "0.5"
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "5"

  # Celery Beat 定时任务调度器
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      args:
        - HTTP_PROXY=http://host.docker.internal:7897
        - HTTPS_PROXY=http://host.docker.internal:7897
        - NO_PROXY=localhost,127.0.0.1,postgresql,redis,rabbitmq,elasticsearch,minio,**********/16
    # 生产环境不设置container_name（与replicas冲突）
    command: celery -A app.tasks beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgresql:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - RABBITMQ_URL=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/${RABBITMQ_VHOST}
      - ENVIRONMENT=production
      - SECRET_KEY=${SECRET_KEY}
      - LOG_LEVEL=INFO
      # Celery Beat运行时代理配置
      - HTTP_PROXY=http://host.docker.internal:7897
      - HTTPS_PROXY=http://host.docker.internal:7897
      - NO_PROXY=localhost,127.0.0.1,postgresql,redis,rabbitmq,elasticsearch,minio,**********/16
    volumes:
      - backend_logs:/app/logs
    networks:
      - chaiguanjia_network
    depends_on:
      - postgresql
      - redis
      - rabbitmq
    deploy:
      replicas: 1 # 定时任务只需要一个实例
      resources:
        limits:
          memory: 512M
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

volumes:
  backend_static:
    driver: local
  frontend_static:
    driver: local
