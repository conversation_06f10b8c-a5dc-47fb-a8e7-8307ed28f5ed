{"environment_verification": {"elasticsearch_status": {"success": true, "details": "服务运行正常: running", "timestamp": 1754921252.191521}, "nginx_status": {"success": true, "details": "服务运行正常: running", "timestamp": 1754921252.191542}, "postgresql_status": {"success": true, "details": "服务运行正常: running", "timestamp": 1754921252.191547}, "rabbitmq_status": {"success": true, "details": "服务运行正常: running", "timestamp": 1754921252.1915479}, "redis_status": {"success": true, "details": "服务运行正常: running", "timestamp": 1754921252.191558}, "PostgreSQL_connectivity": {"success": false, "details": "端口 5432 连接失败", "timestamp": 1754921257.197742}, "Redis_connectivity": {"success": false, "details": "端口 6379 连接失败", "timestamp": 1754921262.199093}, "RabbitMQ_connectivity": {"success": true, "details": "端口 5672 连通正常", "timestamp": 1754921262.199881}, "RabbitMQ Management_connectivity": {"success": true, "details": "端口 15672 连通正常", "timestamp": 1754921262.200306}, "Elasticsearch_connectivity": {"success": true, "details": "端口 9200 连通正常", "timestamp": 1754921262.20081}, "Nginx_connectivity": {"success": true, "details": "端口 80 连通正常", "timestamp": 1754921262.20134}}, "component_verification": {"postgresql": {"success": false, "details": "connection to server at \"localhost\" (::1), port 5432 failed: Operation timed out\n\tIs the server running on that host and accepting TCP/IP connections?\nconnection to server at \"localhost\" (127.0.0.1), port 5432 failed: Operation timed out\n\tIs the server running on that host and accepting TCP/IP connections?\n", "timestamp": 1754921277.7674398}, "redis": {"success": false, "details": "Error 60 connecting to localhost:6379. Operation timed out.", "timestamp": 1754921293.332866}, "rabbitmq": {"success": false, "details": "Timeout during AMQP handshake'localhost'/(<AddressFamily.AF_INET: 2>, <SocketKind.SOCK_STREAM: 1>, 6, '', ('127.0.0.1', 5672)); ssl=False", "timestamp": 1754921323.341911}, "elasticsearch": {"success": false, "details": "BadRequestError(400, 'media_type_header_exception', 'Invalid media-type value on headers [Accept, Content-Type]', Accept version must be either version 8 or 7, but found 9. Accept=application/vnd.elasticsearch+json; compatible-with=9)", "timestamp": 1754921323.496514}, "nginx": {"success": true, "details": "Nginx连接正常，状态码: 200", "timestamp": 1754921323.512886}}, "api_verification": {}, "data_flow_verification": {}, "performance_verification": {}, "security_verification": {}, "monitoring_verification": {}, "error_handling_verification": {}, "final_integration_verification": {}}