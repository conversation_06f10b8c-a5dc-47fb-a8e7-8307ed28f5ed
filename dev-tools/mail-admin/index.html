<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件服务管理 - 柴管家</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #6f42c1;
            padding-bottom: 10px;
        }
        .info-card {
            background: #f8f9fa;
            border: 1px solid #6c757d;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .connection-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #6f42c1;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .btn:hover {
            background: #5a32a3;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.dev {
            background: #fff3cd;
            color: #856404;
        }
        .config-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 邮件服务管理</h1>
        
        <div class="info-card">
            <h3>📊 邮件服务状态</h3>
            <p>邮件服务: <span class="status dev">开发模式</span></p>
            <p>说明: 开发环境使用控制台输出邮件内容</p>
        </div>

        <div class="connection-info">
            <h4>🔧 开发环境配置</h4>
            <p><strong>模式:</strong> 控制台输出</p>
            <p><strong>日志位置:</strong> Backend容器日志</p>
            <p><strong>测试方式:</strong> 查看API日志</p>
        </div>

        <h3>🛠️ 邮件测试工具</h3>
        <p>在开发环境中，邮件不会真实发送，而是输出到控制台。推荐使用以下工具进行邮件开发：</p>
        <ul>
            <li><strong>MailHog</strong> - 开发环境邮件捕获工具</li>
            <li><strong>MailCatcher</strong> - 简单的SMTP服务器</li>
            <li><strong>Mailtrap</strong> - 在线邮件测试服务</li>
            <li><strong>Ethereal Email</strong> - 临时邮件测试账户</li>
        </ul>

        <h3>⚡ 邮件配置示例</h3>
        <div class="config-box">
            # 开发环境邮件配置<br>
            MAIL_BACKEND=console<br>
            MAIL_FROM=<EMAIL><br>
            MAIL_FROM_NAME=柴管家系统
        </div>

        <h3>📋 快速操作</h3>
        <a href="http://localhost:8000/docs" class="btn" target="_blank">API 文档</a>
        <a href="http://localhost:8000/api/v1/health" class="btn" target="_blank">健康检查</a>
        <a href="#" class="btn" onclick="showLogs()">查看邮件日志</a>
    </div>

    <script>
        function showLogs() {
            alert('请在终端中运行以下命令查看邮件日志：\n\ndocker-compose logs backend | grep -i mail');
        }
    </script>
</body>
</html>
