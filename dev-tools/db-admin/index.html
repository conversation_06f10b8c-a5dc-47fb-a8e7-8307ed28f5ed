<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理工具 - 柴管家</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        .info-card {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .connection-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ PostgreSQL 数据库管理</h1>
        
        <div class="info-card">
            <h3>📊 数据库状态</h3>
            <p>数据库服务: <span class="status online">运行中</span></p>
            <p>连接地址: postgresql:5432</p>
            <p>数据库名: chaiguanjia</p>
        </div>

        <div class="connection-info">
            <h4>🔗 连接信息</h4>
            <p><strong>主机:</strong> localhost</p>
            <p><strong>端口:</strong> 5432</p>
            <p><strong>用户名:</strong> admin</p>
            <p><strong>密码:</strong> chaiguanjia2024</p>
            <p><strong>数据库:</strong> chaiguanjia</p>
        </div>

        <div class="connection-info">
            <h4>📝 连接字符串</h4>
            <p>postgresql://admin:chaiguanjia2024@localhost:5432/chaiguanjia</p>
        </div>

        <h3>🛠️ 推荐工具</h3>
        <p>由于网络限制，建议使用以下本地工具连接数据库：</p>
        <ul>
            <li><strong>DBeaver</strong> - 免费的通用数据库工具</li>
            <li><strong>pgAdmin</strong> - PostgreSQL 官方管理工具</li>
            <li><strong>DataGrip</strong> - JetBrains 数据库IDE</li>
            <li><strong>TablePlus</strong> - 现代数据库管理工具</li>
        </ul>

        <h3>📋 快速操作</h3>
        <a href="#" class="btn" onclick="copyToClipboard('postgresql://admin:chaiguanjia2024@localhost:5432/chaiguanjia')">复制连接字符串</a>
        <a href="http://localhost:8000/docs" class="btn" target="_blank">API 文档</a>
        <a href="http://localhost:8000/api/v1/health" class="btn" target="_blank">健康检查</a>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('连接字符串已复制到剪贴板！');
            });
        }
    </script>
</body>
</html>
