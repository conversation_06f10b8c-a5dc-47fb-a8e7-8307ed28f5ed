<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redis管理工具 - 柴管家</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 10px;
        }
        .info-card {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .connection-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #dc3545;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .btn:hover {
            background: #c82333;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        .command-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 Redis 缓存管理</h1>
        
        <div class="info-card">
            <h3>📊 Redis 状态</h3>
            <p>Redis服务: <span class="status online">运行中</span></p>
            <p>连接地址: redis:6379</p>
            <p>数据库: 0</p>
        </div>

        <div class="connection-info">
            <h4>🔗 连接信息</h4>
            <p><strong>主机:</strong> localhost</p>
            <p><strong>端口:</strong> 6379</p>
            <p><strong>密码:</strong> chaiguanjia2024</p>
            <p><strong>数据库:</strong> 0</p>
        </div>

        <div class="connection-info">
            <h4>📝 连接字符串</h4>
            <p>redis://:chaiguanjia2024@localhost:6379/0</p>
        </div>

        <h3>🛠️ 推荐工具</h3>
        <p>由于网络限制，建议使用以下本地工具连接Redis：</p>
        <ul>
            <li><strong>Redis Desktop Manager</strong> - 跨平台Redis管理工具</li>
            <li><strong>Another Redis Desktop Manager</strong> - 免费开源Redis客户端</li>
            <li><strong>RedisInsight</strong> - Redis官方可视化工具</li>
            <li><strong>Medis</strong> - macOS Redis客户端</li>
        </ul>

        <h3>⚡ 常用命令</h3>
        <div class="command-box">
            # 连接Redis<br>
            redis-cli -h localhost -p 6379 -a chaiguanjia2024
        </div>
        <div class="command-box">
            # 查看所有键<br>
            KEYS *
        </div>
        <div class="command-box">
            # 查看Redis信息<br>
            INFO
        </div>

        <h3>📋 快速操作</h3>
        <a href="#" class="btn" onclick="copyToClipboard('redis://:chaiguanjia2024@localhost:6379/0')">复制连接字符串</a>
        <a href="http://localhost:8000/docs" class="btn" target="_blank">API 文档</a>
        <a href="http://localhost:8000/api/v1/health" class="btn" target="_blank">健康检查</a>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('连接字符串已复制到剪贴板！');
            });
        }
    </script>
</body>
</html>
