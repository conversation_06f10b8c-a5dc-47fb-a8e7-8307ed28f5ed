<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elasticsearch管理工具 - 柴管家</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #28a745;
            padding-bottom: 10px;
        }
        .info-card {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .connection-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .btn:hover {
            background: #218838;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        .api-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Elasticsearch 搜索管理</h1>
        
        <div class="info-card">
            <h3>📊 Elasticsearch 状态</h3>
            <p>ES服务: <span class="status online">运行中</span></p>
            <p>连接地址: elasticsearch:9200</p>
            <p>集群名: chaiguanjia-cluster</p>
        </div>

        <div class="connection-info">
            <h4>🔗 连接信息</h4>
            <p><strong>主机:</strong> localhost</p>
            <p><strong>端口:</strong> 9200</p>
            <p><strong>协议:</strong> HTTP</p>
            <p><strong>版本:</strong> 8.10.4</p>
        </div>

        <div class="connection-info">
            <h4>📝 连接URL</h4>
            <p>http://localhost:9200</p>
        </div>

        <h3>🛠️ 推荐工具</h3>
        <p>由于网络限制，建议使用以下本地工具连接Elasticsearch：</p>
        <ul>
            <li><strong>Kibana</strong> - Elasticsearch官方可视化工具</li>
            <li><strong>ElasticHQ</strong> - 轻量级ES管理工具</li>
            <li><strong>Dejavu</strong> - Web界面ES数据浏览器</li>
            <li><strong>Postman/Insomnia</strong> - API测试工具</li>
        </ul>

        <h3>⚡ 常用API</h3>
        <div class="api-box">
            # 集群健康状态<br>
            GET http://localhost:9200/_cluster/health
        </div>
        <div class="api-box">
            # 查看所有索引<br>
            GET http://localhost:9200/_cat/indices?v
        </div>
        <div class="api-box">
            # 搜索所有文档<br>
            GET http://localhost:9200/_search
        </div>

        <h3>📋 快速操作</h3>
        <a href="#" class="btn" onclick="copyToClipboard('http://localhost:9200')">复制连接URL</a>
        <a href="http://localhost:9200" class="btn" target="_blank">ES 根路径</a>
        <a href="http://localhost:9200/_cluster/health" class="btn" target="_blank">集群状态</a>
        <a href="http://localhost:9200/_cat/indices?v" class="btn" target="_blank">查看索引</a>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('连接URL已复制到剪贴板！');
            });
        }
    </script>
</body>
</html>
