# MinIO替代方案 - 使用简单的文件服务器
version: '3.8'

services:
  # 简单文件服务器替代MinIO
  file-server:
    image: nginx:alpine
    container_name: chaiguanjia_fileserver
    environment:
      - MINIO_ROOT_USER=chaiguanjia_admin
      - MINIO_ROOT_PASSWORD=chaiguanjia2024_secret
    volumes:
      - ./file-server/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./file-server/data:/usr/share/nginx/html/files
      - ./file-server/uploads:/usr/share/nginx/html/uploads
    ports:
      - "9000:80"
      - "9001:80"  # 控制台端口，指向同一个服务
    networks:
      - chaiguanjia_network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped

networks:
  chaiguanjia_network:
    external: true

volumes:
  file_data:
    driver: local
