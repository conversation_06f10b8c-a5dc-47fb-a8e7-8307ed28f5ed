# 柴管家项目 Pre-commit 配置
# 在每次提交前自动执行代码质量检查

repos:
  # ==========================================
  # 通用文件检查
  # ==========================================
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      # 基础文件检查
      - id: trailing-whitespace        # 移除行尾空白字符
        args: [--markdown-linebreak-ext=md]
      - id: end-of-file-fixer          # 确保文件以换行符结尾
      - id: check-yaml                 # 检查YAML语法
        args: [--unsafe]               # 允许自定义标签
      - id: check-json                 # 检查JSON语法
      - id: check-toml                 # 检查TOML语法
      - id: check-xml                  # 检查XML语法
      - id: check-merge-conflict       # 检查合并冲突标记
      - id: check-case-conflict        # 检查大小写冲突
      - id: check-symlinks             # 检查符号链接
      - id: check-executables-have-shebangs  # 检查可执行文件有shebang
      - id: check-shebang-scripts-are-executable  # 检查脚本可执行权限

      # 文件大小和编码检查
      - id: check-added-large-files    # 检查大文件（默认500KB）
        args: [--maxkb=1024]          # 限制为1MB
      - id: check-byte-order-marker    # 检查BOM

      # 代码质量检查
      - id: debug-statements           # 检查调试语句
      - id: detect-private-key         # 检测私钥泄露
      - id: fix-encoding-pragma         # 修复Python编码声明
        args: [--remove]

      # 文件格式化
      - id: mixed-line-ending          # 统一行尾格式
        args: [--fix=lf]
      - id: pretty-format-json         # 格式化JSON文件
        args: [--autofix, --indent=2]

  # ==========================================
  # Python 代码检查和格式化
  # ==========================================

  # Black - Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3.11
        args: [--line-length=88]
        files: ^backend/.*\.py$

  # isort - Python导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]
        files: ^backend/.*\.py$

  # Flake8 - Python代码检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: [--config=backend/.flake8]
        files: ^backend/.*\.py$
        additional_dependencies: [
          flake8-bugbear,
          flake8-comprehensions,
          flake8-simplify,
          flake8-unused-arguments,
          flake8-pytest-style,
          flake8-docstrings,
          flake8-quotes,
          flake8-return,
          flake8-eradicate,
          flake8-print,
          flake8-debugger,
          flake8-logging-format,
          flake8-pie,
          flake8-raise,
          flake8-self,
          flake8-tidy-imports,
          flake8-type-checking,
          flake8-use-pathlib,
          flake8-commas,
          flake8-errmsg,
          flake8-future-annotations,
          flake8-import-conventions,
          pep8-naming,
        ]

  # MyPy - Python类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        args: [--config-file=backend/mypy.ini]
        files: ^backend/.*\.py$
        additional_dependencies: [
          types-redis,
          types-requests,
          types-PyYAML,
          types-python-dateutil,
          types-setuptools,
          sqlalchemy[mypy],
          pydantic,
        ]
        exclude: ^backend/(migrations|alembic)/.*\.py$

  # Bandit - Python安全检查
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, backend/, -f, json, -o, bandit-report.json]
        files: ^backend/.*\.py$
        exclude: ^backend/(tests|migrations)/.*\.py$

  # Safety - Python依赖安全检查
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        files: ^backend/requirements.*\.txt$

  # ==========================================
  # JavaScript/TypeScript 检查和格式化
  # ==========================================

  # Prettier - JS/TS/JSON/MD格式化
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        files: ^frontend/.*\.(js|jsx|ts|tsx|json|md|yml|yaml)$
        exclude: ^frontend/(node_modules|build|dist|coverage)/.*$
        additional_dependencies: [
          prettier@3.1.0,
          "@typescript-eslint/parser@6.0.0"
        ]

  # ESLint - JS/TS代码检查
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.54.0
    hooks:
      - id: eslint
        files: ^frontend/.*\.(js|jsx|ts|tsx)$
        exclude: ^frontend/(node_modules|build|dist|coverage)/.*$
        types: [file]
        additional_dependencies: [
          eslint@8.54.0,
          "@typescript-eslint/eslint-plugin@6.0.0",
          "@typescript-eslint/parser@6.0.0",
          "eslint-plugin-react@7.33.0",
          "eslint-plugin-react-hooks@4.6.0",
          "eslint-plugin-jsx-a11y@6.7.0",
          "eslint-plugin-import@2.29.0",
          "eslint-config-prettier@9.0.0",
          "eslint-plugin-prettier@5.0.0",
        ]
        args: [--fix, --config=frontend/.eslintrc.json]

  # ==========================================
  # Docker 和配置文件检查
  # ==========================================

  # Dockerfile检查
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3009, --ignore, DL3015]
        files: Dockerfile.*

  # Docker Compose检查
  - repo: https://github.com/IamTheFij/docker-pre-commit
    rev: v3.0.1
    hooks:
      - id: docker-compose-check
        files: docker-compose.*\.ya?ml$

  # ==========================================
  # 文档和Markdown检查
  # ==========================================

  # Markdown检查
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.38.0
    hooks:
      - id: markdownlint
        args: [--fix, --config=.markdownlint.json]
        files: \.md$
        exclude: ^(CHANGELOG\.md|node_modules/.*\.md)$

  # ==========================================
  # 提交信息检查
  # ==========================================

  # 提交信息格式检查
  - repo: https://github.com/compilerla/conventional-pre-commit
    rev: v3.0.0
    hooks:
      - id: conventional-pre-commit
        stages: [commit-msg]
        args: [--strict, --types=feat,fix,docs,style,refactor,test,chore,perf,ci,build,revert]

  # ==========================================
  # 安全和密钥检查
  # ==========================================

  # 密钥泄露检查
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: [--baseline, .secrets.baseline]
        exclude: package.lock.json

  # ==========================================
  # 自定义本地钩子
  # ==========================================

  - repo: local
    hooks:
      # Python测试
      - id: pytest
        name: pytest
        entry: bash -c 'cd backend && python -m pytest tests/ -v --tb=short'
        language: system
        files: ^backend/.*\.py$
        pass_filenames: false
        stages: [push]  # 只在push时运行

      # 检查requirements.txt是否同步
      - id: requirements-txt-fixer
        name: Fix requirements.txt
        entry: bash -c 'cd backend && pip-compile requirements/base.in && pip-compile requirements/development.in'
        language: system
        files: ^backend/requirements/.*\.in$
        pass_filenames: false

      # 前端测试
      - id: frontend-test
        name: Frontend Tests
        entry: bash -c 'cd frontend && npm test -- --coverage --watchAll=false'
        language: system
        files: ^frontend/.*\.(js|jsx|ts|tsx)$
        pass_filenames: false
        stages: [push]  # 只在push时运行

      # TypeScript类型检查
      - id: typescript-check
        name: TypeScript Check
        entry: bash -c 'cd frontend && npx tsc --noEmit'
        language: system
        files: ^frontend/.*\.(ts|tsx)$
        pass_filenames: false

      # 检查Docker镜像构建
      - id: docker-build-test
        name: Docker Build Test
        entry: bash -c 'docker-compose -f docker-compose.yml config'
        language: system
        files: ^(docker-compose.*\.yml|Dockerfile.*)$
        pass_filenames: false

# ==========================================
# 全局配置
# ==========================================
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: 3.4.0

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: [
    pytest,
    frontend-test,
    docker-build-test,
  ]
  submodules: false
