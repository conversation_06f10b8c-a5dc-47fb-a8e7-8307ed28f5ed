# 全景感知中心

## 用户故事 (User Story)
> 作为 **IP运营者**，我希望 **能在一个统一的界面看到并回复所有已接入账号收到的消息**，以便 **不用再在各个App之间来回切换，从而极大地提升效率**。

## 功能流程 (Feature Flow)
```mermaid
flowchart TD
    A[外部平台新消息] --> B[柴管家系统接收]
    B --> C[消息聚合到统一列表]
    C --> D[运营者查看会话列表]
    D --> E[选择特定会话]
    E --> F[查看完整对话历史]
    F --> G[在输入框输入回复]
    G --> H[点击发送按钮]
    H --> I{消息发送成功?}
    I -->|成功| J[消息通过原账号送达]
    I -->|失败| K[显示发送失败提示]
    K --> G
    J --> L[更新会话列表]
    L --> M[流程结束]
    
    style A fill:#e1f5fe
    style C fill:#e8f5e8
    style J fill:#c8e6c9
    style K fill:#ffcdd2
    style M fill:#f3e5f5
```

## 验收标准 (Acceptance Criteria)

- [ ] 验证系统能将所有已成功接入且在线的账号收到的消息实时展示在统一的会话列表中。
- [ ] 验证会话列表中每一条会话都清晰标明来源账号（使用账号别名）。
- [ ] 验证会话列表项显示联系人/群聊头像、名称、最新消息摘要、时间戳等关键信息。
- [ ] 验证会话列表显示未读消息数角标。
- [ ] 验证用户点开任一会话能看到该会话的完整上下文历史记录。
- [ ] 验证用户在对话窗口中输入回复后，消息能通过原始的接收账号准确送达给终端用户。
- [ ] 验证对话窗口顶部信息栏正确显示联系人/群聊名称和来源平台。
- [ ] 验证历史消息记录区支持滚动加载更多历史消息。
- [ ] 验证消息体能正确显示文本、图片、文件等不同类型的内容。
- [ ] 验证当消息发送失败时，系统提供明确的错误提示。
- [ ] 验证会话列表提供搜索功能，可按联系人/群聊名称或消息内容搜索。
- [ ] 验证会话列表提供筛选/排序功能，可按平台、状态、未读等条件筛选。