# 账户管理

## 用户故事 (User Story)
> 作为 **已注册的IP运营者**，我希望 **能够通过验证码或密码快速登录系统**，以便 **安全便捷地访问柴管家的各项功能**。

## 功能流程 (Feature Flow)
```mermaid
flowchart TD
    A[访问登录页面] --> B[输入手机号]
    B --> C{选择登录方式}
    
    C --> D[验证码登录<br/>默认方式]
    C --> E[密码登录<br/>切换选择]
    
    D --> F[点击获取验证码]
    F --> G[输入验证码]
    G --> H{验证码校验}
    
    E --> I[输入密码]
    I --> J{密码校验}
    
    H --> K[登录成功] 
    H --> L[验证失败]
    J --> K
    J --> L
    
    L --> M[显示错误提示]
    M --> B
    
    K --> N[建立登录会话]
    N --> O[跳转到工作台]
```

## 验收标准 (Acceptance Criteria)

- [ ] 验证登录页面默认显示验证码登录方式。
- [ ] 验证用户可以在验证码登录和密码登录之间切换。
- [ ] 验证输入手机号后，系统检查该手机号是否已注册。
- [ ] 验证未注册手机号登录时，显示"该手机号未注册，请先注册"提示。
- [ ] 验证验证码登录时，点击"获取验证码"按钮发送验证码到用户手机。
- [ ] 验证验证码发送后，按钮显示60秒倒计时，期间不可重复发送。
- [ ] 验证输入正确验证码后，用户成功登录。
- [ ] 验证输入错误验证码时，显示"验证码错误，请重新输入"提示。
- [ ] 验证验证码过期（5分钟）后，显示"验证码已过期，请重新获取"提示。
- [ ] 验证密码登录时，输入正确密码后用户成功登录。
- [ ] 验证密码登录时，输入错误密码显示"密码错误，请重新输入"提示。
- [ ] 验证连续5次登录失败后，账户被锁定30分钟。
- [ ] 验证账户锁定期间，显示"账户已锁定，请30分钟后重试"提示。
- [ ] 验证登录成功后，系统建立用户会话并记录登录信息。
- [ ] 验证登录成功后，用户自动跳转到工作台主界面。
- [ ] 验证登录页面有"忘记密码"链接，点击后跳转到密码重置页面。
- [ ] 验证登录页面有"立即注册"链接，点击后跳转到注册页面。
- [ ] 验证所有错误提示信息清晰明确，帮助用户理解问题并采取正确行动。