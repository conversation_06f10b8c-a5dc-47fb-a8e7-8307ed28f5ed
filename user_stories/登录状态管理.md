# 账户管理

## 用户故事 (User Story)
> 作为 **已登录的IP运营者**，我希望 **系统能够通过定时检测机制安全地维护我的登录状态最多7天，并在环境异常时要求重新验证**，以便 **在保证账户安全的前提下获得流畅的使用体验**。

## 功能流程 (Feature Flow)
```mermaid
stateDiagram-v2
    [*] --> 已登录状态
    已登录状态 --> 定时检测: 每5分钟执行
    
    定时检测 --> 时效检查
    定时检测 --> 环境检查
    
    时效检查 --> 会话有效: <7天
    时效检查 --> 会话过期: ≥7天
    
    环境检查 --> 环境正常: IP/设备未变化
    环境检查 --> 环境异常: IP/设备变化
    
    会话有效 --> 环境检查
    会话过期 --> 跳转登录页面
    环境异常 --> 跳转登录页面
    
    环境正常 --> 已登录状态: 继续维持
    
    跳转登录页面 --> 弹窗提示原因: 会话过期/环境异常
    弹窗提示原因 --> 重新登录流程
    重新登录流程 --> 已登录状态: 登录成功，数据完整
    
    已登录状态 --> 手动退出: 用户主动退出
    手动退出 --> 跳转登录页面
    
    已登录状态 --> 用户操作: 正常使用
    用户操作 --> 更新活跃时间
    更新活跃时间 --> 已登录状态
```

## 验收标准 (Acceptance Criteria)

- [ ] 验证用户成功登录后，系统记录登录时间戳和环境信息（IP地址、设备标识）。
- [ ] 验证系统启动定时检测任务，每5分钟执行一次会话状态检查。
- [ ] 验证定时检测任务检查所有活跃会话的时效性（是否超过7天）。
- [ ] 验证定时检测任务检查所有活跃会话的环境安全性（IP/设备是否变化）。
- [ ] 验证登录状态在7天内保持有效，用户无需重复登录。
- [ ] 验证登录状态超过7天后，定时任务自动标记会话为过期状态。
- [ ] 验证当IP地址发生变化时，定时任务检测到异常并标记会话需要重新验证。
- [ ] 验证当设备标识发生变化时，定时任务检测到异常并标记会话需要重新验证。
- [ ] 验证用户下次访问时，系统检查会话状态并根据标记执行相应操作。
- [ ] 验证会话过期时，用户访问系统会自动跳转到登录页面并弹窗提示"登录已过期，请重新登录"。
- [ ] 验证环境异常时，用户访问系统会跳转到登录页面并弹窗显示"检测到登录环境异常，为保障账户安全，请重新登录"。
- [ ] 验证用户主动退出登录时，系统立即使登录凭证失效并跳转到登录页面。
- [ ] 验证所有登录凭证失效场景下，用户的业务数据（渠道配置、消息记录、知识库等）完全保留不受影响。
- [ ] 验证用户重新登录成功后，能够正常访问之前的所有数据和配置。
- [ ] 验证弹窗提示信息清晰明确，帮助用户理解需要重新登录的原因。
- [ ] 验证在会话有效期内，用户的正常操作会更新最后活跃时间。
- [ ] 验证定时检测任务具有容错机制，网络异常时不会影响用户正常使用。
- [ ] 验证系统能够正确处理多用户并发的定时检测任务。
- [ ] 验证定时检测任务的执行不会对系统性能造成明显影响。