# 人机协作矩阵

## 用户故事 (User Story)
> 作为 **IP运营者**，我希望 **AI能像一个副驾驶一样，实时地帮我分析对方意图并提供回复建议**，以便 **不仅能加快我的回复速度，还能提升我的回复质量**。

## 功能流程 (Feature Flow)
```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 柴管家系统
    participant AI as AI引擎
    participant KB as 知识库
    
    U->>S: 选择会话进行回复
    S->>AI: 发送对话上下文
    AI->>KB: 查询相关知识
    KB-->>AI: 返回匹配内容
    AI->>AI: 分析用户意图
    AI->>AI: 生成回复建议
    AI-->>S: 返回意图分析+回复建议
    S-->>U: 显示AI分析结果和建议
    U->>U: 选择使用建议或自行输入
    U->>S: 发送最终回复
    S->>S: 更新对话记录
```

## 验收标准 (Acceptance Criteria)

- [ ] 验证系统提供知识库管理功能入口，用户可以进行问答对的增删改查操作。
- [ ] 验证用户能够手动录入问答对（FAQ），包括问题和对应答案。
- [ ] 验证知识库支持问答对的搜索功能，方便用户查找和管理。
- [ ] 验证当用户与终端用户对话时，系统在智能看板区域实时展示AI对该用户核心意图的判断结果。
- [ ] 验证AI意图识别能够识别常见意图类型（如"咨询价格"、"询问活动详情"、"产品介绍"等）。
- [ ] 验证针对当前对话的上下文，AI能实时生成至少一条完整的回复建议。
- [ ] 验证AI回复建议基于知识库内容生成，确保回复的准确性和一致性。
- [ ] 验证用户可以直接点击使用AI生成的回复建议。
- [ ] 验证用户可以在AI建议基础上进行修改后发送。
- [ ] 验证当知识库中没有相关内容时，AI能够提示"建议人工处理"或类似信息。
- [ ] 验证AI副驾模块的界面布局清晰，不干扰正常的对话操作。
- [ ] 验证系统能够处理多轮对话的上下文，AI分析和建议考虑完整的对话历史。