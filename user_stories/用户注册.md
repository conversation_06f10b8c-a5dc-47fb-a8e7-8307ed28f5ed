# 账户管理

## 用户故事 (User Story)
> 作为 **新用户**，我希望 **能够使用手机号和验证码进行注册并设置密码**，以便 **创建账户并开始使用柴管家系统**。

## 功能流程 (Feature Flow)
```mermaid
flowchart TD
    A[用户进入注册页面] --> B[输入手机号]
    B --> C[点击获取验证码]
    C --> D{手机号格式验证}
    D -->|格式错误| E[显示格式错误提示]
    E --> B
    D -->|格式正确| F[发送验证码]
    F --> G[用户输入验证码]
    G --> H[用户设置密码]
    H --> I[用户确认密码]
    I --> J[点击注册按钮]
    J --> K{验证码和密码校验}
    K -->|验证失败| L[显示错误信息]
    L --> G
    K -->|验证成功| M[创建用户账户]
    M --> N[自动登录]
    N --> O[跳转到主界面]
    O --> P[注册流程结束]
    
    style A fill:#e1f5fe
    style M fill:#c8e6c9
    style E fill:#ffcdd2
    style L fill:#ffcdd2
    style P fill:#f3e5f5
```

## 验收标准 (Acceptance Criteria)

- [ ] 验证注册页面提供手机号输入框，支持中国大陆手机号格式（11位数字）。
- [ ] 验证手机号格式校验功能，输入非法格式时显示明确的错误提示。
- [ ] 验证获取验证码按钮在手机号格式正确时可点击，格式错误时禁用。
- [ ] 验证验证码发送成功后，按钮显示倒计时（60秒），倒计时期间不可重复发送。
- [ ] 验证验证码输入框只接受6位数字。
- [ ] 验证密码设置功能，密码长度至少8位，包含字母和数字。
- [ ] 验证确认密码功能，两次输入密码必须一致。
- [ ] 验证当验证码错误时，系统提示"验证码错误，请重新输入"。
- [ ] 验证当验证码过期时（5分钟），系统提示用户重新获取验证码。
- [ ] 验证当手机号已注册时，系统提示"该手机号已注册，请直接登录"。
- [ ] 验证注册成功后，系统自动为用户登录并跳转到主界面。
- [ ] 验证注册成功后，用户信息正确保存到系统中。
- [ ] 验证注册页面提供"已有账户？立即登录"的链接跳转到登录页面。