# 人机协作矩阵

## 用户故事 (User Story)
> 作为 **IP运营者**，我希望 **能将常规对话放心地交给AI自动处理，但当AI对回复没有把握时，它应该足够"聪明"地停下来并向我求助，同时我也能随时无缝地接管任何对话**，以便 **实现效率与安全的统一**。

## 功能流程 (Feature Flow)
```mermaid
stateDiagram-v2
    [*] --> 人工模式
    人工模式 --> AI托管模式: 运营者切换模式
    AI托管模式 --> AI处理中: 收到新消息
    AI处理中 --> 置信度评估: AI生成回复
    置信度评估 --> 自动发送: 置信度≥0.8
    置信度评估 --> 待人工接管: 置信度<0.8
    自动发送 --> AI托管模式: 发送成功
    待人工接管 --> 人工模式: 运营者接管
    人工模式 --> AI托管模式: 运营者重新启用
    AI托管模式 --> 人工模式: 运营者主动接管
    
    note right of 置信度评估
        AI评估回复质量
        决定是否自动发送
    end note
    
    note right of 待人工接管
        系统高亮标记
        通知运营者介入
    end note
```

## 验收标准 (Acceptance Criteria)

- [ ] 验证用户能够为单个会话设置工作模式，至少包含"人工模式"（默认）和"AI托管模式"。
- [ ] 验证会话列表中用清晰的视觉方式标明会话的当前状态："人工模式"、"AI托管中"、"待人工接管"。
- [ ] 验证当会话处于"AI托管模式"时，系统收到新消息后AI能生成回复内容并给出置信度分数（范围0-1）。
- [ ] 验证当置信度≥0.8时，系统自动发送AI生成的回复。
- [ ] 验证当置信度<0.8时，系统绝不自动发送该回复。
- [ ] 验证当置信度<0.8时，该会话的AI托管状态被自动暂停。
- [ ] 验证当置信度<0.8时，系统向人工运营者发出通知。
- [ ] 验证当置信度<0.8时，该会话在列表中被高亮标记为"待人工接管"。
- [ ] 验证运营者能通过点击"接管"按钮立即接管任何一个会话。
- [ ] 验证运营者能通过直接在输入框输入文字来接管会话。
- [ ] 验证一旦人工接管成功，该会话的状态自动切换回"人工模式"。
- [ ] 验证运营者可以随时将"人工模式"的会话切换为"AI托管模式"。
- [ ] 验证系统提供通知设置，允许用户配置"待人工接管"的通知方式（桌面通知/声音提醒）。
- [ ] 验证AI托管过程中的所有自动回复都有明确的日志记录，便于后续审查。