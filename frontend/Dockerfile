# 前端Dockerfile - 柴管家项目
FROM node:18-alpine

# 声明接收从命令行传入的构建参数
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 配置npm使用代理和国内镜像源
RUN npm config set registry https://registry.npmmirror.com && \
    if [ -n "$HTTP_PROXY" ]; then \
    npm config set proxy "$HTTP_PROXY" && \
    npm config set https-proxy "$HTTPS_PROXY"; \
    fi && \
    npm install --silent || \
    (npm config delete proxy && npm config delete https-proxy && npm install --silent)

# 复制源代码
COPY . .

# 构建生产版本
RUN npm run build

# 使用nginx提供静态文件
FROM nginx:alpine
COPY --from=0 /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
