{"$schema": "https://json.schemastore.org/prettierrc", "printWidth": 80, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": true, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "rangeStart": 0, "rangeEnd": null, "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "overrides": [{"files": "*.json", "options": {"printWidth": 120, "tabWidth": 2}}, {"files": "*.md", "options": {"printWidth": 100, "proseWrap": "always"}}, {"files": "*.yml", "options": {"tabWidth": 2, "singleQuote": false}}, {"files": "*.yaml", "options": {"tabWidth": 2, "singleQuote": false}}]}