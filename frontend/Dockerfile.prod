# 生产环境Dockerfile - 柴管家前端
FROM node:18-alpine as builder

# 声明接收从命令行传入的构建参数
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY
ARG REACT_APP_API_URL
ARG REACT_APP_ENVIRONMENT

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 配置npm使用代理和国内镜像源
RUN npm config set registry https://registry.npmmirror.com && \
    if [ -n "$HTTP_PROXY" ]; then \
        npm config set proxy "$HTTP_PROXY" && \
        npm config set https-proxy "$HTTPS_PROXY"; \
    fi && \
    npm ci --only=production --silent || \
    (npm config delete proxy && npm config delete https-proxy && npm ci --only=production --silent)

# 复制源代码
COPY . .

# 设置构建环境变量
ENV REACT_APP_API_URL=$REACT_APP_API_URL
ENV REACT_APP_ENVIRONMENT=$REACT_APP_ENVIRONMENT

# 构建生产版本
RUN npm run build

# 生产阶段 - 使用nginx提供静态文件
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/build /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 创建非root用户
RUN addgroup -g 1001 -S nginx && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# 切换到非root用户
USER nginx

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --quiet --tries=1 --spider http://localhost:80/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
