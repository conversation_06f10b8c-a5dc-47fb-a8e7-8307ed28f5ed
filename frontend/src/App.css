.App {
  text-align: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.App-header {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  color: white;
}

.App-header h1 {
  margin: 0 0 1rem 0;
  font-size: 2.5rem;
}

.control-panel {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-size: 0.9rem;
}

.control-group select {
  padding: 0.5rem;
  border-radius: 5px;
  border: none;
  background: white;
  color: #333;
}

.refresh-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.3s ease;
}

.refresh-btn:hover {
  background: #219a52;
}

.last-update {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
}

.App-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.status-section, .services-section, .links-section, .metrics-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.status-section h2, .services-section h2, .links-section h2, .metrics-section h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.metric-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  opacity: 0.9;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
  color: #fff;
}

.metric-detail {
  font-size: 0.85rem;
  opacity: 0.8;
  margin: 0;
}

.status-card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.status-card.healthy {
  border-left: 5px solid #27ae60;
}

.status-card.loading {
  border-left: 5px solid #f39c12;
}

.status-card.unhealthy {
  border-left: 5px solid #e74c3c;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.service-card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
}

.service-card.success {
  border-left: 5px solid #27ae60;
}

.service-card.error {
  border-left: 5px solid #e74c3c;
}

.service-card h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.link-card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  text-decoration: none;
  color: #2c3e50;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-left: 5px solid #3498db;
}

.link-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  color: #2c3e50;
  text-decoration: none;
}

.link-card h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.link-card p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.App-footer {
  background: rgba(0, 0, 0, 0.1);
  color: white;
  padding: 1rem;
  margin-top: auto;
}

.App-footer p {
  margin: 0.5rem 0;
}

@media (max-width: 768px) {
  .App-main {
    padding: 1rem;
  }

  .App-header h1 {
    font-size: 2rem;
  }

  .services-grid, .links-grid {
    grid-template-columns: 1fr;
  }
}
