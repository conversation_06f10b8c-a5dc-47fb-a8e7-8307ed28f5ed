import React, { useEffect, useState } from 'react';
import './App.css';

function App() {
  const [healthStatus, setHealthStatus] = useState(null);
  const [services, setServices] = useState({});
  const [systemMetrics, setSystemMetrics] = useState(null);
  const [refreshInterval, setRefreshInterval] = useState(30);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);

  // 获取系统状态数据
  const fetchSystemData = async () => {
    try {
      // 检查后端健康状态
      const healthResponse = await fetch('/api/health');
      const healthData = await healthResponse.json();
      setHealthStatus(healthData);

      // 检查各个服务状态
      const serviceChecks = [
        { name: 'database', url: '/api/test/database' },
        { name: 'redis', url: '/api/test/redis' },
        { name: 'rabbitmq', url: '/api/test/rabbitmq' },
      ];

      const results = {};
      for (const service of serviceChecks) {
        try {
          const response = await fetch(service.url);
          const data = await response.json();
          results[service.name] = { status: 'success', data };
        } catch (error) {
          results[service.name] = { status: 'error', error: error.message };
        }
      }
      setServices(results);

      // 获取系统状态
      try {
        const statusResponse = await fetch('/api/status');
        const statusData = await statusResponse.json();
        setSystemMetrics(statusData);
      } catch (error) {
        console.warn('获取系统状态失败:', error);
      }

      setLastUpdate(new Date());
    } catch (error) {
      console.error('获取系统数据失败:', error);
    }
  };

  // 初始化和自动刷新
  useEffect(() => {
    fetchSystemData();

    let interval;
    if (isAutoRefresh) {
      interval = setInterval(fetchSystemData, refreshInterval * 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [refreshInterval, isAutoRefresh]);

  return (
    <div className='App'>
      <header className='App-header'>
        <h1>🔧 柴管家系统监控面板</h1>
        <p>多平台聚合智能客服系统</p>

        <div className='control-panel'>
          <div className='control-group'>
            <label>
              <input
                type='checkbox'
                checked={isAutoRefresh}
                onChange={e => setIsAutoRefresh(e.target.checked)}
              />
              自动刷新
            </label>
            <select
              value={refreshInterval}
              onChange={e => setRefreshInterval(Number(e.target.value))}
              disabled={!isAutoRefresh}
            >
              <option value={10}>10秒</option>
              <option value={30}>30秒</option>
              <option value={60}>60秒</option>
              <option value={300}>5分钟</option>
            </select>
          </div>

          <button onClick={fetchSystemData} className='refresh-btn'>
            🔄 立即刷新
          </button>

          {lastUpdate && (
            <div className='last-update'>
              最后更新: {lastUpdate.toLocaleTimeString()}
            </div>
          )}
        </div>
      </header>

      <main className='App-main'>
        <section className='status-section'>
          <h2>系统状态</h2>
          {healthStatus ? (
            <div className={`status-card ${healthStatus.status}`}>
              <h3>后端API服务</h3>
              <p>状态: {healthStatus.status}</p>
              <p>版本: {healthStatus.version}</p>
              <p>运行时间: {healthStatus.uptime_seconds} 秒</p>
              <p>服务: {healthStatus.service}</p>
            </div>
          ) : (
            <div className='status-card loading'>
              <p>正在检查系统状态...</p>
            </div>
          )}
        </section>

        {systemMetrics && (
          <section className='metrics-section'>
            <h2>系统运行指标</h2>
            <div className='metrics-grid'>
              <div className='metric-card'>
                <h3>📊 系统运行时间</h3>
                <p className='metric-value'>{systemMetrics.uptime_human}</p>
                <p className='metric-detail'>
                  {systemMetrics.uptime_seconds} 秒
                </p>
              </div>

              <div className='metric-card'>
                <h3>⚡ 服务版本</h3>
                <p className='metric-value'>{systemMetrics.version}</p>
                <p className='metric-detail'>
                  API框架: {systemMetrics.framework || 'FastAPI'}
                </p>
              </div>

              <div className='metric-card'>
                <h3>🕒 服务启动时间</h3>
                <p className='metric-value'>
                  {new Date(systemMetrics.timestamp).toLocaleString()}
                </p>
                <p className='metric-detail'>运行状态良好</p>
              </div>

              <div className='metric-card'>
                <h3>🔗 API状态</h3>
                <p className='metric-value'>
                  {healthStatus?.status === 'healthy' ? '🟢 正常' : '🔴 异常'}
                </p>
                <p className='metric-detail'>
                  数据库:{' '}
                  {healthStatus?.database === 'configured' ? '✅' : '❌'} |
                  缓存: {healthStatus?.redis === 'configured' ? '✅' : '❌'}
                </p>
              </div>
            </div>
          </section>
        )}

        <section className='services-section'>
          <h2>基础服务状态</h2>
          <div className='services-grid'>
            {Object.entries(services).map(([serviceName, serviceData]) => (
              <div
                key={serviceName}
                className={`service-card ${serviceData.status}`}
              >
                <h3>{serviceName.toUpperCase()}</h3>
                <p>状态: {serviceData.status}</p>
                {serviceData.status === 'success' && serviceData.data && (
                  <p>消息: {serviceData.data.message}</p>
                )}
                {serviceData.status === 'error' && (
                  <p>错误: {serviceData.error}</p>
                )}
              </div>
            ))}
          </div>
        </section>

        <section className='links-section'>
          <h2>服务访问链接</h2>
          <div className='links-grid'>
            <a
              href='/api/health'
              target='_blank'
              rel='noopener noreferrer'
              className='link-card'
            >
              <h3>API健康检查</h3>
              <p>查看详细的系统健康状态</p>
            </a>
            <a
              href='/docs'
              target='_blank'
              rel='noopener noreferrer'
              className='link-card'
            >
              <h3>API文档</h3>
              <p>查看完整的API接口文档</p>
            </a>
            <a
              href='http://localhost:15672'
              target='_blank'
              rel='noopener noreferrer'
              className='link-card'
            >
              <h3>RabbitMQ管理</h3>
              <p>消息队列管理界面</p>
            </a>
            <a
              href='http://localhost:9200'
              target='_blank'
              rel='noopener noreferrer'
              className='link-card'
            >
              <h3>Elasticsearch</h3>
              <p>搜索引擎服务</p>
            </a>
          </div>
        </section>
      </main>

      <footer className='App-footer'>
        <p>柴管家基础设施监控面板 v1.0.0</p>
        <p>Task I-1.3: 基础服务部署</p>
      </footer>
    </div>
  );
}

export default App;
