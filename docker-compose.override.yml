# 开发环境配置覆盖文件
# 此文件会自动与 docker-compose.yml 合并

version: '3.8'

services:
  # PostgreSQL 开发环境配置
  postgresql:
    environment:
      - POSTGRES_DB=chaiguanjia_dev
      - POSTGRES_USER=dev_admin
      - POSTGRES_PASSWORD=dev_password
    volumes:
      # 添加开发数据种子
      - ./database/seeds/development:/docker-entrypoint-initdb.d/seeds:ro
    ports:
      - "5432:5432"  # 固定端口便于开发连接

  # Redis 开发环境配置
  redis:
    command: redis-server --appendonly yes --requirepass dev_password
    ports:
      - "6379:6379"

  # RabbitMQ 开发环境配置
  rabbitmq:
    environment:
      - RABBITMQ_DEFAULT_USER=dev_admin
      - RABBITMQ_DEFAULT_PASS=dev_password
      - RABBITMQ_DEFAULT_VHOST=chaiguanjia_dev
    ports:
      - "5672:5672"
      - "15672:15672"  # 管理界面

  # Elasticsearch 开发环境配置
  elasticsearch:
    environment:
      - node.name=chaiguanjia-dev-es-node
      - cluster.name=chaiguanjia-dev-cluster
      - "ES_JAVA_OPTS=-Xms256m -Xmx256m"  # 开发环境降低内存使用
    ports:
      - "9200:9200"
      - "9300:9300"

  # Backend 开发环境配置
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile  # 使用标准 Dockerfile
    environment:
      - DATABASE_URL=***************************************************/chaiguanjia_dev
      - REDIS_URL=redis://:dev_password@redis:6379/0
      - RABBITMQ_URL=amqp://dev_admin:dev_password@rabbitmq:5672/chaiguanjia_dev
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - RELOAD=true
    volumes:
      # 代码热重载
      - ./backend:/app:delegated
      - /app/.venv  # 保护虚拟环境
    ports:
      - "8000:8000"
      - "5678:5678"  # 调试端口
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug

  # Frontend 开发环境配置
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_ENVIRONMENT=development
      - REACT_APP_DEBUG=true
      - CHOKIDAR_USEPOLLING=true  # 文件监听
    volumes:
      # 代码热重载
      - ./frontend:/app:delegated
      - /app/node_modules  # 保护 node_modules
    ports:
      - "3000:3000"
    stdin_open: true
    tty: true
    command: npm start

  # Nginx 开发环境配置
  nginx:
    volumes:
      - ./infrastructure/nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
      - ./infrastructure/nginx/conf.d/dev.conf:/etc/nginx/conf.d/default.conf:ro
    ports:
      - "80:80"

  # 开发工具容器
  # pgAdmin - PostgreSQL 管理工具
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: chaiguanjia_pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "5050:80"
    networks:
      - chaiguanjia_network
    depends_on:
      - postgresql

  # Redis Commander - Redis 管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: chaiguanjia_redis_commander
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dev_password
    ports:
      - "8081:8081"
    networks:
      - chaiguanjia_network
    depends_on:
      - redis

  # Elasticsearch Head - ES 管理工具
  elasticsearch-head:
    image: mobz/elasticsearch-head:5
    container_name: chaiguanjia_es_head
    ports:
      - "9100:9100"
    networks:
      - chaiguanjia_network
    depends_on:
      - elasticsearch

  # Mailhog - 开发环境邮件服务
  mailhog:
    image: mailhog/mailhog:latest
    container_name: chaiguanjia_mailhog
    ports:
      - "1025:1025"  # SMTP 端口
      - "8025:8025"  # Web UI 端口
    networks:
      - chaiguanjia_network

volumes:
  pgadmin_data:
    driver: local
