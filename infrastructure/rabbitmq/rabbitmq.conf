# RabbitMQ 3.12 配置文件 - 柴管家项目
# 使用国内镜像源优化的配置

# 基础网络配置
listeners.tcp.default = 5672
management.tcp.port = 15672

# 默认用户配置（通过环境变量设置）
# default_user = admin
# default_pass = chaiguanjia2024
# default_vhost = chaiguanjia

# 内存配置
vm_memory_high_watermark.relative = 0.6
vm_memory_high_watermark_paging_ratio = 0.5

# 磁盘空间配置
disk_free_limit.relative = 1.0

# 消息TTL配置
default_user_tags.administrator = true

# 集群配置
cluster_formation.peer_discovery_backend = classic_config
cluster_formation.classic_config.nodes.1 = rabbit@localhost

# 日志配置
log.console = true
log.console.level = info
log.file = /var/log/rabbitmq/rabbit.log
log.file.level = info
log.file.rotation.date = true
log.file.rotation.size = 10485760

# 连接配置
num_acceptors.tcp = 10
handshake_timeout = 10000
reverse_dns_lookups = false

# SSL/TLS配置（生产环境启用）
# listeners.ssl.default = 5671
# ssl_options.cacertfile = /etc/rabbitmq/ssl/ca_certificate.pem
# ssl_options.certfile   = /etc/rabbitmq/ssl/server_certificate.pem
# ssl_options.keyfile    = /etc/rabbitmq/ssl/server_key.pem
# ssl_options.verify     = verify_peer
# ssl_options.fail_if_no_peer_cert = true

# Management界面配置
management.listener.port = 15672
management.listener.ip = 0.0.0.0
management.rates_mode = basic

# 队列配置
queue_master_locator = min-masters

# 消息存储配置
msg_store_file_size_limit = 16777216
msg_store_index_embed_msgs_below = 4096

# 统计收集间隔
collect_statistics_interval = 5000

# 心跳配置
heartbeat = 60

# 通道最大数量
channel_max = 2047

# 连接最大数量
connection_max = 65536

# 消息大小限制
max_message_size = 134217728

# 队列长度限制（用于防止内存溢出）
default_queue_type = classic

# 高可用配置
ha_mode = all
ha_sync_mode = automatic

# 插件配置
plugins.0 = rabbitmq_management
plugins.1 = rabbitmq_management_agent
plugins.2 = rabbitmq_prometheus
plugins.3 = rabbitmq_shovel
plugins.4 = rabbitmq_shovel_management

# Prometheus监控配置
prometheus.tcp.port = 15692
prometheus.path = /metrics
prometheus.format = prometheus_protobuf

# 队列声明配置
queue_ttl = 1800000
message_ttl = 3600000

# 消费者超时配置
consumer_timeout = 900000

# 工作进程配置
delegate_count = 16

# 性能调优
tcp_listen_options.backlog = 128
tcp_listen_options.nodelay = true
tcp_listen_options.linger.on = true
tcp_listen_options.linger.timeout = 0
tcp_listen_options.exit_on_close = false

# 内存使用报告
memory_monitor_interval = 2500

# GC配置
vm_memory_calculation_strategy = rss

# 磁盘监控间隔
disk_monitor_failure_retries = 10
disk_monitor_failure_retry_interval = 120000

# 访问控制
loopback_users.guest = false

# 认证后端
auth_backends.1 = internal

# 默认权限
default_permissions.configure = .*
default_permissions.write = .*
default_permissions.read = .*

# 用户权限
default_user_tags = administrator

# 管理界面HTTP配置
management.http_log_dir = /var/log/rabbitmq
management.load_definitions = /etc/rabbitmq/definitions.json

# 速率限制
channel_operation_timeout = 15000

# 统计数据库配置
management_db_cache_multiplier = 5

# Web调度
web_dispatch_concurrency_hard_limit = 100
web_dispatch_concurrency_soft_limit = 50

