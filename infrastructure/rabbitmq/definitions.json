{"rabbit_version": "3.12.0", "rabbitmq_version": "3.12.0", "product_name": "RabbitMQ", "product_version": "3.12.0", "users": [], "vhosts": [{"name": "chaiguanjia", "description": "柴管家主要虚拟主机", "tags": [], "default_queue_type": "classic", "metadata": {"description": "柴管家系统消息队列虚拟主机", "tags": []}}], "permissions": [], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "internal_cluster_id", "value": "rabbitmq-cluster-id-chaiguanjia"}], "policies": [{"vhost": "chaiguanjia", "name": "ha-all", "pattern": ".*", "apply-to": "all", "definition": {"ha-mode": "all", "ha-sync-mode": "automatic", "ha-sync-batch-size": 1, "message-ttl": 3600000, "max-length": 10000}, "priority": 0}], "queues": [{"name": "webhook_queue", "vhost": "chaiguanjia", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-max-length": 1000, "x-overflow": "reject-publish", "x-dead-letter-exchange": "dlx_exchange", "x-dead-letter-routing-key": "webhook.dead"}}, {"name": "ai_processing_queue", "vhost": "chaiguanjia", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 1800000, "x-max-length": 5000, "x-overflow": "reject-publish", "x-dead-letter-exchange": "dlx_exchange", "x-dead-letter-routing-key": "ai.dead"}}, {"name": "notification_queue", "vhost": "chaiguanjia", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 600000, "x-max-length": 2000, "x-overflow": "drop-head", "x-dead-letter-exchange": "dlx_exchange", "x-dead-letter-routing-key": "notification.dead"}}, {"name": "message_queue", "vhost": "chaiguanjia", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 7200000, "x-max-length": 10000, "x-overflow": "reject-publish", "x-dead-letter-exchange": "dlx_exchange", "x-dead-letter-routing-key": "message.dead"}}, {"name": "task_queue", "vhost": "chaiguanjia", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 1800000, "x-max-length": 3000, "x-overflow": "reject-publish", "x-dead-letter-exchange": "dlx_exchange", "x-dead-letter-routing-key": "task.dead"}}, {"name": "webhook_queue.dead", "vhost": "chaiguanjia", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": ********}}, {"name": "ai_processing_queue.dead", "vhost": "chaiguanjia", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": ********}}, {"name": "notification_queue.dead", "vhost": "chaiguanjia", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": ********}}, {"name": "message_queue.dead", "vhost": "chaiguanjia", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": ********}}, {"name": "task_queue.dead", "vhost": "chaiguanjia", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": ********}}], "exchanges": [{"name": "webhook_exchange", "vhost": "chaiguanjia", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "ai_exchange", "vhost": "chaiguanjia", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "notification_exchange", "vhost": "chaiguanjia", "type": "fanout", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "message_exchange", "vhost": "chaiguanjia", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "task_exchange", "vhost": "chaiguanjia", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "dlx_exchange", "vhost": "chaiguanjia", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "bindings": [{"source": "webhook_exchange", "vhost": "chaiguanjia", "destination": "webhook_queue", "destination_type": "queue", "routing_key": "webhook.*", "arguments": {}}, {"source": "ai_exchange", "vhost": "chaiguanjia", "destination": "ai_processing_queue", "destination_type": "queue", "routing_key": "ai.*", "arguments": {}}, {"source": "notification_exchange", "vhost": "chaiguanjia", "destination": "notification_queue", "destination_type": "queue", "routing_key": "", "arguments": {}}, {"source": "message_exchange", "vhost": "chaiguanjia", "destination": "message_queue", "destination_type": "queue", "routing_key": "message", "arguments": {}}, {"source": "task_exchange", "vhost": "chaiguanjia", "destination": "task_queue", "destination_type": "queue", "routing_key": "task.*", "arguments": {}}, {"source": "dlx_exchange", "vhost": "chaiguanjia", "destination": "webhook_queue.dead", "destination_type": "queue", "routing_key": "webhook.dead", "arguments": {}}, {"source": "dlx_exchange", "vhost": "chaiguanjia", "destination": "ai_processing_queue.dead", "destination_type": "queue", "routing_key": "ai.dead", "arguments": {}}, {"source": "dlx_exchange", "vhost": "chaiguanjia", "destination": "notification_queue.dead", "destination_type": "queue", "routing_key": "notification.dead", "arguments": {}}, {"source": "dlx_exchange", "vhost": "chaiguanjia", "destination": "message_queue.dead", "destination_type": "queue", "routing_key": "message.dead", "arguments": {}}, {"source": "dlx_exchange", "vhost": "chaiguanjia", "destination": "task_queue.dead", "destination_type": "queue", "routing_key": "task.dead", "arguments": {}}]}