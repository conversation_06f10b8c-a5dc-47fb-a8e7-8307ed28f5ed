# Redis 7.x 配置文件 - 柴管家项目
# 使用国内镜像源优化的配置

# 基础配置
port 6379
bind 0.0.0.0
protected-mode yes

# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志配置
loglevel notice
logfile ""

# 客户端配置
timeout 300
tcp-keepalive 300
tcp-backlog 511

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 安全配置
# requirepass 密码通过环境变量设置

# 性能优化
databases 16
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes

# 网络配置
tcp-keepalive 60

# 内存统计
used-memory-limit 512mb

# 复制配置（为后续主从复制准备）
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5

# 高可用配置
replica-priority 100

# 客户端超时
timeout 0

# 键空间通知（用于监控）
notify-keyspace-events ""

# 哈希配置优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表配置优化
list-max-ziplist-size -2
list-compress-depth 0

# 集合配置优化
set-max-intset-entries 512

# 有序集合配置优化
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog配置
hll-sparse-max-bytes 3000

# 流配置
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重哈希
activerehashing yes

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议最大批量请求大小
proto-max-bulk-len 512mb

# Hz频率设置
hz 10

# 动态Hz
dynamic-hz yes

# AOF重写增量fsync
aof-rewrite-incremental-fsync yes

# RDB快照增量fsync
rdb-save-incremental-fsync yes

# LFU算法配置
lfu-log-factor 10
lfu-decay-time 1

# 懒释放配置
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# 删除HT调整
lazyfree-lazy-user-del no

# 模块系统
loadmodule-allowlist ""

# TLS/SSL配置（生产环境启用）
# tls-port 6380
# tls-cert-file redis.crt
# tls-key-file redis.key
# tls-ca-cert-file ca.crt

