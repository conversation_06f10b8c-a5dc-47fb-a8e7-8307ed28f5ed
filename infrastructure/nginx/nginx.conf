# Nginx 主配置文件 - 柴管家项目
# 使用国内镜像源优化的配置

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

# 工作连接数配置
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# HTTP配置
http {
    # 基础配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

    log_format json_combined escape=json
        '{'
            '"time_local":"$time_local",'
            '"remote_addr":"$remote_addr",'
            '"remote_user":"$remote_user",'
            '"request":"$request",'
            '"status":"$status",'
            '"body_bytes_sent":"$body_bytes_sent",'
            '"request_time":"$request_time",'
            '"http_referrer":"$http_referer",'
            '"http_user_agent":"$http_user_agent",'
            '"http_x_forwarded_for":"$http_x_forwarded_for",'
            '"upstream_response_time":"$upstream_response_time",'
            '"upstream_addr":"$upstream_addr"'
        '}';

    access_log /var/log/nginx/access.log main;

    # 性能优化配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # 客户端配置
    client_max_body_size 20M;
    client_body_buffer_size 128k;
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;

    # 缓冲区配置
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 上游服务器配置
    upstream backend_servers {
        least_conn;
        server chaiguanjia_backend:8000 max_fails=3 fail_timeout=30s weight=1;
        # 可以添加更多后端服务器实现负载均衡
        # server backend2:8000 max_fails=3 fail_timeout=30s weight=1;
        keepalive 32;
    }

    upstream frontend_servers {
        least_conn;
        server chaiguanjia_frontend:80 max_fails=3 fail_timeout=30s weight=1;
        # server frontend2:80 max_fails=3 fail_timeout=30s weight=1;
        keepalive 32;
    }

    # 速率限制配置
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=general_limit:10m rate=50r/s;
    limit_conn_zone $binary_remote_addr zone=conn_limit:10m;

    # 缓存过期时间映射
    map $sent_http_content_type $expires {
        default                    off;
        text/html                  epoch;
        text/css                   max;
        application/javascript     max;
        ~image/                    1y;
        ~font/                     1y;
        application/pdf            1M;
    }

    # 安全头配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 包含其他配置文件
    include /etc/nginx/conf.d/*.conf;
}

# Stream配置（用于TCP/UDP代理）
stream {
    # 日志配置
    log_format stream_format '$remote_addr [$time_local] '
                             '$protocol $status $bytes_sent $bytes_received '
                             '$session_time "$upstream_addr" '
                             '"$upstream_bytes_sent" "$upstream_bytes_received" "$upstream_connect_time"';

    access_log /var/log/nginx/stream_access.log stream_format;
    error_log /var/log/nginx/stream_error.log;

    # 可以在这里添加TCP/UDP代理配置
    # 例如：数据库连接代理、消息队列代理等
}

