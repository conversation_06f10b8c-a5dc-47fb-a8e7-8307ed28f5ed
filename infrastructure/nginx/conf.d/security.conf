# Nginx 安全配置 - 柴管家项目
# 仅包含server级别的安全设置

# 安全响应头配置
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header X-Download-Options "noopen" always;
add_header X-Permitted-Cross-Domain-Policies "none" always;

# HSTS配置（HTTPS环境启用时取消注释）
# add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# CSP内容安全策略
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' ws: wss:; frame-ancestors 'self';" always;

# 特征策略（Permissions Policy）
add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), interest-cohort=()" always;

# 防止MIME类型嗅探
add_header X-Content-Type-Options "nosniff" always;

# 防止点击劫持
add_header X-Frame-Options "SAMEORIGIN" always;

# XSS保护
add_header X-XSS-Protection "1; mode=block" always;

# 限制并发连接（zone定义在主配置文件中）
limit_conn conn_limit 10;

# 速率限制（zone定义在主配置文件中）
limit_req zone=general_limit burst=10 nodelay;
