# Nginx 虚拟主机配置 - 柴管家项目
# 主要服务配置

# 重定向HTTP到HTTPS（生产环境启用）
# server {
#     listen 80;
#     server_name chaiguanjia.com www.chaiguanjia.com;
#     return 301 https://$server_name$request_uri;
# }

# 主要HTTPS服务器配置（生产环境）
# server {
#     listen 443 ssl http2;
#     server_name chaiguanjia.com www.chaiguanjia.com;

#     # SSL证书配置
#     ssl_certificate /etc/nginx/ssl/cert.pem;
#     ssl_certificate_key /etc/nginx/ssl/key.pem;

#     # SSL安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;

#     # 安全头
#     add_header Strict-Transport-Security "max-age=63072000" always;

#     # 包含主要配置
#     include /etc/nginx/conf.d/chaiguanjia_main.conf;
# }

# 开发环境HTTP服务器配置
server {
    listen 80;
    server_name localhost 127.0.0.1 _;
    root /var/www/html;
    index index.html index.htm;

    # 日志配置
    access_log /var/log/nginx/chaiguanjia_access.log main;
    error_log /var/log/nginx/chaiguanjia_error.log;

    # 通用安全配置
    include /etc/nginx/conf.d/security.conf;

    # API代理配置
    location /api/ {
        # 安全检查 - 禁用不安全的HTTP方法
        if ($request_method !~ ^(GET|HEAD|POST|PUT|PATCH|DELETE|OPTIONS)$) {
            return 405;
        }

        # 阻止用户代理为空的请求
        if ($http_user_agent = "") {
            return 444;
        }

        # 阻止常见的恶意用户代理
        if ($http_user_agent ~* (nmap|nikto|wikto|sf|sqlmap|bsqlbf|w3af|acunetix|havij|appscan)) {
            return 444;
        }

        # 阻止SQL注入尝试
        if ($query_string ~* "(\;|\||\`|\"|\[|\]|select|union|insert|update|delete|create|drop|exec|script)") {
            return 444;
        }

        # 阻止文件包含攻击
        if ($query_string ~* "(\.\.\/|\.\.\\|etc\/passwd|boot\.ini)") {
            return 444;
        }

        # 阻止跨站脚本攻击
        if ($query_string ~* "(<|%3C).*script.*(>|%3E)") {
            return 444;
        }

        # 限流配置
        limit_req zone=api_limit burst=20 nodelay;
        limit_conn conn_limit 10;

        # 代理配置
        proxy_pass http://backend_servers/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;

        # 健康检查
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 30s;
    }

    # WebSocket代理配置
    location /ws/ {
        proxy_pass http://backend_servers/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket特殊配置
        proxy_read_timeout 86400s;
        proxy_send_timeout 86400s;
    }

    # 静态文件服务
    location /static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Static-File "nginx";

        # 安全配置
        location ~* \.(js|css)$ {
            add_header Content-Type text/plain;
        }
    }

    # 媒体文件服务
    location /media/ {
        alias /var/www/media/;
        expires 30d;
        add_header Cache-Control "public";
    }

    # 上传文件处理
    location /uploads/ {
        client_max_body_size 50M;
        proxy_pass http://backend_servers/uploads/;
        proxy_request_buffering off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 前端应用代理
    location / {
        # 安全检查 - 禁用不安全的HTTP方法
        if ($request_method !~ ^(GET|HEAD|POST|PUT|PATCH|DELETE|OPTIONS)$) {
            return 405;
        }

        # 阻止用户代理为空的请求
        if ($http_user_agent = "") {
            return 444;
        }

        # 阻止常见的攻击模式
        if ($request_uri ~* "(\<|%3C).*script.*(\>|%3E)") {
            return 444;
        }

        if ($request_uri ~* "(\<|%3C).*iframe.*(\>|%3E)") {
            return 444;
        }

        if ($request_uri ~* "(\<|%3C).*object.*(\>|%3E)") {
            return 444;
        }

        # 限流配置
        limit_req zone=general_limit burst=100 nodelay;

        # 代理到前端服务
        proxy_pass http://frontend_servers/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 前端特殊配置
        proxy_redirect off;

        # 超时配置
        proxy_connect_timeout 10s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Nginx状态监控
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        allow **********/16;  # Docker网络
        deny all;
    }

    # 管理接口代理（RabbitMQ管理界面）
    location /rabbitmq/ {
        proxy_pass http://rabbitmq:15672/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 认证配置
        auth_basic "RabbitMQ Management";
        auth_basic_user_file /etc/nginx/.htpasswd;
    }

    # 错误页面配置
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    location = /404.html {
        root /var/www/html;
        internal;
    }

    location = /50x.html {
        root /var/www/html;
        internal;
    }

    # 安全配置 - 拒绝访问敏感文件
    # 阻止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 阻止访问备份文件
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 阻止访问敏感文件扩展名
    location ~* \.(htaccess|htpasswd|ini|log|sh|sql|conf|bak|old|tmp|env)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 阻止访问版本控制文件
    location ~ /\.(git|svn|hg) {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 阻止访问composer/node_modules等目录
    location ~ /(composer\.json|composer\.lock|package\.json|package-lock\.json|yarn\.lock|node_modules|vendor) {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 防止直接访问PHP文件（如果有）
    location ~* \.php$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

