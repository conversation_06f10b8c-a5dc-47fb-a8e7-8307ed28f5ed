version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: chaiguanjia-postgres
    environment:
      POSTGRES_DB: chaiguanjia
      POSTGRES_USER: chaiguanjia
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-chaiguanjia123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgresql/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - chaiguanjia-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: chaiguanjia-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - chaiguanjia-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: chaiguanjia-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-chaiguanjia}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-rabbitmq123}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - ./rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - chaiguanjia-network

  # 后端API服务
  backend:
    build:
      context: ../../backend
      dockerfile: Dockerfile
    container_name: chaiguanjia-backend
    environment:
      - DATABASE_URL=postgresql://chaiguanjia:${POSTGRES_PASSWORD:-chaiguanjia123}@postgres:5432/chaiguanjia
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379
      - RABBITMQ_URL=amqp://${RABBITMQ_USER:-chaiguanjia}:${RABBITMQ_PASSWORD:-rabbitmq123}@rabbitmq:5672/
    depends_on:
      - postgres
      - redis
      - rabbitmq
    ports:
      - "8000:8000"
    volumes:
      - ../../backend:/app
    networks:
      - chaiguanjia-network

  # 前端服务
  frontend:
    build:
      context: ../../frontend
      dockerfile: Dockerfile
    container_name: chaiguanjia-frontend
    ports:
      - "3000:3000"
    volumes:
      - ../../frontend:/app
      - /app/node_modules
    networks:
      - chaiguanjia-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: chaiguanjia-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    networks:
      - chaiguanjia-network

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:

networks:
  chaiguanjia-network:
    driver: bridge
