<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>柴管家服务监控面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .status-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .status-card {
            background: #fff;
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-5px);
        }

        .status-card .icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .status-card .title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .status-card .value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .status-card .description {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .healthy { color: #27ae60; }
        .warning { color: #f39c12; }
        .error { color: #e74c3c; }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .service-card {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .service-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.5em;
            color: white;
        }

        .service-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
        }

        .service-status {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .service-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .detail-row:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .detail-value {
            font-weight: 500;
            color: #2c3e50;
        }

        .actions {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .refresh-info {
            text-align: center;
            color: #7f8c8d;
            margin-top: 20px;
            font-size: 0.9em;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #fee;
            border: 1px solid #fcc;
            color: #a00;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 柴管家服务监控面板</h1>
            <div class="subtitle">实时监控系统健康状态</div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>正在加载系统状态...</div>
        </div>

        <div class="error-message" id="errorMessage"></div>

        <div class="status-overview" id="statusOverview">
            <div class="status-card">
                <div class="icon">🚀</div>
                <div class="title">系统状态</div>
                <div class="value healthy" id="systemStatus">运行中</div>
                <div class="description">整体运行状态</div>
            </div>
            <div class="status-card">
                <div class="icon">📊</div>
                <div class="title">活跃服务</div>
                <div class="value" id="activeServices">0/0</div>
                <div class="description">正在运行的服务</div>
            </div>
            <div class="status-card">
                <div class="icon">⚡</div>
                <div class="title">响应时间</div>
                <div class="value" id="responseTime">0ms</div>
                <div class="description">平均API响应时间</div>
            </div>
            <div class="status-card">
                <div class="icon">💾</div>
                <div class="title">数据存储</div>
                <div class="value healthy" id="dataStorage">正常</div>
                <div class="description">数据库和缓存状态</div>
            </div>
        </div>

        <div class="services-grid" id="servicesGrid">
            <!-- 服务卡片将通过JavaScript动态生成 -->
        </div>

        <div class="actions">
            <button class="btn" onclick="refreshStatus()">🔄 刷新状态</button>
            <button class="btn" onclick="viewLogs()">📝 查看日志</button>
            <button class="btn" onclick="downloadReport()">📋 下载报告</button>
        </div>

        <div class="refresh-info">
            最后更新时间: <span id="lastUpdate">--</span> | 自动刷新: <span id="autoRefresh">开启</span>
        </div>
    </div>

    <script>
        // 服务配置
        const services = [
            {
                name: 'PostgreSQL',
                icon: '🐘',
                color: '#336791',
                endpoint: '/api/health/postgresql',
                port: 5432
            },
            {
                name: 'Redis',
                icon: '📱',
                color: '#dc382d',
                endpoint: '/api/health/redis',
                port: 6379
            },
            {
                name: 'RabbitMQ',
                icon: '🐰',
                color: '#ff6600',
                endpoint: '/api/health/rabbitmq',
                port: 5672
            },
            {
                name: 'Elasticsearch',
                icon: '🔍',
                color: '#005571',
                endpoint: '/api/health/elasticsearch',
                port: 9200
            },
            {
                name: 'Backend API',
                icon: '⚙️',
                color: '#28a745',
                endpoint: '/api/health',
                port: 8000
            },
            {
                name: 'Frontend',
                icon: '🌐',
                color: '#61dafb',
                endpoint: '/health',
                port: 3000
            },
            {
                name: 'Nginx',
                icon: '🌊',
                color: '#009639',
                endpoint: '/health',
                port: 80
            }
        ];

        let autoRefreshInterval;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMonitoring();
            startAutoRefresh();
        });

        // 初始化监控
        function initMonitoring() {
            showLoading(true);
            refreshStatus();
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('statusOverview').style.display = show ? 'none' : 'grid';
            document.getElementById('servicesGrid').style.display = show ? 'none' : 'grid';
        }

        // 显示错误信息
        function showError(message) {
            const errorElement = document.getElementById('errorMessage');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            setTimeout(() => {
                errorElement.style.display = 'none';
            }, 5000);
        }

        // 刷新状态
        async function refreshStatus() {
            try {
                showLoading(true);

                // 模拟API调用 - 在实际环境中这里应该调用真实的健康检查API
                const healthData = await mockHealthCheck();

                updateStatusOverview(healthData);
                updateServicesGrid(healthData);
                updateLastRefreshTime();

                showLoading(false);
            } catch (error) {
                showError('无法获取系统状态: ' + error.message);
                showLoading(false);
            }
        }

        // 模拟健康检查（实际使用时应替换为真实API调用）
        async function mockHealthCheck() {
            // 模拟API延迟
            await new Promise(resolve => setTimeout(resolve, 1000));

            const healthData = {
                system: {
                    status: 'healthy',
                    activeServices: 6,
                    totalServices: 7,
                    responseTime: Math.floor(Math.random() * 500) + 100,
                    dataStorage: 'healthy'
                },
                services: services.map(service => ({
                    name: service.name,
                    status: Math.random() > 0.8 ? 'warning' : 'healthy',
                    responseTime: Math.floor(Math.random() * 200) + 50,
                    uptime: Math.floor(Math.random() * 72) + 1,
                    memoryUsage: Math.floor(Math.random() * 80) + 10,
                    cpuUsage: Math.floor(Math.random() * 60) + 5
                }))
            };

            return healthData;
        }

        // 更新状态概览
        function updateStatusOverview(data) {
            const systemStatusEl = document.getElementById('systemStatus');
            const activeServicesEl = document.getElementById('activeServices');
            const responseTimeEl = document.getElementById('responseTime');
            const dataStorageEl = document.getElementById('dataStorage');

            // 系统状态
            systemStatusEl.textContent = data.system.status === 'healthy' ? '运行中' : '异常';
            systemStatusEl.className = data.system.status === 'healthy' ? 'value healthy' : 'value error';

            // 活跃服务
            activeServicesEl.textContent = `${data.system.activeServices}/${data.system.totalServices}`;
            activeServicesEl.className = data.system.activeServices === data.system.totalServices ? 'value healthy' : 'value warning';

            // 响应时间
            responseTimeEl.textContent = `${data.system.responseTime}ms`;
            responseTimeEl.className = data.system.responseTime < 500 ? 'value healthy' : 'value warning';

            // 数据存储
            dataStorageEl.textContent = data.system.dataStorage === 'healthy' ? '正常' : '异常';
            dataStorageEl.className = data.system.dataStorage === 'healthy' ? 'value healthy' : 'value error';
        }

        // 更新服务网格
        function updateServicesGrid(data) {
            const servicesGrid = document.getElementById('servicesGrid');
            servicesGrid.innerHTML = '';

            data.services.forEach((serviceData, index) => {
                const service = services[index];
                const serviceCard = createServiceCard(service, serviceData);
                servicesGrid.appendChild(serviceCard);
            });
        }

        // 创建服务卡片
        function createServiceCard(service, data) {
            const card = document.createElement('div');
            card.className = 'service-card';

            const statusClass = data.status === 'healthy' ? 'healthy' :
                               data.status === 'warning' ? 'warning' : 'error';

            card.innerHTML = `
                <div class="service-header">
                    <div class="service-icon" style="background-color: ${service.color}">
                        ${service.icon}
                    </div>
                    <div class="service-name">${service.name}</div>
                </div>
                <div class="service-status">
                    <div class="status-dot ${statusClass}" style="background-color: ${
                        data.status === 'healthy' ? '#27ae60' :
                        data.status === 'warning' ? '#f39c12' : '#e74c3c'
                    }"></div>
                    <span>状态: ${
                        data.status === 'healthy' ? '正常' :
                        data.status === 'warning' ? '警告' : '错误'
                    }</span>
                </div>
                <div class="service-details">
                    <div class="detail-row">
                        <span class="detail-label">响应时间:</span>
                        <span class="detail-value">${data.responseTime}ms</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">运行时间:</span>
                        <span class="detail-value">${data.uptime}小时</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">内存使用:</span>
                        <span class="detail-value">${data.memoryUsage}%</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">CPU使用:</span>
                        <span class="detail-value">${data.cpuUsage}%</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">端口:</span>
                        <span class="detail-value">${service.port}</span>
                    </div>
                </div>
            `;

            return card;
        }

        // 更新最后刷新时间
        function updateLastRefreshTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('lastUpdate').textContent = timeString;
        }

        // 开始自动刷新
        function startAutoRefresh() {
            autoRefreshInterval = setInterval(refreshStatus, 30000); // 每30秒刷新一次
            document.getElementById('autoRefresh').textContent = '开启';
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                document.getElementById('autoRefresh').textContent = '关闭';
            }
        }

        // 查看日志
        function viewLogs() {
            // 在实际环境中，这里应该打开日志查看页面
            alert('功能开发中：将打开日志查看界面');
        }

        // 下载报告
        function downloadReport() {
            // 在实际环境中，这里应该生成并下载健康检查报告
            alert('功能开发中：将生成健康检查报告');
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'r':
                        e.preventDefault();
                        refreshStatus();
                        break;
                    case 'l':
                        e.preventDefault();
                        viewLogs();
                        break;
                }
            }
        });
    </script>
</body>
</html>
