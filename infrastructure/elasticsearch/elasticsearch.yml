# Elasticsearch 8.10.4 配置文件 - 柴管家项目
# 使用国内镜像源优化的配置

# 集群名称
cluster.name: chaiguanjia-cluster

# 节点名称
node.name: chaiguanjia-es-node

# 网络配置
network.host: 0.0.0.0
http.port: 9200
transport.port: 9300

# 发现配置
discovery.type: single-node

# 路径配置
path.data: /usr/share/elasticsearch/data
path.logs: /usr/share/elasticsearch/logs

# 内存配置
bootstrap.memory_lock: true

# 安全配置（开发环境禁用，生产环境启用）
xpack.security.enabled: false
xpack.security.enrollment.enabled: false
xpack.security.http.ssl.enabled: false
xpack.security.transport.ssl.enabled: false

# 监控配置
xpack.monitoring.collection.enabled: true

# 许可证配置
xpack.license.self_generated.type: basic

# 索引配置
action.destructive_requires_name: true

# 搜索配置
search.max_buckets: 65536

# 线程池配置
thread_pool.search.queue_size: 1000

# HTTP配置
http.max_content_length: 100mb
http.max_header_size: 8kb
http.max_initial_line_length: 4kb

# 索引管理
indices.recovery.max_bytes_per_sec: 40mb
indices.memory.index_buffer_size: 10%
indices.memory.min_index_buffer_size: 48mb

# 分片配置
cluster.max_shards_per_node: 1000
action.auto_create_index: true

# 慢查询日志
index.search.slowlog.threshold.query.warn: 10s
index.search.slowlog.threshold.query.info: 5s
index.search.slowlog.threshold.query.debug: 2s
index.search.slowlog.threshold.query.trace: 500ms

index.search.slowlog.threshold.fetch.warn: 1s
index.search.slowlog.threshold.fetch.info: 800ms
index.search.slowlog.threshold.fetch.debug: 500ms
index.search.slowlog.threshold.fetch.trace: 200ms

index.indexing.slowlog.threshold.index.warn: 10s
index.indexing.slowlog.threshold.index.info: 5s
index.indexing.slowlog.threshold.index.debug: 2s
index.indexing.slowlog.threshold.index.trace: 500ms

# 日志配置
logger.org.elasticsearch.deprecation: warn

# GC配置优化
monitor.jvm.gc.enabled: true

# 熔断器配置
indices.breaker.total.limit: 70%
indices.breaker.fielddata.limit: 40%
indices.breaker.request.limit: 40%

# 发现和集群配置
cluster.initial_master_nodes: ["chaiguanjia-es-node"]

# 节点角色
node.roles: ["master", "data", "ingest", "ml", "remote_cluster_client"]

# 跨域配置（开发环境）
http.cors.enabled: true
http.cors.allow-origin: "*"
http.cors.allow-methods: OPTIONS, HEAD, GET, POST, PUT, DELETE
http.cors.allow-headers: X-Requested-With, X-Auth-Token, Content-Type, Content-Length, Authorization

# 中文分词配置
# 需要安装 analysis-ik 插件
# plugin.mandatory: analysis-ik

# 索引模板配置
index.number_of_shards: 1
index.number_of_replicas: 0
index.refresh_interval: 30s

# 搜索优化
search.allow_expensive_queries: true

# 脚本配置
script.allowed_types: inline, stored
script.allowed_contexts: search, update, aggs

# 网络超时配置
network.tcp.keep_alive: true
network.tcp.reuse_address: true
transport.tcp.compress: true

# 队列大小配置
thread_pool.get.queue_size: 1000
thread_pool.analyze.queue_size: 16
thread_pool.write.queue_size: 10000

# 批量操作配置
indices.recovery.max_concurrent_file_chunks: 2
cluster.routing.allocation.node_concurrent_recoveries: 2

# 磁盘水位线配置
cluster.routing.allocation.disk.threshold_enabled: true
cluster.routing.allocation.disk.watermark.low: 85%
cluster.routing.allocation.disk.watermark.high: 90%
cluster.routing.allocation.disk.watermark.flood_stage: 95%

# 映射配置
indices.mapping.nested_fields.limit: 50
indices.mapping.nested_objects.limit: 10000
indices.mapping.total_fields.limit: 1000

# 查询缓存
indices.queries.cache.size: 10%

# 请求缓存
indices.requests.cache.size: 1%

# 字段数据缓存
indices.fielddata.cache.size: 40%

# 段内存配置
indices.memory.index_buffer_size: 10%
indices.memory.min_index_buffer_size: 48mb
indices.memory.max_index_buffer_size: unbounded

# 事务日志配置
index.translog.flush_threshold_size: 512mb
index.translog.sync_interval: 5s

# 合并配置
index.merge.scheduler.max_thread_count: 1

# 刷新配置
index.refresh_interval: 1s

# 开发环境调试
reindex.remote.whitelist: []

