[mypy]
# 柴管家项目 MyPy 类型检查配置
# 严格的类型检查配置，确保代码类型安全

# ==========================================
# 基本配置
# ==========================================
python_version = 3.11
warn_return_any = True
warn_unused_configs = True
show_error_codes = True
show_column_numbers = True
pretty = True
color_output = True

# ==========================================
# 类型检查严格性配置
# ==========================================
# 函数定义检查
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True

# 可选类型检查
no_implicit_optional = True
strict_optional = True

# 类型推断检查
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_return_any = True
warn_unreachable = True

# 杂项检查
strict_equality = True
extra_checks = True
allow_untyped_globals = False
allow_redefinition = False
local_partial_types = True
implicit_reexport = False
strict_concatenate = True

# ==========================================
# 错误报告配置
# ==========================================
show_error_context = True
show_traceback = True
raise_exceptions = False
cache_dir = .mypy_cache
sqlite_cache = True
incremental = True
skip_version_check = False

# ==========================================
# 导入检查配置
# ==========================================
ignore_missing_imports = False
follow_imports = normal
follow_imports_for_stubs = True
namespace_packages = True
explicit_package_bases = True

# ==========================================
# 平台配置
# ==========================================
platform = linux
always_true =
always_false =

# ==========================================
# 第三方库配置 - 忽略缺失的导入
# ==========================================

[mypy-celery.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True

[mypy-psycopg2.*]
ignore_missing_imports = True

[mypy-alembic.*]
ignore_missing_imports = True

[mypy-passlib.*]
ignore_missing_imports = True

[mypy-jose.*]
ignore_missing_imports = True

[mypy-bcrypt.*]
ignore_missing_imports = True

[mypy-cryptography.*]
ignore_missing_imports = True

[mypy-elasticsearch.*]
ignore_missing_imports = True

[mypy-rabbitmq.*]
ignore_missing_imports = True

[mypy-pika.*]
ignore_missing_imports = True

[mypy-httpx.*]
ignore_missing_imports = True

[mypy-pytest.*]
ignore_missing_imports = True

[mypy-factory.*]
ignore_missing_imports = True

[mypy-faker.*]
ignore_missing_imports = True

[mypy-PIL.*]
ignore_missing_imports = True

[mypy-cv2.*]
ignore_missing_imports = True

[mypy-numpy.*]
ignore_missing_imports = True

[mypy-pandas.*]
ignore_missing_imports = True

[mypy-matplotlib.*]
ignore_missing_imports = True

[mypy-seaborn.*]
ignore_missing_imports = True

[mypy-sklearn.*]
ignore_missing_imports = True

[mypy-tensorflow.*]
ignore_missing_imports = True

[mypy-torch.*]
ignore_missing_imports = True

[mypy-openai.*]
ignore_missing_imports = True

[mypy-anthropic.*]
ignore_missing_imports = True

# ==========================================
# 项目特定模块配置
# ==========================================

# 测试文件 - 宽松检查
[mypy-tests.*]
ignore_errors = True
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = False
warn_return_any = False
warn_unused_ignores = False

[mypy-test_*]
ignore_errors = True
disallow_untyped_defs = False
disallow_incomplete_defs = False

# 迁移文件 - 忽略类型检查
[mypy-migrations.*]
ignore_errors = True

[mypy-alembic.versions.*]
ignore_errors = True

# 配置文件 - 允许动态配置
[mypy-app.core.config]
warn_return_any = False
allow_untyped_globals = True

[mypy-app.core.settings.*]
warn_return_any = False
allow_untyped_globals = True

# API 路由 - 允许某些动态特性
[mypy-app.api.*]
warn_return_any = False

# 数据库模型 - SQLAlchemy 特殊处理
[mypy-app.*.models.*]
plugins = sqlalchemy.ext.mypy.plugin
warn_return_any = False

# Pydantic 模型
[mypy-app.*.schemas.*]
plugins = pydantic.mypy
warn_return_any = False

# 脚本文件 - 宽松检查
[mypy-scripts.*]
ignore_errors = True
disallow_untyped_defs = False

# 开发工具 - 宽松检查
[mypy-dev_tools.*]
ignore_errors = True

# ==========================================
# SQLAlchemy 插件配置
# ==========================================
[mypy.plugins.sqlalchemy]
# SQLAlchemy 2.0+ 配置
warn_on_ignored_attributes = True

# ==========================================
# Pydantic 插件配置
# ==========================================
[mypy.plugins.pydantic]
init_forbid_extra = True
init_typed = True
warn_required_dynamic_aliases = True
warn_untyped_fields = True
