[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "chaiguanjia-backend"
description = "柴管家多平台聚合智能客服系统后端"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "技术架构团队", email = "<EMAIL>"},
]
keywords = ["chatbot", "customer-service", "multi-platform", "ai"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dynamic = ["version"]

dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "psycopg2-binary>=2.9.0",
    "redis>=5.0.0",
    "celery>=5.3.0",
    "pydantic>=2.4.0",
    "pydantic-settings>=2.0.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.0",
    "python-multipart>=0.0.6",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0",
    "bandit>=1.7.0",
    "pre-commit>=3.4.0",
    "httpx>=0.25.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",
    "factory-boy>=3.3.0",
]

docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.23.0",
]

[project.urls]
homepage = "https://github.com/chaiguanjia/backend"
repository = "https://github.com/chaiguanjia/backend"
documentation = "https://docs.chaiguanjia.com"

# ==========================================
# Black 代码格式化配置
# ==========================================
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
# A regex preceded with ^/ will apply only to files and directories
# in the root of the project.
(
  ^/migrations/     # exclude migration files
  | __pycache__
  | \.git
  | \.venv
  | \.tox
  | build
  | dist
)
'''

# ==========================================
# isort 导入排序配置
# ==========================================
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
split_on_trailing_comma = true
skip_gitignore = true
known_first_party = ["app", "tests"]
known_third_party = [
    "fastapi",
    "uvicorn",
    "sqlalchemy",
    "alembic",
    "psycopg2",
    "redis",
    "celery",
    "pydantic",
    "pytest",
    "httpx"
]
sections = [
    "FUTURE",
    "STDLIB",
    "THIRDPARTY",
    "FIRSTPARTY",
    "LOCALFOLDER"
]

# ==========================================
# MyPy 类型检查配置
# ==========================================
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true
pretty = true

# 第三方库配置
[[tool.mypy.overrides]]
module = [
    "celery.*",
    "redis.*",
    "psycopg2.*",
    "alembic.*",
    "sqlalchemy.*",
    "passlib.*",
    "jose.*"
]
ignore_missing_imports = true

# 测试文件配置
[[tool.mypy.overrides]]
module = "tests.*"
ignore_errors = true

# ==========================================
# Pytest 测试配置
# ==========================================
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-branch",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=80"
]
testpaths = ["tests"]
pythonpath = ["."]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning"
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "api: marks tests as API tests",
    "database: marks tests that require database",
    "redis: marks tests that require Redis",
    "celery: marks tests that require Celery"
]

# ==========================================
# Coverage 覆盖率配置
# ==========================================
[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/alembic/*",
    "app/core/config.py"
]
branch = true

[tool.coverage.report]
precision = 2
show_missing = true
skip_covered = false
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod"
]

[tool.coverage.html]
directory = "htmlcov"

# ==========================================
# Bandit 安全检查配置
# ==========================================
[tool.bandit]
exclude_dirs = ["tests", "migrations", "venv", ".venv"]
skips = ["B101", "B601"]  # 跳过 assert 和 shell 注入检查（测试中常用）

# ==========================================
# Ruff 快速 Linter 配置（可选）
# ==========================================
[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "C",   # mccabe
    "N",   # pep8-naming
    "D",   # pydocstyle
    "UP",  # pyupgrade
    "B",   # flake8-bugbear
    "A",   # flake8-builtins
    "COM", # flake8-commas
    "C4",  # flake8-comprehensions
    "DTZ", # flake8-datetimez
    "T10", # flake8-debugger
    "EM",  # flake8-errmsg
    "FA",  # flake8-future-annotations
    "ICN", # flake8-import-conventions
    "G",   # flake8-logging-format
    "PIE", # flake8-pie
    "T20", # flake8-print
    "PT",  # flake8-pytest-style
    "Q",   # flake8-quotes
    "RSE", # flake8-raise
    "RET", # flake8-return
    "SLF", # flake8-self
    "SIM", # flake8-simplify
    "TID", # flake8-tidy-imports
    "TCH", # flake8-type-checking
    "ARG", # flake8-unused-arguments
    "PTH", # flake8-use-pathlib
    "ERA", # eradicate
    "PD",  # pandas-vet
    "PGH", # pygrep-hooks
    "PL",  # pylint
    "TRY", # tryceratops
    "FLY", # flynt
    "NPY", # numpy
    "RUF", # ruff-specific rules
]

ignore = [
    "E501",   # line too long, handled by black
    "B008",   # do not perform function calls in argument defaults
    "C901",   # too complex
    "D100",   # missing docstring in public module
    "D101",   # missing docstring in public class
    "D102",   # missing docstring in public method
    "D103",   # missing docstring in public function
    "D104",   # missing docstring in public package
    "D105",   # missing docstring in magic method
    "D107",   # missing docstring in __init__
]

# 排除目录
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",
]

[tool.ruff.mccabe]
max-complexity = 10

[tool.ruff.pydocstyle]
convention = "google"

[tool.ruff.isort]
known-first-party = ["app", "tests"]

[tool.ruff.flake8-tidy-imports]
ban-relative-imports = "all"

[tool.ruff.per-file-ignores]
"tests/**/*" = ["PGH", "S101", "TID252"]
"migrations/**/*" = ["ERA001"]
