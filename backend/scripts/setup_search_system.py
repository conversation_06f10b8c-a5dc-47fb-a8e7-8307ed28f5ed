#!/usr/bin/env python3
"""
搜索系统设置脚本

初始化和配置搜索系统，包括索引创建、插件安装和验证。
"""

import asyncio
import logging
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def setup_search_system():
    """设置搜索系统"""
    logger.info("开始设置搜索系统...")

    try:
        # 1. 初始化 Elasticsearch 连接
        logger.info("1. 初始化 Elasticsearch 连接...")
        from app.shared.search import get_es_client, init_elasticsearch

        success = await init_elasticsearch()
        if not success:
            logger.error("❌ Elasticsearch 连接失败")
            return False

        es_client = get_es_client()
        cluster_info = await es_client.get_cluster_info()
        logger.info(
            f"✅ 连接成功: {cluster_info.get('cluster_name')} v{cluster_info.get('version')}"
        )

        # 2. 设置默认索引
        logger.info("2. 设置默认索引...")
        from app.shared.search import get_index_manager

        index_manager = get_index_manager()
        success = await index_manager.setup_default_indices()

        if success:
            logger.info("✅ 默认索引设置成功")
        else:
            logger.warning("⚠️  部分索引设置失败")

        # 3. 验证搜索功能
        logger.info("3. 验证搜索功能...")
        from app.shared.search import get_search_service

        search_service = get_search_service()

        # 测试基本搜索
        test_doc = {
            "title": "搜索系统测试文档",
            "content": "这是一个用于验证搜索系统功能的测试文档",
            "category": "测试",
            "created_at": "2024-01-01T00:00:00Z",
        }

        doc_id = await search_service.index_document("messages", test_doc, refresh=True)

        if doc_id:
            logger.info("✅ 文档索引功能正常")

            # 清理测试文档
            await search_service.delete_document("messages", doc_id, refresh=True)
        else:
            logger.error("❌ 文档索引功能异常")
            return False

        # 4. 测试中文分词
        logger.info("4. 测试中文分词...")

        test_text = "柴管家智能客服系统"
        tokens = await search_service.analyze_text(test_text, "ik_max_word")

        if tokens:
            logger.info(f"✅ 中文分词功能正常: {tokens}")
        else:
            logger.warning("⚠️  中文分词功能可能异常，请检查 IK 插件")

        logger.info("🎉 搜索系统设置完成！")
        return True

    except Exception as e:
        logger.error(f"❌ 搜索系统设置失败: {e}")
        return False

    finally:
        try:
            from app.shared.search import close_elasticsearch

            await close_elasticsearch()
        except Exception:
            pass


if __name__ == "__main__":
    success = asyncio.run(setup_search_system())
    sys.exit(0 if success else 1)
