#!/usr/bin/env python3
"""
缓存系统测试脚本

测试Redis缓存系统的各项功能，包括：
- Redis连接测试
- 缓存基本操作测试
- 会话缓存测试
- 权限缓存测试
- 分布式锁测试
- 性能测试
"""

import asyncio
import json
import logging
import time
from typing import Dict, List

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class CacheSystemTester:
    """缓存系统测试器"""

    def __init__(self):
        self.test_results = {}
        self.performance_results = {}

    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        logger.info("开始缓存系统测试...")

        tests = [
            ("Redis连接测试", self.test_redis_connection),
            ("基本缓存操作测试", self.test_basic_cache_operations),
            ("缓存管理器测试", self.test_cache_manager),
            ("会话缓存测试", self.test_session_cache),
            ("权限缓存测试", self.test_permission_cache),
            ("分布式锁测试", self.test_distributed_lock),
            ("缓存性能测试", self.test_cache_performance),
            ("并发测试", self.test_concurrent_operations),
        ]

        for test_name, test_func in tests:
            try:
                logger.info(f"开始执行: {test_name}")
                result = await test_func()
                self.test_results[test_name] = result
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"{test_name}: {status}")
            except Exception as e:
                logger.error(f"{test_name} 执行异常: {e}")
                self.test_results[test_name] = False

        return self.test_results

    async def test_redis_connection(self) -> bool:
        """测试Redis连接"""
        try:
            from app.shared.cache import close_redis, init_redis, redis_client

            # 初始化连接
            await init_redis()

            # 测试基本连接
            result = await redis_client.redis.ping()
            if not result:
                return False

            # 测试基本操作
            test_key = "test:connection"
            test_value = "connection_test_value"

            await redis_client.set(test_key, test_value, ttl=60)
            retrieved_value = await redis_client.get(test_key)

            if retrieved_value != test_value:
                return False

            # 清理测试数据
            await redis_client.delete(test_key)

            logger.info("Redis连接测试通过")
            return True

        except Exception as e:
            logger.error(f"Redis连接测试失败: {e}")
            return False

    async def test_basic_cache_operations(self) -> bool:
        """测试基本缓存操作"""
        try:
            from app.shared.cache import redis_client

            # 测试数据
            test_cases = [
                ("string_test", "string_value"),
                ("dict_test", {"key": "value", "number": 123}),
                ("list_test", [1, 2, 3, "four", "five"]),
                ("number_test", 42),
                ("boolean_test", True),
            ]

            for key, value in test_cases:
                # 设置缓存
                success = await redis_client.set(key, value, ttl=300)
                if not success:
                    logger.error(f"设置缓存失败: {key}")
                    return False

                # 获取缓存
                retrieved = await redis_client.get(key)
                if isinstance(value, (dict, list)):
                    retrieved = json.loads(retrieved)

                if retrieved != value:
                    logger.error(f"缓存值不匹配: {key}, 期望={value}, 实际={retrieved}")
                    return False

                # 检查存在性
                exists = await redis_client.exists(key)
                if not exists:
                    logger.error(f"缓存键不存在: {key}")
                    return False

                # 检查TTL
                ttl = await redis_client.ttl(key)
                if ttl <= 0:
                    logger.error(f"TTL异常: {key}, ttl={ttl}")
                    return False

            # 批量删除
            keys = [key for key, _ in test_cases]
            deleted_count = await redis_client.delete(*keys)
            if deleted_count != len(keys):
                logger.error(f"批量删除失败: 期望={len(keys)}, 实际={deleted_count}")
                return False

            logger.info("基本缓存操作测试通过")
            return True

        except Exception as e:
            logger.error(f"基本缓存操作测试失败: {e}")
            return False

    async def test_cache_manager(self) -> bool:
        """测试缓存管理器"""
        try:
            from app.shared.cache import CacheConfig, CacheStrategy, get_cache_manager

            # 创建测试缓存管理器
            config = CacheConfig(
                prefix="test_manager",
                default_ttl=300,
                strategy=CacheStrategy.TTL,
                enable_stats=True,
            )
            cache_manager = get_cache_manager("default")

            # 测试基本操作
            test_data = {
                "user:1": {"name": "Alice", "age": 30},
                "user:2": {"name": "Bob", "age": 25},
                "user:3": {"name": "Charlie", "age": 35},
            }

            # 批量设置
            success = await cache_manager.set_many(test_data, ttl=300)
            if not success:
                return False

            # 批量获取
            retrieved_data = await cache_manager.get_many(list(test_data.keys()))
            if len(retrieved_data) != len(test_data):
                return False

            # 测试get_or_set
            async def expensive_operation():
                await asyncio.sleep(0.1)  # 模拟耗时操作
                return {"computed": "value", "timestamp": time.time()}

            result1 = await cache_manager.get_or_set(
                "expensive:1", expensive_operation, ttl=300
            )
            result2 = await cache_manager.get_or_set(
                "expensive:1", expensive_operation, ttl=300
            )

            # 第二次应该从缓存获取，时间戳应该相同
            if result1["timestamp"] != result2["timestamp"]:
                return False

            # 测试统计信息
            stats = await cache_manager.get_stats()
            if "hits" not in stats or "misses" not in stats:
                return False

            # 清理测试数据
            await cache_manager.clear_prefix("test_manager")
            await cache_manager.delete("expensive:1")
            await cache_manager.delete(*test_data.keys())

            logger.info("缓存管理器测试通过")
            return True

        except Exception as e:
            logger.error(f"缓存管理器测试失败: {e}")
            return False

    async def test_session_cache(self) -> bool:
        """测试会话缓存"""
        try:
            from app.shared.cache import get_session_cache

            session_cache = await get_session_cache()

            # 测试用户会话
            user_id = 123
            session_id = "test_session_123"
            session_data = {
                "ip_address": "***********",
                "user_agent": "Test Browser",
                "device_info": {"type": "desktop", "os": "linux"},
                "login_method": "password",
            }

            # 创建会话
            success = await session_cache.create_user_session(
                user_id, session_id, session_data, ttl=300
            )
            if not success:
                return False

            # 获取会话
            retrieved_session = await session_cache.get_user_session(
                user_id, session_id
            )
            if not retrieved_session:
                return False

            if retrieved_session["user_id"] != user_id:
                return False

            # 测试JWT黑名单
            test_token = "test.jwt.token.123"
            await session_cache.add_token_to_blacklist(test_token, ttl=300)

            is_blacklisted = await session_cache.is_token_blacklisted(test_token)
            if not is_blacklisted:
                return False

            # 测试refresh token
            token_id = "refresh_token_123"
            token_data = {
                "device_info": {"type": "mobile"},
                "ip_address": "***********",
            }

            await session_cache.store_refresh_token(
                user_id, token_id, token_data, ttl=300
            )
            refresh_info = await session_cache.get_refresh_token(token_id)

            if not refresh_info or refresh_info["user_id"] != user_id:
                return False

            # 测试登录尝试追踪
            identifier = "<EMAIL>"
            result = await session_cache.track_login_attempt(
                identifier, False, "***********"
            )
            if result["failed_count"] != 1:
                return False

            # 清理测试数据
            await session_cache.terminate_user_session(user_id, session_id)
            await session_cache.revoke_refresh_token(token_id)
            await session_cache.unlock_account(identifier)

            logger.info("会话缓存测试通过")
            return True

        except Exception as e:
            logger.error(f"会话缓存测试失败: {e}")
            return False

    async def test_permission_cache(self) -> bool:
        """测试权限缓存"""
        try:
            from app.shared.cache import get_permission_cache

            permission_cache = await get_permission_cache()

            # 测试用户权限
            user_id = 456
            permissions = ["read", "write", "delete", "admin"]

            await permission_cache.set_user_permissions(user_id, permissions)
            retrieved_permissions = await permission_cache.get_user_permissions(user_id)

            if retrieved_permissions != permissions:
                return False

            # 测试用户角色
            roles = ["user", "moderator", "admin"]
            await permission_cache.set_user_roles(user_id, roles)
            retrieved_roles = await permission_cache.get_user_roles(user_id)

            if retrieved_roles != roles:
                return False

            # 测试角色权限
            role_id = 1
            role_permissions = ["read", "write"]
            await permission_cache.set_role_permissions(role_id, role_permissions)
            retrieved_role_permissions = await permission_cache.get_role_permissions(
                role_id
            )

            if retrieved_role_permissions != role_permissions:
                return False

            # 测试权限检查缓存
            await permission_cache.cache_permission_check(user_id, "read", True)
            check_result = await permission_cache.check_permission(user_id, "read")

            if check_result is not True:
                return False

            # 测试缓存统计
            stats = await permission_cache.get_cache_stats()
            if "cache_version" not in stats:
                return False

            # 清理测试数据
            await permission_cache.invalidate_user_cache(user_id)
            await permission_cache.invalidate_role_cache(role_id)

            logger.info("权限缓存测试通过")
            return True

        except Exception as e:
            logger.error(f"权限缓存测试失败: {e}")
            return False

    async def test_distributed_lock(self) -> bool:
        """测试分布式锁"""
        try:
            from app.shared.cache import DistributedLock, get_lock_manager, with_lock

            lock_manager = get_lock_manager()

            # 测试基本锁操作
            lock_name = "test_lock_1"
            lock = DistributedLock(lock_name=lock_name, timeout=30)

            # 获取锁
            success = await lock.acquire()
            if not success:
                return False

            # 检查锁状态
            is_locked = await lock.is_locked_by_me()
            if not is_locked:
                return False

            # 释放锁
            success = await lock.release()
            if not success:
                return False

            # 测试上下文管理器
            async with with_lock("test_lock_2", timeout=30) as context_lock:
                if not context_lock.is_locked:
                    return False

            # 测试锁信息
            async with lock_manager.acquire_lock("test_lock_3", timeout=30):
                lock_info = await lock_manager.get_lock_info("test_lock_3")
                if not lock_info or not lock_info["is_active"]:
                    return False

            # 测试并发锁（应该阻塞）
            async def concurrent_task():
                async with with_lock("concurrent_test", timeout=5, blocking_timeout=1):
                    await asyncio.sleep(0.5)
                    return True

            # 启动两个并发任务
            task1 = asyncio.create_task(concurrent_task())
            task2 = asyncio.create_task(concurrent_task())

            results = await asyncio.gather(task1, task2, return_exceptions=True)

            # 应该有一个成功，一个因为超时失败
            success_count = sum(1 for result in results if result is True)
            if success_count != 1:
                logger.warning(f"并发锁测试结果异常: {results}")

            logger.info("分布式锁测试通过")
            return True

        except Exception as e:
            logger.error(f"分布式锁测试失败: {e}")
            return False

    async def test_cache_performance(self) -> bool:
        """测试缓存性能"""
        try:
            from app.shared.cache import redis_client

            # 性能测试配置
            test_iterations = 1000
            test_data_size = 100  # 字节

            test_value = "x" * test_data_size
            start_time = time.time()

            # 写入性能测试
            write_start = time.time()
            for i in range(test_iterations):
                await redis_client.set(f"perf_test:{i}", test_value, ttl=300)
            write_end = time.time()

            write_duration = write_end - write_start
            write_ops_per_sec = test_iterations / write_duration

            # 读取性能测试
            read_start = time.time()
            for i in range(test_iterations):
                await redis_client.get(f"perf_test:{i}")
            read_end = time.time()

            read_duration = read_end - read_start
            read_ops_per_sec = test_iterations / read_duration

            # 清理测试数据
            keys_to_delete = [f"perf_test:{i}" for i in range(test_iterations)]
            delete_start = time.time()
            await redis_client.delete(*keys_to_delete)
            delete_end = time.time()

            delete_duration = delete_end - delete_start

            # 记录性能结果
            self.performance_results = {
                "write_ops_per_sec": round(write_ops_per_sec, 2),
                "read_ops_per_sec": round(read_ops_per_sec, 2),
                "write_duration_ms": round(write_duration * 1000, 2),
                "read_duration_ms": round(read_duration * 1000, 2),
                "delete_duration_ms": round(delete_duration * 1000, 2),
                "test_iterations": test_iterations,
                "data_size_bytes": test_data_size,
            }

            logger.info(f"缓存性能测试结果: {self.performance_results}")

            # 性能验收标准
            if write_ops_per_sec < 100:  # 每秒至少100次写入
                logger.warning(f"写入性能不达标: {write_ops_per_sec} ops/sec")
                return False

            if read_ops_per_sec < 500:  # 每秒至少500次读取
                logger.warning(f"读取性能不达标: {read_ops_per_sec} ops/sec")
                return False

            logger.info("缓存性能测试通过")
            return True

        except Exception as e:
            logger.error(f"缓存性能测试失败: {e}")
            return False

    async def test_concurrent_operations(self) -> bool:
        """测试并发操作"""
        try:
            from app.shared.cache import redis_client

            # 并发测试配置
            concurrent_tasks = 50
            operations_per_task = 20

            async def concurrent_task(task_id: int):
                """并发任务"""
                try:
                    for i in range(operations_per_task):
                        key = f"concurrent:{task_id}:{i}"
                        value = f"task_{task_id}_value_{i}"

                        # 设置缓存
                        await redis_client.set(key, value, ttl=300)

                        # 获取缓存
                        retrieved = await redis_client.get(key)
                        if retrieved != value:
                            return False

                        # 检查存在性
                        exists = await redis_client.exists(key)
                        if not exists:
                            return False

                    return True
                except Exception as e:
                    logger.error(f"并发任务{task_id}失败: {e}")
                    return False

            # 启动并发任务
            start_time = time.time()
            tasks = [concurrent_task(i) for i in range(concurrent_tasks)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()

            # 检查结果
            success_count = sum(1 for result in results if result is True)
            total_operations = concurrent_tasks * operations_per_task

            # 清理测试数据
            cleanup_tasks = []
            for task_id in range(concurrent_tasks):
                for i in range(operations_per_task):
                    cleanup_tasks.append(f"concurrent:{task_id}:{i}")

            if cleanup_tasks:
                await redis_client.delete(*cleanup_tasks)

            duration = end_time - start_time
            ops_per_sec = total_operations / duration

            logger.info(f"并发测试结果: {success_count}/{concurrent_tasks} 任务成功")
            logger.info(f"总操作数: {total_operations}, 耗时: {duration:.2f}秒")
            logger.info(f"并发性能: {ops_per_sec:.2f} ops/sec")

            # 验收标准：至少90%的任务成功
            if success_count < concurrent_tasks * 0.9:
                logger.error(
                    f"并发测试失败: 成功率 {success_count/concurrent_tasks*100:.1f}%"
                )
                return False

            logger.info("并发操作测试通过")
            return True

        except Exception as e:
            logger.error(f"并发操作测试失败: {e}")
            return False

    def print_test_report(self):
        """打印测试报告"""
        print("\n" + "=" * 60)
        print("缓存系统测试报告")
        print("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)

        print(f"\n测试总结:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过测试: {passed_tests}")
        print(f"  失败测试: {total_tests - passed_tests}")
        print(f"  通过率: {passed_tests/total_tests*100:.1f}%")

        print(f"\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")

        if self.performance_results:
            print(f"\n性能测试结果:")
            for metric, value in self.performance_results.items():
                print(f"  {metric}: {value}")

        print("\n" + "=" * 60)


async def main():
    """主函数"""
    tester = CacheSystemTester()

    try:
        # 运行所有测试
        results = await tester.run_all_tests()

        # 打印测试报告
        tester.print_test_report()

        # 确定退出状态
        all_passed = all(results.values())
        if all_passed:
            logger.info("所有缓存系统测试通过！")
            return 0
        else:
            logger.error("部分缓存系统测试失败！")
            return 1

    except Exception as e:
        logger.error(f"测试执行异常: {e}")
        return 1

    finally:
        # 清理资源
        try:
            from app.shared.cache import close_redis

            await close_redis()
        except Exception as e:
            logger.warning(f"清理资源失败: {e}")


if __name__ == "__main__":
    import sys

    exit_code = asyncio.run(main())
    sys.exit(exit_code)
