#!/usr/bin/env python3
"""
文件存储系统初始化脚本

初始化MinIO对象存储服务，创建必要的bucket和配置
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from app.core.config import get_settings
from app.shared.storage import get_minio_client, init_minio

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def setup_file_storage():
    """初始化文件存储系统"""
    try:
        logger.info("开始初始化文件存储系统...")

        # 初始化MinIO客户端
        logger.info("连接到MinIO服务...")
        minio_client = await init_minio()

        if not minio_client.is_connected:
            logger.error("MinIO连接失败")
            return False

        logger.info("✅ MinIO连接成功")

        # 检查bucket状态
        settings = get_settings()
        bucket_name = settings.minio_bucket_name

        logger.info(f"检查bucket: {bucket_name}")

        # 获取bucket统计信息
        stats = await minio_client.get_bucket_stats()
        logger.info(f"Bucket统计信息:")
        logger.info(f"  - 总文件数: {stats['total_files']}")
        logger.info(f"  - 总大小: {stats['total_size']} bytes")
        logger.info(f"  - 按类型分布: {stats['files_by_type']}")

        # 创建默认文件夹结构
        await create_default_folders(minio_client)

        # 测试基本功能
        await test_basic_operations(minio_client)

        logger.info("✅ 文件存储系统初始化完成")
        return True

    except Exception as e:
        logger.error(f"文件存储系统初始化失败: {e}")
        return False


async def create_default_folders(minio_client):
    """创建默认文件夹结构"""
    logger.info("创建默认文件夹结构...")

    # 定义默认文件夹
    default_folders = [
        "uploads/images/",
        "uploads/documents/",
        "uploads/videos/",
        "uploads/audio/",
        "uploads/others/",
        "thumbnails/",
        "temp_thumbnails/",
        "cache/",
    ]

    try:
        for folder in default_folders:
            # 创建一个空文件作为文件夹标记
            placeholder_content = f"# {folder} folder\nThis is a placeholder file to create the folder structure.\n"

            await minio_client.upload_file(
                file_path=f"{folder}.placeholder",
                file_data=placeholder_content.encode(),
                content_type="text/plain",
                metadata={"type": "folder_placeholder"},
            )

            logger.info(f"  ✅ 创建文件夹: {folder}")

    except Exception as e:
        logger.warning(f"创建默认文件夹时出错: {e}")


async def test_basic_operations(minio_client):
    """测试基本操作"""
    logger.info("测试基本文件操作...")

    try:
        # 测试文件上传
        test_file_path = "test/test_file.txt"
        test_content = "This is a test file for file storage system validation."

        logger.info("测试文件上传...")
        etag = await minio_client.upload_file(
            file_path=test_file_path,
            file_data=test_content.encode(),
            content_type="text/plain",
            metadata={"test": "true", "created_by": "setup_script"},
        )
        logger.info(f"  ✅ 文件上传成功, ETag: {etag}")

        # 测试文件存在检查
        logger.info("测试文件存在检查...")
        exists = await minio_client.file_exists(test_file_path)
        if exists:
            logger.info("  ✅ 文件存在检查通过")
        else:
            logger.error("  ❌ 文件存在检查失败")

        # 测试文件信息获取
        logger.info("测试文件信息获取...")
        file_info = await minio_client.get_file_info(test_file_path)
        if file_info:
            logger.info(f"  ✅ 文件信息获取成功: {file_info}")
        else:
            logger.error("  ❌ 文件信息获取失败")

        # 测试文件下载
        logger.info("测试文件下载...")
        downloaded_data = await minio_client.download_file(test_file_path)
        if downloaded_data.decode() == test_content:
            logger.info("  ✅ 文件下载验证通过")
        else:
            logger.error("  ❌ 文件下载验证失败")

        # 测试预签名URL生成
        logger.info("测试预签名URL生成...")
        url = await minio_client.generate_presigned_url(test_file_path)
        if url:
            logger.info(f"  ✅ 预签名URL生成成功: {url}")
        else:
            logger.error("  ❌ 预签名URL生成失败")

        # 测试文件复制
        logger.info("测试文件复制...")
        copy_path = "test/test_file_copy.txt"
        copy_success = await minio_client.copy_file(test_file_path, copy_path)
        if copy_success:
            logger.info("  ✅ 文件复制成功")
        else:
            logger.error("  ❌ 文件复制失败")

        # 测试文件列表
        logger.info("测试文件列表...")
        files = await minio_client.list_files(prefix="test/", max_keys=10)
        logger.info(f"  ✅ 找到测试文件: {len(files)} 个")
        for file in files:
            logger.info(f"    - {file['object_name']} ({file['size']} bytes)")

        # 清理测试文件
        logger.info("清理测试文件...")
        await minio_client.delete_file(test_file_path)
        await minio_client.delete_file(copy_path)
        logger.info("  ✅ 测试文件清理完成")

    except Exception as e:
        logger.error(f"基本操作测试失败: {e}")
        raise


async def validate_configuration():
    """验证配置"""
    logger.info("验证文件存储配置...")

    settings = get_settings()

    # 检查必要的配置
    required_configs = [
        "minio_endpoint",
        "minio_access_key",
        "minio_secret_key",
        "minio_bucket_name",
    ]

    missing_configs = []
    for config in required_configs:
        value = getattr(settings, config, None)
        if not value:
            missing_configs.append(config)

    if missing_configs:
        logger.error(f"缺少必要配置: {missing_configs}")
        return False

    logger.info("配置验证:")
    logger.info(f"  - MinIO端点: {settings.minio_endpoint}")
    logger.info(f"  - Bucket名称: {settings.minio_bucket_name}")
    logger.info(f"  - 最大文件大小: {settings.max_file_size / (1024*1024):.1f}MB")
    logger.info(f"  - 允许的文件扩展名: {settings.get_allowed_extensions()}")
    logger.info(f"  - 图片质量: {settings.image_quality}")
    logger.info(f"  - 缩略图尺寸: {settings.get_thumbnail_size()}")

    return True


async def check_dependencies():
    """检查依赖"""
    logger.info("检查系统依赖...")

    try:
        import minio

        logger.info(f"  ✅ MinIO客户端: {minio.__version__}")
    except ImportError:
        logger.error("  ❌ MinIO客户端未安装")
        return False

    try:
        import PIL

        logger.info(f"  ✅ Pillow: {PIL.__version__}")
    except ImportError:
        logger.error("  ❌ Pillow未安装")
        return False

    try:
        import magic

        logger.info("  ✅ python-magic 可用")
    except ImportError:
        logger.warning("  ⚠️ python-magic 未安装，文件类型检测功能有限")

    return True


async def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("文件存储系统初始化脚本")
    logger.info("=" * 60)

    try:
        # 检查依赖
        if not await check_dependencies():
            logger.error("依赖检查失败，请安装必要的依赖包")
            return 1

        # 验证配置
        if not await validate_configuration():
            logger.error("配置验证失败，请检查环境变量")
            return 1

        # 初始化文件存储系统
        if not await setup_file_storage():
            logger.error("文件存储系统初始化失败")
            return 1

        logger.info("=" * 60)
        logger.info("🎉 文件存储系统初始化成功!")
        logger.info("=" * 60)

        return 0

    except KeyboardInterrupt:
        logger.info("\n用户中断，退出程序")
        return 130
    except Exception as e:
        logger.error(f"初始化过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
