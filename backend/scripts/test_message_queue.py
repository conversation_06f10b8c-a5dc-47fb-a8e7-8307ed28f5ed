#!/usr/bin/env python3
"""
消息队列系统验收测试

验证 Task I-3.1：消息队列系统 的验收标准
"""

import logging
import os
import subprocess
import sys
import time
from typing import Any, Dict

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.shared.messaging import get_message_queue_manager, init_message_queue_manager
from app.shared.messaging.config import QueueType
from app.shared.messaging.consumer import MessageHandler
from app.shared.messaging.handlers import WebhookHandler
from app.shared.messaging.models import MessagePriority, MessageType

logger = logging.getLogger(__name__)


class TestResultReporter:
    """测试结果报告器"""

    def __init__(self):
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0

    def add_test_result(self, test_name: str, passed: bool, message: str = ""):
        """添加测试结果"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            status = "✅ PASS"
        else:
            self.failed_tests += 1
            status = "❌ FAIL"

        result = {
            "test_name": test_name,
            "status": status,
            "passed": passed,
            "message": message,
        }
        self.test_results.append(result)

        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")

    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 80)
        print("Task I-3.1 消息队列系统 验收测试报告")
        print("=" * 80)

        print(f"总测试数: {self.total_tests}")
        print(f"通过数量: {self.passed_tests}")
        print(f"失败数量: {self.failed_tests}")
        print(f"通过率: {(self.passed_tests/self.total_tests*100):.1f}%")

        if self.failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["passed"]:
                    print(f"  - {result['test_name']}: {result['message']}")

        print("\n验收标准检查:")
        self._check_acceptance_criteria()

    def _check_acceptance_criteria(self):
        """检查验收标准"""
        criteria = [
            (
                "AC1: RabbitMQ服务正常运行，管理界面可访问",
                self._check_rabbitmq_running(),
            ),
            ("AC2: 消息队列配置合理，支持不同优先级", self._check_queue_config()),
            ("AC3: 消息发送和接收功能正常", self._check_message_flow()),
            ("AC4: 消息失败重试机制有效", self._check_retry_mechanism()),
            ("AC5: 队列监控和告警机制正常", self._check_monitoring()),
        ]

        for criterion, passed in criteria:
            status = "✅" if passed else "❌"
            print(f"  {status} {criterion}")

    def _check_rabbitmq_running(self) -> bool:
        """检查RabbitMQ是否运行"""
        # 检查是否有RabbitMQ连接测试通过
        return any(
            "rabbitmq" in result["test_name"].lower() and result["passed"]
            for result in self.test_results
        )

    def _check_queue_config(self) -> bool:
        """检查队列配置"""
        return any(
            "queue" in result["test_name"].lower() and result["passed"]
            for result in self.test_results
        )

    def _check_message_flow(self) -> bool:
        """检查消息流"""
        publish_test = any(
            "publish" in result["test_name"].lower() and result["passed"]
            for result in self.test_results
        )
        consume_test = any(
            "consume" in result["test_name"].lower() and result["passed"]
            for result in self.test_results
        )
        return publish_test and consume_test

    def _check_retry_mechanism(self) -> bool:
        """检查重试机制"""
        return any(
            "retry" in result["test_name"].lower() and result["passed"]
            for result in self.test_results
        )

    def _check_monitoring(self) -> bool:
        """检查监控功能"""
        return any(
            "monitor" in result["test_name"].lower() and result["passed"]
            for result in self.test_results
        )


class MessageQueueAcceptanceTest:
    """消息队列验收测试"""

    def __init__(self):
        self.reporter = TestResultReporter()
        self.manager = None
        self.test_message_count = 0

    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )

    def test_rabbitmq_connection(self):
        """测试RabbitMQ连接"""
        try:
            self.manager = get_message_queue_manager()
            health = self.manager.get_health_status()

            if health.get("status") in ["healthy", "degraded"]:
                self.reporter.add_test_result(
                    "RabbitMQ连接测试", True, f"连接状态: {health.get('status')}"
                )
            else:
                self.reporter.add_test_result(
                    "RabbitMQ连接测试", False, f"连接失败: {health}"
                )
        except Exception as e:
            self.reporter.add_test_result(
                "RabbitMQ连接测试", False, f"连接异常: {str(e)}"
            )

    def test_queue_configuration(self):
        """测试队列配置"""
        try:
            # 测试队列配置是否正确
            config = self.manager.config

            # 检查预定义队列
            expected_queues = [
                "webhook_queue",
                "ai_processing_queue",
                "notification_queue",
                "message_queue",
                "task_queue",
            ]

            configured_queues = list(config.queues.keys())
            missing_queues = [q for q in expected_queues if q not in configured_queues]

            if not missing_queues:
                self.reporter.add_test_result(
                    "队列配置测试",
                    True,
                    f"所有预定义队列已配置: {len(configured_queues)} 个队列",
                )
            else:
                self.reporter.add_test_result(
                    "队列配置测试", False, f"缺少队列: {missing_queues}"
                )
        except Exception as e:
            self.reporter.add_test_result(
                "队列配置测试", False, f"配置检查异常: {str(e)}"
            )

    def test_message_publishing(self):
        """测试消息发布"""
        try:
            self.manager.start()

            # 发布测试消息
            success = self.manager.publish_message(
                message_type=MessageType.SYSTEM,
                payload={"test": True, "message": "测试消息发布"},
                priority=MessagePriority.NORMAL,
                source="acceptance_test",
            )

            if success:
                self.test_message_count += 1
                self.reporter.add_test_result("消息发布测试", True, "消息发布成功")
            else:
                self.reporter.add_test_result("消息发布测试", False, "消息发布失败")
        except Exception as e:
            self.reporter.add_test_result("消息发布测试", False, f"发布异常: {str(e)}")

    def test_batch_publishing(self):
        """测试批量发布"""
        try:
            messages = [
                {
                    "type": MessageType.SYSTEM.value,
                    "payload": {"batch_test": True, "index": i},
                    "priority": MessagePriority.NORMAL.value,
                    "source": "batch_test",
                }
                for i in range(5)
            ]

            result = self.manager.publish_batch_messages(messages)

            if result["successful"] == result["total"]:
                self.test_message_count += result["successful"]
                self.reporter.add_test_result(
                    "批量发布测试",
                    True,
                    f"批量发布成功: {result['successful']}/{result['total']}",
                )
            else:
                self.reporter.add_test_result(
                    "批量发布测试",
                    False,
                    f"批量发布部分失败: {result['successful']}/{result['total']}",
                )
        except Exception as e:
            self.reporter.add_test_result(
                "批量发布测试", False, f"批量发布异常: {str(e)}"
            )

    def test_message_consuming(self):
        """测试消息消费"""
        try:
            # 创建测试处理器
            class TestHandler(MessageHandler):
                def __init__(self):
                    super().__init__("test_handler")
                    self.processed_count = 0

                def process(self, message) -> bool:
                    self.processed_count += 1
                    logger.info(f"Test handler processed message: {message.message_id}")
                    return True

            # 注册处理器
            test_handler = TestHandler()
            self.manager.register_handler(QueueType.TASK, test_handler)

            # 发布测试消息
            for i in range(3):
                self.manager.publish_message(
                    message_type=MessageType.TASK,
                    payload={"consume_test": True, "index": i},
                    priority=MessagePriority.NORMAL,
                    source="consume_test",
                )

            # 等待消息处理
            time.sleep(3)

            # 检查处理结果
            if test_handler.processed_count > 0:
                self.reporter.add_test_result(
                    "消息消费测试",
                    True,
                    f"成功处理 {test_handler.processed_count} 条消息",
                )
            else:
                self.reporter.add_test_result("消息消费测试", False, "未检测到消息处理")
        except Exception as e:
            self.reporter.add_test_result(
                "消息消费测试", False, f"消费测试异常: {str(e)}"
            )

    def test_priority_handling(self):
        """测试优先级处理"""
        try:
            # 发布不同优先级的消息
            priorities = [
                (MessagePriority.LOW, "低优先级"),
                (MessagePriority.NORMAL, "普通优先级"),
                (MessagePriority.HIGH, "高优先级"),
                (MessagePriority.URGENT, "紧急优先级"),
            ]

            success_count = 0
            for priority, desc in priorities:
                success = self.manager.publish_message(
                    message_type=MessageType.SYSTEM,
                    payload={"priority_test": True, "description": desc},
                    priority=priority,
                    source="priority_test",
                )
                if success:
                    success_count += 1

            if success_count == len(priorities):
                self.reporter.add_test_result(
                    "优先级处理测试", True, f"成功发布 {success_count} 条不同优先级消息"
                )
            else:
                self.reporter.add_test_result(
                    "优先级处理测试",
                    False,
                    f"优先级消息发布失败: {success_count}/{len(priorities)}",
                )
        except Exception as e:
            self.reporter.add_test_result(
                "优先级处理测试", False, f"优先级测试异常: {str(e)}"
            )

    def test_retry_mechanism(self):
        """测试重试机制"""
        try:
            # 创建会失败的处理器来测试重试
            class FailingHandler(MessageHandler):
                def __init__(self):
                    super().__init__("failing_handler")
                    self.attempt_count = 0

                def process(self, message) -> bool:
                    self.attempt_count += 1
                    logger.info(f"Failing handler attempt {self.attempt_count}")

                    # 前两次失败，第三次成功
                    if self.attempt_count < 3:
                        raise Exception("Simulated processing failure")
                    return True

            # 注册失败处理器
            failing_handler = FailingHandler()
            self.manager.register_handler(QueueType.WEBHOOK, failing_handler)

            # 发布测试消息
            success = self.manager.publish_message(
                message_type=MessageType.WEBHOOK,
                payload={"retry_test": True},
                priority=MessagePriority.NORMAL,
                source="retry_test",
                max_retries=3,
            )

            if success:
                # 等待重试处理
                time.sleep(5)

                # 检查重试统计
                stats = self.manager.get_statistics()
                retry_stats = stats.get("retry_handler", {})

                if retry_stats.get("total_retries", 0) > 0:
                    self.reporter.add_test_result(
                        "重试机制测试",
                        True,
                        f"重试机制正常，总重试次数: {retry_stats.get('total_retries', 0)}",
                    )
                else:
                    self.reporter.add_test_result(
                        "重试机制测试", False, "未检测到重试活动"
                    )
            else:
                self.reporter.add_test_result(
                    "重试机制测试", False, "重试测试消息发布失败"
                )
        except Exception as e:
            self.reporter.add_test_result(
                "重试机制测试", False, f"重试测试异常: {str(e)}"
            )

    def test_monitoring_system(self):
        """测试监控系统"""
        try:
            # 获取监控数据
            stats = self.manager.get_statistics()
            health = self.manager.get_health_status()

            # 检查统计数据
            if "publisher" in stats and "consumer" in stats:
                pub_stats = stats["publisher"]
                con_stats = stats["consumer"]

                messages_published = pub_stats.get("messages_published", 0)
                messages_consumed = con_stats.get("messages_consumed", 0)

                self.reporter.add_test_result(
                    "监控系统测试",
                    True,
                    f"监控数据正常 - 发布: {messages_published}, 消费: {messages_consumed}",
                )
            else:
                self.reporter.add_test_result(
                    "监控系统测试", False, "监控统计数据不完整"
                )

            # 检查健康状态
            if health.get("status") in ["healthy", "degraded"]:
                self.reporter.add_test_result(
                    "健康检查测试", True, f"系统健康状态: {health.get('status')}"
                )
            else:
                self.reporter.add_test_result(
                    "健康检查测试", False, f"系统健康状态异常: {health}"
                )
        except Exception as e:
            self.reporter.add_test_result(
                "监控系统测试", False, f"监控测试异常: {str(e)}"
            )

    def test_queue_management(self):
        """测试队列管理"""
        try:
            # 测试获取队列信息
            queue_info_count = 0

            for queue_type in QueueType:
                try:
                    queue_info = self.manager.get_queue_info(queue_type)
                    if queue_info and "message_count" in queue_info:
                        queue_info_count += 1
                except Exception:
                    pass

            if queue_info_count > 0:
                self.reporter.add_test_result(
                    "队列管理测试", True, f"成功获取 {queue_info_count} 个队列信息"
                )
            else:
                self.reporter.add_test_result("队列管理测试", False, "无法获取队列信息")
        except Exception as e:
            self.reporter.add_test_result(
                "队列管理测试", False, f"队列管理测试异常: {str(e)}"
            )

    def test_error_handling(self):
        """测试错误处理"""
        try:
            # 测试无效消息类型处理
            try:
                success = self.manager.publish_message(
                    message_type=MessageType.SYSTEM,
                    payload=None,  # 无效载荷
                    priority=MessagePriority.NORMAL,
                    source="error_test",
                )

                # 如果没有抛出异常，检查是否正确处理
                if not success:
                    self.reporter.add_test_result(
                        "错误处理测试", True, "正确处理了无效消息"
                    )
                else:
                    self.reporter.add_test_result(
                        "错误处理测试", False, "未正确处理无效消息"
                    )
            except Exception:
                # 抛出异常也是正确的错误处理
                self.reporter.add_test_result("错误处理测试", True, "正确抛出了异常")
        except Exception as e:
            self.reporter.add_test_result(
                "错误处理测试", False, f"错误处理测试异常: {str(e)}"
            )

    def run_all_tests(self):
        """运行所有测试"""
        print("开始 Task I-3.1 消息队列系统验收测试...")
        print("=" * 80)

        self.setup_logging()

        try:
            # 基础连接测试
            self.test_rabbitmq_connection()
            self.test_queue_configuration()

            # 核心功能测试
            self.test_message_publishing()
            self.test_batch_publishing()
            self.test_message_consuming()
            self.test_priority_handling()

            # 高级功能测试
            self.test_retry_mechanism()
            self.test_monitoring_system()
            self.test_queue_management()
            self.test_error_handling()

            # 等待所有异步操作完成
            time.sleep(2)

        except Exception as e:
            logger.error(f"测试执行异常: {e}")
        finally:
            # 清理资源
            if self.manager:
                try:
                    self.manager.stop()
                except Exception as e:
                    logger.warning(f"清理资源时发生异常: {e}")

        # 打印测试结果
        self.reporter.print_summary()

        return self.reporter.passed_tests == self.reporter.total_tests


def main():
    """主函数"""
    print("Task I-3.1 消息队列系统验收测试")
    print("验证消息队列系统是否满足以下验收标准：")
    print("1. RabbitMQ服务正常运行，管理界面可访问")
    print("2. 消息队列配置合理，支持不同优先级")
    print("3. 消息发送和接收功能正常")
    print("4. 消息失败重试机制有效")
    print("5. 队列监控和告警机制正常")
    print()

    # 运行测试
    test_suite = MessageQueueAcceptanceTest()
    all_passed = test_suite.run_all_tests()

    if all_passed:
        print("\n🎉 所有验收测试通过！Task I-3.1 完成。")
        return 0
    else:
        print("\n❌ 部分验收测试失败，需要修复后重新测试。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
