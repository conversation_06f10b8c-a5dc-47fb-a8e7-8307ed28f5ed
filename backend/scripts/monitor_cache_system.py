#!/usr/bin/env python3
"""
缓存系统监控脚本

实时监控Redis缓存系统的性能和状态，包括：
- Redis服务状态监控
- 缓存命中率统计
- 内存使用情况监控
- 连接数监控
- 分布式锁状态监控
- 自动告警功能
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class CacheMonitor:
    """缓存系统监控器"""

    def __init__(self, check_interval: int = 30):
        self.check_interval = check_interval
        self.monitoring = False
        self.alert_thresholds = {
            "memory_usage_percent": 80,  # 内存使用率告警阈值
            "hit_rate_percent": 70,  # 缓存命中率告警阈值
            "connection_count": 100,  # 连接数告警阈值
            "response_time_ms": 100,  # 响应时间告警阈值
        }
        self.metrics_history = []
        self.max_history = 100

    async def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        logger.info(f"开始缓存系统监控，检查间隔: {self.check_interval}秒")

        try:
            from app.shared.cache import close_redis, init_redis

            await init_redis()

            while self.monitoring:
                try:
                    metrics = await self.collect_metrics()
                    await self.analyze_metrics(metrics)
                    await self.store_metrics(metrics)

                    logger.info(
                        f"监控指标收集完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    )

                    await asyncio.sleep(self.check_interval)

                except Exception as e:
                    logger.error(f"监控过程出错: {e}")
                    await asyncio.sleep(self.check_interval)

        except KeyboardInterrupt:
            logger.info("监控被用户中断")
        except Exception as e:
            logger.error(f"监控启动失败: {e}")
        finally:
            self.monitoring = False
            try:
                await close_redis()
            except:
                pass

    async def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        logger.info("缓存系统监控已停止")

    async def collect_metrics(self) -> Dict:
        """收集监控指标"""
        try:
            from app.shared.cache import (
                get_cache_manager,
                get_lock_manager,
                redis_client,
            )

            start_time = time.time()

            # Redis基础信息
            redis_info = await redis_client.redis.info()

            # 性能测试
            test_key = "monitor:test"
            await redis_client.set(test_key, "test_value", ttl=60)
            await redis_client.get(test_key)
            await redis_client.delete(test_key)

            response_time = (time.time() - start_time) * 1000  # 转换为毫秒

            # 缓存管理器统计
            cache_manager = get_cache_manager()
            cache_stats = await cache_manager.get_stats()

            # 分布式锁信息
            lock_manager = get_lock_manager()
            active_locks = await lock_manager.get_all_locks()

            # 组装监控指标
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "redis_info": {
                    "version": redis_info.get("redis_version", "unknown"),
                    "uptime_seconds": redis_info.get("uptime_in_seconds", 0),
                    "connected_clients": redis_info.get("connected_clients", 0),
                    "used_memory": redis_info.get("used_memory", 0),
                    "used_memory_human": redis_info.get("used_memory_human", "0B"),
                    "used_memory_peak": redis_info.get("used_memory_peak", 0),
                    "used_memory_peak_human": redis_info.get(
                        "used_memory_peak_human", "0B"
                    ),
                    "maxmemory": redis_info.get("maxmemory", 0),
                    "total_commands_processed": redis_info.get(
                        "total_commands_processed", 0
                    ),
                    "instantaneous_ops_per_sec": redis_info.get(
                        "instantaneous_ops_per_sec", 0
                    ),
                    "keyspace_hits": redis_info.get("keyspace_hits", 0),
                    "keyspace_misses": redis_info.get("keyspace_misses", 0),
                },
                "performance": {
                    "response_time_ms": round(response_time, 2),
                },
                "cache_stats": cache_stats,
                "locks": {
                    "active_count": len(active_locks),
                    "locks": active_locks,
                },
            }

            # 计算衍生指标
            metrics["derived"] = self._calculate_derived_metrics(metrics)

            return metrics

        except Exception as e:
            logger.error(f"收集监控指标失败: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
            }

    def _calculate_derived_metrics(self, metrics: Dict) -> Dict:
        """计算衍生指标"""
        derived = {}

        redis_info = metrics.get("redis_info", {})

        # 内存使用率
        used_memory = redis_info.get("used_memory", 0)
        max_memory = redis_info.get("maxmemory", 0)
        if max_memory > 0:
            derived["memory_usage_percent"] = round((used_memory / max_memory) * 100, 2)
        else:
            derived["memory_usage_percent"] = 0

        # 缓存命中率
        hits = redis_info.get("keyspace_hits", 0)
        misses = redis_info.get("keyspace_misses", 0)
        total_requests = hits + misses
        if total_requests > 0:
            derived["hit_rate_percent"] = round((hits / total_requests) * 100, 2)
        else:
            derived["hit_rate_percent"] = 100

        # 连接状态
        derived["connection_count"] = redis_info.get("connected_clients", 0)

        # 响应时间
        derived["response_time_ms"] = metrics.get("performance", {}).get(
            "response_time_ms", 0
        )

        return derived

    async def analyze_metrics(self, metrics: Dict):
        """分析监控指标并生成告警"""
        if "error" in metrics:
            await self._send_alert("CRITICAL", "监控指标收集失败", metrics["error"])
            return

        derived = metrics.get("derived", {})

        # 检查各项指标是否超过阈值
        alerts = []

        # 内存使用率检查
        memory_usage = derived.get("memory_usage_percent", 0)
        if memory_usage > self.alert_thresholds["memory_usage_percent"]:
            alerts.append(f"内存使用率过高: {memory_usage}%")

        # 缓存命中率检查
        hit_rate = derived.get("hit_rate_percent", 100)
        if hit_rate < self.alert_thresholds["hit_rate_percent"]:
            alerts.append(f"缓存命中率过低: {hit_rate}%")

        # 连接数检查
        connection_count = derived.get("connection_count", 0)
        if connection_count > self.alert_thresholds["connection_count"]:
            alerts.append(f"连接数过多: {connection_count}")

        # 响应时间检查
        response_time = derived.get("response_time_ms", 0)
        if response_time > self.alert_thresholds["response_time_ms"]:
            alerts.append(f"响应时间过长: {response_time}ms")

        # 发送告警
        if alerts:
            alert_message = "; ".join(alerts)
            await self._send_alert("WARNING", "缓存系统性能告警", alert_message)

        # 记录正常状态
        logger.info(
            f"监控指标正常 - 内存: {memory_usage}%, 命中率: {hit_rate}%, "
            f"连接数: {connection_count}, 响应时间: {response_time}ms"
        )

    async def store_metrics(self, metrics: Dict):
        """存储监控指标"""
        try:
            # 添加到历史记录
            self.metrics_history.append(metrics)

            # 保持历史记录数量限制
            if len(self.metrics_history) > self.max_history:
                self.metrics_history = self.metrics_history[-self.max_history :]

            # 可以在这里添加持久化存储逻辑
            # 例如存储到文件、数据库或发送到监控系统

        except Exception as e:
            logger.error(f"存储监控指标失败: {e}")

    async def _send_alert(self, level: str, title: str, message: str):
        """发送告警"""
        alert_info = {
            "level": level,
            "title": title,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "service": "cache_system",
        }

        logger.warning(f"🚨 [{level}] {title}: {message}")

        # 这里可以集成实际的告警系统
        # 例如发送邮件、企业微信、钉钉等
        # await self._send_to_alert_system(alert_info)

    async def get_metrics_summary(self, hours: int = 1) -> Dict:
        """获取指定时间内的指标摘要"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            recent_metrics = [
                m
                for m in self.metrics_history
                if datetime.fromisoformat(m["timestamp"]) > cutoff_time
            ]

            if not recent_metrics:
                return {"error": "没有找到指定时间范围内的监控数据"}

            # 计算摘要统计
            memory_usages = [
                m.get("derived", {}).get("memory_usage_percent", 0)
                for m in recent_metrics
            ]
            hit_rates = [
                m.get("derived", {}).get("hit_rate_percent", 100)
                for m in recent_metrics
            ]
            response_times = [
                m.get("derived", {}).get("response_time_ms", 0) for m in recent_metrics
            ]
            connection_counts = [
                m.get("derived", {}).get("connection_count", 0) for m in recent_metrics
            ]

            summary = {
                "time_range_hours": hours,
                "data_points": len(recent_metrics),
                "memory_usage": {
                    "avg": round(sum(memory_usages) / len(memory_usages), 2),
                    "max": max(memory_usages),
                    "min": min(memory_usages),
                },
                "hit_rate": {
                    "avg": round(sum(hit_rates) / len(hit_rates), 2),
                    "max": max(hit_rates),
                    "min": min(hit_rates),
                },
                "response_time": {
                    "avg": round(sum(response_times) / len(response_times), 2),
                    "max": max(response_times),
                    "min": min(response_times),
                },
                "connection_count": {
                    "avg": round(sum(connection_counts) / len(connection_counts), 2),
                    "max": max(connection_counts),
                    "min": min(connection_counts),
                },
                "latest_metrics": recent_metrics[-1] if recent_metrics else None,
            }

            return summary

        except Exception as e:
            logger.error(f"获取监控摘要失败: {e}")
            return {"error": str(e)}

    async def health_check(self) -> Dict:
        """健康检查"""
        try:
            from app.shared.cache import redis_client

            start_time = time.time()

            # 测试基本连接
            ping_result = await redis_client.redis.ping()

            # 测试读写操作
            test_key = "health_check:test"
            test_value = f"health_check_{int(time.time())}"

            await redis_client.set(test_key, test_value, ttl=60)
            retrieved_value = await redis_client.get(test_key)
            await redis_client.delete(test_key)

            response_time = (time.time() - start_time) * 1000

            # 获取基本信息
            redis_info = await redis_client.redis.info()

            health_status = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "checks": {
                    "ping": ping_result,
                    "read_write": retrieved_value == test_value,
                    "response_time_ms": round(response_time, 2),
                },
                "redis_info": {
                    "version": redis_info.get("redis_version"),
                    "uptime_seconds": redis_info.get("uptime_in_seconds"),
                    "connected_clients": redis_info.get("connected_clients"),
                    "used_memory_human": redis_info.get("used_memory_human"),
                },
            }

            # 判断整体健康状态
            all_checks_pass = all(health_status["checks"].values())
            if not all_checks_pass or response_time > 1000:  # 响应时间超过1秒认为不健康
                health_status["status"] = "unhealthy"

            return health_status

        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
            }

    def print_current_status(self):
        """打印当前状态"""
        if not self.metrics_history:
            print("暂无监控数据")
            return

        latest = self.metrics_history[-1]
        derived = latest.get("derived", {})
        redis_info = latest.get("redis_info", {})

        print("\n" + "=" * 50)
        print("缓存系统当前状态")
        print("=" * 50)
        print(f"时间: {latest.get('timestamp', 'unknown')}")
        print(f"Redis版本: {redis_info.get('version', 'unknown')}")
        print(f"运行时间: {redis_info.get('uptime_seconds', 0)}秒")
        print(
            f"内存使用: {redis_info.get('used_memory_human', '0B')} ({derived.get('memory_usage_percent', 0)}%)"
        )
        print(f"缓存命中率: {derived.get('hit_rate_percent', 100)}%")
        print(f"当前连接数: {derived.get('connection_count', 0)}")
        print(f"响应时间: {derived.get('response_time_ms', 0)}ms")
        print(f"每秒操作数: {redis_info.get('instantaneous_ops_per_sec', 0)}")

        locks_info = latest.get("locks", {})
        print(f"活跃锁数量: {locks_info.get('active_count', 0)}")

        print("=" * 50)


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="缓存系统监控工具")
    parser.add_argument(
        "--action",
        choices=["monitor", "health", "summary", "status"],
        default="monitor",
        help="执行的操作",
    )
    parser.add_argument("--interval", type=int, default=30, help="监控检查间隔（秒）")
    parser.add_argument("--hours", type=int, default=1, help="摘要统计时间范围（小时）")

    args = parser.parse_args()

    monitor = CacheMonitor(check_interval=args.interval)

    try:
        if args.action == "monitor":
            # 持续监控模式
            await monitor.start_monitoring()

        elif args.action == "health":
            # 健康检查模式
            from app.shared.cache import close_redis, init_redis

            await init_redis()

            health_status = await monitor.health_check()
            print(json.dumps(health_status, indent=2, ensure_ascii=False))

            await close_redis()

        elif args.action == "summary":
            # 摘要统计模式
            summary = await monitor.get_metrics_summary(hours=args.hours)
            print(json.dumps(summary, indent=2, ensure_ascii=False))

        elif args.action == "status":
            # 当前状态模式
            from app.shared.cache import close_redis, init_redis

            await init_redis()

            metrics = await monitor.collect_metrics()
            await monitor.store_metrics(metrics)
            monitor.print_current_status()

            await close_redis()

    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    import sys

    exit_code = asyncio.run(main())
    sys.exit(exit_code)
