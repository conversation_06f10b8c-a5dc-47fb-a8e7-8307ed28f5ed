#!/usr/bin/env python3
"""
任务系统集成测试脚本

全面测试任务处理系统的各项功能，验证验收标准：
1. Celery任务处理器正常运行
2. 任务定义和注册机制有效
3. Celery Beat定时任务调度正常
4. 任务监控和管理界面功能正常
5. 任务重试和错误处理机制有效
"""

import logging
import sys
import time
from datetime import datetime, timezone
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.shared.tasks.app import create_celery_app
from app.shared.tasks.manager import get_task_manager
from app.shared.tasks.models import TaskPriority
from app.shared.tasks.monitor import get_task_monitor, start_monitoring
from app.shared.tasks.registry import TaskRegistry
from app.shared.tasks.retry_handler import get_retry_handler
from app.shared.tasks.scheduler import TaskScheduler, add_cron_task


class TestResult:
    """测试结果类"""

    def __init__(self, name: str):
        self.name = name
        self.success = False
        self.message = ""
        self.details = {}
        self.start_time = None
        self.end_time = None

    def start(self):
        """开始测试"""
        self.start_time = time.time()
        print(f"开始测试: {self.name}")

    def finish(self, success: bool, message: str = "", details: dict = None):
        """结束测试"""
        self.end_time = time.time()
        self.success = success
        self.message = message
        self.details = details or {}

        duration = self.end_time - self.start_time
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {self.name} ({duration:.2f}s)")
        if message:
            print(f"   {message}")
        if not success and details:
            print(f"   详情: {details}")


class TaskSystemTester:
    """任务系统测试器"""

    def __init__(self):
        self.results = []
        self.celery_app = None
        self.manager = None
        self.monitor = None
        self.registry = None
        self.scheduler = None
        self.retry_handler = None

    def setup(self):
        """初始化测试环境"""
        print("=" * 60)
        print("柴管家任务处理系统集成测试")
        print("=" * 60)

        try:
            # 创建Celery应用
            self.celery_app = create_celery_app()

            # 获取各种组件
            self.manager = get_task_manager()
            self.monitor = get_task_monitor()
            self.registry = TaskRegistry.get_instance()
            self.scheduler = TaskScheduler.get_instance()
            self.retry_handler = get_retry_handler()

            # 启动监控
            start_monitoring()

            print("测试环境初始化完成\n")
            return True

        except Exception as e:
            print(f"测试环境初始化失败: {e}")
            return False

    def test_celery_app_creation(self):
        """测试1: Celery应用创建"""
        result = TestResult("AC1.1 - Celery应用创建")
        result.start()

        try:
            if self.celery_app is None:
                raise Exception("Celery应用未创建")

            # 检查应用配置
            config = self.celery_app.conf

            # 验证关键配置
            checks = {
                "broker_url": config.get("broker_url"),
                "result_backend": config.get("result_backend"),
                "task_serializer": config.get("task_serializer"),
                "result_serializer": config.get("result_serializer"),
            }

            missing_configs = [k for k, v in checks.items() if not v]
            if missing_configs:
                raise Exception(f"缺少配置: {missing_configs}")

            result.finish(True, f"Celery应用配置正常: {self.celery_app.main}", checks)

        except Exception as e:
            result.finish(False, str(e))

        self.results.append(result)

    def test_task_registration(self):
        """测试2: 任务注册机制"""
        result = TestResult("AC2.1 - 任务注册机制")
        result.start()

        try:
            # 获取注册信息
            summary = self.registry.get_registry_summary()

            # 验证注册的任务数量
            total_tasks = summary.get("total_tasks", 0)
            if total_tasks == 0:
                raise Exception("没有注册任何任务")

            # 验证任务类型分布
            task_types = summary.get("task_count_by_type", {})
            if not task_types:
                raise Exception("任务类型分布为空")

            # 验证队列分布
            queues = summary.get("queue_names", [])
            if not queues:
                raise Exception("没有配置队列")

            details = {
                "total_tasks": total_tasks,
                "task_types": len(task_types),
                "queues": len(queues),
            }

            result.finish(True, f"成功注册 {total_tasks} 个任务", details)

        except Exception as e:
            result.finish(False, str(e))

        self.results.append(result)

    def test_basic_task_execution(self):
        """测试3: 基本任务执行"""
        result = TestResult("AC1.2 - 基本任务执行")
        result.start()

        try:
            # 提交健康检查任务
            task_id = self.manager.submit_task("tasks.system.health_check")
            if not task_id:
                raise Exception("任务提交失败")

            # 等待任务完成
            max_wait = 30  # 最多等待30秒
            wait_time = 0
            task_result = None

            while wait_time < max_wait:
                task_result = self.manager.get_task_result(task_id)
                if task_result and task_result.is_finished():
                    break
                time.sleep(1)
                wait_time += 1

            if not task_result:
                raise Exception("获取任务结果失败")

            if not task_result.is_successful():
                raise Exception(f"任务执行失败: {task_result.error}")

            details = {
                "task_id": task_id,
                "status": task_result.status.value,
                "execution_time": wait_time,
            }

            result.finish(True, f"任务执行成功，用时 {wait_time}s", details)

        except Exception as e:
            result.finish(False, str(e))

        self.results.append(result)

    def test_queue_routing(self):
        """测试4: 队列路由"""
        result = TestResult("AC1.3 - 队列路由")
        result.start()

        try:
            # 测试不同队列的任务提交
            test_tasks = [
                ("tasks.system.health_check", "default"),
                ("tasks.notification.send_email", "notification_queue"),
                ("tasks.ai.process_nlp", "ai_queue"),
            ]

            submitted_tasks = []

            for task_name, queue in test_tasks:
                try:
                    task_id = self.manager.submit_task(
                        task_name,
                        queue=queue,
                        kwargs={"test": True} if "email" in task_name else {},
                    )
                    if task_id:
                        submitted_tasks.append((task_name, queue, task_id))
                except Exception as e:
                    print(f"   警告: 任务 {task_name} 提交失败: {e}")

            if not submitted_tasks:
                raise Exception("所有队列路由测试失败")

            details = {
                "tested_queues": len(test_tasks),
                "successful_submissions": len(submitted_tasks),
            }

            result.finish(True, f"成功测试 {len(submitted_tasks)} 个队列路由", details)

        except Exception as e:
            result.finish(False, str(e))

        self.results.append(result)

    def test_task_priorities(self):
        """测试5: 任务优先级"""
        result = TestResult("AC2.2 - 任务优先级")
        result.start()

        try:
            # 提交不同优先级的任务
            priorities = [
                TaskPriority.LOW,
                TaskPriority.NORMAL,
                TaskPriority.HIGH,
                TaskPriority.URGENT,
            ]
            submitted_tasks = []

            for priority in priorities:
                try:
                    task_id = self.manager.submit_task(
                        "tasks.system.health_check", priority=priority
                    )
                    if task_id:
                        submitted_tasks.append((priority.name, task_id))
                except Exception as e:
                    print(f"   警告: 优先级 {priority.name} 任务提交失败: {e}")

            if not submitted_tasks:
                raise Exception("所有优先级任务提交失败")

            details = {
                "tested_priorities": len(priorities),
                "successful_submissions": len(submitted_tasks),
            }

            result.finish(True, f"成功测试 {len(submitted_tasks)} 个优先级", details)

        except Exception as e:
            result.finish(False, str(e))

        self.results.append(result)

    def test_scheduled_tasks(self):
        """测试6: 定时任务调度"""
        result = TestResult("AC3.1 - 定时任务调度")
        result.start()

        try:
            # 添加测试定时任务
            task_name = "test_scheduled_task"
            success = add_cron_task(
                name=task_name,
                task="tasks.system.health_check",
                cron_expr="* * * * *",  # 每分钟执行
                enabled=False,  # 测试时不启用
            )

            if not success:
                raise Exception("定时任务添加失败")

            # 验证任务是否已添加
            scheduled_task = self.scheduler.get_periodic_task(task_name)
            if not scheduled_task:
                raise Exception("定时任务未找到")

            # 获取调度器状态
            scheduler_status = self.scheduler.get_scheduler_status()

            details = {
                "task_name": task_name,
                "total_scheduled": scheduler_status.get("total_tasks", 0),
                "enabled_tasks": scheduler_status.get("enabled_tasks", 0),
            }

            # 清理测试任务
            self.scheduler.remove_periodic_task(task_name)

            result.finish(True, "定时任务调度功能正常", details)

        except Exception as e:
            result.finish(False, str(e))

        self.results.append(result)

    def test_task_monitoring(self):
        """测试7: 任务监控"""
        result = TestResult("AC4.1 - 任务监控")
        result.start()

        try:
            # 等待监控收集数据
            time.sleep(3)

            # 获取监控统计
            stats = self.monitor.get_statistics()

            # 验证统计数据
            if not hasattr(stats, "total_tasks"):
                raise Exception("监控统计数据结构异常")

            # 获取队列状态
            queue_status = self.monitor.get_queue_status()
            if "queue_lengths" not in queue_status:
                raise Exception("队列监控数据缺失")

            # 获取性能指标
            performance = self.monitor.get_performance_metrics()
            if not performance:
                raise Exception("性能指标收集失败")

            details = {
                "total_tasks": stats.total_tasks,
                "running_tasks": stats.running_tasks,
                "completed_tasks": stats.completed_tasks,
                "success_rate": stats.success_rate,
            }

            result.finish(True, "任务监控功能正常", details)

        except Exception as e:
            result.finish(False, str(e))

        self.results.append(result)

    def test_error_handling(self):
        """测试8: 错误处理机制"""
        result = TestResult("AC5.1 - 错误处理机制")
        result.start()

        try:
            # 获取错误统计
            error_stats = self.retry_handler.get_error_statistics()

            # 验证错误统计结构
            required_keys = [
                "total_errors",
                "error_by_category",
                "error_by_task",
                "recent_errors",
            ]
            missing_keys = [key for key in required_keys if key not in error_stats]
            if missing_keys:
                raise Exception(f"错误统计数据结构不完整，缺少: {missing_keys}")

            # 测试重试配置
            config = self.retry_handler.config
            if not config:
                raise Exception("重试处理器配置缺失")

            details = {
                "total_errors": error_stats["total_errors"],
                "error_categories": len(error_stats["error_by_category"]),
                "retry_strategy": config.strategy.value,
                "max_retries": config.max_retries,
            }

            result.finish(True, "错误处理机制正常", details)

        except Exception as e:
            result.finish(False, str(e))

        self.results.append(result)

    def test_batch_operations(self):
        """测试9: 批量操作"""
        result = TestResult("AC1.4 - 批量操作")
        result.start()

        try:
            # 准备批量任务
            batch_tasks = []
            for i in range(10):
                batch_tasks.append(
                    {"name": "tasks.system.health_check", "queue": "default"}
                )

            # 提交批量任务
            task_ids = self.manager.submit_batch_tasks(batch_tasks, batch_size=5)
            if not task_ids:
                raise Exception("批量任务提交失败")

            # 等待任务完成
            results = self.manager.wait_for_tasks(task_ids, timeout=60)

            successful = sum(1 for r in results.values() if r.is_successful())
            failed = sum(1 for r in results.values() if r.is_failed())

            if successful == 0:
                raise Exception("所有批量任务都失败了")

            details = {
                "submitted": len(task_ids),
                "completed": len(results),
                "successful": successful,
                "failed": failed,
                "success_rate": (successful / len(results)) * 100 if results else 0,
            }

            result.finish(
                True, f"批量操作成功，成功率: {details['success_rate']:.1f}%", details
            )

        except Exception as e:
            result.finish(False, str(e))

        self.results.append(result)

    def test_task_management(self):
        """测试10: 任务管理"""
        result = TestResult("AC4.2 - 任务管理")
        result.start()

        try:
            # 获取任务总览
            summary = self.manager.get_task_summary()
            if not summary:
                raise Exception("无法获取任务总览")

            # 验证总览数据结构
            required_sections = [
                "registry",
                "scheduler",
                "monitor",
                "queues",
                "workers",
            ]
            missing_sections = [
                section for section in required_sections if section not in summary
            ]
            if missing_sections:
                raise Exception(f"任务总览数据不完整，缺少: {missing_sections}")

            # 测试活跃任务获取
            active_tasks = self.manager.get_active_tasks()

            # 测试队列管理
            queue_names = self.registry.get_queue_names()
            if not queue_names:
                raise Exception("没有配置任何队列")

            details = {
                "registry_tasks": summary["registry"].get("total_tasks", 0),
                "active_tasks": len(active_tasks),
                "configured_queues": len(queue_names),
                "worker_count": summary["workers"].get("count", 0),
            }

            result.finish(True, "任务管理功能正常", details)

        except Exception as e:
            result.finish(False, str(e))

        self.results.append(result)

    def run_all_tests(self):
        """运行所有测试"""
        print("开始执行集成测试...\n")

        # 按验收标准分组的测试
        acceptance_criteria_tests = [
            # AC1: Celery任务处理器正常运行
            self.test_celery_app_creation,
            self.test_basic_task_execution,
            self.test_queue_routing,
            self.test_batch_operations,
            # AC2: 任务定义和注册机制有效
            self.test_task_registration,
            self.test_task_priorities,
            # AC3: Celery Beat定时任务调度正常
            self.test_scheduled_tasks,
            # AC4: 任务监控和管理界面功能正常
            self.test_task_monitoring,
            self.test_task_management,
            # AC5: 任务重试和错误处理机制有效
            self.test_error_handling,
        ]

        # 执行所有测试
        for test_func in acceptance_criteria_tests:
            try:
                test_func()
            except Exception as e:
                # 如果测试函数本身出错，创建失败结果
                result = TestResult(test_func.__name__)
                result.start()
                result.finish(False, f"测试执行异常: {e}")
                self.results.append(result)

            time.sleep(1)  # 测试间隔

    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("测试报告")
        print("=" * 60)

        # 统计结果
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.success)
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0

        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {success_rate:.1f}%")

        # 验收标准检查
        print("\n" + "-" * 40)
        print("验收标准检查")
        print("-" * 40)

        ac_mapping = {
            "AC1": ["Celery应用创建", "基本任务执行", "队列路由", "批量操作"],
            "AC2": ["任务注册机制", "任务优先级"],
            "AC3": ["定时任务调度"],
            "AC4": ["任务监控", "任务管理"],
            "AC5": ["错误处理机制"],
        }

        for ac_code, test_names in ac_mapping.items():
            ac_results = [
                r for r in self.results if any(name in r.name for name in test_names)
            ]
            ac_passed = all(r.success for r in ac_results)
            status = "✅ 通过" if ac_passed else "❌ 失败"

            ac_descriptions = {
                "AC1": "Celery任务处理器正常运行",
                "AC2": "任务定义和注册机制有效",
                "AC3": "Celery Beat定时任务调度正常",
                "AC4": "任务监控和管理界面功能正常",
                "AC5": "任务重试和错误处理机制有效",
            }

            print(f"{status} {ac_code}: {ac_descriptions.get(ac_code, '')}")

        # 详细结果
        print("\n" + "-" * 40)
        print("详细测试结果")
        print("-" * 40)

        for result in self.results:
            status = "✅" if result.success else "❌"
            duration = (
                result.end_time - result.start_time
                if result.end_time and result.start_time
                else 0
            )
            print(f"{status} {result.name} ({duration:.2f}s)")
            if result.message:
                print(f"   {result.message}")
            if not result.success and result.details:
                print(f"   详情: {result.details}")

        # 系统信息
        print("\n" + "-" * 40)
        print("系统信息")
        print("-" * 40)

        try:
            summary = self.manager.get_task_summary()
            print(f"注册任务数: {summary.get('registry', {}).get('total_tasks', 0)}")
            print(f"队列数量: {len(summary.get('queues', {}))}")
            print(f"Worker数量: {summary.get('workers', {}).get('count', 0)}")
            print(f"定时任务数: {summary.get('scheduler', {}).get('total_tasks', 0)}")
        except Exception as e:
            print(f"无法获取系统信息: {e}")

        print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        return success_rate >= 80  # 80%以上通过率视为成功


def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.WARNING,  # 减少测试期间的日志噪音
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # 创建测试器
    tester = TaskSystemTester()

    # 初始化测试环境
    if not tester.setup():
        print("测试环境初始化失败，退出测试")
        sys.exit(1)

    try:
        # 运行所有测试
        tester.run_all_tests()

        # 生成测试报告
        success = tester.generate_report()

        # 根据结果设置退出码
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n测试执行过程中出现异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
