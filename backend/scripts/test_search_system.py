#!/usr/bin/env python3
"""
搜索系统测试脚本

全面测试 Elasticsearch 搜索系统的功能和性能。
"""

import asyncio
import json
import logging
import random
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class SearchSystemTester:
    """搜索系统测试器"""

    def __init__(self):
        self.test_results = {}
        self.performance_results = {}
        self.test_data = []

    async def init_search_system(self) -> bool:
        """初始化搜索系统"""
        try:
            from app.shared.search import (
                get_es_client,
                get_index_manager,
                get_search_service,
                init_elasticsearch,
            )

            # 初始化 Elasticsearch 连接
            success = await init_elasticsearch()
            if not success:
                logger.error("Elasticsearch 初始化失败")
                return False

            self.es_client = get_es_client()
            self.index_manager = get_index_manager()
            self.search_service = get_search_service()

            logger.info("✅ 搜索系统初始化成功")
            return True

        except Exception as e:
            logger.error(f"搜索系统初始化失败: {e}")
            return False

    async def test_elasticsearch_connection(self) -> bool:
        """测试 Elasticsearch 连接"""
        try:
            # 检查连接状态
            if not await self.es_client.ping():
                logger.error("Elasticsearch ping 失败")
                return False

            # 获取集群信息
            cluster_info = await self.es_client.get_cluster_info()
            logger.info(f"集群信息: {cluster_info}")

            # 检查集群状态
            if cluster_info.get("status") not in ["green", "yellow"]:
                logger.warning(f"集群状态异常: {cluster_info.get('status')}")

            logger.info("✅ Elasticsearch 连接测试通过")
            return True

        except Exception as e:
            logger.error(f"Elasticsearch 连接测试失败: {e}")
            return False

    async def test_index_management(self) -> bool:
        """测试索引管理"""
        try:
            test_index = "test_search_index"

            # 删除可能存在的测试索引
            await self.index_manager.delete_index(test_index)

            # 创建测试索引
            mapping = self.index_manager.get_default_chinese_mapping()
            settings = self.index_manager.get_default_settings()

            success = await self.index_manager.create_index(
                test_index, mapping, settings
            )

            if not success:
                logger.error("索引创建失败")
                return False

            # 检查索引是否存在
            exists = await self.es_client.index_exists(test_index)
            if not exists:
                logger.error("索引创建后不存在")
                return False

            # 获取索引信息
            index_info = await self.index_manager.get_index_info(test_index)
            if not index_info:
                logger.error("无法获取索引信息")
                return False

            # 清理测试索引
            await self.index_manager.delete_index(test_index)

            logger.info("✅ 索引管理测试通过")
            return True

        except Exception as e:
            logger.error(f"索引管理测试失败: {e}")
            return False

    async def test_document_operations(self) -> bool:
        """测试文档操作"""
        try:
            test_index = "test_documents"

            # 创建测试索引
            await self.index_manager.delete_index(test_index)
            mapping = self.index_manager.get_default_chinese_mapping()
            await self.index_manager.create_index(test_index, mapping)

            # 测试单个文档索引
            test_doc = {
                "title": "测试文档标题",
                "content": "这是一个测试文档的内容，包含中文分词测试",
                "category": "测试",
                "tags": ["搜索", "测试", "中文"],
                "created_at": datetime.now().isoformat(),
                "status": "published",
                "priority": 1,
            }

            doc_id = await self.search_service.index_document(
                test_index, test_doc, refresh=True
            )

            if not doc_id:
                logger.error("文档索引失败")
                return False

            # 测试文档获取
            retrieved_doc = await self.search_service.get_document(test_index, doc_id)
            if not retrieved_doc:
                logger.error("文档获取失败")
                return False

            # 测试文档更新
            update_data = {"priority": 2, "updated_at": datetime.now().isoformat()}
            success = await self.search_service.update_document(
                test_index, doc_id, update_data, refresh=True
            )

            if not success:
                logger.error("文档更新失败")
                return False

            # 测试文档删除
            success = await self.search_service.delete_document(
                test_index, doc_id, refresh=True
            )

            if not success:
                logger.error("文档删除失败")
                return False

            # 清理测试索引
            await self.index_manager.delete_index(test_index)

            logger.info("✅ 文档操作测试通过")
            return True

        except Exception as e:
            logger.error(f"文档操作测试失败: {e}")
            return False

    async def test_bulk_operations(self) -> bool:
        """测试批量操作"""
        try:
            test_index = "test_bulk"

            # 创建测试索引
            await self.index_manager.delete_index(test_index)
            mapping = self.index_manager.get_default_chinese_mapping()
            await self.index_manager.create_index(test_index, mapping)

            # 准备测试数据
            test_docs = []
            for i in range(100):
                doc = {
                    "title": f"批量测试文档 {i}",
                    "content": f"这是第 {i} 个批量测试文档，用于测试批量索引功能",
                    "category": f"分类{i % 5}",
                    "tags": [f"标签{i}", f"标签{i+1}"],
                    "created_at": (datetime.now() - timedelta(days=i)).isoformat(),
                    "status": "published" if i % 2 == 0 else "draft",
                    "priority": i % 3,
                }
                test_docs.append(doc)

            # 批量索引
            start_time = time.time()
            result = await self.search_service.bulk_index_documents(
                test_index, test_docs, refresh=True
            )
            bulk_time = time.time() - start_time

            # 检查结果
            if result.get("errors"):
                logger.error(f"批量索引有错误: {result}")
                return False

            # 统计文档数量
            count = await self.search_service.count_documents(test_index)
            if count != len(test_docs):
                logger.error(f"文档数量不匹配: 期望 {len(test_docs)}, 实际 {count}")
                return False

            # 记录性能数据
            self.performance_results["bulk_index_time"] = f"{bulk_time:.2f}s"
            self.performance_results["bulk_index_rate"] = (
                f"{len(test_docs)/bulk_time:.1f} docs/s"
            )

            # 清理测试索引
            await self.index_manager.delete_index(test_index)

            logger.info("✅ 批量操作测试通过")
            return True

        except Exception as e:
            logger.error(f"批量操作测试失败: {e}")
            return False

    async def test_search_functionality(self) -> bool:
        """测试搜索功能"""
        try:
            test_index = "test_search"

            # 创建测试索引
            await self.index_manager.delete_index(test_index)
            mapping = self.index_manager.get_default_chinese_mapping()
            await self.index_manager.create_index(test_index, mapping)

            # 准备测试数据
            test_docs = [
                {
                    "title": "柴管家智能客服系统介绍",
                    "content": "柴管家是一个多平台聚合的智能客服系统，支持微信、钉钉等多种平台",
                    "category": "产品介绍",
                    "tags": ["智能客服", "多平台", "聊天机器人"],
                    "created_at": datetime.now().isoformat(),
                    "status": "published",
                    "priority": 1,
                },
                {
                    "title": "如何配置搜索引擎",
                    "content": "本文介绍如何配置和使用 Elasticsearch 搜索引擎进行全文搜索",
                    "category": "技术文档",
                    "tags": ["搜索引擎", "Elasticsearch", "配置"],
                    "created_at": (datetime.now() - timedelta(days=1)).isoformat(),
                    "status": "published",
                    "priority": 2,
                },
                {
                    "title": "中文分词技术详解",
                    "content": "深入了解中文分词技术，包括 IK 分词器的使用和配置方法",
                    "category": "技术文档",
                    "tags": ["中文分词", "IK分词器", "自然语言处理"],
                    "created_at": (datetime.now() - timedelta(days=2)).isoformat(),
                    "status": "published",
                    "priority": 3,
                },
            ]

            # 批量索引测试数据
            await self.search_service.bulk_index_documents(
                test_index, test_docs, refresh=True
            )

            # 测试基本搜索
            from app.shared.search import SearchQuery

            # 1. 简单关键词搜索
            query = SearchQuery(query="柴管家", size=10)
            result = await self.search_service.search(test_index, query)

            if result.total == 0:
                logger.error("关键词搜索无结果")
                return False

            # 2. 多字段搜索
            query = SearchQuery(
                query="搜索引擎", fields=["title^2", "content", "tags"], size=10
            )
            result = await self.search_service.search(test_index, query)

            if result.total == 0:
                logger.error("多字段搜索无结果")
                return False

            # 3. 过滤搜索
            from app.shared.search import SearchFilter

            query = SearchQuery(
                query="技术",
                size=10,
                filters=[SearchFilter(field="category", values=["技术文档"])],
            )
            result = await self.search_service.search(test_index, query)

            if result.total == 0:
                logger.error("过滤搜索无结果")
                return False

            # 4. 排序搜索
            from app.shared.search import SearchSort

            query = SearchQuery(
                query="",
                size=10,
                sort=[
                    SearchSort(field="priority", order="desc"),
                    SearchSort(field="created_at", order="desc"),
                ],
            )
            result = await self.search_service.search(test_index, query)

            if result.total == 0:
                logger.error("排序搜索无结果")
                return False

            # 5. 高亮搜索
            from app.shared.search import SearchHighlight

            query = SearchQuery(
                query="分词",
                fields=["title", "content"],
                highlight=SearchHighlight(
                    fields=["title", "content"],
                    fragment_size=100,
                    number_of_fragments=2,
                ),
            )
            result = await self.search_service.search(test_index, query)

            # 检查高亮结果
            has_highlight = any(doc.highlight for doc in result.documents)

            if not has_highlight:
                logger.warning("高亮功能可能未正常工作")

            # 清理测试索引
            await self.index_manager.delete_index(test_index)

            logger.info("✅ 搜索功能测试通过")
            return True

        except Exception as e:
            logger.error(f"搜索功能测试失败: {e}")
            return False

    async def test_chinese_analysis(self) -> bool:
        """测试中文分词"""
        try:
            # 测试中文分词
            test_text = "柴管家是一个基于人工智能的多平台聚合智能客服系统"

            # 使用 ik_max_word 分析器
            tokens = await self.search_service.analyze_text(test_text, "ik_max_word")

            if not tokens:
                logger.error("中文分词无结果")
                return False

            logger.info(f"分词结果: {tokens}")

            # 检查是否包含预期的词
            expected_tokens = ["柴管家", "人工智能", "智能客服", "系统"]
            found_tokens = []

            for expected in expected_tokens:
                if expected in tokens:
                    found_tokens.append(expected)

            if len(found_tokens) < 2:
                logger.warning(f"分词结果可能不理想，找到的期望词: {found_tokens}")
            else:
                logger.info(f"分词效果良好，找到期望词: {found_tokens}")

            logger.info("✅ 中文分词测试通过")
            return True

        except Exception as e:
            logger.error(f"中文分词测试失败: {e}")
            return False

    async def test_performance(self) -> bool:
        """测试搜索性能"""
        try:
            test_index = "test_performance"

            # 创建测试索引
            await self.index_manager.delete_index(test_index)
            mapping = self.index_manager.get_default_chinese_mapping()
            await self.index_manager.create_index(test_index, mapping)

            # 生成大量测试数据
            logger.info("生成性能测试数据...")
            test_docs = []
            for i in range(1000):
                doc = {
                    "title": f"性能测试文档 {i}",
                    "content": f"这是第 {i} 个性能测试文档，用于测试搜索性能。内容包含各种关键词：搜索、测试、性能、优化、系统、功能。",
                    "category": f"分类{i % 10}",
                    "tags": [f"标签{i % 20}", f"性能", f"测试"],
                    "created_at": (
                        datetime.now() - timedelta(days=i % 365)
                    ).isoformat(),
                    "status": "published",
                    "priority": i % 5,
                }
                test_docs.append(doc)

            # 批量索引
            logger.info("索引测试数据...")
            start_time = time.time()
            await self.search_service.bulk_index_documents(
                test_index, test_docs, refresh=True
            )
            index_time = time.time() - start_time

            # 执行多次搜索测试
            search_queries = [
                "性能测试",
                "搜索功能",
                "系统优化",
                "文档内容",
                "关键词查询",
            ]

            search_times = []

            for query_text in search_queries:
                from app.shared.search import SearchQuery

                query = SearchQuery(
                    query=query_text, fields=["title^2", "content"], size=20
                )

                # 执行多次搜索求平均值
                times = []
                for _ in range(5):
                    start_time = time.time()
                    result = await self.search_service.search(test_index, query)
                    search_time = time.time() - start_time
                    times.append(search_time)

                avg_time = sum(times) / len(times)
                search_times.append(avg_time)

                logger.info(
                    f"查询 '{query_text}': {avg_time:.3f}s, 结果数: {result.total}"
                )

            # 计算性能指标
            avg_search_time = sum(search_times) / len(search_times)
            max_search_time = max(search_times)

            # 记录性能数据
            self.performance_results.update(
                {
                    "index_docs_count": len(test_docs),
                    "index_time": f"{index_time:.2f}s",
                    "index_rate": f"{len(test_docs)/index_time:.1f} docs/s",
                    "avg_search_time": f"{avg_search_time:.3f}s",
                    "max_search_time": f"{max_search_time:.3f}s",
                    "search_queries_tested": len(search_queries),
                }
            )

            # 性能验收标准
            if avg_search_time > 1.0:  # 平均搜索时间不超过1秒
                logger.warning(f"搜索性能较慢: {avg_search_time:.3f}s")
            else:
                logger.info(f"搜索性能良好: {avg_search_time:.3f}s")

            # 清理测试索引
            await self.index_manager.delete_index(test_index)

            logger.info("✅ 性能测试完成")
            return True

        except Exception as e:
            logger.error(f"性能测试失败: {e}")
            return False

    async def test_business_scenarios(self) -> bool:
        """测试业务场景"""
        try:
            # 设置默认索引
            await self.index_manager.setup_default_indices()

            # 测试消息搜索
            test_messages = [
                {
                    "content": "用户咨询产品价格信息",
                    "sender_name": "客户001",
                    "category": "销售咨询",
                    "created_at": datetime.now().isoformat(),
                    "channel": "微信",
                },
                {
                    "content": "技术支持请求，系统登录问题",
                    "sender_name": "客户002",
                    "category": "技术支持",
                    "created_at": datetime.now().isoformat(),
                    "channel": "钉钉",
                },
            ]

            # 索引消息
            await self.search_service.bulk_index_documents(
                "messages", test_messages, refresh=True
            )

            # 测试消息搜索
            result = await self.search_service.search_messages(
                query="价格", filters={"category": ["销售咨询"]}, size=10
            )

            if result.total == 0:
                logger.error("消息搜索无结果")
                return False

            # 测试知识库搜索
            test_knowledge = [
                {
                    "title": "产品价格说明",
                    "content": "我们的产品采用阶梯定价模式，根据使用量确定价格",
                    "category": "产品说明",
                    "tags": ["价格", "定价", "产品"],
                    "created_at": datetime.now().isoformat(),
                }
            ]

            # 索引知识库
            await self.search_service.bulk_index_documents(
                "knowledge", test_knowledge, refresh=True
            )

            # 测试知识库搜索
            result = await self.search_service.search_knowledge(
                query="价格定价", category="产品说明", size=5
            )

            if result.total == 0:
                logger.error("知识库搜索无结果")
                return False

            logger.info("✅ 业务场景测试通过")
            return True

        except Exception as e:
            logger.error(f"业务场景测试失败: {e}")
            return False

    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        logger.info("开始搜索系统全面测试...")

        # 初始化搜索系统
        if not await self.init_search_system():
            return {"init": False}

        # 定义测试列表
        tests = [
            ("Elasticsearch连接", self.test_elasticsearch_connection),
            ("索引管理", self.test_index_management),
            ("文档操作", self.test_document_operations),
            ("批量操作", self.test_bulk_operations),
            ("搜索功能", self.test_search_functionality),
            ("中文分词", self.test_chinese_analysis),
            ("性能测试", self.test_performance),
            ("业务场景", self.test_business_scenarios),
        ]

        # 执行测试
        for test_name, test_func in tests:
            logger.info(f"\n开始测试: {test_name}")
            try:
                result = await test_func()
                self.test_results[test_name] = result

                if result:
                    logger.info(f"✅ {test_name} 测试通过")
                else:
                    logger.error(f"❌ {test_name} 测试失败")

            except Exception as e:
                logger.error(f"❌ {test_name} 测试异常: {e}")
                self.test_results[test_name] = False

        return self.test_results

    def print_test_report(self):
        """打印测试报告"""
        print("\n" + "=" * 60)
        print("搜索系统测试报告")
        print("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)

        print(f"\n测试总结:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过测试: {passed_tests}")
        print(f"  失败测试: {total_tests - passed_tests}")
        print(f"  通过率: {passed_tests/total_tests*100:.1f}%")

        print(f"\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")

        if self.performance_results:
            print(f"\n性能测试结果:")
            for metric, value in self.performance_results.items():
                print(f"  {metric}: {value}")

        print("\n系统建议:")
        if passed_tests == total_tests:
            print("  🎉 所有测试通过，搜索系统运行良好！")
        else:
            print("  ⚠️  部分测试失败，请检查相关配置和依赖")
            if (
                "Elasticsearch连接" in self.test_results
                and not self.test_results["Elasticsearch连接"]
            ):
                print("  - 检查 Elasticsearch 服务是否正常运行")
            if "中文分词" in self.test_results and not self.test_results["中文分词"]:
                print("  - 检查 IK 中文分词器是否正确安装")

        print("\n" + "=" * 60)


async def main():
    """主函数"""
    tester = SearchSystemTester()

    try:
        # 运行所有测试
        results = await tester.run_all_tests()

        # 打印测试报告
        tester.print_test_report()

        # 确定退出状态
        all_passed = all(results.values())

        if all_passed:
            logger.info("🎉 搜索系统测试全部通过！")
            return 0
        else:
            logger.error("❌ 部分搜索系统测试失败！")
            return 1

    except Exception as e:
        logger.error(f"测试执行异常: {e}")
        return 1

    finally:
        # 清理资源
        try:
            from app.shared.search import close_elasticsearch

            await close_elasticsearch()
        except Exception as e:
            logger.warning(f"清理资源失败: {e}")


if __name__ == "__main__":
    import os
    import sys

    # 添加项目根目录到 Python 路径
    sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

    exit_code = asyncio.run(main())
    sys.exit(exit_code)
