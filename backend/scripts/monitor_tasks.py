#!/usr/bin/env python3
"""
任务监控脚本

提供命令行界面监控任务系统状态，包括：
- 实时监控任务状态
- 查看队列信息
- 检查worker状态
- 性能统计
- 错误分析
"""

import argparse
import json
import logging
import sys
import time
from datetime import datetime, timezone
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.shared.tasks.manager import get_task_manager
from app.shared.tasks.monitor import get_task_monitor
from app.shared.tasks.registry import TaskRegistry
from app.shared.tasks.retry_handler import get_retry_handler
from app.shared.tasks.scheduler import TaskScheduler


def setup_logging(log_level: str = "INFO"):
    """配置日志"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


def format_timestamp(timestamp):
    """格式化时间戳"""
    if isinstance(timestamp, str):
        try:
            dt = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
        except:
            return timestamp
    else:
        dt = timestamp

    return dt.strftime("%Y-%m-%d %H:%M:%S")


def print_section(title: str, width: int = 60):
    """打印章节标题"""
    print("\n" + "=" * width)
    print(f" {title}")
    print("=" * width)


def print_subsection(title: str, width: int = 40):
    """打印子章节标题"""
    print(f"\n{'-' * width}")
    print(f" {title}")
    print("-" * width)


def monitor_overview():
    """显示监控概览"""
    print_section("任务系统监控概览")

    try:
        manager = get_task_manager()
        summary = manager.get_task_summary()

        # 基本信息
        print(f"时间戳: {format_timestamp(summary.get('timestamp', 'N/A'))}")

        # 注册信息
        registry_info = summary.get("registry", {})
        print(f"注册任务总数: {registry_info.get('total_tasks', 0)}")
        print(f"任务类型数量: {len(registry_info.get('task_count_by_type', {}))}")
        print(f"队列数量: {len(registry_info.get('queue_names', []))}")

        # 运行状态
        print(f"活跃任务: {summary.get('active_tasks', 0)}")
        print(f"调度任务: {summary.get('scheduled_tasks', 0)}")
        print(f"预留任务: {summary.get('reserved_tasks', 0)}")

        # Worker信息
        workers = summary.get("workers", {})
        print(f"Worker数量: {workers.get('count', 0)}")

        return True

    except Exception as e:
        print(f"获取监控概览失败: {e}")
        return False


def monitor_queues():
    """监控队列状态"""
    print_section("队列监控")

    try:
        manager = get_task_manager()
        monitor = get_task_monitor()

        # 获取队列信息
        queue_status = monitor.get_queue_status()
        queue_lengths = queue_status.get("queue_lengths", {})

        if not queue_lengths:
            print("当前没有队列信息")
            return True

        print(f"{'队列名称':<20} {'长度':<10} {'状态':<10}")
        print("-" * 40)

        for queue_name, length in queue_lengths.items():
            status = "正常"
            if length > 1000:
                status = "拥堵"
            elif length > 100:
                status = "繁忙"

            print(f"{queue_name:<20} {length:<10} {status:<10}")

        # 队列统计
        total_messages = sum(queue_lengths.values())
        print(f"\n总消息数: {total_messages}")
        print(f"平均队列长度: {total_messages / len(queue_lengths):.1f}")

        return True

    except Exception as e:
        print(f"获取队列状态失败: {e}")
        return False


def monitor_workers():
    """监控Worker状态"""
    print_section("Worker监控")

    try:
        manager = get_task_manager()

        # 获取worker统计
        worker_stats = manager.get_worker_stats()

        if not worker_stats:
            print("当前没有活跃的Worker")
            return True

        print(f"{'Worker主机':<25} {'进程数':<8} {'活跃任务':<10} {'总处理量':<12}")
        print("-" * 55)

        for worker_name, stats in worker_stats.items():
            processed = stats.get("total", {}).get("tasks.system.health_check", 0)
            active = len(stats.get("active", []))
            pool_size = stats.get("pool", {}).get("max-concurrency", 0)

            print(f"{worker_name:<25} {pool_size:<8} {active:<10} {processed:<12}")

        # Worker总览
        total_workers = len(worker_stats)
        total_active = sum(len(s.get("active", [])) for s in worker_stats.values())

        print(f"\nWorker总数: {total_workers}")
        print(f"总活跃任务: {total_active}")

        return True

    except Exception as e:
        print(f"获取Worker状态失败: {e}")
        return False


def monitor_tasks():
    """监控任务统计"""
    print_section("任务统计")

    try:
        monitor = get_task_monitor()
        stats = monitor.get_statistics()

        # 基本统计
        print(f"总任务数: {stats.total_tasks}")
        print(f"运行中任务: {stats.running_tasks}")
        print(f"等待中任务: {stats.pending_tasks}")
        print(f"已完成任务: {stats.completed_tasks}")
        print(f"失败任务: {stats.failed_tasks}")
        print(f"成功率: {stats.success_rate:.2f}%")

        # 性能指标
        print(f"\n平均执行时间: {stats.average_execution_time:.2f}秒")
        print(f"每分钟处理量: {stats.tasks_per_minute}")

        # 系统资源（如果可用）
        if hasattr(stats, "system_metrics") and stats.system_metrics:
            metrics = stats.system_metrics
            print(f"\nCPU使用率: {metrics.get('cpu_usage', 0):.1f}%")
            print(f"内存使用率: {metrics.get('memory_usage', 0):.1f}%")

        print(f"\n最后更新: {format_timestamp(stats.last_updated)}")

        return True

    except Exception as e:
        print(f"获取任务统计失败: {e}")
        return False


def monitor_registry():
    """监控任务注册表"""
    print_section("任务注册表")

    try:
        registry = TaskRegistry.get_instance()
        summary = registry.get_registry_summary()

        print(f"注册任务总数: {summary['total_tasks']}")

        # 按类型分组
        print_subsection("按任务类型分组")
        type_counts = summary["task_count_by_type"]
        for task_type, count in type_counts.items():
            print(f"  {task_type}: {count}")

        # 按队列分组
        print_subsection("按队列分组")
        queue_counts = summary["task_count_by_queue"]
        for queue_name, count in queue_counts.items():
            print(f"  {queue_name}: {count}")

        # 按优先级分组
        print_subsection("按优先级分组")
        priority_counts = summary["task_count_by_priority"]
        for priority, count in priority_counts.items():
            print(f"  {priority}: {count}")

        return True

    except Exception as e:
        print(f"获取注册表信息失败: {e}")
        return False


def monitor_scheduler():
    """监控定时任务调度器"""
    print_section("定时任务调度")

    try:
        scheduler = TaskScheduler.get_instance()
        status = scheduler.get_scheduler_status()

        print(f"总定时任务数: {status['total_tasks']}")
        print(f"启用任务数: {status['enabled_tasks']}")
        print(f"禁用任务数: {status['disabled_tasks']}")

        # 任务详情
        tasks = status.get("tasks", {})
        if tasks:
            print_subsection("定时任务详情")
            print(f"{'任务名称':<30} {'状态':<8} {'下次执行':<20}")
            print("-" * 58)

            for name, task_info in tasks.items():
                enabled = "启用" if task_info["enabled"] else "禁用"
                next_run = task_info.get("next_run", "N/A")
                if next_run != "N/A":
                    next_run = format_timestamp(next_run)

                print(f"{name:<30} {enabled:<8} {next_run:<20}")

        return True

    except Exception as e:
        print(f"获取调度器状态失败: {e}")
        return False


def monitor_errors():
    """监控错误统计"""
    print_section("错误统计")

    try:
        retry_handler = get_retry_handler()
        error_stats = retry_handler.get_error_statistics()

        total_errors = error_stats["total_errors"]
        print(f"总错误数: {total_errors}")

        if total_errors == 0:
            print("当前没有错误记录")
            return True

        # 按分类统计
        print_subsection("按错误分类统计")
        error_by_category = error_stats["error_by_category"]
        for category, count in error_by_category.items():
            percentage = (count / total_errors) * 100
            print(f"  {category}: {count} ({percentage:.1f}%)")

        # 按任务统计
        print_subsection("按任务统计")
        error_by_task = error_stats["error_by_task"]
        sorted_tasks = sorted(error_by_task.items(), key=lambda x: x[1], reverse=True)
        for task_name, count in sorted_tasks[:10]:  # 显示前10个
            print(f"  {task_name}: {count}")

        # 熔断器状态
        circuit_states = error_stats.get("circuit_breaker_states", {})
        if circuit_states:
            print_subsection("熔断器状态")
            for task_name, state in circuit_states.items():
                status = state["state"]
                failures = state["failure_count"]
                print(f"  {task_name}: {status} (失败次数: {failures})")

        # 最近错误
        recent_errors = error_stats.get("recent_errors", [])
        if recent_errors:
            print_subsection("最近错误 (前5个)")
            for error in recent_errors[:5]:
                timestamp = format_timestamp(error["timestamp"])
                print(
                    f"  [{timestamp}] {error['task_name']}: {error['message'][:50]}..."
                )

        return True

    except Exception as e:
        print(f"获取错误统计失败: {e}")
        return False


def monitor_realtime(interval: int = 5):
    """实时监控"""
    print_section("实时监控")
    print(f"监控间隔: {interval}秒 (按 Ctrl+C 退出)")

    try:
        while True:
            # 清屏
            import os

            os.system("clear" if os.name == "posix" else "cls")

            print(f"实时监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 60)

            # 快速概览
            manager = get_task_manager()
            monitor = get_task_monitor()

            summary = manager.get_task_summary()
            stats = monitor.get_statistics()
            queue_status = monitor.get_queue_status()

            # 关键指标
            print(f"活跃任务: {summary.get('active_tasks', 0)}")
            print(f"队列总长度: {sum(queue_status.get('queue_lengths', {}).values())}")
            print(f"Worker数量: {summary.get('workers', {}).get('count', 0)}")
            print(f"成功率: {stats.success_rate:.2f}%")
            print(f"每分钟处理量: {stats.tasks_per_minute}")

            # 队列状态
            print("\n队列状态:")
            queue_lengths = queue_status.get("queue_lengths", {})
            for queue_name, length in queue_lengths.items():
                status = "🟢" if length < 10 else "🟡" if length < 100 else "🔴"
                print(f"  {status} {queue_name}: {length}")

            # 系统资源
            if hasattr(stats, "system_metrics") and stats.system_metrics:
                metrics = stats.system_metrics
                cpu = metrics.get("cpu_usage", 0)
                memory = metrics.get("memory_usage", 0)

                cpu_status = "🟢" if cpu < 70 else "🟡" if cpu < 90 else "🔴"
                mem_status = "🟢" if memory < 70 else "🟡" if memory < 90 else "🔴"

                print(f"\n系统资源:")
                print(f"  {cpu_status} CPU: {cpu:.1f}%")
                print(f"  {mem_status} 内存: {memory:.1f}%")

            time.sleep(interval)

    except KeyboardInterrupt:
        print("\n\n监控已停止")
        return True
    except Exception as e:
        print(f"实时监控失败: {e}")
        return False


def export_report(output_file: str):
    """导出监控报告"""
    print_section("导出监控报告")

    try:
        report = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "overview": {},
            "tasks": {},
            "queues": {},
            "workers": {},
            "registry": {},
            "scheduler": {},
            "errors": {},
        }

        # 收集各种数据
        manager = get_task_manager()
        monitor = get_task_monitor()
        registry = TaskRegistry.get_instance()
        scheduler = TaskScheduler.get_instance()
        retry_handler = get_retry_handler()

        print("收集数据...")

        # 概览
        report["overview"] = manager.get_task_summary()

        # 任务统计
        stats = monitor.get_statistics()
        report["tasks"] = {
            "total_tasks": stats.total_tasks,
            "running_tasks": stats.running_tasks,
            "completed_tasks": stats.completed_tasks,
            "failed_tasks": stats.failed_tasks,
            "success_rate": stats.success_rate,
            "average_execution_time": stats.average_execution_time,
            "tasks_per_minute": stats.tasks_per_minute,
        }

        # 队列状态
        report["queues"] = monitor.get_queue_status()

        # Worker状态
        report["workers"] = manager.get_worker_stats()

        # 注册表
        report["registry"] = registry.get_registry_summary()

        # 调度器
        report["scheduler"] = scheduler.get_scheduler_status()

        # 错误统计
        report["errors"] = retry_handler.get_error_statistics()

        # 写入文件
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        print(f"监控报告已导出到: {output_file}")
        return True

    except Exception as e:
        print(f"导出报告失败: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="任务系统监控工具")

    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 概览命令
    overview_parser = subparsers.add_parser("overview", help="显示系统概览")

    # 队列监控
    queue_parser = subparsers.add_parser("queues", help="监控队列状态")

    # Worker监控
    worker_parser = subparsers.add_parser("workers", help="监控Worker状态")

    # 任务统计
    task_parser = subparsers.add_parser("tasks", help="显示任务统计")

    # 注册表
    registry_parser = subparsers.add_parser("registry", help="显示任务注册表")

    # 调度器
    scheduler_parser = subparsers.add_parser("scheduler", help="显示定时任务状态")

    # 错误统计
    error_parser = subparsers.add_parser("errors", help="显示错误统计")

    # 实时监控
    realtime_parser = subparsers.add_parser("realtime", help="实时监控")
    realtime_parser.add_argument(
        "-i", "--interval", type=int, default=5, help="监控间隔（秒）"
    )

    # 全面监控
    full_parser = subparsers.add_parser("full", help="显示完整监控信息")

    # 导出报告
    export_parser = subparsers.add_parser("export", help="导出监控报告")
    export_parser.add_argument(
        "-o",
        "--output",
        default=f"task_monitor_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        help="输出文件名",
    )

    # 全局参数
    parser.add_argument(
        "-l",
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="WARNING",
        help="日志级别",
    )

    args = parser.parse_args()

    # 设置日志
    setup_logging(args.log_level)

    # 执行命令
    success = True

    if args.command == "overview":
        success = monitor_overview()
    elif args.command == "queues":
        success = monitor_queues()
    elif args.command == "workers":
        success = monitor_workers()
    elif args.command == "tasks":
        success = monitor_tasks()
    elif args.command == "registry":
        success = monitor_registry()
    elif args.command == "scheduler":
        success = monitor_scheduler()
    elif args.command == "errors":
        success = monitor_errors()
    elif args.command == "realtime":
        success = monitor_realtime(args.interval)
    elif args.command == "full":
        # 显示所有监控信息
        success = (
            monitor_overview()
            and monitor_tasks()
            and monitor_queues()
            and monitor_workers()
            and monitor_registry()
            and monitor_scheduler()
            and monitor_errors()
        )
    elif args.command == "export":
        success = export_report(args.output)
    else:
        parser.print_help()
        success = False

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
