#!/usr/bin/env python3
"""
消息队列监控脚本

用于监控消息队列状态和性能
"""

import argparse
import json
import os
import sys
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.shared.messaging import get_message_queue_manager
from app.shared.messaging.config import QueueType
from app.shared.messaging.monitor import AlertLevel


class MessageQueueMonitor:
    """消息队列监控器"""

    def __init__(self, rabbitmq_url: str = None):
        self.manager = get_message_queue_manager()
        if rabbitmq_url:
            from app.shared.messaging import init_message_queue_manager

            self.manager = init_message_queue_manager(rabbitmq_url)

    def get_queue_status(self) -> Dict[str, Any]:
        """获取所有队列状态"""
        status = {}

        for queue_type in QueueType:
            try:
                queue_info = self.manager.get_queue_info(queue_type)
                if queue_info:
                    status[queue_type.value] = queue_info
                else:
                    status[queue_type.value] = {"error": "Unable to get queue info"}
            except Exception as e:
                status[queue_type.value] = {"error": str(e)}

        return status

    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            return self.manager.get_health_status()
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def get_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            return self.manager.get_statistics()
        except Exception as e:
            return {"error": str(e)}

    def get_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的告警信息"""
        try:
            if not self.manager.monitor:
                return []

            since = datetime.now() - timedelta(hours=hours)
            alerts = self.manager.monitor.get_alerts(since=since)

            return [alert.to_dict() for alert in alerts]
        except Exception as e:
            return [{"error": str(e)}]

    def print_dashboard(self) -> None:
        """打印监控面板"""
        print("=" * 80)
        print(
            f"柴管家消息队列监控面板 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        print("=" * 80)

        # 系统健康状态
        health = self.get_system_health()
        status_color = {"healthy": "🟢", "degraded": "🟡", "unhealthy": "🔴"}.get(
            health.get("status", "unknown"), "⚪"
        )

        print(
            f"\n📊 系统状态: {status_color} {health.get('status', 'unknown').upper()}"
        )

        # 管理器状态
        if "manager" in health:
            manager_info = health["manager"]
            print(f"   - 已初始化: {'✅' if manager_info.get('initialized') else '❌'}")
            print(f"   - 运行中: {'✅' if manager_info.get('running') else '❌'}")

        # 组件状态
        components = ["publisher", "consumer", "monitor"]
        for component in components:
            if component in health:
                comp_status = health[component].get("status", "unknown")
                comp_color = {"healthy": "🟢", "degraded": "🟡", "unhealthy": "🔴"}.get(
                    comp_status, "⚪"
                )
                print(f"   - {component.title()}: {comp_color} {comp_status}")

        # 队列状态
        print(f"\n📥 队列状态:")
        queue_status = self.get_queue_status()

        for queue_name, info in queue_status.items():
            if "error" in info:
                print(f"   - {queue_name}: ❌ {info['error']}")
            else:
                msg_count = info.get("message_count", 0)
                consumer_count = info.get("consumer_count", 0)

                # 根据消息数量显示状态
                if msg_count == 0:
                    queue_color = "🟢"
                elif msg_count < 100:
                    queue_color = "🟡"
                else:
                    queue_color = "🔴"

                print(
                    f"   - {queue_name}: {queue_color} {msg_count} 消息, {consumer_count} 消费者"
                )

        # 统计信息
        print(f"\n📈 统计信息:")
        stats = self.get_statistics()

        if "publisher" in stats:
            pub_stats = stats["publisher"]
            print(f"   - 发布消息: {pub_stats.get('messages_published', 0)}")
            print(f"   - 发布失败: {pub_stats.get('messages_failed', 0)}")
            print(f"   - 发布字节: {pub_stats.get('bytes_published', 0):,}")

        if "consumer" in stats:
            con_stats = stats["consumer"]
            print(f"   - 消费消息: {con_stats.get('messages_consumed', 0)}")
            print(f"   - 确认消息: {con_stats.get('messages_acked', 0)}")
            print(f"   - 拒绝消息: {con_stats.get('messages_rejected', 0)}")
            print(f"   - 活跃消费者: {con_stats.get('active_consumers', 0)}")

        if "retry_handler" in stats:
            retry_stats = stats["retry_handler"]
            print(f"   - 总重试: {retry_stats.get('total_retries', 0)}")
            print(f"   - 重试成功: {retry_stats.get('successful_retries', 0)}")
            print(f"   - 重试耗尽: {retry_stats.get('exhausted_retries', 0)}")

        # 告警信息
        print(f"\n🚨 最近告警 (24小时):")
        alerts = self.get_alerts(24)

        if not alerts:
            print("   - 无告警")
        else:
            for alert in alerts[-5:]:  # 显示最近5个告警
                if "error" in alert:
                    print(f"   - ❌ 获取告警失败: {alert['error']}")
                    continue

                level_color = {
                    "info": "🔵",
                    "warning": "🟡",
                    "error": "🔴",
                    "critical": "🆘",
                }.get(alert.get("level", "info"), "⚪")

                timestamp = alert.get("timestamp", "")
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
                        time_str = dt.strftime("%H:%M:%S")
                    except:
                        time_str = timestamp[:8]
                else:
                    time_str = "Unknown"

                title = alert.get("title", "Unknown Alert")
                print(f"   - {level_color} [{time_str}] {title}")

        print("\n" + "=" * 80)

    def print_detailed_stats(self) -> None:
        """打印详细统计信息"""
        print("=" * 80)
        print("详细统计信息")
        print("=" * 80)

        stats = self.get_statistics()
        print(json.dumps(stats, indent=2, ensure_ascii=False, default=str))

    def print_alerts_summary(self, hours: int = 24) -> None:
        """打印告警摘要"""
        print("=" * 80)
        print(f"告警摘要 (最近{hours}小时)")
        print("=" * 80)

        alerts = self.get_alerts(hours)

        if not alerts:
            print("无告警记录")
            return

        # 按级别统计
        level_counts = {}
        for alert in alerts:
            if "error" in alert:
                continue
            level = alert.get("level", "unknown")
            level_counts[level] = level_counts.get(level, 0) + 1

        print(f"总告警数: {len(alerts)}")
        for level, count in level_counts.items():
            level_color = {
                "info": "🔵",
                "warning": "🟡",
                "error": "🔴",
                "critical": "🆘",
            }.get(level, "⚪")
            print(f"{level_color} {level.upper()}: {count}")

        print("\n详细告警:")
        for alert in alerts:
            if "error" in alert:
                print(f"❌ 告警错误: {alert['error']}")
                continue

            level_color = {
                "info": "🔵",
                "warning": "🟡",
                "error": "🔴",
                "critical": "🆘",
            }.get(alert.get("level", "info"), "⚪")

            timestamp = alert.get("timestamp", "")
            if timestamp:
                try:
                    dt = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
                    time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    time_str = timestamp
            else:
                time_str = "Unknown Time"

            print(f"{level_color} [{time_str}] {alert.get('title', 'Unknown')}")
            print(f"   消息: {alert.get('message', 'No message')}")
            print(f"   来源: {alert.get('source', 'Unknown')}")
            if alert.get("resolved"):
                print(f"   状态: ✅ 已解决")
            else:
                print(f"   状态: ⏳ 未解决")
            print()

    def monitor_continuously(self, interval: int = 30) -> None:
        """持续监控"""
        print(f"开始持续监控 (每{interval}秒刷新一次，按 Ctrl+C 停止)")

        try:
            while True:
                # 清屏
                os.system("clear" if os.name == "posix" else "cls")

                # 显示面板
                self.print_dashboard()

                # 等待
                time.sleep(interval)

        except KeyboardInterrupt:
            print("\n监控已停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Message Queue Monitor")

    parser.add_argument(
        "--rabbitmq-url",
        type=str,
        help="RabbitMQ connection URL",
        default=os.getenv("RABBITMQ_URL"),
    )

    parser.add_argument(
        "--command",
        choices=["dashboard", "stats", "alerts", "watch"],
        default="dashboard",
        help="Monitor command",
    )

    parser.add_argument(
        "--interval",
        type=int,
        default=30,
        help="Refresh interval for watch mode (seconds)",
    )

    parser.add_argument(
        "--hours", type=int, default=24, help="Hours to look back for alerts"
    )

    args = parser.parse_args()

    # 创建监控器
    monitor = MessageQueueMonitor(args.rabbitmq_url)

    try:
        if args.command == "dashboard":
            monitor.print_dashboard()

        elif args.command == "stats":
            monitor.print_detailed_stats()

        elif args.command == "alerts":
            monitor.print_alerts_summary(args.hours)

        elif args.command == "watch":
            monitor.monitor_continuously(args.interval)

    except Exception as e:
        print(f"监控错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
