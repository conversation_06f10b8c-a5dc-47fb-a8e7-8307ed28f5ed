#!/usr/bin/env python3
"""
文件存储系统监控脚本

实时监控文件存储系统的状态、性能和健康状况
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from app.core.config import get_settings
from app.shared.storage import get_file_manager, get_minio_client, init_minio

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class FileStorageMonitor:
    """文件存储系统监控器"""

    def __init__(self):
        self.minio_client = None
        self.file_manager = None
        self.settings = get_settings()
        self.monitoring = False
        self.metrics_history: List[Dict] = []
        self.alerts: List[Dict] = []

        # 监控阈值
        self.thresholds = {
            "storage_usage_percent": 80,  # 存储使用率告警阈值
            "response_time_ms": 1000,  # 响应时间告警阈值
            "error_rate_percent": 5,  # 错误率告警阈值
            "file_count_limit": 100000,  # 文件数量告警阈值
        }

    async def init_monitor(self) -> bool:
        """初始化监控器"""
        try:
            logger.info("初始化文件存储监控器...")

            # 初始化客户端
            self.minio_client = await init_minio()
            self.file_manager = get_file_manager()

            if not self.minio_client.is_connected:
                logger.error("MinIO连接失败")
                return False

            logger.info("✅ 监控器初始化完成")
            return True

        except Exception as e:
            logger.error(f"监控器初始化失败: {e}")
            return False

    async def collect_metrics(self) -> Dict:
        """收集监控指标"""
        try:
            metrics = {
                "timestamp": datetime.utcnow().isoformat(),
                "status": "healthy",
                "storage": {},
                "performance": {},
                "errors": {},
                "connectivity": {},
            }

            # 收集存储指标
            storage_metrics = await self._collect_storage_metrics()
            metrics["storage"] = storage_metrics

            # 收集性能指标
            performance_metrics = await self._collect_performance_metrics()
            metrics["performance"] = performance_metrics

            # 收集连接指标
            connectivity_metrics = await self._collect_connectivity_metrics()
            metrics["connectivity"] = connectivity_metrics

            # 计算整体健康状态
            metrics["status"] = self._calculate_health_status(metrics)

            return metrics

        except Exception as e:
            logger.error(f"收集监控指标失败: {e}")
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "status": "error",
                "error": str(e),
            }

    async def _collect_storage_metrics(self) -> Dict:
        """收集存储相关指标"""
        try:
            # 获取bucket统计信息
            bucket_stats = await self.minio_client.get_bucket_stats()

            # 计算存储使用率（假设有存储限制）
            # 这里使用一个假设的存储限制值，实际应该从配置或API获取
            storage_limit = 100 * 1024 * 1024 * 1024  # 100GB
            usage_percent = (bucket_stats["total_size"] / storage_limit) * 100

            return {
                "total_files": bucket_stats["total_files"],
                "total_size_bytes": bucket_stats["total_size"],
                "total_size_mb": bucket_stats["total_size"] / (1024 * 1024),
                "storage_usage_percent": usage_percent,
                "files_by_type": bucket_stats["files_by_type"],
                "bucket_name": bucket_stats["bucket_name"],
            }

        except Exception as e:
            logger.error(f"收集存储指标失败: {e}")
            return {"error": str(e)}

    async def _collect_performance_metrics(self) -> Dict:
        """收集性能相关指标"""
        try:
            # 测试上传性能
            upload_time = await self._test_upload_performance()

            # 测试下载性能
            download_time = await self._test_download_performance()

            # 测试列表性能
            list_time = await self._test_list_performance()

            return {
                "upload_response_time_ms": upload_time,
                "download_response_time_ms": download_time,
                "list_response_time_ms": list_time,
                "average_response_time_ms": (upload_time + download_time + list_time)
                / 3,
            }

        except Exception as e:
            logger.error(f"收集性能指标失败: {e}")
            return {"error": str(e)}

    async def _collect_connectivity_metrics(self) -> Dict:
        """收集连接相关指标"""
        try:
            # 测试连接
            start_time = time.time()
            is_connected = self.minio_client.is_connected
            connection_test_time = (time.time() - start_time) * 1000

            # 获取服务器信息
            endpoint = self.settings.minio_endpoint

            return {
                "is_connected": is_connected,
                "endpoint": endpoint,
                "connection_test_time_ms": connection_test_time,
                "bucket_accessible": True,  # TODO: 实际测试bucket访问
            }

        except Exception as e:
            logger.error(f"收集连接指标失败: {e}")
            return {"error": str(e)}

    async def _test_upload_performance(self) -> float:
        """测试上传性能"""
        try:
            test_data = b"x" * 1024  # 1KB测试数据
            file_path = f"monitor/upload_test_{int(time.time())}.txt"

            start_time = time.time()
            await self.minio_client.upload_file(
                file_path=file_path, file_data=test_data, content_type="text/plain"
            )
            upload_time = (time.time() - start_time) * 1000

            # 清理测试文件
            try:
                await self.minio_client.delete_file(file_path)
            except Exception:
                pass

            return upload_time

        except Exception as e:
            logger.warning(f"上传性能测试失败: {e}")
            return -1

    async def _test_download_performance(self) -> float:
        """测试下载性能"""
        try:
            # 首先上传一个测试文件
            test_data = b"x" * 1024  # 1KB测试数据
            file_path = f"monitor/download_test_{int(time.time())}.txt"

            await self.minio_client.upload_file(
                file_path=file_path, file_data=test_data, content_type="text/plain"
            )

            # 测试下载
            start_time = time.time()
            await self.minio_client.download_file(file_path)
            download_time = (time.time() - start_time) * 1000

            # 清理测试文件
            try:
                await self.minio_client.delete_file(file_path)
            except Exception:
                pass

            return download_time

        except Exception as e:
            logger.warning(f"下载性能测试失败: {e}")
            return -1

    async def _test_list_performance(self) -> float:
        """测试列表性能"""
        try:
            start_time = time.time()
            await self.minio_client.list_files(prefix="", max_keys=100)
            list_time = (time.time() - start_time) * 1000

            return list_time

        except Exception as e:
            logger.warning(f"列表性能测试失败: {e}")
            return -1

    def _calculate_health_status(self, metrics: Dict) -> str:
        """计算系统健康状态"""
        try:
            # 检查连接状态
            if not metrics.get("connectivity", {}).get("is_connected", False):
                return "critical"

            # 检查存储使用率
            storage_usage = metrics.get("storage", {}).get("storage_usage_percent", 0)
            if storage_usage > self.thresholds["storage_usage_percent"]:
                return "warning"

            # 检查响应时间
            avg_response_time = metrics.get("performance", {}).get(
                "average_response_time_ms", 0
            )
            if avg_response_time > self.thresholds["response_time_ms"]:
                return "warning"

            # 检查文件数量
            total_files = metrics.get("storage", {}).get("total_files", 0)
            if total_files > self.thresholds["file_count_limit"]:
                return "warning"

            return "healthy"

        except Exception as e:
            logger.error(f"计算健康状态失败: {e}")
            return "unknown"

    def analyze_metrics(self, metrics: Dict):
        """分析指标并生成告警"""
        try:
            alerts = []

            # 存储使用率告警
            storage_usage = metrics.get("storage", {}).get("storage_usage_percent", 0)
            if storage_usage > self.thresholds["storage_usage_percent"]:
                alerts.append(
                    {
                        "type": "storage_usage",
                        "level": "warning",
                        "message": f"存储使用率过高: {storage_usage:.1f}%",
                        "threshold": self.thresholds["storage_usage_percent"],
                        "current_value": storage_usage,
                        "timestamp": metrics["timestamp"],
                    }
                )

            # 响应时间告警
            avg_response_time = metrics.get("performance", {}).get(
                "average_response_time_ms", 0
            )
            if avg_response_time > self.thresholds["response_time_ms"]:
                alerts.append(
                    {
                        "type": "response_time",
                        "level": "warning",
                        "message": f"平均响应时间过长: {avg_response_time:.1f}ms",
                        "threshold": self.thresholds["response_time_ms"],
                        "current_value": avg_response_time,
                        "timestamp": metrics["timestamp"],
                    }
                )

            # 连接状态告警
            if not metrics.get("connectivity", {}).get("is_connected", False):
                alerts.append(
                    {
                        "type": "connectivity",
                        "level": "critical",
                        "message": "MinIO连接断开",
                        "timestamp": metrics["timestamp"],
                    }
                )

            # 文件数量告警
            total_files = metrics.get("storage", {}).get("total_files", 0)
            if total_files > self.thresholds["file_count_limit"]:
                alerts.append(
                    {
                        "type": "file_count",
                        "level": "warning",
                        "message": f"文件数量过多: {total_files}",
                        "threshold": self.thresholds["file_count_limit"],
                        "current_value": total_files,
                        "timestamp": metrics["timestamp"],
                    }
                )

            # 添加新告警
            for alert in alerts:
                self.alerts.append(alert)
                logger.warning(f"📢 告警: {alert['message']}")

            return alerts

        except Exception as e:
            logger.error(f"分析指标失败: {e}")
            return []

    def store_metrics(self, metrics: Dict):
        """存储历史指标"""
        try:
            # 保存到内存历史记录
            self.metrics_history.append(metrics)

            # 只保留最近1000条记录
            if len(self.metrics_history) > 1000:
                self.metrics_history = self.metrics_history[-1000:]

            # TODO: 可以保存到数据库或文件

        except Exception as e:
            logger.error(f"存储指标失败: {e}")

    def get_metrics_summary(self, hours: int = 24) -> Dict:
        """获取指标摘要"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)

            recent_metrics = [
                m
                for m in self.metrics_history
                if datetime.fromisoformat(m["timestamp"]) > cutoff_time
            ]

            if not recent_metrics:
                return {"message": "No recent metrics available"}

            # 计算平均值
            total_files = sum(
                m.get("storage", {}).get("total_files", 0) for m in recent_metrics
            )
            avg_files = total_files / len(recent_metrics)

            total_size = sum(
                m.get("storage", {}).get("total_size_bytes", 0) for m in recent_metrics
            )
            avg_size = total_size / len(recent_metrics)

            response_times = [
                m.get("performance", {}).get("average_response_time_ms", 0)
                for m in recent_metrics
                if m.get("performance", {}).get("average_response_time_ms", 0) > 0
            ]
            avg_response_time = (
                sum(response_times) / len(response_times) if response_times else 0
            )

            return {
                "period_hours": hours,
                "total_metrics": len(recent_metrics),
                "average_files": avg_files,
                "average_size_mb": avg_size / (1024 * 1024),
                "average_response_time_ms": avg_response_time,
                "latest_status": recent_metrics[-1].get("status", "unknown"),
                "alerts_count": len(
                    [
                        a
                        for a in self.alerts
                        if datetime.fromisoformat(a["timestamp"]) > cutoff_time
                    ]
                ),
            }

        except Exception as e:
            logger.error(f"获取指标摘要失败: {e}")
            return {"error": str(e)}

    async def health_check(self) -> Dict:
        """执行健康检查"""
        try:
            health_status = {
                "timestamp": datetime.utcnow().isoformat(),
                "overall_status": "healthy",
                "components": {},
            }

            # MinIO连接检查
            try:
                is_connected = self.minio_client.is_connected
                if is_connected:
                    # 测试基本操作
                    await self.minio_client.list_files(max_keys=1)
                    health_status["components"]["minio"] = {
                        "status": "healthy",
                        "message": "MinIO连接正常",
                    }
                else:
                    health_status["components"]["minio"] = {
                        "status": "unhealthy",
                        "message": "MinIO连接断开",
                    }
                    health_status["overall_status"] = "unhealthy"
            except Exception as e:
                health_status["components"]["minio"] = {
                    "status": "unhealthy",
                    "message": f"MinIO检查失败: {str(e)}",
                }
                health_status["overall_status"] = "unhealthy"

            # 存储空间检查
            try:
                storage_metrics = await self._collect_storage_metrics()
                usage_percent = storage_metrics.get("storage_usage_percent", 0)

                if usage_percent > 90:
                    health_status["components"]["storage"] = {
                        "status": "critical",
                        "message": f"存储空间严重不足: {usage_percent:.1f}%",
                    }
                    health_status["overall_status"] = "critical"
                elif usage_percent > 80:
                    health_status["components"]["storage"] = {
                        "status": "warning",
                        "message": f"存储空间不足: {usage_percent:.1f}%",
                    }
                    if health_status["overall_status"] == "healthy":
                        health_status["overall_status"] = "warning"
                else:
                    health_status["components"]["storage"] = {
                        "status": "healthy",
                        "message": f"存储空间充足: {usage_percent:.1f}%",
                    }
            except Exception as e:
                health_status["components"]["storage"] = {
                    "status": "unknown",
                    "message": f"存储检查失败: {str(e)}",
                }

            return health_status

        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "overall_status": "error",
                "error": str(e),
            }

    def print_current_status(self, metrics: Dict):
        """打印当前状态"""
        try:
            print("\n" + "=" * 60)
            print(f"文件存储系统监控 - {metrics['timestamp']}")
            print("=" * 60)

            # 整体状态
            status = metrics.get("status", "unknown")
            status_emoji = {
                "healthy": "🟢",
                "warning": "🟡",
                "critical": "🔴",
                "error": "❌",
            }.get(status, "⚪")
            print(f"整体状态: {status_emoji} {status.upper()}")

            # 存储信息
            storage = metrics.get("storage", {})
            if storage:
                print(f"\n📁 存储信息:")
                print(f"  文件总数: {storage.get('total_files', 0):,}")
                print(f"  总大小: {storage.get('total_size_mb', 0):.1f} MB")
                print(f"  使用率: {storage.get('storage_usage_percent', 0):.1f}%")

            # 性能信息
            performance = metrics.get("performance", {})
            if performance:
                print(f"\n⚡ 性能信息:")
                print(
                    f"  平均响应时间: {performance.get('average_response_time_ms', 0):.1f} ms"
                )
                print(
                    f"  上传时间: {performance.get('upload_response_time_ms', 0):.1f} ms"
                )
                print(
                    f"  下载时间: {performance.get('download_response_time_ms', 0):.1f} ms"
                )

            # 连接信息
            connectivity = metrics.get("connectivity", {})
            if connectivity:
                print(f"\n🔗 连接信息:")
                print(
                    f"  连接状态: {'✅ 已连接' if connectivity.get('is_connected') else '❌ 断开'}"
                )
                print(f"  端点: {connectivity.get('endpoint', 'unknown')}")

            print("=" * 60)

        except Exception as e:
            logger.error(f"打印状态失败: {e}")

    async def start_monitoring(self, interval: int = 60):
        """开始监控"""
        logger.info(f"开始监控文件存储系统，监控间隔: {interval}秒")

        self.monitoring = True

        try:
            while self.monitoring:
                # 收集指标
                metrics = await self.collect_metrics()

                # 存储指标
                self.store_metrics(metrics)

                # 分析指标并生成告警
                self.analyze_metrics(metrics)

                # 打印当前状态
                self.print_current_status(metrics)

                # 等待下一次监控
                await asyncio.sleep(interval)

        except KeyboardInterrupt:
            logger.info("监控被用户中断")
        except Exception as e:
            logger.error(f"监控过程中发生错误: {e}")
        finally:
            self.monitoring = False

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        logger.info("监控已停止")

    async def cleanup(self):
        """清理资源"""
        self.stop_monitoring()

        # 清理测试文件
        try:
            test_files = await self.minio_client.list_files(prefix="monitor/")
            for file_info in test_files:
                await self.minio_client.delete_file(file_info["object_name"])
            logger.info("清理了监控测试文件")
        except Exception as e:
            logger.warning(f"清理测试文件失败: {e}")


async def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("文件存储系统监控脚本")
    logger.info("=" * 60)

    monitor = FileStorageMonitor()

    try:
        # 初始化监控器
        if not await monitor.init_monitor():
            logger.error("监控器初始化失败")
            return 1

        # 执行一次健康检查
        health_status = await monitor.health_check()
        logger.info(f"健康检查结果: {health_status}")

        # 开始监控
        await monitor.start_monitoring(interval=30)  # 30秒间隔

    except KeyboardInterrupt:
        logger.info("用户中断监控")
    except Exception as e:
        logger.error(f"监控过程中发生错误: {e}")
        return 1
    finally:
        await monitor.cleanup()

    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
