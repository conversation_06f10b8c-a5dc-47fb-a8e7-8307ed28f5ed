#!/usr/bin/env python3
"""
缓存系统配置验证脚本

验证Redis缓存系统的基本配置和连接
"""

import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))


async def validate_cache_setup():
    """验证缓存系统配置"""
    print("正在验证缓存系统配置...")

    try:
        # 测试导入
        from app.shared.cache import (
            close_redis,
            get_cache_manager,
            get_permission_cache,
            get_session_cache,
            init_redis,
            redis_client,
            with_lock,
        )

        print("✅ 缓存模块导入成功")

        # 测试Redis连接
        await init_redis()
        await redis_client.set("test_key", "test_value", ttl=60)
        value = await redis_client.get("test_key")

        if value == "test_value":
            print("✅ Redis连接测试成功")
        else:
            print("❌ Redis连接测试失败")
            return False

        # 清理测试数据
        await redis_client.delete("test_key")

        # 测试缓存管理器
        cache_manager = get_cache_manager()
        await cache_manager.set("test_cache", {"test": "data"}, ttl=60)
        cached_data = await cache_manager.get("test_cache")

        if cached_data and cached_data.get("test") == "data":
            print("✅ 缓存管理器测试成功")
        else:
            print("❌ 缓存管理器测试失败")
            return False

        await cache_manager.delete("test_cache")

        # 测试分布式锁
        async with with_lock("test_lock", timeout=10):
            print("✅ 分布式锁测试成功")

        # 测试会话缓存
        session_cache = await get_session_cache()
        await session_cache.create_user_session(
            999, "test_session", {"ip": "127.0.0.1"}, ttl=60
        )
        session_data = await session_cache.get_user_session(999, "test_session")

        if session_data:
            print("✅ 会话缓存测试成功")
            await session_cache.terminate_user_session(999, "test_session")
        else:
            print("❌ 会话缓存测试失败")
            return False

        # 测试权限缓存
        permission_cache = await get_permission_cache()
        await permission_cache.set_user_permissions(999, ["test_permission"])
        permissions = await permission_cache.get_user_permissions(999)

        if permissions and "test_permission" in permissions:
            print("✅ 权限缓存测试成功")
            await permission_cache.invalidate_user_cache(999)
        else:
            print("❌ 权限缓存测试失败")
            return False

        # 关闭连接
        await close_redis()

        print("\n🎉 缓存系统配置验证完成，所有测试通过！")
        return True

    except Exception as e:
        print(f"❌ 缓存系统配置验证失败: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(validate_cache_setup())
    sys.exit(0 if success else 1)
