#!/usr/bin/env python3
"""
搜索系统监控脚本

监控 Elasticsearch 的健康状态、性能指标和搜索质量。
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class SearchSystemMonitor:
    """搜索系统监控器"""

    def __init__(self, check_interval: int = 60):
        """
        初始化监控器

        Args:
            check_interval: 检查间隔（秒）
        """
        self.check_interval = check_interval
        self.alert_thresholds = {
            "cluster_status": "yellow",  # 集群状态告警阈值
            "search_latency_ms": 1000,  # 搜索延迟告警阈值
            "index_rate_per_sec": 100,  # 索引速率告警阈值
            "search_rate_per_sec": 1000,  # 搜索速率告警阈值
            "disk_usage_percent": 85,  # 磁盘使用率告警阈值
            "memory_usage_percent": 90,  # 内存使用率告警阈值
        }
        self.metrics_history = []
        self.max_history = 1000

    async def init_monitor(self) -> bool:
        """初始化监控器"""
        try:
            from app.shared.search import (
                get_es_client,
                get_search_service,
                init_elasticsearch,
            )

            success = await init_elasticsearch()
            if not success:
                logger.error("Elasticsearch 初始化失败")
                return False

            self.es_client = get_es_client()
            self.search_service = get_search_service()

            logger.info("✅ 搜索系统监控器初始化成功")
            return True

        except Exception as e:
            logger.error(f"监控器初始化失败: {e}")
            return False

    async def start_monitoring(self):
        """开始监控"""
        logger.info("开始搜索系统监控...")

        if not await self.init_monitor():
            logger.error("监控器初始化失败，退出监控")
            return

        try:
            while True:
                try:
                    # 收集监控指标
                    metrics = await self.collect_metrics()

                    # 分析指标并生成告警
                    await self.analyze_metrics(metrics)

                    # 存储指标历史
                    await self.store_metrics(metrics)

                    logger.info(
                        f"监控指标收集完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    )

                    await asyncio.sleep(self.check_interval)

                except Exception as e:
                    logger.error(f"监控过程出错: {e}")
                    await asyncio.sleep(self.check_interval)

        except KeyboardInterrupt:
            logger.info("监控已停止")
        finally:
            await self.cleanup()

    async def collect_metrics(self) -> Dict[str, Any]:
        """收集监控指标"""
        try:
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "cluster": {},
                "indices": {},
                "nodes": {},
                "performance": {},
                "search_quality": {},
            }

            # 获取集群信息
            cluster_info = await self.es_client.get_cluster_info()
            metrics["cluster"] = cluster_info

            # 获取集群健康状态
            if self.es_client.client:
                cluster_health = await self.es_client.client.cluster.health()
                metrics["cluster"].update(cluster_health)

                # 获取集群统计信息
                cluster_stats = await self.es_client.client.cluster.stats()
                metrics["cluster"].update(cluster_stats)

                # 获取节点信息
                nodes_info = await self.es_client.client.nodes.info()
                metrics["nodes"] = nodes_info

                # 获取节点统计信息
                nodes_stats = await self.es_client.client.nodes.stats()
                metrics["nodes"].update(nodes_stats)

                # 获取索引统计信息
                indices_stats = await self.es_client.client.indices.stats()
                metrics["indices"] = indices_stats

            # 测试搜索性能
            performance_metrics = await self.test_search_performance()
            metrics["performance"] = performance_metrics

            # 评估搜索质量
            quality_metrics = await self.evaluate_search_quality()
            metrics["search_quality"] = quality_metrics

            return metrics

        except Exception as e:
            logger.error(f"收集监控指标失败: {e}")
            return {"timestamp": datetime.now().isoformat(), "error": str(e)}

    async def test_search_performance(self) -> Dict[str, Any]:
        """测试搜索性能"""
        try:
            performance = {
                "ping_time_ms": 0,
                "simple_search_time_ms": 0,
                "complex_search_time_ms": 0,
                "bulk_index_time_ms": 0,
                "availability": True,
            }

            # 测试基本连接
            start_time = time.time()
            ping_success = await self.es_client.ping()
            performance["ping_time_ms"] = (time.time() - start_time) * 1000
            performance["availability"] = ping_success

            if not ping_success:
                return performance

            # 创建临时测试索引
            test_index = "monitor_test_index"
            await self.es_client.delete_index(test_index)

            mapping = {
                "properties": {
                    "title": {"type": "text", "analyzer": "standard"},
                    "content": {"type": "text", "analyzer": "standard"},
                    "category": {"type": "keyword"},
                }
            }

            await self.es_client.create_index(test_index, mapping)

            # 测试索引性能
            test_docs = [
                {
                    "title": f"Test Document {i}",
                    "content": f"This is test content for document {i}",
                    "category": f"category_{i % 3}",
                }
                for i in range(10)
            ]

            start_time = time.time()
            await self.search_service.bulk_index_documents(
                test_index, test_docs, refresh=True
            )
            performance["bulk_index_time_ms"] = (time.time() - start_time) * 1000

            # 测试简单搜索性能
            from app.shared.search import SearchQuery

            start_time = time.time()
            simple_query = SearchQuery(query="test", size=5)
            await self.search_service.search(test_index, simple_query)
            performance["simple_search_time_ms"] = (time.time() - start_time) * 1000

            # 测试复杂搜索性能
            from app.shared.search import SearchFilter, SearchHighlight, SearchSort

            start_time = time.time()
            complex_query = SearchQuery(
                query="document content",
                fields=["title^2", "content"],
                filters=[SearchFilter(field="category", values=["category_1"])],
                sort=[SearchSort(field="title.keyword", order="asc")],
                highlight=SearchHighlight(fields=["title", "content"]),
                size=5,
            )
            await self.search_service.search(test_index, complex_query)
            performance["complex_search_time_ms"] = (time.time() - start_time) * 1000

            # 清理测试索引
            await self.es_client.delete_index(test_index)

            return performance

        except Exception as e:
            logger.error(f"搜索性能测试失败: {e}")
            return {
                "ping_time_ms": 0,
                "simple_search_time_ms": 0,
                "complex_search_time_ms": 0,
                "bulk_index_time_ms": 0,
                "availability": False,
                "error": str(e),
            }

    async def evaluate_search_quality(self) -> Dict[str, Any]:
        """评估搜索质量"""
        try:
            quality = {
                "relevance_score": 0.0,
                "response_completeness": 0.0,
                "chinese_tokenization_quality": 0.0,
                "overall_quality": 0.0,
            }

            # 测试中文分词质量
            test_text = "柴管家智能客服系统支持多平台聚合功能"
            tokens = await self.search_service.analyze_text(test_text, "ik_max_word")

            if tokens:
                expected_tokens = [
                    "柴管家",
                    "智能客服",
                    "系统",
                    "多平台",
                    "聚合",
                    "功能",
                ]
                found_count = sum(1 for token in expected_tokens if token in tokens)
                quality["chinese_tokenization_quality"] = found_count / len(
                    expected_tokens
                )

            # 测试搜索相关性（如果有测试数据）
            try:
                from app.shared.search import SearchQuery

                test_query = SearchQuery(query="测试", size=5)
                result = await self.search_service.search("messages", test_query)

                if result.documents:
                    # 计算相关性得分
                    scores = [doc.score or 0 for doc in result.documents if doc.score]
                    if scores:
                        quality["relevance_score"] = sum(scores) / len(scores)

                    # 计算响应完整性
                    complete_docs = sum(
                        1
                        for doc in result.documents
                        if doc.source and len(doc.source) > 0
                    )
                    quality["response_completeness"] = complete_docs / len(
                        result.documents
                    )

            except Exception:
                # 如果没有测试数据，跳过这些测试
                pass

            # 计算整体质量评分
            scores = [
                quality["chinese_tokenization_quality"],
                quality["relevance_score"],
                quality["response_completeness"],
            ]
            valid_scores = [s for s in scores if s > 0]
            quality["overall_quality"] = (
                sum(valid_scores) / len(valid_scores) if valid_scores else 0.0
            )

            return quality

        except Exception as e:
            logger.error(f"搜索质量评估失败: {e}")
            return {
                "relevance_score": 0.0,
                "response_completeness": 0.0,
                "chinese_tokenization_quality": 0.0,
                "overall_quality": 0.0,
                "error": str(e),
            }

    async def analyze_metrics(self, metrics: Dict[str, Any]):
        """分析指标并生成告警"""
        try:
            alerts = []

            # 检查集群状态
            cluster_status = metrics.get("cluster", {}).get("status", "red")
            if cluster_status == "red":
                alerts.append("🔴 集群状态为红色，存在严重问题")
            elif cluster_status == "yellow":
                alerts.append("🟡 集群状态为黄色，存在潜在问题")

            # 检查搜索性能
            performance = metrics.get("performance", {})

            search_latency = performance.get("simple_search_time_ms", 0)
            if search_latency > self.alert_thresholds["search_latency_ms"]:
                alerts.append(f"⚠️ 搜索延迟过高: {search_latency:.1f}ms")

            # 检查可用性
            if not performance.get("availability", True):
                alerts.append("❌ Elasticsearch 服务不可用")

            # 检查搜索质量
            quality = metrics.get("search_quality", {})
            overall_quality = quality.get("overall_quality", 0)
            if overall_quality < 0.7:
                alerts.append(f"📉 搜索质量较低: {overall_quality:.2f}")

            # 检查节点资源使用情况
            nodes = metrics.get("nodes", {}).get("nodes", {})
            for node_id, node_info in nodes.items():
                if "jvm" in node_info:
                    heap_used_percent = (
                        node_info["jvm"].get("mem", {}).get("heap_used_percent", 0)
                    )
                    if (
                        heap_used_percent
                        > self.alert_thresholds["memory_usage_percent"]
                    ):
                        alerts.append(f"🧠 节点内存使用率过高: {heap_used_percent}%")

            # 输出告警
            if alerts:
                logger.warning("搜索系统告警:")
                for alert in alerts:
                    logger.warning(f"  {alert}")
            else:
                logger.info(
                    f"搜索系统状态正常 - "
                    f"集群状态: {cluster_status}, "
                    f"搜索延迟: {search_latency:.1f}ms, "
                    f"搜索质量: {overall_quality:.2f}"
                )

        except Exception as e:
            logger.error(f"指标分析失败: {e}")

    async def store_metrics(self, metrics: Dict[str, Any]):
        """存储指标历史"""
        try:
            # 添加到历史记录
            self.metrics_history.append(metrics)

            # 保持历史记录数量限制
            if len(self.metrics_history) > self.max_history:
                self.metrics_history = self.metrics_history[-self.max_history :]

            # 可以在这里添加持久化存储逻辑
            # 例如存储到数据库或文件系统

        except Exception as e:
            logger.error(f"存储指标失败: {e}")

    async def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """获取指标摘要"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            recent_metrics = [
                m
                for m in self.metrics_history
                if datetime.fromisoformat(m["timestamp"]) > cutoff_time
            ]

            if not recent_metrics:
                return {"message": "没有最近的指标数据"}

            # 计算摘要统计
            search_latencies = [
                m.get("performance", {}).get("simple_search_time_ms", 0)
                for m in recent_metrics
            ]

            quality_scores = [
                m.get("search_quality", {}).get("overall_quality", 0)
                for m in recent_metrics
            ]

            summary = {
                "time_range_hours": hours,
                "metrics_count": len(recent_metrics),
                "search_performance": {
                    "avg_latency_ms": (
                        sum(search_latencies) / len(search_latencies)
                        if search_latencies
                        else 0
                    ),
                    "max_latency_ms": max(search_latencies) if search_latencies else 0,
                    "min_latency_ms": min(search_latencies) if search_latencies else 0,
                },
                "search_quality": {
                    "avg_quality": (
                        sum(quality_scores) / len(quality_scores)
                        if quality_scores
                        else 0
                    ),
                    "max_quality": max(quality_scores) if quality_scores else 0,
                    "min_quality": min(quality_scores) if quality_scores else 0,
                },
                "latest_cluster_status": recent_metrics[-1]
                .get("cluster", {})
                .get("status", "unknown"),
                "availability_rate": sum(
                    1
                    for m in recent_metrics
                    if m.get("performance", {}).get("availability", False)
                )
                / len(recent_metrics),
            }

            return summary

        except Exception as e:
            logger.error(f"获取指标摘要失败: {e}")
            return {"error": str(e)}

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not await self.init_monitor():
                return {"status": "unhealthy", "message": "监控器初始化失败"}

            # 测试基本连接
            ping_result = await self.es_client.ping()

            if not ping_result:
                return {"status": "unhealthy", "message": "Elasticsearch 连接失败"}

            # 获取集群信息
            cluster_info = await self.es_client.get_cluster_info()

            # 测试搜索性能
            performance = await self.test_search_performance()

            # 评估整体健康状态
            status = "healthy"
            issues = []

            if cluster_info.get("status") == "red":
                status = "unhealthy"
                issues.append("集群状态为红色")
            elif cluster_info.get("status") == "yellow":
                status = "warning"
                issues.append("集群状态为黄色")

            if not performance.get("availability"):
                status = "unhealthy"
                issues.append("搜索服务不可用")

            search_latency = performance.get("simple_search_time_ms", 0)
            if search_latency > 1000:
                if status == "healthy":
                    status = "warning"
                issues.append(f"搜索延迟较高: {search_latency:.1f}ms")

            return {
                "status": status,
                "cluster": cluster_info,
                "performance": performance,
                "issues": issues,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat(),
            }

    def print_current_status(self):
        """打印当前状态"""
        if not self.metrics_history:
            print("❌ 没有监控数据")
            return

        latest = self.metrics_history[-1]

        print("\n" + "=" * 50)
        print("搜索系统当前状态")
        print("=" * 50)

        # 集群信息
        cluster = latest.get("cluster", {})
        print(f"时间: {latest.get('timestamp', 'unknown')}")
        print(f"集群状态: {cluster.get('status', 'unknown')}")
        print(f"节点数量: {cluster.get('number_of_nodes', 0)}")
        print(f"索引数量: {cluster.get('indices_count', 0)}")
        print(f"文档数量: {cluster.get('docs_count', 0)}")

        # 性能信息
        performance = latest.get("performance", {})
        print(f"\n性能指标:")
        print(f"  连接延迟: {performance.get('ping_time_ms', 0):.1f}ms")
        print(f"  搜索延迟: {performance.get('simple_search_time_ms', 0):.1f}ms")
        print(f"  复杂搜索延迟: {performance.get('complex_search_time_ms', 0):.1f}ms")
        print(f"  批量索引时间: {performance.get('bulk_index_time_ms', 0):.1f}ms")
        print(f"  服务可用性: {'✅' if performance.get('availability') else '❌'}")

        # 搜索质量
        quality = latest.get("search_quality", {})
        print(f"\n搜索质量:")
        print(f"  整体质量: {quality.get('overall_quality', 0):.2f}")
        print(f"  中文分词质量: {quality.get('chinese_tokenization_quality', 0):.2f}")
        print(f"  相关性得分: {quality.get('relevance_score', 0):.2f}")
        print(f"  响应完整性: {quality.get('response_completeness', 0):.2f}")

        print("=" * 50)

    async def cleanup(self):
        """清理资源"""
        try:
            from app.shared.search import close_elasticsearch

            await close_elasticsearch()
            logger.info("监控器清理完成")
        except Exception as e:
            logger.warning(f"清理资源失败: {e}")


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="搜索系统监控工具")
    parser.add_argument(
        "--action",
        choices=["monitor", "health", "summary", "status"],
        default="monitor",
        help="执行的操作",
    )
    parser.add_argument("--interval", type=int, default=60, help="监控检查间隔（秒）")
    parser.add_argument("--hours", type=int, default=1, help="摘要统计时间范围（小时）")

    args = parser.parse_args()

    monitor = SearchSystemMonitor(check_interval=args.interval)

    try:
        if args.action == "monitor":
            # 持续监控模式
            await monitor.start_monitoring()

        elif args.action == "health":
            # 健康检查模式
            health_status = await monitor.health_check()
            print(json.dumps(health_status, indent=2, ensure_ascii=False))

        elif args.action == "summary":
            # 摘要统计模式
            if not await monitor.init_monitor():
                print("❌ 监控器初始化失败")
                return 1

            summary = await monitor.get_metrics_summary(hours=args.hours)
            print(json.dumps(summary, indent=2, ensure_ascii=False))

        elif args.action == "status":
            # 当前状态模式
            if not await monitor.init_monitor():
                print("❌ 监控器初始化失败")
                return 1

            metrics = await monitor.collect_metrics()
            await monitor.store_metrics(metrics)
            monitor.print_current_status()

    except KeyboardInterrupt:
        logger.info("监控已停止")
        return 0
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1
    finally:
        await monitor.cleanup()

    return 0


if __name__ == "__main__":
    import os
    import sys

    # 添加项目根目录到 Python 路径
    sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

    exit_code = asyncio.run(main())
    sys.exit(exit_code)
