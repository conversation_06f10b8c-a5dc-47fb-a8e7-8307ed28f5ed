#!/usr/bin/env python3
"""
Celery Worker启动脚本

用于启动Celery Worker进程，支持不同的配置和队列。
"""

import argparse
import logging
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.shared.tasks.app import create_celery_app
from app.shared.tasks.config import get_celery_config


def setup_logging(log_level: str = "INFO"):
    """配置日志"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


def start_worker(
    queues: list = None,
    concurrency: int = None,
    log_level: str = "INFO",
    max_tasks_per_child: int = None,
    prefetch_multiplier: int = None,
):
    """
    启动Celery Worker

    Args:
        queues: 监听的队列列表
        concurrency: 并发进程数
        log_level: 日志级别
        max_tasks_per_child: 每个子进程最大任务数
        prefetch_multiplier: 预取倍数
    """
    # 创建Celery应用
    app = create_celery_app()

    # 构建启动参数
    worker_args = []

    # 队列设置
    if queues:
        worker_args.extend(["-Q", ",".join(queues)])

    # 并发设置
    if concurrency:
        worker_args.extend(["-c", str(concurrency)])

    # 日志级别
    worker_args.extend(["-l", log_level.lower()])

    # 其他参数
    if max_tasks_per_child:
        worker_args.extend(["--max-tasks-per-child", str(max_tasks_per_child)])

    if prefetch_multiplier:
        worker_args.extend(["--prefetch-multiplier", str(prefetch_multiplier)])

    # 启动Worker
    print(f"启动Celery Worker...")
    print(f"队列: {queues or '默认队列'}")
    print(f"并发数: {concurrency or '自动'}")
    print(f"日志级别: {log_level}")
    print("-" * 50)

    app.worker_main(worker_args)


def start_beat():
    """启动Celery Beat调度器"""
    app = create_celery_app()

    print("启动Celery Beat调度器...")
    print("-" * 50)

    app.start(["celery", "beat", "-l", "info"])


def main():
    parser = argparse.ArgumentParser(description="Celery Worker启动脚本")

    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # Worker命令
    worker_parser = subparsers.add_parser("worker", help="启动Worker")
    worker_parser.add_argument(
        "-Q", "--queues", nargs="+", help="监听的队列列表", default=None
    )
    worker_parser.add_argument(
        "-c", "--concurrency", type=int, help="并发进程数", default=None
    )
    worker_parser.add_argument(
        "-l",
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别",
    )
    worker_parser.add_argument(
        "--max-tasks-per-child", type=int, help="每个子进程最大任务数", default=None
    )
    worker_parser.add_argument(
        "--prefetch-multiplier", type=int, help="预取倍数", default=None
    )

    # Beat命令
    beat_parser = subparsers.add_parser("beat", help="启动Beat调度器")

    # 预设配置
    preset_parser = subparsers.add_parser("preset", help="使用预设配置启动")
    preset_parser.add_argument(
        "preset_name",
        choices=["default", "ai", "notification", "data", "maintenance"],
        help="预设配置名称",
    )

    args = parser.parse_args()

    # 设置日志
    log_level = getattr(args, "log_level", "INFO")
    setup_logging(log_level)

    if args.command == "worker":
        start_worker(
            queues=args.queues,
            concurrency=args.concurrency,
            log_level=args.log_level,
            max_tasks_per_child=args.max_tasks_per_child,
            prefetch_multiplier=args.prefetch_multiplier,
        )

    elif args.command == "beat":
        start_beat()

    elif args.command == "preset":
        # 预设配置
        presets = {
            "default": {"queues": ["default"], "concurrency": 4, "log_level": "INFO"},
            "ai": {
                "queues": ["ai_queue"],
                "concurrency": 2,
                "log_level": "INFO",
                "max_tasks_per_child": 10,  # AI任务内存消耗大，限制子进程任务数
            },
            "notification": {
                "queues": ["notification_queue"],
                "concurrency": 8,
                "log_level": "INFO",
            },
            "data": {
                "queues": ["data_queue"],
                "concurrency": 2,
                "log_level": "INFO",
                "max_tasks_per_child": 50,
            },
            "maintenance": {
                "queues": ["maintenance_queue", "scheduled_queue"],
                "concurrency": 1,
                "log_level": "INFO",
            },
        }

        preset_config = presets.get(args.preset_name)
        if preset_config:
            print(f"使用预设配置: {args.preset_name}")
            start_worker(**preset_config)
        else:
            print(f"未知的预设配置: {args.preset_name}")
            sys.exit(1)

    else:
        parser.print_help()


if __name__ == "__main__":
    main()
