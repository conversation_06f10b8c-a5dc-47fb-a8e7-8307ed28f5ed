#!/usr/bin/env python3
"""
消息队列启动脚本

用于启动和管理消息队列服务
"""

import argparse
import logging
import os
import signal
import sys
import time
from typing import Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.shared.messaging import (
    MessageQueueManager,
    get_message_queue_manager,
    init_message_queue_manager,
)
from app.shared.messaging.config import QueueType
from app.shared.messaging.handlers import (
    AIProcessingHandler,
    MessageProcessHandler,
    NotificationHandler,
    TaskHandler,
    WebhookHandler,
)

logger = logging.getLogger(__name__)


class MessageQueueService:
    """消息队列服务"""

    def __init__(self, rabbitmq_url: Optional[str] = None):
        self.manager: Optional[MessageQueueManager] = None
        self.rabbitmq_url = rabbitmq_url
        self.is_running = False

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)

    def setup_logging(self, log_level: str = "INFO", log_file: Optional[str] = None):
        """设置日志"""
        level = getattr(logging, log_level.upper())

        handlers = [logging.StreamHandler()]
        if log_file:
            handlers.append(logging.FileHandler(log_file))

        logging.basicConfig(
            level=level,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=handlers,
        )

        logger.info(f"Logging configured at {log_level} level")

    def initialize_manager(self) -> None:
        """初始化管理器"""
        logger.info("Initializing message queue manager...")

        if self.rabbitmq_url:
            self.manager = init_message_queue_manager(self.rabbitmq_url)
        else:
            self.manager = get_message_queue_manager()

        logger.info("Message queue manager initialized")

    def register_handlers(self) -> None:
        """注册消息处理器"""
        logger.info("Registering message handlers...")

        if not self.manager:
            raise RuntimeError("Manager not initialized")

        # 注册Webhook处理器
        webhook_handler = WebhookHandler()
        self.manager.register_handler(QueueType.WEBHOOK, webhook_handler)
        logger.info("Webhook handler registered")

        # 注册AI处理器
        ai_handler = AIProcessingHandler()
        self.manager.register_handler(QueueType.AI_PROCESSING, ai_handler)
        logger.info("AI processing handler registered")

        # 注册通知处理器
        notification_handler = NotificationHandler()
        self.manager.register_handler(QueueType.NOTIFICATION, notification_handler)
        logger.info("Notification handler registered")

        # 注册消息处理器
        message_handler = MessageProcessHandler()
        self.manager.register_handler(QueueType.MESSAGE, message_handler)
        logger.info("Message process handler registered")

        # 注册任务处理器
        task_handler = TaskHandler()
        self.manager.register_handler(QueueType.TASK, task_handler)
        logger.info("Task handler registered")

        logger.info("All message handlers registered successfully")

    def start(self, enable_monitoring: bool = True) -> None:
        """启动服务"""
        logger.info("Starting message queue service...")

        try:
            # 初始化管理器
            self.initialize_manager()

            # 注册处理器
            self.register_handlers()

            # 启动管理器
            self.manager.start(start_monitoring=enable_monitoring)

            self.is_running = True
            logger.info("Message queue service started successfully")

        except Exception as e:
            logger.error(f"Failed to start message queue service: {e}")
            raise

    def stop(self) -> None:
        """停止服务"""
        if not self.is_running:
            return

        logger.info("Stopping message queue service...")

        try:
            if self.manager:
                self.manager.stop()

            self.is_running = False
            logger.info("Message queue service stopped successfully")

        except Exception as e:
            logger.error(f"Error stopping message queue service: {e}")

    def status(self) -> dict:
        """获取服务状态"""
        if not self.manager:
            return {"status": "not_initialized"}

        return self.manager.get_health_status()

    def statistics(self) -> dict:
        """获取服务统计"""
        if not self.manager:
            return {}

        return self.manager.get_statistics()

    def run_forever(self, enable_monitoring: bool = True) -> None:
        """持续运行服务"""
        self.start(enable_monitoring)

        try:
            logger.info("Message queue service is running. Press Ctrl+C to stop.")

            while self.is_running:
                time.sleep(1)

                # 定期检查健康状态
                if hasattr(self, "_last_health_check"):
                    if time.time() - self._last_health_check > 60:  # 每分钟检查一次
                        self._check_health()
                else:
                    self._last_health_check = time.time()

        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Unexpected error in main loop: {e}")
        finally:
            self.stop()

    def _check_health(self) -> None:
        """检查健康状态"""
        try:
            health = self.status()
            if health["status"] != "healthy":
                logger.warning(f"Service health degraded: {health}")

            self._last_health_check = time.time()

        except Exception as e:
            logger.error(f"Error checking health: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Message Queue Service")

    parser.add_argument(
        "--rabbitmq-url",
        type=str,
        help="RabbitMQ connection URL",
        default=os.getenv("RABBITMQ_URL"),
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Log level",
    )

    parser.add_argument("--log-file", type=str, help="Log file path", default=None)

    parser.add_argument(
        "--no-monitoring", action="store_true", help="Disable monitoring"
    )

    parser.add_argument(
        "--command",
        choices=["start", "status", "stats", "test"],
        default="start",
        help="Command to execute",
    )

    args = parser.parse_args()

    # 创建服务实例
    service = MessageQueueService(args.rabbitmq_url)

    # 设置日志
    service.setup_logging(args.log_level, args.log_file)

    try:
        if args.command == "start":
            # 启动服务
            enable_monitoring = not args.no_monitoring
            service.run_forever(enable_monitoring)

        elif args.command == "status":
            # 显示状态
            service.initialize_manager()
            status = service.status()
            print(f"Service Status: {status}")

        elif args.command == "stats":
            # 显示统计
            service.initialize_manager()
            stats = service.statistics()
            print(f"Service Statistics: {stats}")

        elif args.command == "test":
            # 运行测试
            logger.info("Running message queue test...")
            service.start(enable_monitoring=False)

            # 发送测试消息
            from app.shared.messaging.models import MessagePriority, MessageType

            success = service.manager.publish_message(
                message_type=MessageType.SYSTEM,
                payload={"test": True, "message": "Test message"},
                priority=MessagePriority.NORMAL,
                source="test_script",
            )

            if success:
                logger.info("Test message sent successfully")
            else:
                logger.error("Failed to send test message")

            # 等待处理
            time.sleep(3)

            # 显示统计
            stats = service.statistics()
            logger.info(f"Test results: {stats}")

            service.stop()

    except Exception as e:
        logger.error(f"Error in main: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
