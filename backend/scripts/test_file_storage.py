#!/usr/bin/env python3
"""
文件存储系统测试脚本

全面测试文件存储系统的各项功能
"""

import asyncio
import io
import logging
import os
import sys
import tempfile
import time
from pathlib import Path
from typing import Dict, List

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from app.core.config import get_settings
from app.shared.storage import (
    FileAccessType,
    FileSearchRequest,
    FileThumbnailRequest,
    FileUploadRequest,
    get_file_manager,
    get_file_processor,
    get_minio_client,
    init_minio,
)
from PIL import Image

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class FileStorageSystemTester:
    """文件存储系统测试器"""

    def __init__(self):
        self.minio_client = None
        self.file_manager = None
        self.file_processor = None
        self.test_results: Dict[str, bool] = {}
        self.test_files: List[str] = []

    async def init_tester(self):
        """初始化测试器"""
        logger.info("初始化测试器...")

        try:
            # 初始化客户端
            self.minio_client = await init_minio()
            self.file_manager = get_file_manager()
            self.file_processor = get_file_processor()

            logger.info("✅ 测试器初始化完成")
            return True

        except Exception as e:
            logger.error(f"测试器初始化失败: {e}")
            return False

    def create_test_image(self, width: int = 800, height: int = 600) -> bytes:
        """创建测试图片"""
        # 创建一个简单的测试图片
        image = Image.new("RGB", (width, height), color="red")

        # 添加一些内容
        from PIL import ImageDraw

        draw = ImageDraw.Draw(image)
        draw.rectangle([50, 50, width - 50, height - 50], fill="blue")
        draw.rectangle([100, 100, width - 100, height - 100], fill="green")

        # 转换为字节
        buffer = io.BytesIO()
        image.save(buffer, format="JPEG", quality=90)
        return buffer.getvalue()

    def create_test_document(self, content: str = None) -> bytes:
        """创建测试文档"""
        if content is None:
            content = """
# 测试文档

这是一个用于测试文件存储系统的文档。

## 功能测试

- 文件上传
- 文件下载
- 文件删除
- 权限控制
- 缩略图生成

## 测试时间

测试时间: {time}

## 结论

如果您能看到这个文档，说明文件存储系统工作正常！
""".format(
                time=time.strftime("%Y-%m-%d %H:%M:%S")
            )

        return content.encode("utf-8")

    async def test_minio_connection(self) -> bool:
        """测试MinIO连接"""
        logger.info("测试MinIO连接...")

        try:
            if not self.minio_client.is_connected:
                logger.error("MinIO未连接")
                return False

            # 测试bucket访问
            stats = await self.minio_client.get_bucket_stats()
            logger.info(f"Bucket统计: {stats}")

            logger.info("✅ MinIO连接测试通过")
            return True

        except Exception as e:
            logger.error(f"MinIO连接测试失败: {e}")
            return False

    async def test_file_upload_download(self) -> bool:
        """测试文件上传下载"""
        logger.info("测试文件上传下载...")

        try:
            # 测试文本文件上传
            test_content = self.create_test_document()

            file_path = "test/upload_test.txt"
            etag = await self.minio_client.upload_file(
                file_path=file_path,
                file_data=io.BytesIO(test_content),
                content_type="text/plain",
            )

            self.test_files.append(file_path)
            logger.info(f"文件上传成功，ETag: {etag}")

            # 测试文件下载
            downloaded_data = await self.minio_client.download_file(file_path)

            if downloaded_data == test_content:
                logger.info("✅ 文件上传下载测试通过")
                return True
            else:
                logger.error("下载的文件内容不匹配")
                return False

        except Exception as e:
            logger.error(f"文件上传下载测试失败: {e}")
            return False

    async def test_image_processing(self) -> bool:
        """测试图片处理"""
        logger.info("测试图片处理...")

        try:
            # 创建测试图片
            image_data = self.create_test_image(1920, 1080)

            # 测试图片处理
            processed_data, metadata = self.file_processor.process_image(
                image_data, max_dimension=1024
            )

            logger.info(f"图片处理元数据: {metadata}")

            # 验证图片被压缩
            if len(processed_data) < len(image_data):
                logger.info("✅ 图片压缩成功")

            # 测试缩略图生成
            thumbnail_data = self.file_processor.generate_thumbnail(
                image_data, size=(200, 200)
            )

            if len(thumbnail_data) > 0:
                logger.info("✅ 缩略图生成成功")

            logger.info("✅ 图片处理测试通过")
            return True

        except Exception as e:
            logger.error(f"图片处理测试失败: {e}")
            return False

    async def test_file_metadata(self) -> bool:
        """测试文件元数据"""
        logger.info("测试文件元数据...")

        try:
            # 创建测试文件
            test_content = self.create_test_document()

            # 创建文件元数据
            metadata = self.file_processor.create_file_metadata(
                file_id="test_metadata_001",
                filename="test_metadata.txt",
                file_data=test_content,
                file_path="test/metadata_test.txt",
                owner_id="test_user",
                access_type=FileAccessType.PRIVATE,
            )

            # 验证元数据
            assert metadata.filename == "test_metadata.txt"
            assert metadata.file_size == len(test_content)
            assert metadata.file_extension == "txt"
            assert metadata.file_type == "text/plain"
            assert metadata.owner_id == "test_user"

            logger.info(f"文件元数据: {metadata}")
            logger.info("✅ 文件元数据测试通过")
            return True

        except Exception as e:
            logger.error(f"文件元数据测试失败: {e}")
            return False

    async def test_file_validation(self) -> bool:
        """测试文件验证"""
        logger.info("测试文件验证...")

        try:
            # 测试文件扩展名验证
            valid_files = ["test.jpg", "test.png", "test.pdf", "test.docx"]
            invalid_files = ["test.exe", "test.bat", "test.php"]

            for filename in valid_files:
                is_valid = self.file_processor.validate_file_extension(filename)
                if not is_valid:
                    logger.error(f"有效文件被拒绝: {filename}")
                    return False

            for filename in invalid_files:
                is_valid = self.file_processor.validate_file_extension(filename)
                if is_valid:
                    logger.error(f"无效文件被接受: {filename}")
                    return False

            # 测试文件大小验证
            settings = get_settings()
            valid_size = settings.max_file_size - 1000
            invalid_size = settings.max_file_size + 1000

            if not self.file_processor.validate_file_size(valid_size):
                logger.error("有效文件大小被拒绝")
                return False

            if self.file_processor.validate_file_size(invalid_size):
                logger.error("无效文件大小被接受")
                return False

            logger.info("✅ 文件验证测试通过")
            return True

        except Exception as e:
            logger.error(f"文件验证测试失败: {e}")
            return False

    async def test_presigned_urls(self) -> bool:
        """测试预签名URL"""
        logger.info("测试预签名URL...")

        try:
            # 上传测试文件
            test_content = b"Test content for presigned URL"
            file_path = "test/presigned_test.txt"

            await self.minio_client.upload_file(
                file_path=file_path,
                file_data=io.BytesIO(test_content),
                content_type="text/plain",
            )

            self.test_files.append(file_path)

            # 生成下载URL
            download_url = await self.minio_client.generate_presigned_url(
                file_path, method="GET"
            )

            if download_url and download_url.startswith("http"):
                logger.info(f"预签名URL生成成功: {download_url[:50]}...")
                logger.info("✅ 预签名URL测试通过")
                return True
            else:
                logger.error("预签名URL格式无效")
                return False

        except Exception as e:
            logger.error(f"预签名URL测试失败: {e}")
            return False

    async def test_file_operations(self) -> bool:
        """测试文件操作"""
        logger.info("测试文件操作...")

        try:
            # 测试文件复制
            source_path = "test/source_file.txt"
            dest_path = "test/dest_file.txt"

            # 创建源文件
            test_content = b"Test content for file operations"
            await self.minio_client.upload_file(
                file_path=source_path,
                file_data=io.BytesIO(test_content),
                content_type="text/plain",
            )

            self.test_files.extend([source_path, dest_path])

            # 复制文件
            copy_success = await self.minio_client.copy_file(source_path, dest_path)
            if not copy_success:
                logger.error("文件复制失败")
                return False

            # 验证复制结果
            copied_data = await self.minio_client.download_file(dest_path)
            if copied_data != test_content:
                logger.error("复制的文件内容不匹配")
                return False

            # 测试文件存在检查
            if not await self.minio_client.file_exists(source_path):
                logger.error("文件存在检查失败")
                return False

            # 测试文件信息获取
            file_info = await self.minio_client.get_file_info(source_path)
            if not file_info or file_info["size"] != len(test_content):
                logger.error("文件信息获取失败")
                return False

            logger.info("✅ 文件操作测试通过")
            return True

        except Exception as e:
            logger.error(f"文件操作测试失败: {e}")
            return False

    async def test_performance(self) -> bool:
        """测试性能"""
        logger.info("测试文件存储性能...")

        try:
            # 测试小文件上传性能
            small_file_size = 1024  # 1KB
            small_content = b"x" * small_file_size

            start_time = time.time()

            # 上传10个小文件
            for i in range(10):
                file_path = f"test/perf_small_{i}.txt"
                await self.minio_client.upload_file(
                    file_path=file_path,
                    file_data=io.BytesIO(small_content),
                    content_type="text/plain",
                )
                self.test_files.append(file_path)

            small_files_time = time.time() - start_time
            logger.info(f"10个小文件(1KB)上传时间: {small_files_time:.2f}秒")

            # 测试大文件上传性能
            large_file_size = 1024 * 1024  # 1MB
            large_content = b"x" * large_file_size

            start_time = time.time()

            file_path = "test/perf_large.txt"
            await self.minio_client.upload_file(
                file_path=file_path,
                file_data=io.BytesIO(large_content),
                content_type="text/plain",
            )
            self.test_files.append(file_path)

            large_file_time = time.time() - start_time
            logger.info(f"1个大文件(1MB)上传时间: {large_file_time:.2f}秒")

            # 测试下载性能
            start_time = time.time()
            downloaded_data = await self.minio_client.download_file(file_path)
            download_time = time.time() - start_time

            logger.info(f"大文件下载时间: {download_time:.2f}秒")

            # 验证性能要求
            if small_files_time > 10:  # 小文件上传不应超过10秒
                logger.warning(f"小文件上传性能较慢: {small_files_time:.2f}秒")

            if large_file_time > 30:  # 大文件上传不应超过30秒
                logger.warning(f"大文件上传性能较慢: {large_file_time:.2f}秒")

            logger.info("✅ 性能测试完成")
            return True

        except Exception as e:
            logger.error(f"性能测试失败: {e}")
            return False

    async def test_error_handling(self) -> bool:
        """测试错误处理"""
        logger.info("测试错误处理...")

        try:
            # 测试不存在的文件下载
            try:
                await self.minio_client.download_file("non_existent_file.txt")
                logger.error("下载不存在文件应该失败")
                return False
            except Exception:
                logger.info("✅ 不存在文件下载正确抛出异常")

            # 测试删除不存在的文件
            delete_result = await self.minio_client.delete_file("non_existent_file.txt")
            if delete_result:
                logger.warning("删除不存在文件返回成功（这可能是正常的）")

            # 测试文件存在检查（不存在的文件）
            exists = await self.minio_client.file_exists("non_existent_file.txt")
            if exists:
                logger.error("不存在文件的存在检查返回True")
                return False

            logger.info("✅ 错误处理测试通过")
            return True

        except Exception as e:
            logger.error(f"错误处理测试失败: {e}")
            return False

    async def cleanup_test_files(self):
        """清理测试文件"""
        logger.info("清理测试文件...")

        cleanup_count = 0
        for file_path in self.test_files:
            try:
                success = await self.minio_client.delete_file(file_path)
                if success:
                    cleanup_count += 1
            except Exception as e:
                logger.warning(f"清理文件失败 {file_path}: {e}")

        # 清理测试文件夹的占位符
        try:
            await self.minio_client.delete_file("test/.placeholder")
        except Exception:
            pass

        logger.info(f"清理了 {cleanup_count}/{len(self.test_files)} 个测试文件")

    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        logger.info("开始运行文件存储系统测试套件...")

        tests = [
            ("MinIO连接", self.test_minio_connection),
            ("文件上传下载", self.test_file_upload_download),
            ("图片处理", self.test_image_processing),
            ("文件元数据", self.test_file_metadata),
            ("文件验证", self.test_file_validation),
            ("预签名URL", self.test_presigned_urls),
            ("文件操作", self.test_file_operations),
            ("性能测试", self.test_performance),
            ("错误处理", self.test_error_handling),
        ]

        for test_name, test_func in tests:
            try:
                logger.info(f"\n{'='*50}")
                logger.info(f"运行测试: {test_name}")
                logger.info("=" * 50)

                success = await test_func()
                self.test_results[test_name] = success

                if success:
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    logger.error(f"❌ {test_name} - 失败")

            except Exception as e:
                logger.error(f"❌ {test_name} - 异常: {e}")
                self.test_results[test_name] = False

        return self.test_results


async def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("文件存储系统测试脚本")
    logger.info("=" * 60)

    tester = FileStorageSystemTester()

    try:
        # 初始化测试器
        if not await tester.init_tester():
            logger.error("测试器初始化失败")
            return 1

        # 运行所有测试
        results = await tester.run_all_tests()

        # 清理测试文件
        await tester.cleanup_test_files()

        # 生成测试报告
        logger.info("\n" + "=" * 60)
        logger.info("测试结果汇总")
        logger.info("=" * 60)

        passed = 0
        failed = 0

        for test_name, success in results.items():
            status = "✅ 通过" if success else "❌ 失败"
            logger.info(f"{test_name:<20} {status}")

            if success:
                passed += 1
            else:
                failed += 1

        logger.info("-" * 60)
        logger.info(f"总计: {len(results)} 个测试")
        logger.info(f"通过: {passed} 个")
        logger.info(f"失败: {failed} 个")
        logger.info(f"成功率: {passed/len(results)*100:.1f}%")

        if failed == 0:
            logger.info("\n🎉 所有测试通过! 文件存储系统运行正常!")
            return 0
        else:
            logger.error(f"\n❌ {failed} 个测试失败，请检查问题")
            return 1

    except KeyboardInterrupt:
        logger.info("\n用户中断测试")
        await tester.cleanup_test_files()
        return 130
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        await tester.cleanup_test_files()
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
