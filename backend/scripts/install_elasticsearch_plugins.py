#!/usr/bin/env python3
"""
Elasticsearch 插件安装脚本

安装必要的插件，特别是中文分词器 IK Analysis。
"""

import asyncio
import logging
import subprocess
import sys
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ElasticsearchPluginInstaller:
    """Elasticsearch 插件安装器"""

    def __init__(self):
        self.plugins = {
            "analysis-ik": {
                "name": "IK 中文分词器",
                "version": "8.10.4",
                "url": "https://github.com/medcl/elasticsearch-analysis-ik/releases/download/v8.10.4/elasticsearch-analysis-ik-8.10.4.zip",
                "description": "支持中文分词和搜索",
            },
            "analysis-pinyin": {
                "name": "拼音分析器",
                "version": "8.10.4",
                "url": "https://github.com/medcl/elasticsearch-analysis-pinyin/releases/download/v8.10.4/elasticsearch-analysis-pinyin-8.10.4.zip",
                "description": "支持拼音搜索",
            },
        }

    async def check_elasticsearch_running(self) -> bool:
        """检查 Elasticsearch 是否运行"""
        try:
            from app.shared.search import (
                close_elasticsearch,
                get_es_client,
                init_elasticsearch,
            )

            success = await init_elasticsearch()
            if success:
                es_client = get_es_client()
                if await es_client.ping():
                    logger.info("✅ Elasticsearch 服务正在运行")
                    await close_elasticsearch()
                    return True

            logger.error("❌ Elasticsearch 服务未运行")
            return False

        except Exception as e:
            logger.error(f"❌ 检查 Elasticsearch 状态失败: {e}")
            return False

    def run_docker_command(self, command: str) -> bool:
        """
        执行 Docker 命令

        Args:
            command: Docker 命令

        Returns:
            bool: 执行是否成功
        """
        try:
            result = subprocess.run(
                command.split(), capture_output=True, text=True, timeout=300
            )

            if result.returncode == 0:
                logger.info(f"✅ 命令执行成功: {command}")
                if result.stdout:
                    logger.info(f"输出: {result.stdout}")
                return True
            else:
                logger.error(f"❌ 命令执行失败: {command}")
                logger.error(f"错误: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error(f"❌ 命令执行超时: {command}")
            return False
        except Exception as e:
            logger.error(f"❌ 命令执行异常: {e}")
            return False

    async def install_plugin_in_container(self, plugin_name: str) -> bool:
        """
        在容器中安装插件

        Args:
            plugin_name: 插件名称

        Returns:
            bool: 安装是否成功
        """
        plugin_info = self.plugins.get(plugin_name)
        if not plugin_info:
            logger.error(f"❌ 未知插件: {plugin_name}")
            return False

        logger.info(f"开始安装插件: {plugin_info['name']}")

        # 检查容器是否存在
        check_cmd = (
            "docker ps -a --filter name=chaiguanjia_elasticsearch --format {{.Names}}"
        )
        result = subprocess.run(check_cmd.split(), capture_output=True, text=True)

        if "chaiguanjia_elasticsearch" not in result.stdout:
            logger.error("❌ Elasticsearch 容器不存在，请先启动 Docker Compose")
            return False

        # 在容器中安装插件
        install_cmd = f"docker exec chaiguanjia_elasticsearch /usr/share/elasticsearch/bin/elasticsearch-plugin install {plugin_info['url']}"

        logger.info(f"执行安装命令: {install_cmd}")
        success = self.run_docker_command(install_cmd)

        if success:
            logger.info(f"✅ 插件安装成功: {plugin_info['name']}")
            logger.info(f"描述: {plugin_info['description']}")
        else:
            logger.error(f"❌ 插件安装失败: {plugin_info['name']}")

        return success

    async def restart_elasticsearch_container(self) -> bool:
        """重启 Elasticsearch 容器"""
        logger.info("重启 Elasticsearch 容器...")

        # 重启容器
        restart_cmd = "docker restart chaiguanjia_elasticsearch"
        success = self.run_docker_command(restart_cmd)

        if success:
            logger.info("✅ 容器重启成功")

            # 等待服务启动
            logger.info("等待 Elasticsearch 服务启动...")
            await asyncio.sleep(30)

            # 检查服务状态
            if await self.check_elasticsearch_running():
                logger.info("✅ Elasticsearch 服务启动成功")
                return True

        logger.error("❌ 容器重启失败")
        return False

    async def verify_plugin_installation(self, plugin_name: str) -> bool:
        """
        验证插件安装

        Args:
            plugin_name: 插件名称

        Returns:
            bool: 插件是否安装成功
        """
        try:
            from app.shared.search import get_es_client

            es_client = get_es_client()

            if not es_client.client:
                logger.error("❌ Elasticsearch 客户端未连接")
                return False

            # 检查插件列表
            plugins_info = await es_client.client.cat.plugins(format="json")

            installed_plugins = [plugin.get("component", "") for plugin in plugins_info]

            if plugin_name in installed_plugins:
                logger.info(f"✅ 插件验证成功: {plugin_name}")
                return True
            else:
                logger.error(f"❌ 插件验证失败: {plugin_name}")
                logger.info(f"已安装插件: {installed_plugins}")
                return False

        except Exception as e:
            logger.error(f"❌ 插件验证异常: {e}")
            return False

    async def test_chinese_analysis(self) -> bool:
        """测试中文分词功能"""
        try:
            from app.shared.search import get_search_service

            search_service = get_search_service()

            # 测试中文分词
            test_text = "柴管家是一个智能客服系统，支持多平台聚合"
            tokens = await search_service.analyze_text(test_text, "ik_max_word")

            if tokens:
                logger.info(f"✅ 中文分词测试成功")
                logger.info(f"原文: {test_text}")
                logger.info(f"分词结果: {tokens}")
                return True
            else:
                logger.error("❌ 中文分词测试失败")
                return False

        except Exception as e:
            logger.error(f"❌ 中文分词测试异常: {e}")
            return False

    async def install_all_plugins(self) -> bool:
        """安装所有插件"""
        logger.info("开始安装 Elasticsearch 插件...")

        # 检查 Elasticsearch 是否运行
        if not await self.check_elasticsearch_running():
            logger.error("❌ Elasticsearch 未运行，请先启动服务")
            return False

        success_count = 0
        total_plugins = len(self.plugins)

        # 停止容器以安装插件
        logger.info("停止 Elasticsearch 容器以安装插件...")
        stop_cmd = "docker stop chaiguanjia_elasticsearch"
        self.run_docker_command(stop_cmd)

        # 安装所有插件
        for plugin_name in self.plugins.keys():
            if await self.install_plugin_in_container(plugin_name):
                success_count += 1

        # 重启容器
        if success_count > 0:
            if await self.restart_elasticsearch_container():
                # 验证插件安装
                verified_count = 0
                for plugin_name in self.plugins.keys():
                    if await self.verify_plugin_installation(plugin_name):
                        verified_count += 1

                if verified_count == success_count:
                    logger.info(
                        f"✅ 所有插件安装和验证成功: {verified_count}/{total_plugins}"
                    )

                    # 测试中文分词
                    await self.test_chinese_analysis()
                    return True

        logger.error(f"❌ 插件安装失败: {success_count}/{total_plugins}")
        return False

    def print_installation_summary(self):
        """打印安装摘要"""
        print("\n" + "=" * 60)
        print("Elasticsearch 插件安装摘要")
        print("=" * 60)

        for plugin_name, plugin_info in self.plugins.items():
            print(f"\n插件: {plugin_info['name']}")
            print(f"  版本: {plugin_info['version']}")
            print(f"  功能: {plugin_info['description']}")
            print(f"  下载地址: {plugin_info['url']}")

        print("\n安装说明:")
        print("1. 确保 Docker Compose 服务已启动")
        print("2. 运行此脚本会自动安装中文分词器")
        print("3. 安装完成后会自动重启 Elasticsearch")
        print("4. 验证插件安装并测试中文分词功能")

        print("\n注意事项:")
        print("- 插件版本必须与 Elasticsearch 版本匹配")
        print("- 安装过程中会暂时停止 Elasticsearch 服务")
        print("- 如果安装失败，请检查网络连接和插件版本")

        print("\n" + "=" * 60)


async def main():
    """主函数"""
    installer = ElasticsearchPluginInstaller()

    # 打印安装摘要
    installer.print_installation_summary()

    # 询问用户是否继续
    try:
        confirm = input("\n是否继续安装插件？(y/n): ").lower().strip()
        if confirm not in ["y", "yes", "是"]:
            logger.info("安装已取消")
            return 0
    except KeyboardInterrupt:
        logger.info("\n安装已取消")
        return 0

    try:
        # 安装所有插件
        success = await installer.install_all_plugins()

        if success:
            logger.info("🎉 Elasticsearch 插件安装完成！")
            logger.info("现在可以使用中文分词和搜索功能了")
            return 0
        else:
            logger.error("❌ Elasticsearch 插件安装失败！")
            return 1

    except Exception as e:
        logger.error(f"安装过程异常: {e}")
        return 1


if __name__ == "__main__":
    import os
    import sys

    # 添加项目根目录到 Python 路径
    sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

    exit_code = asyncio.run(main())
    sys.exit(exit_code)
