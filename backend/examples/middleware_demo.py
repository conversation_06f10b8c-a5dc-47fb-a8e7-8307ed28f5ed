"""
中间件系统演示文件
展示如何使用和配置各种中间件
"""

import asyncio
import time

from app.api.v1.monitoring import router as monitoring_router

# 导入中间件
from app.core.middleware import setup_middleware
from app.core.middleware_utils import get_middleware_monitor, get_request_tracker
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse


def create_demo_app() -> FastAPI:
    """创建演示应用"""
    app = FastAPI(
        title="中间件系统演示",
        description="展示柴管家中间件系统的功能",
        version="1.0.0",
    )

    # 设置中间件
    setup_middleware(app)

    # 添加监控路由
    app.include_router(monitoring_router)

    # 添加演示路由
    setup_demo_routes(app)

    return app


def setup_demo_routes(app: FastAPI):
    """设置演示路由"""

    @app.get("/demo/fast")
    async def fast_endpoint():
        """快速响应端点"""
        return {"message": "快速响应", "timestamp": time.time()}

    @app.get("/demo/slow")
    async def slow_endpoint():
        """慢响应端点（用于测试慢请求监控）"""
        await asyncio.sleep(3)  # 模拟3秒的处理时间
        return {"message": "慢响应", "timestamp": time.time()}

    @app.get("/demo/error")
    async def error_endpoint():
        """错误端点（用于测试错误监控）"""
        raise ValueError("这是一个演示错误")

    @app.get("/demo/large-response")
    async def large_response_endpoint():
        """大响应端点（用于测试响应大小）"""
        # 生成1MB的数据
        data = "x" * (1024 * 1024)
        return {"message": "大响应", "data": data}

    @app.post("/demo/upload")
    async def upload_endpoint(request: Request):
        """上传端点（用于测试请求大小限制）"""
        # 读取请求体
        body = await request.body()
        return {"message": "上传成功", "size": len(body), "timestamp": time.time()}

    @app.get("/demo/stats")
    async def stats_endpoint():
        """统计信息端点"""
        monitor = get_middleware_monitor()
        tracker = get_request_tracker()

        return {
            "middleware_stats": monitor.get_global_metrics(),
            "active_requests": len(tracker.get_active_requests()),
            "endpoint_stats": monitor.get_endpoint_metrics(5),
            "ip_stats": monitor.get_ip_metrics(5),
        }


async def demo_rate_limiting():
    """演示限流功能"""
    import httpx

    print("=== 限流功能演示 ===")

    # 快速发送多个请求测试限流
    async with httpx.AsyncClient() as client:
        for i in range(10):
            try:
                response = await client.get("http://localhost:8000/demo/fast")
                print(f"请求 {i+1}: 状态码 {response.status_code}")
                if response.status_code == 429:
                    print(f"触发限流: {response.json()}")
                    break
            except Exception as e:
                print(f"请求 {i+1} 失败: {e}")

            # 短暂延迟
            await asyncio.sleep(0.1)


async def demo_monitoring():
    """演示监控功能"""
    import httpx

    print("\n=== 监控功能演示 ===")

    # 发送一些测试请求
    async with httpx.AsyncClient() as client:
        # 快速请求
        await client.get("http://localhost:8000/demo/fast")

        # 慢请求
        print("发送慢请求...")
        await client.get("http://localhost:8000/demo/slow")

        # 错误请求
        try:
            await client.get("http://localhost:8000/demo/error")
        except:
            pass

        # 获取统计信息
        stats = await client.get("http://localhost:8000/demo/stats")
        print("统计信息:", stats.json())


def demo_configuration():
    """演示配置功能"""
    from app.core.middleware_utils import get_middleware_config

    print("\n=== 配置功能演示 ===")

    config = get_middleware_config()

    print("当前配置:")
    print(f"  限流: {config.rate_limit_enabled}")
    print(f"  限流参数: {config.rate_limit_requests}/{config.rate_limit_window}秒")
    print(f"  IP白名单: {config.ip_whitelist_enabled}")
    print(f"  请求大小限制: {config.request_size_limit}")
    print(f"  慢请求阈值: {config.slow_request_threshold}秒")

    # 动态更新配置
    print("\n动态更新限流配置...")
    config.update_rate_limit(200, 60)
    print(f"  新限流参数: {config.rate_limit_requests}/{config.rate_limit_window}秒")

    # 启用IP白名单
    print("\n启用IP白名单...")
    config.enable_ip_whitelist(["127.0.0.1", "***********/24"])
    print(f"  白名单: {config.ip_whitelist}")


if __name__ == "__main__":
    # 创建演示应用
    app = create_demo_app()

    # 演示配置功能
    demo_configuration()

    print("\n启动演示应用...")
    print("访问 http://localhost:8000/docs 查看API文档")
    print("访问 http://localhost:8000/api/v1/monitoring/health 查看健康状态")
    print("访问 http://localhost:8000/demo/stats 查看统计信息")

    # 如果需要运行演示，取消下面的注释
    # import uvicorn
    # uvicorn.run(app, host="0.0.0.0", port=8000)
