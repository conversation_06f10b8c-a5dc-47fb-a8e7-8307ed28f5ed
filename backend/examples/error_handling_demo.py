"""
错误处理机制演示
展示如何使用错误处理系统的各种功能
"""

import asyncio
import time
from datetime import datetime
from typing import Optional

from app.api.v1.error_monitoring import router as error_monitoring_router
from app.core.error_handlers import (
    raise_business_error,
    raise_forbidden,
    raise_not_found,
    raise_rate_limit_exceeded,
    raise_unauthorized,
    raise_validation_error,
    setup_error_handlers,
)
from app.core.error_monitor import (
    get_error_monitor,
    get_error_reporter,
    record_error_event,
)

# 导入错误处理模块
from app.core.exceptions import (
    AuthenticationException,
    AuthorizationException,
    BaseAPIException,
    BusinessLogicException,
    DatabaseException,
    ErrorCode,
    ErrorSeverity,
    ExternalServiceException,
    RateLimitException,
    ResourceNotFoundException,
    ValidationException,
)
from fastapi import Depends, FastAPI, Query, Request
from fastapi.responses import JSONResponse


def create_demo_app() -> FastAPI:
    """创建错误处理演示应用"""
    app = FastAPI(
        title="错误处理系统演示",
        description="展示柴管家错误处理机制的功能",
        version="1.0.0",
    )

    # 设置错误处理器
    setup_error_handlers(app)

    # 添加错误监控路由
    app.include_router(error_monitoring_router)

    # 添加演示路由
    setup_demo_routes(app)

    return app


def setup_demo_routes(app: FastAPI):
    """设置演示路由"""

    @app.get("/demo/errors/validation")
    async def demo_validation_error(email: str = Query(..., description="邮箱地址")):
        """演示验证错误"""
        if "@" not in email:
            raise_validation_error("email", "邮箱格式不正确")
        return {"message": "邮箱验证通过", "email": email}

    @app.get("/demo/errors/not-found")
    async def demo_not_found_error(user_id: str):
        """演示资源未找到错误"""
        # 模拟查找用户
        if user_id != "123":
            raise_not_found("用户", user_id)
        return {"message": "用户找到", "user_id": user_id}

    @app.get("/demo/errors/business-logic")
    async def demo_business_logic_error(amount: float):
        """演示业务逻辑错误"""
        if amount <= 0:
            raise_business_error("金额必须大于0")
        if amount > 10000:
            raise_business_error(
                "单次转账金额不能超过10000元", ErrorCode.QUOTA_EXCEEDED
            )
        return {"message": "转账金额验证通过", "amount": amount}

    @app.get("/demo/errors/unauthorized")
    async def demo_unauthorized_error(token: Optional[str] = None):
        """演示认证错误"""
        if not token:
            raise_unauthorized("缺少访问令牌")
        if token != "valid-token":
            raise_unauthorized("访问令牌无效")
        return {"message": "认证成功", "user": "demo_user"}

    @app.get("/demo/errors/forbidden")
    async def demo_forbidden_error(role: str = "user"):
        """演示授权错误"""
        if role != "admin":
            raise_forbidden("只有管理员可以访问此资源")
        return {"message": "管理员权限验证通过"}

    @app.get("/demo/errors/rate-limit")
    async def demo_rate_limit_error():
        """演示限流错误"""
        # 模拟限流检查
        raise_rate_limit_exceeded(retry_after=60)

    @app.get("/demo/errors/database")
    async def demo_database_error():
        """演示数据库错误"""
        raise DatabaseException(
            message="数据库连接失败", error_code=ErrorCode.DATABASE_CONNECTION_ERROR
        )

    @app.get("/demo/errors/external-service")
    async def demo_external_service_error():
        """演示外部服务错误"""
        raise ExternalServiceException(
            message="支付服务不可用",
            service_name="payment_service",
            error_code=ErrorCode.THIRD_PARTY_API_ERROR,
        )

    @app.get("/demo/errors/custom")
    async def demo_custom_error(error_type: str = "medium"):
        """演示自定义错误"""
        severity_map = {
            "low": ErrorSeverity.LOW,
            "medium": ErrorSeverity.MEDIUM,
            "high": ErrorSeverity.HIGH,
            "critical": ErrorSeverity.CRITICAL,
        }

        severity = severity_map.get(error_type, ErrorSeverity.MEDIUM)

        raise BaseAPIException(
            message=f"这是一个{error_type}级别的自定义错误",
            error_code=ErrorCode.INTERNAL_SERVER_ERROR,
            severity=severity,
            custom_data={"demo": True, "severity": error_type},
        )

    @app.post("/demo/errors/record")
    async def demo_record_error(
        error_code: str,
        message: str,
        severity: str = "medium",
        user_id: Optional[str] = None,
    ):
        """演示手动记录错误事件"""
        try:
            error_code_enum = ErrorCode(error_code)
        except ValueError:
            raise_validation_error("error_code", f"无效的错误码: {error_code}")

        try:
            severity_enum = ErrorSeverity(severity)
        except ValueError:
            raise_validation_error("severity", f"无效的严重程度: {severity}")

        # 记录错误事件
        await record_error_event(
            error_code=error_code_enum,
            message=message,
            severity=severity_enum,
            user_id=user_id,
            request_path="/demo/errors/record",
        )

        return {
            "message": "错误事件已记录",
            "error_code": error_code,
            "severity": severity,
            "timestamp": datetime.now().isoformat(),
        }

    @app.get("/demo/errors/trigger-multiple")
    async def demo_trigger_multiple_errors():
        """演示触发多个错误（用于测试监控）"""
        error_scenarios = [
            (ErrorCode.VALIDATION_ERROR, "参数验证失败", ErrorSeverity.LOW),
            (ErrorCode.BUSINESS_LOGIC_ERROR, "业务规则违反", ErrorSeverity.MEDIUM),
            (ErrorCode.DATABASE_ERROR, "数据库查询失败", ErrorSeverity.HIGH),
            (ErrorCode.EXTERNAL_SERVICE_ERROR, "外部API超时", ErrorSeverity.HIGH),
            (ErrorCode.RATE_LIMIT_EXCEEDED, "频率限制", ErrorSeverity.LOW),
        ]

        # 随机触发一些错误事件
        import random

        for _ in range(random.randint(3, 8)):
            error_code, message, severity = random.choice(error_scenarios)
            await record_error_event(
                error_code=error_code,
                message=message,
                severity=severity,
                user_id=f"user_{random.randint(1, 100)}",
                request_path="/demo/errors/trigger-multiple",
            )
            await asyncio.sleep(0.1)  # 短暂延迟

        return {"message": "已触发多个错误事件，请查看监控面板"}

    @app.get("/demo/errors/summary")
    async def demo_error_summary():
        """演示获取错误统计摘要"""
        monitor = get_error_monitor()
        summary = monitor.get_error_summary()

        return {
            "summary": summary,
            "recent_incidents": monitor.get_recent_incidents(limit=5),
            "trends": monitor.get_error_trends(hours=1),
        }

    @app.get("/demo/errors/report")
    async def demo_error_report():
        """演示生成错误报告"""
        reporter = get_error_reporter()
        report = reporter.generate_daily_report()

        return report


async def demo_error_scenarios():
    """演示各种错误场景"""
    import httpx

    print("=== 错误处理演示 ===")

    base_url = "http://localhost:8000"

    async with httpx.AsyncClient() as client:
        print("\n1. 演示验证错误")
        try:
            response = await client.get(
                f"{base_url}/demo/errors/validation?email=invalid-email"
            )
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
        except Exception as e:
            print(f"请求失败: {e}")

        print("\n2. 演示资源未找到错误")
        try:
            response = await client.get(f"{base_url}/demo/errors/not-found?user_id=999")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
        except Exception as e:
            print(f"请求失败: {e}")

        print("\n3. 演示业务逻辑错误")
        try:
            response = await client.get(
                f"{base_url}/demo/errors/business-logic?amount=15000"
            )
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
        except Exception as e:
            print(f"请求失败: {e}")

        print("\n4. 演示认证错误")
        try:
            response = await client.get(f"{base_url}/demo/errors/unauthorized")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
        except Exception as e:
            print(f"请求失败: {e}")

        print("\n5. 触发多个错误用于监控测试")
        try:
            response = await client.get(f"{base_url}/demo/errors/trigger-multiple")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
        except Exception as e:
            print(f"请求失败: {e}")

        print("\n6. 查看错误统计")
        try:
            response = await client.get(f"{base_url}/demo/errors/summary")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
        except Exception as e:
            print(f"请求失败: {e}")


def demo_error_codes():
    """演示错误码体系"""
    print("\n=== 错误码体系演示 ===")

    print("\n1. 错误码分类:")
    categories = {
        "通用错误 (E1xxx)": [ErrorCode.UNKNOWN_ERROR, ErrorCode.INTERNAL_SERVER_ERROR],
        "请求错误 (E2xxx)": [ErrorCode.BAD_REQUEST, ErrorCode.VALIDATION_ERROR],
        "认证授权错误 (E3xxx)": [
            ErrorCode.UNAUTHORIZED,
            ErrorCode.INSUFFICIENT_PERMISSIONS,
        ],
        "资源错误 (E4xxx)": [ErrorCode.NOT_FOUND, ErrorCode.RESOURCE_NOT_FOUND],
        "业务逻辑错误 (E5xxx)": [
            ErrorCode.BUSINESS_LOGIC_ERROR,
            ErrorCode.QUOTA_EXCEEDED,
        ],
        "限流错误 (E6xxx)": [
            ErrorCode.RATE_LIMIT_EXCEEDED,
            ErrorCode.TOO_MANY_REQUESTS,
        ],
        "数据库错误 (E7xxx)": [
            ErrorCode.DATABASE_ERROR,
            ErrorCode.DATABASE_CONNECTION_ERROR,
        ],
        "外部服务错误 (E8xxx)": [
            ErrorCode.EXTERNAL_SERVICE_ERROR,
            ErrorCode.REDIS_CONNECTION_ERROR,
        ],
        "网络错误 (E9xxx)": [ErrorCode.NETWORK_ERROR, ErrorCode.CONNECTION_TIMEOUT],
    }

    for category, codes in categories.items():
        print(f"\n{category}:")
        for code in codes:
            print(f"  {code.value}: {code.name}")

    print("\n2. 错误严重程度:")
    severities = [
        (ErrorSeverity.LOW, "低：不影响核心功能"),
        (ErrorSeverity.MEDIUM, "中：影响部分功能"),
        (ErrorSeverity.HIGH, "高：影响核心功能"),
        (ErrorSeverity.CRITICAL, "严重：系统不可用"),
    ]

    for severity, description in severities:
        print(f"  {severity.value}: {description}")


def demo_monitoring_features():
    """演示监控功能"""
    print("\n=== 错误监控功能演示 ===")

    monitor = get_error_monitor()

    print("\n1. 当前错误统计:")
    summary = monitor.get_error_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")

    print("\n2. 最近错误事件:")
    incidents = monitor.get_recent_incidents(limit=3)
    for incident in incidents:
        print(f"  事件ID: {incident['incident_id']}")
        print(f"  错误码: {incident['error_code']}")
        print(f"  消息: {incident['message']}")
        print(f"  严重程度: {incident['severity']}")
        print(f"  发生次数: {incident['count']}")
        print()

    print("3. 错误趋势分析:")
    trends = monitor.get_error_trends(hours=1)
    print(f"  时间范围: {trends['time_range']}")
    print(f"  总事件数: {trends['total_incidents']}")
    print(f"  总错误数: {trends['total_errors']}")
    print(f"  错误码分布: {trends['error_code_distribution']}")


if __name__ == "__main__":
    # 创建演示应用
    app = create_demo_app()

    # 演示错误码体系
    demo_error_codes()

    # 演示监控功能
    demo_monitoring_features()

    print("\n启动演示应用...")
    print("访问 http://localhost:8000/docs 查看API文档")
    print("访问 http://localhost:8000/error-monitoring/ 查看错误监控接口")
    print("\n演示端点:")
    print("- GET /demo/errors/validation?email=invalid")
    print("- GET /demo/errors/not-found?user_id=999")
    print("- GET /demo/errors/business-logic?amount=15000")
    print("- GET /demo/errors/unauthorized")
    print("- GET /demo/errors/forbidden?role=user")
    print("- GET /demo/errors/trigger-multiple")
    print("- GET /demo/errors/summary")
    print("- GET /error-monitoring/summary")
    print("- GET /error-monitoring/incidents")
    print("- GET /error-monitoring/health-check")

    # 如果需要运行演示，取消下面的注释
    # import uvicorn
    # uvicorn.run(app, host="0.0.0.0", port=8000)
