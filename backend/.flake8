[flake8]
# 柴管家项目 Flake8 配置
# 与 Black 格式化工具兼容的代码检查配置

# ==========================================
# 基本配置
# ==========================================
max-line-length = 88
max-complexity = 10
count = True
statistics = True
show-source = True

# ==========================================
# 忽略的错误和警告
# ==========================================
ignore =
    # Black 格式化工具相关忽略
    E203,    # whitespace before ':'
    W503,    # line break before binary operator
    E501,    # line too long (handled by black)

    # 文档字符串相关（使用单独的工具检查）
    D100, D101, D102, D103, D104, D105, D107,

    # 其他常见忽略
    F401,    # imported but unused (handled by isort)
    F403,    # star import (允许在特定场景使用)
    E402,    # module level import not at top (某些情况下需要)

# ==========================================
# 需要检查的错误类型
# ==========================================
select =
    E,       # pycodestyle errors
    W,       # pycodestyle warnings
    F,       # pyflakes
    C,       # mccabe complexity
    N,       # pep8-naming
    B,       # flake8-bugbear
    A,       # flake8-builtins
    COM,     # flake8-commas
    C4,      # flake8-comprehensions
    T10,     # flake8-debugger
    EM,      # flake8-errmsg
    G,       # flake8-logging-format
    PIE,     # flake8-pie
    T20,     # flake8-print
    PT,      # flake8-pytest-style
    Q,       # flake8-quotes
    RSE,     # flake8-raise
    RET,     # flake8-return
    SIM,     # flake8-simplify
    ARG,     # flake8-unused-arguments
    ERA,     # eradicate

# ==========================================
# 排除目录和文件
# ==========================================
exclude =
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    ENV,
    .tox,
    .pytest_cache,
    .mypy_cache,
    build,
    dist,
    *.egg-info,
    migrations/versions/*.py,
    alembic/versions/*.py,
    .eggs,
    docs/

# ==========================================
# 文件模式匹配
# ==========================================
filename =
    *.py

# ==========================================
# 每个文件类型的特殊配置
# ==========================================
per-file-ignores =
    # 测试文件允许的特殊规则
    tests/*.py: S101, T20, F401, F811, E501, ARG
    test_*.py: S101, T20, F401, F811, E501, ARG
    **/test_*.py: S101, T20, F401, F811, E501, ARG

    # 配置文件允许的特殊规则
    */settings/*.py: F401, F403
    */config/*.py: F401, F403

    # 初始化文件允许的特殊规则
    __init__.py: F401, F403

    # 迁移文件允许的特殊规则
    migrations/*.py: E501, F401, F403, ARG
    alembic/versions/*.py: E501, F401, F403, ARG

    # 脚本文件允许的特殊规则
    scripts/*.py: T20, ARG

# ==========================================
# 命名规范检查
# ==========================================
# 使用 flake8-naming 插件进行命名检查
classmethod-decorators = classmethod
staticmethod-decorators = staticmethod

# ==========================================
# Bugbear 插件配置
# ==========================================
# 使用 flake8-bugbear 进行额外的错误检查
extend-immutable-calls = frozenset, tuple

# ==========================================
# 引号检查配置
# ==========================================
# 使用 flake8-quotes 进行引号一致性检查
inline-quotes = double
multiline-quotes = double
docstring-quotes = double
avoid-escape = True

# ==========================================
# 测试相关配置
# ==========================================
# pytest 风格检查
pytest-fixture-no-parentheses = True
pytest-parametrize-names-type = csv
pytest-parametrize-values-type = tuple
pytest-parametrize-values-row-type = tuple

# ==========================================
# 返回值检查配置
# ==========================================
# flake8-return 插件配置
max-returns = 6
max-branches = 12
max-arguments = 7
max-local-variables = 15

# ==========================================
# 复杂度检查配置
# ==========================================
# McCabe 复杂度检查
max-complexity = 10
complex-extra-ignore = __init__

# ==========================================
# 输出格式配置
# ==========================================
format = %(path)s:%(row)d:%(col)d: %(code)s %(text)s
hang-closing = True
doctests = True

# ==========================================
# 基准配置
# ==========================================
benchmark = True
tee = True
jobs = auto
