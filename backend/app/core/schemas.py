"""
通用数据模型和响应格式
定义标准的API请求/响应格式
"""

from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic import BaseModel, Field

DataT = TypeVar("DataT")


class BaseResponse(BaseModel, Generic[DataT]):
    """标准API响应格式"""

    success: bool = Field(default=True, description="请求是否成功")
    message: str = Field(default="操作成功", description="响应消息")
    data: Optional[DataT] = Field(default=None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class ErrorResponse(BaseModel):
    """错误响应格式"""

    success: bool = Field(default=False, description="请求是否成功")
    error_code: str = Field(description="错误代码")
    error_message: str = Field(description="错误消息")
    details: Optional[Dict[str, Any]] = Field(default=None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间戳")
    request_id: Optional[str] = Field(default=None, description="请求ID")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class PaginationParams(BaseModel):
    """分页参数"""

    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小")

    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.page_size


class PaginationResponse(BaseModel, Generic[DataT]):
    """分页响应格式"""

    items: List[DataT] = Field(description="数据列表")
    total: int = Field(description="总数量")
    page: int = Field(description="当前页码")
    page_size: int = Field(description="每页大小")
    total_pages: int = Field(description="总页数")
    has_next: bool = Field(description="是否有下一页")
    has_prev: bool = Field(description="是否有上一页")

    @classmethod
    def create(
        cls, items: List[DataT], total: int, page: int, page_size: int
    ) -> "PaginationResponse[DataT]":
        """创建分页响应"""
        total_pages = (total + page_size - 1) // page_size

        return cls(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1,
        )


class HealthCheck(BaseModel):
    """健康检查响应"""

    status: str = Field(description="服务状态")
    version: str = Field(description="版本号")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    uptime_seconds: int = Field(description="运行时间（秒）")
    service: str = Field(description="服务名称")
    dependencies: Optional[Dict[str, str]] = Field(
        default=None, description="依赖服务状态"
    )

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class VersionInfo(BaseModel):
    """版本信息"""

    version: str = Field(description="应用版本")
    build_time: datetime = Field(description="构建时间")
    python_version: str = Field(description="Python版本")
    framework: str = Field(description="框架名称")
    environment: str = Field(description="运行环境")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class StatusInfo(BaseModel):
    """状态信息"""

    status: str = Field(description="运行状态")
    uptime_seconds: int = Field(description="运行时间（秒）")
    uptime_human: str = Field(description="可读运行时间")
    timestamp: datetime = Field(default_factory=datetime.now, description="状态时间")
    memory_usage: Optional[Dict[str, Any]] = Field(
        default=None, description="内存使用情况"
    )
    cpu_usage: Optional[float] = Field(default=None, description="CPU使用率")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class TestResult(BaseModel):
    """测试结果"""

    service: str = Field(description="服务名称")
    status: str = Field(description="测试状态")
    message: str = Field(description="测试消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="测试时间")
    latency_ms: Optional[float] = Field(default=None, description="延迟（毫秒）")
    details: Optional[Dict[str, Any]] = Field(default=None, description="测试详情")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}
