"""
FastAPI应用工厂
创建和配置FastAPI应用实例
"""

import structlog
from app.api.router import router as api_router
from fastapi import FastAPI

from .config import get_settings
from .error_handlers import setup_error_handlers
from .exceptions import setup_exception_handlers
from .middleware import setup_middleware

logger = structlog.get_logger()


def create_app() -> FastAPI:
    """创建并配置FastAPI应用实例"""
    settings = get_settings()

    # 创建FastAPI应用
    app = FastAPI(
        title=settings.app_name,
        description=settings.app_description,
        version=settings.app_version,
        docs_url=settings.docs_url,
        redoc_url=settings.redoc_url,
        debug=settings.debug,
        # 自定义OpenAPI配置
        openapi_tags=[
            {
                "name": "健康检查",
                "description": "服务健康检查和状态监控相关接口",
            },
            {
                "name": "认证授权",
                "description": "用户认证、授权和权限管理相关接口",
            },
            {
                "name": "用户管理",
                "description": "用户注册、登录、个人信息管理相关接口",
            },
            {
                "name": "渠道管理",
                "description": "多平台渠道接入和管理相关接口",
            },
            {
                "name": "消息处理",
                "description": "消息接收、处理、转发相关接口",
            },
            {
                "name": "AI服务",
                "description": "AI智能回复和处理相关接口",
            },
            {
                "name": "工作流",
                "description": "自动化工作流配置和管理相关接口",
            },
        ],
    )

    # 设置中间件
    setup_middleware(app)

    # 设置错误处理器
    setup_error_handlers(app)

    # 注册API路由
    app.include_router(api_router)

    # 添加根路径
    @app.get("/", tags=["根目录"])
    async def root():
        """根路径欢迎信息"""
        return {
            "message": "欢迎使用柴管家API！",
            "app_name": settings.app_name,
            "version": settings.app_version,
            "docs_url": settings.docs_url,
            "redoc_url": settings.redoc_url,
            "api_prefix": "/api",
            "status": "running",
        }

    # 添加启动和关闭事件处理
    @app.on_event("startup")
    async def startup_event():
        """应用启动事件"""
        logger.info(
            "柴管家API服务启动",
            app_name=settings.app_name,
            version=settings.app_version,
            debug=settings.debug,
            docs_url=settings.docs_url,
        )

    @app.on_event("shutdown")
    async def shutdown_event():
        """应用关闭事件"""
        logger.info(
            "柴管家API服务关闭",
            app_name=settings.app_name,
            version=settings.app_version,
        )

    logger.info("FastAPI应用创建完成")
    return app
