"""
错误监控和报告系统
收集、分析和报告系统错误信息
"""

import asyncio
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import structlog

from .exceptions import ErrorCode, ErrorSeverity

logger = structlog.get_logger(__name__)


@dataclass
class ErrorMetrics:
    """错误指标数据结构"""

    # 基础统计
    total_errors: int = 0
    error_rate: float = 0.0

    # 按严重程度统计
    critical_errors: int = 0
    high_errors: int = 0
    medium_errors: int = 0
    low_errors: int = 0

    # 按错误码统计
    error_code_counts: Dict[str, int] = field(default_factory=dict)

    # 按时间段统计（最近1小时）
    recent_errors: deque = field(default_factory=lambda: deque(maxlen=3600))

    # 错误率趋势
    error_rate_trend: List[float] = field(default_factory=list)

    # 最频繁的错误
    top_errors: List[Dict[str, Any]] = field(default_factory=list)

    def add_error(
        self,
        error_code: str,
        severity: ErrorSeverity,
        timestamp: Optional[float] = None,
    ):
        """添加错误记录"""
        if timestamp is None:
            timestamp = time.time()

        self.total_errors += 1
        self.recent_errors.append(timestamp)

        # 按严重程度统计
        if severity == ErrorSeverity.CRITICAL:
            self.critical_errors += 1
        elif severity == ErrorSeverity.HIGH:
            self.high_errors += 1
        elif severity == ErrorSeverity.MEDIUM:
            self.medium_errors += 1
        else:  # LOW
            self.low_errors += 1

        # 按错误码统计
        self.error_code_counts[error_code] = (
            self.error_code_counts.get(error_code, 0) + 1
        )

        # 更新错误率（基于最近1小时）
        self._update_error_rate()

        # 更新热门错误
        self._update_top_errors()

    def _update_error_rate(self):
        """更新错误率（每分钟）"""
        current_time = time.time()
        minute_ago = current_time - 60

        # 统计最近1分钟的错误数
        recent_count = sum(1 for t in self.recent_errors if t > minute_ago)

        # 假设正常情况下每分钟有100个请求
        # 实际项目中应该从请求监控中获取真实数据
        estimated_requests = 100
        self.error_rate = (recent_count / max(estimated_requests, 1)) * 100

        # 记录错误率趋势（保留最近60个数据点）
        self.error_rate_trend.append(self.error_rate)
        if len(self.error_rate_trend) > 60:
            self.error_rate_trend.pop(0)

    def _update_top_errors(self):
        """更新最频繁的错误列表"""
        # 按错误数量排序，取前10名
        sorted_errors = sorted(
            self.error_code_counts.items(), key=lambda x: x[1], reverse=True
        )[:10]

        self.top_errors = [
            {
                "error_code": code,
                "count": count,
                "percentage": (count / max(self.total_errors, 1)) * 100,
            }
            for code, count in sorted_errors
        ]

    def get_summary(self) -> Dict[str, Any]:
        """获取错误统计摘要"""
        return {
            "total_errors": self.total_errors,
            "error_rate": round(self.error_rate, 2),
            "severity_distribution": {
                "critical": self.critical_errors,
                "high": self.high_errors,
                "medium": self.medium_errors,
                "low": self.low_errors,
            },
            "top_errors": self.top_errors[:5],
            "recent_trend": (
                self.error_rate_trend[-10:] if self.error_rate_trend else []
            ),
        }


@dataclass
class ErrorIncident:
    """错误事件记录"""

    incident_id: str
    error_code: str
    message: str
    severity: ErrorSeverity
    first_seen: datetime
    last_seen: datetime
    count: int = 1
    affected_users: set = field(default_factory=set)
    request_paths: set = field(default_factory=set)
    resolved: bool = False
    resolution_notes: Optional[str] = None

    def update(self, user_id: Optional[str] = None, request_path: Optional[str] = None):
        """更新事件信息"""
        self.last_seen = datetime.now()
        self.count += 1

        if user_id:
            self.affected_users.add(user_id)
        if request_path:
            self.request_paths.add(request_path)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "incident_id": self.incident_id,
            "error_code": self.error_code,
            "message": self.message,
            "severity": self.severity.value,
            "first_seen": self.first_seen.isoformat(),
            "last_seen": self.last_seen.isoformat(),
            "count": self.count,
            "affected_users_count": len(self.affected_users),
            "affected_paths_count": len(self.request_paths),
            "resolved": self.resolved,
            "resolution_notes": self.resolution_notes,
        }


class ErrorMonitor:
    """错误监控器"""

    def __init__(self):
        self.metrics = ErrorMetrics()
        self.incidents: Dict[str, ErrorIncident] = {}
        self.alert_thresholds = {
            ErrorSeverity.CRITICAL: 1,  # 1个严重错误就告警
            ErrorSeverity.HIGH: 5,  # 5个高级错误告警
            ErrorSeverity.MEDIUM: 20,  # 20个中级错误告警
            ErrorSeverity.LOW: 100,  # 100个低级错误告警
        }
        self.error_rate_threshold = 10.0  # 错误率超过10%告警
        self._lock = asyncio.Lock()

    async def record_error(
        self,
        error_code: str,
        message: str,
        severity: ErrorSeverity,
        user_id: Optional[str] = None,
        request_path: Optional[str] = None,
        **context,
    ):
        """记录错误事件"""
        async with self._lock:
            # 更新全局指标
            self.metrics.add_error(error_code, severity)

            # 创建或更新事件
            incident_key = f"{error_code}_{message}"

            if incident_key in self.incidents:
                # 更新现有事件
                self.incidents[incident_key].update(user_id, request_path)
            else:
                # 创建新事件
                incident_id = f"INC_{int(time.time())}_{len(self.incidents)}"
                self.incidents[incident_key] = ErrorIncident(
                    incident_id=incident_id,
                    error_code=error_code,
                    message=message,
                    severity=severity,
                    first_seen=datetime.now(),
                    last_seen=datetime.now(),
                )

                if user_id:
                    self.incidents[incident_key].affected_users.add(user_id)
                if request_path:
                    self.incidents[incident_key].request_paths.add(request_path)

            # 检查是否需要告警
            await self._check_alerts(error_code, severity)

            # 记录到日志
            logger.info(
                "错误事件记录",
                error_code=error_code,
                message=message,
                severity=severity.value,
                user_id=user_id,
                request_path=request_path,
                **context,
            )

    async def _check_alerts(self, error_code: str, severity: ErrorSeverity):
        """检查是否触发告警"""

        # 检查错误计数阈值
        current_count = self.metrics.error_code_counts.get(error_code, 0)
        threshold = self.alert_thresholds.get(severity, 50)

        if current_count >= threshold:
            await self._send_alert(
                f"错误代码 {error_code} 达到告警阈值",
                f"错误 {error_code} 在短时间内出现了 {current_count} 次，已达到 {severity.value} 级别的告警阈值（{threshold}）",
                severity,
            )

        # 检查错误率阈值
        if self.metrics.error_rate >= self.error_rate_threshold:
            await self._send_alert(
                "系统错误率过高",
                f"当前错误率为 {self.metrics.error_rate:.2f}%，超过阈值 {self.error_rate_threshold}%",
                ErrorSeverity.HIGH,
            )

    async def _send_alert(self, title: str, description: str, severity: ErrorSeverity):
        """发送告警通知"""

        alert_data = {
            "title": title,
            "description": description,
            "severity": severity.value,
            "timestamp": datetime.now().isoformat(),
            "metrics_summary": self.metrics.get_summary(),
        }

        # 记录告警日志
        logger.warning(
            "系统错误告警",
            alert_title=title,
            alert_description=description,
            severity=severity.value,
            **alert_data,
        )

        # TODO: 实际项目中可以集成邮件、短信、钉钉等告警渠道
        # await self._send_email_alert(alert_data)
        # await self._send_slack_alert(alert_data)

    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误统计摘要"""
        return self.metrics.get_summary()

    def get_recent_incidents(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的错误事件"""
        sorted_incidents = sorted(
            self.incidents.values(), key=lambda x: x.last_seen, reverse=True
        )[:limit]

        return [incident.to_dict() for incident in sorted_incidents]

    def get_error_trends(self, hours: int = 24) -> Dict[str, Any]:
        """获取错误趋势分析"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        # 过滤最近N小时的事件
        recent_incidents = [
            incident
            for incident in self.incidents.values()
            if incident.last_seen > cutoff_time
        ]

        # 按小时分组统计
        hourly_stats: Dict[str, int] = defaultdict(int)
        for incident in recent_incidents:
            hour_key = incident.last_seen.strftime("%Y-%m-%d %H:00")
            hourly_stats[hour_key] += incident.count

        # 按错误码分组统计
        error_code_stats: Dict[str, int] = defaultdict(int)
        for incident in recent_incidents:
            error_code_stats[incident.error_code] += incident.count

        return {
            "time_range": f"最近{hours}小时",
            "total_incidents": len(recent_incidents),
            "total_errors": sum(incident.count for incident in recent_incidents),
            "hourly_distribution": dict(hourly_stats),
            "error_code_distribution": dict(error_code_stats),
            "severity_distribution": {
                severity.value: len(
                    [
                        incident
                        for incident in recent_incidents
                        if incident.severity == severity
                    ]
                )
                for severity in ErrorSeverity
            },
        }

    def resolve_incident(self, incident_id: str, resolution_notes: str):
        """标记事件为已解决"""
        for incident in self.incidents.values():
            if incident.incident_id == incident_id:
                incident.resolved = True
                incident.resolution_notes = resolution_notes

                logger.info(
                    "错误事件已解决",
                    incident_id=incident_id,
                    error_code=incident.error_code,
                    resolution_notes=resolution_notes,
                )
                return True

        return False

    def reset_metrics(self):
        """重置错误统计"""
        self.metrics = ErrorMetrics()
        self.incidents.clear()
        logger.info("错误监控指标已重置")


# 全局错误监控器实例
error_monitor = ErrorMonitor()


def get_error_monitor() -> ErrorMonitor:
    """获取错误监控器实例"""
    return error_monitor


async def record_error_event(
    error_code: ErrorCode,
    message: str,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    user_id: Optional[str] = None,
    request_path: Optional[str] = None,
    **context,
):
    """记录错误事件的便捷函数"""
    monitor = get_error_monitor()
    await monitor.record_error(
        error_code=error_code.value,
        message=message,
        severity=severity,
        user_id=user_id,
        request_path=request_path,
        **context,
    )


class ErrorReporter:
    """错误报告生成器"""

    def __init__(self, monitor: ErrorMonitor):
        self.monitor = monitor

    def generate_daily_report(self) -> Dict[str, Any]:
        """生成日报"""
        trends = self.monitor.get_error_trends(hours=24)
        summary = self.monitor.get_error_summary()
        recent_incidents = self.monitor.get_recent_incidents(limit=20)

        # 计算关键指标
        critical_incidents = [
            incident
            for incident in recent_incidents
            if incident["severity"] == ErrorSeverity.CRITICAL.value
        ]

        unresolved_incidents = [
            incident for incident in recent_incidents if not incident["resolved"]
        ]

        return {
            "report_type": "daily",
            "generated_at": datetime.now().isoformat(),
            "summary": {
                **summary,
                "critical_incidents_count": len(critical_incidents),
                "unresolved_incidents_count": len(unresolved_incidents),
            },
            "trends": trends,
            "critical_incidents": critical_incidents,
            "unresolved_incidents": unresolved_incidents[:10],
            "recommendations": self._generate_recommendations(summary, trends),
        }

    def _generate_recommendations(
        self, summary: Dict[str, Any], trends: Dict[str, Any]
    ) -> List[str]:
        """生成改进建议"""
        recommendations = []

        # 基于错误率的建议
        if summary["error_rate"] > 5.0:
            recommendations.append("系统错误率较高，建议检查核心功能和依赖服务")

        # 基于严重错误的建议
        if summary["severity_distribution"]["critical"] > 0:
            recommendations.append("存在严重错误，需要立即处理")

        # 基于错误趋势的建议
        if len(summary["recent_trend"]) >= 3:
            recent_avg = sum(summary["recent_trend"][-3:]) / 3
            if recent_avg > summary["error_rate"]:
                recommendations.append("错误率呈上升趋势，建议加强监控")

        # 基于热门错误的建议
        if summary["top_errors"]:
            top_error = summary["top_errors"][0]
            if top_error["percentage"] > 50:
                recommendations.append(
                    f"错误 {top_error['error_code']} 占比过高，建议优先修复"
                )

        return recommendations


def get_error_reporter() -> ErrorReporter:
    """获取错误报告生成器"""
    return ErrorReporter(get_error_monitor())
