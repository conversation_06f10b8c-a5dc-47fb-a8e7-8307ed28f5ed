"""
错误处理机制核心模块
定义标准的错误响应格式、异常类型和错误码体系
"""

import traceback
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

import structlog
from fastapi import FastAPI, Request, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

logger = structlog.get_logger(__name__)


class ErrorCode(str, Enum):
    """错误码枚举类 - 统一的错误代码体系"""

    # 通用错误 (1000-1999)
    UNKNOWN_ERROR = "E1000"
    INTERNAL_SERVER_ERROR = "E1001"
    SERVICE_UNAVAILABLE = "E1002"
    TIMEOUT_ERROR = "E1003"
    CONFIGURATION_ERROR = "E1004"

    # 请求相关错误 (2000-2999)
    BAD_REQUEST = "E2000"
    VALIDATION_ERROR = "E2001"
    MISSING_PARAMETER = "E2002"
    INVALID_PARAMETER = "E2003"
    REQUEST_TOO_LARGE = "E2004"
    UNSUPPORTED_MEDIA_TYPE = "E2005"

    # 认证授权错误 (3000-3999)
    UNAUTHORIZED = "E3000"
    INVALID_TOKEN = "E3001"
    TOKEN_EXPIRED = "E3002"
    INSUFFICIENT_PERMISSIONS = "E3003"
    ACCOUNT_DISABLED = "E3004"
    LOGIN_REQUIRED = "E3005"

    # 资源相关错误 (4000-4999)
    NOT_FOUND = "E4000"
    RESOURCE_NOT_FOUND = "E4001"
    USER_NOT_FOUND = "E4002"
    FILE_NOT_FOUND = "E4003"
    ENDPOINT_NOT_FOUND = "E4004"

    # 业务逻辑错误 (5000-5999)
    BUSINESS_LOGIC_ERROR = "E5000"
    DUPLICATE_RESOURCE = "E5001"
    RESOURCE_CONFLICT = "E5002"
    INVALID_OPERATION = "E5003"
    QUOTA_EXCEEDED = "E5004"

    # 限流和频控错误 (6000-6999)
    RATE_LIMIT_EXCEEDED = "E6000"
    TOO_MANY_REQUESTS = "E6001"
    CONCURRENT_LIMIT_EXCEEDED = "E6002"

    # 数据库相关错误 (7000-7999)
    DATABASE_ERROR = "E7000"
    DATABASE_CONNECTION_ERROR = "E7001"
    DATABASE_QUERY_ERROR = "E7002"
    DATABASE_CONSTRAINT_ERROR = "E7003"
    DATABASE_TIMEOUT = "E7004"

    # 外部服务错误 (8000-8999)
    EXTERNAL_SERVICE_ERROR = "E8000"
    REDIS_CONNECTION_ERROR = "E8001"
    MESSAGE_QUEUE_ERROR = "E8002"
    FILE_STORAGE_ERROR = "E8003"
    THIRD_PARTY_API_ERROR = "E8004"

    # 网络和通信错误 (9000-9999)
    NETWORK_ERROR = "E9000"
    CONNECTION_TIMEOUT = "E9001"
    DNS_RESOLUTION_ERROR = "E9002"
    SSL_ERROR = "E9003"


class ErrorSeverity(str, Enum):
    """错误严重程度"""

    LOW = "low"  # 低：不影响核心功能
    MEDIUM = "medium"  # 中：影响部分功能
    HIGH = "high"  # 高：影响核心功能
    CRITICAL = "critical"  # 严重：系统不可用


class ErrorResponse(BaseModel):
    """标准错误响应格式"""

    success: bool = Field(False, description="请求是否成功")
    error_code: str = Field(..., description="错误代码")
    message: str = Field(..., description="用户友好的错误消息")
    details: Optional[str] = Field(None, description="详细错误信息（仅开发环境）")
    timestamp: datetime = Field(
        default_factory=datetime.now, description="错误发生时间"
    )
    request_id: Optional[str] = Field(None, description="请求ID")
    path: Optional[str] = Field(None, description="请求路径")
    method: Optional[str] = Field(None, description="请求方法")
    severity: ErrorSeverity = Field(ErrorSeverity.MEDIUM, description="错误严重程度")

    # 验证错误的详细信息
    validation_errors: Optional[List[Dict[str, Any]]] = Field(
        None, description="验证错误详情"
    )

    # 追踪信息（仅开发环境）
    trace_id: Optional[str] = Field(None, description="追踪ID")
    stack_trace: Optional[str] = Field(None, description="堆栈信息（仅开发环境）")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class BaseAPIException(Exception):
    """基础API异常类"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[str] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        **kwargs,
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details
        self.severity = severity
        self.extra_data = kwargs
        super().__init__(message)


class ValidationException(BaseAPIException):
    """验证异常"""

    def __init__(
        self,
        message: str = "请求参数验证失败",
        validation_errors: Optional[List[Dict[str, Any]]] = None,
        **kwargs,
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.VALIDATION_ERROR,
            status_code=status.HTTP_400_BAD_REQUEST,
            severity=ErrorSeverity.LOW,
            **kwargs,
        )
        self.validation_errors = validation_errors or []


class BusinessLogicException(BaseAPIException):
    """业务逻辑异常"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.BUSINESS_LOGIC_ERROR,
        **kwargs,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_400_BAD_REQUEST,
            severity=ErrorSeverity.MEDIUM,
            **kwargs,
        )


class ResourceNotFoundException(BaseAPIException):
    """资源未找到异常"""

    def __init__(
        self,
        message: str = "请求的资源不存在",
        resource_type: str = "resource",
        resource_id: Optional[str] = None,
        **kwargs,
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.RESOURCE_NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND,
            severity=ErrorSeverity.LOW,
            **kwargs,
        )
        self.resource_type = resource_type
        self.resource_id = resource_id


class AuthenticationException(BaseAPIException):
    """认证异常"""

    def __init__(
        self,
        message: str = "认证失败，请检查您的凭据",
        error_code: ErrorCode = ErrorCode.UNAUTHORIZED,
        **kwargs,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_401_UNAUTHORIZED,
            severity=ErrorSeverity.MEDIUM,
            **kwargs,
        )


class AuthorizationException(BaseAPIException):
    """授权异常"""

    def __init__(
        self,
        message: str = "权限不足，无法访问此资源",
        error_code: ErrorCode = ErrorCode.INSUFFICIENT_PERMISSIONS,
        **kwargs,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_403_FORBIDDEN,
            severity=ErrorSeverity.MEDIUM,
            **kwargs,
        )


class RateLimitException(BaseAPIException):
    """限流异常"""

    def __init__(
        self,
        message: str = "请求频率过高，请稍后重试",
        retry_after: Optional[int] = None,
        **kwargs,
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.RATE_LIMIT_EXCEEDED,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            severity=ErrorSeverity.LOW,
            **kwargs,
        )
        self.retry_after = retry_after


class DatabaseException(BaseAPIException):
    """数据库异常"""

    def __init__(
        self,
        message: str = "数据库操作失败",
        error_code: ErrorCode = ErrorCode.DATABASE_ERROR,
        **kwargs,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            severity=ErrorSeverity.HIGH,
            **kwargs,
        )


class ExternalServiceException(BaseAPIException):
    """外部服务异常"""

    def __init__(
        self,
        message: str = "外部服务不可用",
        service_name: Optional[str] = None,
        error_code: ErrorCode = ErrorCode.EXTERNAL_SERVICE_ERROR,
        **kwargs,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            severity=ErrorSeverity.HIGH,
            **kwargs,
        )
        self.service_name = service_name


def create_error_response(
    error_code: ErrorCode,
    message: str,
    details: Optional[str] = None,
    request: Optional[Request] = None,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    **kwargs,
) -> ErrorResponse:
    """创建标准错误响应"""

    # 获取请求信息
    request_id = None
    path = None
    method = None

    if request:
        request_id = getattr(request.state, "request_id", None)
        path = str(request.url.path) if request.url else None
        method = request.method

    response = ErrorResponse(
        error_code=error_code.value,
        message=message,
        details=details,
        request_id=request_id,
        path=path,
        method=method,
        severity=severity,
        **kwargs,
    )

    return response


def get_user_friendly_message(error_code: ErrorCode) -> str:
    """获取用户友好的错误消息"""

    friendly_messages = {
        ErrorCode.UNKNOWN_ERROR: "系统发生未知错误，请稍后重试",
        ErrorCode.INTERNAL_SERVER_ERROR: "服务器内部错误，请联系技术支持",
        ErrorCode.SERVICE_UNAVAILABLE: "服务暂时不可用，请稍后重试",
        ErrorCode.TIMEOUT_ERROR: "请求超时，请检查网络连接后重试",
        ErrorCode.BAD_REQUEST: "请求格式不正确，请检查请求参数",
        ErrorCode.VALIDATION_ERROR: "请求参数验证失败，请检查输入内容",
        ErrorCode.MISSING_PARAMETER: "缺少必需的参数",
        ErrorCode.INVALID_PARAMETER: "参数值不正确",
        ErrorCode.REQUEST_TOO_LARGE: "请求内容过大，请减少数据量",
        ErrorCode.UNAUTHORIZED: "身份验证失败，请重新登录",
        ErrorCode.INVALID_TOKEN: "访问令牌无效，请重新登录",
        ErrorCode.TOKEN_EXPIRED: "访问令牌已过期，请重新登录",
        ErrorCode.INSUFFICIENT_PERMISSIONS: "权限不足，无法访问此资源",
        ErrorCode.ACCOUNT_DISABLED: "账户已被禁用，请联系管理员",
        ErrorCode.NOT_FOUND: "请求的资源不存在",
        ErrorCode.RESOURCE_NOT_FOUND: "请求的资源不存在",
        ErrorCode.USER_NOT_FOUND: "用户不存在",
        ErrorCode.FILE_NOT_FOUND: "文件不存在",
        ErrorCode.ENDPOINT_NOT_FOUND: "接口不存在",
        ErrorCode.RATE_LIMIT_EXCEEDED: "请求频率过高，请稍后重试",
        ErrorCode.TOO_MANY_REQUESTS: "请求次数过多，请稍后重试",
        ErrorCode.DATABASE_ERROR: "数据库操作失败，请稍后重试",
        ErrorCode.DATABASE_CONNECTION_ERROR: "数据库连接失败，请稍后重试",
        ErrorCode.EXTERNAL_SERVICE_ERROR: "外部服务不可用，请稍后重试",
        ErrorCode.REDIS_CONNECTION_ERROR: "缓存服务连接失败",
        ErrorCode.MESSAGE_QUEUE_ERROR: "消息队列服务异常",
    }

    return friendly_messages.get(error_code, "系统发生错误，请稍后重试")


def should_include_details(request: Optional[Request] = None) -> bool:
    """判断是否应该包含详细错误信息（仅开发环境）"""
    from .config import get_settings

    settings = get_settings()
    return settings.debug


def log_error_details(
    error: Exception,
    request: Optional[Request] = None,
    error_code: Optional[ErrorCode] = None,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
):
    """记录详细的错误信息到日志"""

    # 构建日志上下文
    log_context = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "error_code": error_code.value if error_code else "UNKNOWN",
        "severity": severity.value,
    }

    # 添加请求信息
    if request:
        log_context.update(
            {
                "request_id": getattr(request.state, "request_id", None),
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host if request.client else None,
                "user_agent": request.headers.get("user-agent"),
            }
        )

    # 添加异常特定信息
    if isinstance(error, BaseAPIException):
        log_context.update(
            {
                "custom_error_code": error.error_code.value,
                "status_code": error.status_code,
                "extra_data": error.extra_data,
            }
        )

    # 添加堆栈跟踪（仅严重错误）
    if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
        log_context["stack_trace"] = traceback.format_exc()

    # 根据严重程度选择日志级别
    if severity == ErrorSeverity.CRITICAL:
        logger.critical("严重系统错误", **log_context)
    elif severity == ErrorSeverity.HIGH:
        logger.error("系统错误", **log_context)
    elif severity == ErrorSeverity.MEDIUM:
        logger.warning("业务错误", **log_context)
    else:  # LOW
        logger.info("请求错误", **log_context)


# 错误码到HTTP状态码的映射
ERROR_CODE_TO_HTTP_STATUS = {
    ErrorCode.UNKNOWN_ERROR: status.HTTP_500_INTERNAL_SERVER_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR: status.HTTP_500_INTERNAL_SERVER_ERROR,
    ErrorCode.SERVICE_UNAVAILABLE: status.HTTP_503_SERVICE_UNAVAILABLE,
    ErrorCode.TIMEOUT_ERROR: status.HTTP_408_REQUEST_TIMEOUT,
    ErrorCode.CONFIGURATION_ERROR: status.HTTP_500_INTERNAL_SERVER_ERROR,
    ErrorCode.BAD_REQUEST: status.HTTP_400_BAD_REQUEST,
    ErrorCode.VALIDATION_ERROR: status.HTTP_400_BAD_REQUEST,
    ErrorCode.MISSING_PARAMETER: status.HTTP_400_BAD_REQUEST,
    ErrorCode.INVALID_PARAMETER: status.HTTP_400_BAD_REQUEST,
    ErrorCode.REQUEST_TOO_LARGE: status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
    ErrorCode.UNSUPPORTED_MEDIA_TYPE: status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
    ErrorCode.UNAUTHORIZED: status.HTTP_401_UNAUTHORIZED,
    ErrorCode.INVALID_TOKEN: status.HTTP_401_UNAUTHORIZED,
    ErrorCode.TOKEN_EXPIRED: status.HTTP_401_UNAUTHORIZED,
    ErrorCode.INSUFFICIENT_PERMISSIONS: status.HTTP_403_FORBIDDEN,
    ErrorCode.ACCOUNT_DISABLED: status.HTTP_403_FORBIDDEN,
    ErrorCode.LOGIN_REQUIRED: status.HTTP_401_UNAUTHORIZED,
    ErrorCode.NOT_FOUND: status.HTTP_404_NOT_FOUND,
    ErrorCode.RESOURCE_NOT_FOUND: status.HTTP_404_NOT_FOUND,
    ErrorCode.USER_NOT_FOUND: status.HTTP_404_NOT_FOUND,
    ErrorCode.FILE_NOT_FOUND: status.HTTP_404_NOT_FOUND,
    ErrorCode.ENDPOINT_NOT_FOUND: status.HTTP_404_NOT_FOUND,
    ErrorCode.BUSINESS_LOGIC_ERROR: status.HTTP_400_BAD_REQUEST,
    ErrorCode.DUPLICATE_RESOURCE: status.HTTP_409_CONFLICT,
    ErrorCode.RESOURCE_CONFLICT: status.HTTP_409_CONFLICT,
    ErrorCode.INVALID_OPERATION: status.HTTP_400_BAD_REQUEST,
    ErrorCode.QUOTA_EXCEEDED: status.HTTP_429_TOO_MANY_REQUESTS,
    ErrorCode.RATE_LIMIT_EXCEEDED: status.HTTP_429_TOO_MANY_REQUESTS,
    ErrorCode.TOO_MANY_REQUESTS: status.HTTP_429_TOO_MANY_REQUESTS,
    ErrorCode.CONCURRENT_LIMIT_EXCEEDED: status.HTTP_429_TOO_MANY_REQUESTS,
    ErrorCode.DATABASE_ERROR: status.HTTP_500_INTERNAL_SERVER_ERROR,
    ErrorCode.DATABASE_CONNECTION_ERROR: status.HTTP_503_SERVICE_UNAVAILABLE,
    ErrorCode.DATABASE_QUERY_ERROR: status.HTTP_500_INTERNAL_SERVER_ERROR,
    ErrorCode.DATABASE_CONSTRAINT_ERROR: status.HTTP_400_BAD_REQUEST,
    ErrorCode.DATABASE_TIMEOUT: status.HTTP_408_REQUEST_TIMEOUT,
    ErrorCode.EXTERNAL_SERVICE_ERROR: status.HTTP_503_SERVICE_UNAVAILABLE,
    ErrorCode.REDIS_CONNECTION_ERROR: status.HTTP_503_SERVICE_UNAVAILABLE,
    ErrorCode.MESSAGE_QUEUE_ERROR: status.HTTP_503_SERVICE_UNAVAILABLE,
    ErrorCode.FILE_STORAGE_ERROR: status.HTTP_503_SERVICE_UNAVAILABLE,
    ErrorCode.THIRD_PARTY_API_ERROR: status.HTTP_503_SERVICE_UNAVAILABLE,
    ErrorCode.NETWORK_ERROR: status.HTTP_503_SERVICE_UNAVAILABLE,
    ErrorCode.CONNECTION_TIMEOUT: status.HTTP_408_REQUEST_TIMEOUT,
    ErrorCode.DNS_RESOLUTION_ERROR: status.HTTP_503_SERVICE_UNAVAILABLE,
    ErrorCode.SSL_ERROR: status.HTTP_503_SERVICE_UNAVAILABLE,
}


def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""

    @app.exception_handler(BaseAPIException)
    async def api_exception_handler(request: Request, exc: BaseAPIException):
        """处理自定义API异常"""
        log_error_details(exc, request, exc.error_code, exc.severity)

        error_response = create_error_response(
            error_code=exc.error_code,
            message=exc.message,
            details=exc.details if should_include_details(request) else None,
            request=request,
            severity=exc.severity,
        )

        return JSONResponse(status_code=exc.status_code, content=error_response.dict())

    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """处理通用异常"""
        log_error_details(
            exc, request, ErrorCode.INTERNAL_SERVER_ERROR, ErrorSeverity.HIGH
        )

        error_response = create_error_response(
            error_code=ErrorCode.INTERNAL_SERVER_ERROR,
            message="服务器内部错误",
            details=str(exc) if should_include_details(request) else None,
            request=request,
            severity=ErrorSeverity.HIGH,
        )

        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_response.dict(),
        )
