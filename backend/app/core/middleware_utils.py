"""
中间件工具模块
提供中间件相关的辅助函数和装饰器
"""

import asyncio
import functools
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List

import structlog
from fastapi import Request
from starlette.responses import Response

logger = structlog.get_logger()


@dataclass
class MiddlewareMetrics:
    """中间件性能指标"""

    total_requests: int = 0
    total_time: float = 0.0
    error_count: int = 0
    rate_limit_hits: int = 0
    slow_requests: int = 0
    average_response_time: float = 0.0
    peak_requests_per_minute: int = 0
    current_active_requests: int = 0

    # 最近1分钟的请求时间戳
    recent_requests: deque = field(default_factory=lambda: deque(maxlen=1000))

    def add_request(
        self, process_time: float, is_error: bool = False, is_slow: bool = False
    ):
        """添加请求记录"""
        current_time = time.time()

        self.total_requests += 1
        self.total_time += process_time
        self.recent_requests.append(current_time)

        if is_error:
            self.error_count += 1
        if is_slow:
            self.slow_requests += 1

        # 计算平均响应时间
        self.average_response_time = self.total_time / self.total_requests

        # 计算最近1分钟的请求数
        minute_ago = current_time - 60
        recent_count = sum(1 for t in self.recent_requests if t > minute_ago)
        self.peak_requests_per_minute = max(self.peak_requests_per_minute, recent_count)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        total_reqs = max(self.total_requests, 1)
        return {
            "total_requests": self.total_requests,
            "total_time": round(self.total_time, 2),
            "error_count": self.error_count,
            "rate_limit_hits": self.rate_limit_hits,
            "slow_requests": self.slow_requests,
            "average_response_time": round(self.average_response_time, 4),
            "peak_requests_per_minute": self.peak_requests_per_minute,
            "current_active_requests": self.current_active_requests,
            "error_rate": round(self.error_count / total_reqs * 100, 2),
            "slow_request_rate": round(self.slow_requests / total_reqs * 100, 2),
        }


class MiddlewareMonitor:
    """中间件监控器"""

    def __init__(self):
        self.metrics = MiddlewareMetrics()
        self.endpoint_metrics: Dict[str, MiddlewareMetrics] = defaultdict(
            MiddlewareMetrics
        )
        self.ip_metrics: Dict[str, MiddlewareMetrics] = defaultdict(MiddlewareMetrics)
        self._lock = asyncio.Lock()

    async def record_request(
        self, request: Request, response: Response, process_time: float
    ):
        """记录请求指标"""
        async with self._lock:
            endpoint = f"{request.method} {request.url.path}"
            client_ip = self._get_client_ip(request)

            is_error = response.status_code >= 400
            is_slow = process_time > 2.0

            # 全局指标
            self.metrics.add_request(process_time, is_error, is_slow)

            # 端点指标
            self.endpoint_metrics[endpoint].add_request(process_time, is_error, is_slow)

            # IP指标
            self.ip_metrics[client_ip].add_request(process_time, is_error, is_slow)

    async def record_rate_limit_hit(self, _client_id: str):
        """记录限流命中"""
        async with self._lock:
            self.metrics.rate_limit_hits += 1

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        return request.client.host if request.client else "unknown"

    def get_global_metrics(self) -> Dict[str, Any]:
        """获取全局指标"""
        return self.metrics.to_dict()

    def get_endpoint_metrics(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取端点指标（按请求数排序）"""
        sorted_endpoints = sorted(
            self.endpoint_metrics.items(),
            key=lambda x: x[1].total_requests,
            reverse=True,
        )[:limit]

        return [
            {"endpoint": endpoint, **metrics.to_dict()}
            for endpoint, metrics in sorted_endpoints
        ]

    def get_ip_metrics(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取IP指标（按请求数排序）"""
        sorted_ips = sorted(
            self.ip_metrics.items(), key=lambda x: x[1].total_requests, reverse=True
        )[:limit]

        return [{"ip": ip, **metrics.to_dict()} for ip, metrics in sorted_ips]

    def reset_metrics(self):
        """重置所有指标"""
        self.metrics = MiddlewareMetrics()
        self.endpoint_metrics.clear()
        self.ip_metrics.clear()


# 全局监控器实例
middleware_monitor = MiddlewareMonitor()


def get_middleware_monitor() -> MiddlewareMonitor:
    """获取中间件监控器实例"""
    return middleware_monitor


class RequestTracker:
    """请求追踪器"""

    def __init__(self):
        self.active_requests: Dict[str, Dict] = {}
        self._lock = asyncio.Lock()

    async def start_request(self, request_id: str, request: Request):
        """开始追踪请求"""
        async with self._lock:
            self.active_requests[request_id] = {
                "start_time": time.time(),
                "method": request.method,
                "url": str(request.url),
                "client_ip": self._get_client_ip(request),
                "user_agent": request.headers.get("user-agent", "unknown"),
            }
            middleware_monitor.metrics.current_active_requests = len(
                self.active_requests
            )

    async def end_request(self, request_id: str):
        """结束追踪请求"""
        async with self._lock:
            if request_id in self.active_requests:
                del self.active_requests[request_id]
                middleware_monitor.metrics.current_active_requests = len(
                    self.active_requests
                )

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        return request.client.host if request.client else "unknown"

    def get_active_requests(self) -> List[Dict]:
        """获取活跃请求列表"""
        current_time = time.time()
        return [
            {
                "request_id": req_id,
                "duration": round(current_time - req_info["start_time"], 2),
                **req_info,
            }
            for req_id, req_info in self.active_requests.items()
        ]


# 全局请求追踪器实例
request_tracker = RequestTracker()


def get_request_tracker() -> RequestTracker:
    """获取请求追踪器实例"""
    return request_tracker


def timing_middleware(func: Callable) -> Callable:
    """性能计时装饰器"""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            process_time = time.time() - start_time
            logger.debug(
                "中间件执行时间",
                middleware=func.__name__,
                process_time_ms=round(process_time * 1000, 2),
            )

    return wrapper


def safe_middleware(func: Callable) -> Callable:
    """安全中间件装饰器 - 捕获异常并记录"""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(
                "中间件执行异常", middleware=func.__name__, error=str(e), exc_info=True
            )
            # 重新抛出异常，让上层处理
            raise

    return wrapper


class MiddlewareConfig:
    """中间件配置管理"""

    def __init__(self):
        self.rate_limit_enabled = True
        self.rate_limit_requests = 100
        self.rate_limit_window = 60
        self.ip_whitelist_enabled = False
        self.ip_whitelist = []
        self.request_size_limit = 10 * 1024 * 1024  # 10MB
        self.slow_request_threshold = 2.0  # 秒
        self.security_headers_enabled = True

    def update_rate_limit(self, requests: int, window: int):
        """更新限流配置"""
        self.rate_limit_requests = requests
        self.rate_limit_window = window
        logger.info("限流配置已更新", requests=requests, window=window)

    def enable_ip_whitelist(self, whitelist: List[str]):
        """启用IP白名单"""
        self.ip_whitelist_enabled = True
        self.ip_whitelist = whitelist
        logger.info("IP白名单已启用", whitelist=whitelist)

    def disable_ip_whitelist(self):
        """禁用IP白名单"""
        self.ip_whitelist_enabled = False
        self.ip_whitelist = []
        logger.info("IP白名单已禁用")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "rate_limit_enabled": self.rate_limit_enabled,
            "rate_limit_requests": self.rate_limit_requests,
            "rate_limit_window": self.rate_limit_window,
            "ip_whitelist_enabled": self.ip_whitelist_enabled,
            "ip_whitelist": self.ip_whitelist,
            "request_size_limit": self.request_size_limit,
            "slow_request_threshold": self.slow_request_threshold,
            "security_headers_enabled": self.security_headers_enabled,
        }


# 全局中间件配置实例
middleware_config = MiddlewareConfig()


def get_middleware_config() -> MiddlewareConfig:
    """获取中间件配置实例"""
    return middleware_config
