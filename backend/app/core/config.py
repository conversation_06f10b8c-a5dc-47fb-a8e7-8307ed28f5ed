"""
柴管家应用配置模块
统一管理应用配置和环境变量
"""

from typing import List, Optional

from pydantic import validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基础配置
    app_name: str = "柴管家API"
    app_description: str = "多平台聚合智能客服系统后端API"
    app_version: str = "1.0.0"
    debug: bool = False
    environment: str = "development"

    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000

    # API配置
    api_v1_prefix: str = "/api/v1"
    api_v2_prefix: str = "/api/v2"
    docs_url: str = "/docs"
    redoc_url: str = "/redoc"

    # CORS配置
    cors_origins: str = "*"
    cors_credentials: bool = True
    cors_methods: str = "*"
    cors_headers: str = "*"

    # 数据库配置
    database_url: Optional[str] = None
    database_pool_size: int = 10
    database_pool_overflow: int = 20

    # Redis配置
    redis_url: Optional[str] = None
    redis_db: int = 0
    redis_password: Optional[str] = None

    # 消息队列配置
    rabbitmq_url: Optional[str] = None

    # Elasticsearch配置
    elasticsearch_url: Optional[str] = None
    elasticsearch_host: str = "localhost"
    elasticsearch_port: int = 9200
    elasticsearch_username: Optional[str] = None
    elasticsearch_password: Optional[str] = None
    elasticsearch_timeout: int = 30
    elasticsearch_max_retries: int = 3

    # MinIO对象存储配置
    minio_endpoint: str = "localhost:9000"
    minio_access_key: str = "chaiguanjia_admin"
    minio_secret_key: str = "chaiguanjia2024_secret"
    minio_bucket_name: str = "chaiguanjia-files"
    minio_secure: bool = False
    minio_region: str = "us-east-1"

    # 文件存储配置
    upload_folder: str = "/app/uploads"
    max_content_length: int = 104857600  # 100MB
    max_file_size: int = 104857600  # 100MB
    allowed_extensions: str = (
        "jpg,jpeg,png,gif,bmp,webp,svg,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv,zip,rar,7z,mp4,mp3,avi,mov"
    )

    # 图片处理配置
    image_quality: int = 85
    thumbnail_size: str = "200,200"
    max_image_dimension: int = 2048

    # 文件存储策略
    file_retention_days: int = 365

    # 安全配置
    secret_key: str = "your-secret-key-change-in-production"
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"

    # 日志配置
    log_level: str = "INFO"
    log_format: str = "json"

    # 文件上传配置
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_file_types: str = "image/jpeg,image/png,image/gif,application/pdf"

    # API限流配置
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # 秒

    def get_cors_origins(self) -> List[str]:
        """获取CORS origins列表"""
        if isinstance(self.cors_origins, str):
            return [i.strip() for i in self.cors_origins.split(",") if i.strip()]
        return ["*"]

    def get_cors_methods(self) -> List[str]:
        """获取CORS methods列表"""
        if isinstance(self.cors_methods, str):
            return [i.strip() for i in self.cors_methods.split(",") if i.strip()]
        return ["*"]

    def get_cors_headers(self) -> List[str]:
        """获取CORS headers列表"""
        if isinstance(self.cors_headers, str):
            return [i.strip() for i in self.cors_headers.split(",") if i.strip()]
        return ["*"]

    def get_allowed_file_types(self) -> List[str]:
        """获取允许的文件类型列表"""
        if isinstance(self.allowed_file_types, str):
            return [i.strip() for i in self.allowed_file_types.split(",") if i.strip()]
        return ["image/jpeg", "image/png", "image/gif", "application/pdf"]

    def get_allowed_extensions(self) -> List[str]:
        """获取允许的文件扩展名列表"""
        if isinstance(self.allowed_extensions, str):
            return [
                i.strip().lower()
                for i in self.allowed_extensions.split(",")
                if i.strip()
            ]
        return ["jpg", "jpeg", "png", "gif", "pdf"]

    def get_thumbnail_size(self) -> tuple:
        """获取缩略图尺寸"""
        if isinstance(self.thumbnail_size, str):
            try:
                width, height = map(int, self.thumbnail_size.split(","))
                return (width, height)
            except (ValueError, TypeError):
                return (200, 200)
        return (200, 200)

    def is_image_file(self, filename: str) -> bool:
        """检查是否为图片文件"""
        if not filename:
            return False
        ext = filename.rsplit(".", 1)[1].lower() if "." in filename else ""
        image_extensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"]
        return ext in image_extensions

    class Config:
        env_file = ".env"
        case_sensitive = False
        env_ignore_empty = True
        extra = "ignore"


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取应用配置"""
    return settings
