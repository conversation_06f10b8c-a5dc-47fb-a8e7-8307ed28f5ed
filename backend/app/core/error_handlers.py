"""
全局异常处理器
处理各种类型的异常，并返回标准化的错误响应
"""

import asyncio
import traceback
from typing import Optional

import structlog
from fastapi import Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from sqlalchemy.exc import (
    DatabaseError,
    IntegrityError,
    OperationalError,
    TimeoutError as SQLTimeoutError,
)
from starlette.exceptions import HTTPException as StarletteHTTPException

from .exceptions import (
    ERROR_CODE_TO_HTTP_STATUS,
    BaseAPIException,
    ErrorCode,
    ErrorSeverity,
    create_error_response,
    get_user_friendly_message,
    log_error_details,
    should_include_details,
)

logger = structlog.get_logger(__name__)


async def base_api_exception_handler(
    request: Request, exc: BaseAPIException
) -> JSONResponse:
    """处理自定义API异常"""

    # 记录错误日志
    log_error_details(exc, request, exc.error_code, exc.severity)

    # 创建错误响应
    error_response = create_error_response(
        error_code=exc.error_code,
        message=exc.message,
        status_code=exc.status_code,
        details=exc.details if should_include_details(request) else None,
        request=request,
        severity=exc.severity,
    )

    # 添加验证错误详情
    if hasattr(exc, "validation_errors") and exc.validation_errors:
        error_response.validation_errors = exc.validation_errors

    # 添加开发环境的调试信息
    if should_include_details(request):
        error_response.stack_trace = traceback.format_exc()
        error_response.trace_id = getattr(request.state, "request_id", None)

    # 添加特殊响应头
    headers = {}
    if hasattr(exc, "retry_after") and exc.retry_after:
        headers["Retry-After"] = str(exc.retry_after)

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.dict(exclude_none=True),
        headers=headers,
    )


async def http_exception_handler(
    request: Request, exc: StarletteHTTPException
) -> JSONResponse:
    """处理HTTP异常"""

    # 映射HTTP状态码到错误码
    error_code_mapping = {
        400: ErrorCode.BAD_REQUEST,
        401: ErrorCode.UNAUTHORIZED,
        403: ErrorCode.INSUFFICIENT_PERMISSIONS,
        404: ErrorCode.NOT_FOUND,
        405: ErrorCode.INVALID_OPERATION,
        409: ErrorCode.RESOURCE_CONFLICT,
        413: ErrorCode.REQUEST_TOO_LARGE,
        415: ErrorCode.UNSUPPORTED_MEDIA_TYPE,
        429: ErrorCode.TOO_MANY_REQUESTS,
        500: ErrorCode.INTERNAL_SERVER_ERROR,
        502: ErrorCode.EXTERNAL_SERVICE_ERROR,
        503: ErrorCode.SERVICE_UNAVAILABLE,
        504: ErrorCode.TIMEOUT_ERROR,
    }

    error_code = error_code_mapping.get(exc.status_code, ErrorCode.UNKNOWN_ERROR)

    # 确定错误严重程度
    if exc.status_code >= 500:
        severity = ErrorSeverity.HIGH
    elif exc.status_code >= 400:
        severity = ErrorSeverity.MEDIUM
    else:
        severity = ErrorSeverity.LOW

    # 记录错误日志
    log_error_details(exc, request, error_code, severity)

    # 获取用户友好的消息
    user_message = get_user_friendly_message(error_code)

    # 创建错误响应
    error_response = create_error_response(
        error_code=error_code,
        message=user_message,
        status_code=exc.status_code,
        details=str(exc.detail) if should_include_details(request) else None,
        request=request,
        severity=severity,
    )

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.dict(exclude_none=True),
    )


async def validation_exception_handler(
    request: Request, exc: RequestValidationError
) -> JSONResponse:
    """处理请求验证异常"""

    # 解析验证错误详情
    validation_errors = []
    for error in exc.errors():
        validation_errors.append(
            {
                "field": ".".join(str(loc) for loc in error["loc"]),
                "message": error["msg"],
                "type": error["type"],
                "input": error.get("input"),
            }
        )

    # 记录错误日志
    log_error_details(exc, request, ErrorCode.VALIDATION_ERROR, ErrorSeverity.LOW)

    # 创建错误响应
    error_response = create_error_response(
        error_code=ErrorCode.VALIDATION_ERROR,
        message="请求参数验证失败",
        status_code=status.HTTP_400_BAD_REQUEST,
        details="请检查请求参数格式和内容" if should_include_details(request) else None,
        request=request,
        severity=ErrorSeverity.LOW,
        validation_errors=validation_errors,
    )

    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=error_response.dict(exclude_none=True),
    )


async def database_exception_handler(
    request: Request, exc: DatabaseError
) -> JSONResponse:
    """处理数据库异常"""

    # 根据异常类型确定错误码
    if isinstance(exc, IntegrityError):
        error_code = ErrorCode.DATABASE_CONSTRAINT_ERROR
        user_message = "数据完整性约束错误"
        severity = ErrorSeverity.MEDIUM
        status_code = status.HTTP_400_BAD_REQUEST
    elif isinstance(exc, OperationalError):
        error_code = ErrorCode.DATABASE_CONNECTION_ERROR
        user_message = "数据库连接错误"
        severity = ErrorSeverity.HIGH
        status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    elif isinstance(exc, SQLTimeoutError):
        error_code = ErrorCode.DATABASE_TIMEOUT
        user_message = "数据库操作超时"
        severity = ErrorSeverity.MEDIUM
        status_code = status.HTTP_408_REQUEST_TIMEOUT
    else:
        error_code = ErrorCode.DATABASE_ERROR
        user_message = "数据库操作失败"
        severity = ErrorSeverity.HIGH
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

    # 记录错误日志
    log_error_details(exc, request, error_code, severity)

    # 创建错误响应（不暴露数据库内部错误信息）
    error_response = create_error_response(
        error_code=error_code,
        message=user_message,
        status_code=status_code,
        details=str(exc) if should_include_details(request) else None,
        request=request,
        severity=severity,
    )

    return JSONResponse(
        status_code=status_code,
        content=error_response.dict(exclude_none=True),
    )


async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """处理未捕获的通用异常"""

    # 确定错误类型和严重程度
    if isinstance(exc, (asyncio.TimeoutError, TimeoutError)):
        error_code = ErrorCode.TIMEOUT_ERROR
        severity = ErrorSeverity.MEDIUM
        status_code = status.HTTP_408_REQUEST_TIMEOUT
        user_message = "请求超时，请稍后重试"
    elif isinstance(exc, ConnectionError):
        error_code = ErrorCode.NETWORK_ERROR
        severity = ErrorSeverity.HIGH
        status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        user_message = "网络连接错误，请稍后重试"
    elif isinstance(exc, MemoryError):
        error_code = ErrorCode.INTERNAL_SERVER_ERROR
        severity = ErrorSeverity.CRITICAL
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        user_message = "系统资源不足，请稍后重试"
    elif isinstance(exc, PermissionError):
        error_code = ErrorCode.INSUFFICIENT_PERMISSIONS
        severity = ErrorSeverity.MEDIUM
        status_code = status.HTTP_403_FORBIDDEN
        user_message = "权限不足"
    elif isinstance(exc, FileNotFoundError):
        error_code = ErrorCode.FILE_NOT_FOUND
        severity = ErrorSeverity.LOW
        status_code = status.HTTP_404_NOT_FOUND
        user_message = "文件不存在"
    elif isinstance(exc, ValueError):
        error_code = ErrorCode.INVALID_PARAMETER
        severity = ErrorSeverity.LOW
        status_code = status.HTTP_400_BAD_REQUEST
        user_message = "参数值错误"
    else:
        error_code = ErrorCode.INTERNAL_SERVER_ERROR
        severity = ErrorSeverity.CRITICAL
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        user_message = "系统发生内部错误"

    # 记录错误日志
    log_error_details(exc, request, error_code, severity)

    # 创建错误响应
    error_response = create_error_response(
        error_code=error_code,
        message=user_message,
        status_code=status_code,
        details=str(exc) if should_include_details(request) else None,
        request=request,
        severity=severity,
    )

    # 添加开发环境的调试信息
    if should_include_details(request):
        error_response.stack_trace = traceback.format_exc()
        error_response.trace_id = getattr(request.state, "request_id", None)

    return JSONResponse(
        status_code=status_code,
        content=error_response.dict(exclude_none=True),
    )


def setup_error_handlers(app):
    """设置全局错误处理器"""

    # 自定义API异常
    app.add_exception_handler(BaseAPIException, base_api_exception_handler)

    # HTTP异常
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)

    # 请求验证异常
    app.add_exception_handler(RequestValidationError, validation_exception_handler)

    # 数据库异常
    app.add_exception_handler(DatabaseError, database_exception_handler)
    app.add_exception_handler(IntegrityError, database_exception_handler)
    app.add_exception_handler(OperationalError, database_exception_handler)
    app.add_exception_handler(SQLTimeoutError, database_exception_handler)

    # 通用异常（兜底处理）
    app.add_exception_handler(Exception, generic_exception_handler)

    logger.info("全局错误处理器设置完成")


def raise_for_error_code(
    error_code: ErrorCode,
    message: Optional[str] = None,
    details: Optional[str] = None,
    **kwargs,
):
    """根据错误码抛出相应的异常"""

    status_code = ERROR_CODE_TO_HTTP_STATUS.get(
        error_code, status.HTTP_500_INTERNAL_SERVER_ERROR
    )

    user_message = message or get_user_friendly_message(error_code)

    # 根据错误码确定严重程度
    if error_code.value.startswith("E1"):  # 通用错误
        severity = ErrorSeverity.HIGH
    elif error_code.value.startswith("E2"):  # 请求错误
        severity = ErrorSeverity.LOW
    elif error_code.value.startswith("E3"):  # 认证授权错误
        severity = ErrorSeverity.MEDIUM
    elif error_code.value.startswith("E4"):  # 资源错误
        severity = ErrorSeverity.LOW
    elif error_code.value.startswith("E5"):  # 业务逻辑错误
        severity = ErrorSeverity.MEDIUM
    elif error_code.value.startswith("E6"):  # 限流错误
        severity = ErrorSeverity.LOW
    elif error_code.value.startswith("E7"):  # 数据库错误
        severity = ErrorSeverity.HIGH
    elif error_code.value.startswith("E8"):  # 外部服务错误
        severity = ErrorSeverity.HIGH
    elif error_code.value.startswith("E9"):  # 网络错误
        severity = ErrorSeverity.MEDIUM
    else:
        severity = ErrorSeverity.MEDIUM

    raise BaseAPIException(
        message=user_message,
        error_code=error_code,
        status_code=status_code,
        details=details,
        severity=severity,
        **kwargs,
    )


# 便捷的异常抛出函数
def raise_not_found(resource_type: str = "resource", resource_id: Optional[str] = None):
    """抛出资源未找到异常"""
    from .exceptions import ResourceNotFoundException

    message = f"{resource_type}不存在"
    if resource_id:
        message += f"（ID: {resource_id}）"

    raise ResourceNotFoundException(
        message=message, resource_type=resource_type, resource_id=resource_id
    )


def raise_validation_error(field: str, message: str):
    """抛出验证错误异常"""
    from .exceptions import ValidationException

    validation_errors = [
        {
            "field": field,
            "message": message,
            "type": "value_error",
        }
    ]

    raise ValidationException(
        message=f"字段 {field} 验证失败: {message}", validation_errors=validation_errors
    )


def raise_business_error(
    message: str, error_code: ErrorCode = ErrorCode.BUSINESS_LOGIC_ERROR
):
    """抛出业务逻辑错误异常"""
    from .exceptions import BusinessLogicException

    raise BusinessLogicException(message=message, error_code=error_code)


def raise_unauthorized(message: str = "认证失败"):
    """抛出认证异常"""
    from .exceptions import AuthenticationException

    raise AuthenticationException(message=message)


def raise_forbidden(message: str = "权限不足"):
    """抛出授权异常"""
    from .exceptions import AuthorizationException

    raise AuthorizationException(message=message)


def raise_rate_limit_exceeded(retry_after: Optional[int] = None):
    """抛出限流异常"""
    from .exceptions import RateLimitException

    raise RateLimitException(retry_after=retry_after)
