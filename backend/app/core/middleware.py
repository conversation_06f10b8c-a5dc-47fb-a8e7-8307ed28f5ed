"""
FastAPI中间件模块
提供请求日志、性能监控、错误处理、限流等中间件
"""

import time
import uuid
from typing import Callable, Optional

import structlog
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from ..shared.cache import get_redis
from .config import get_settings
from .error_handlers import log_error_details
from .exceptions import ErrorCode, ErrorSeverity
from .middleware_utils import get_middleware_monitor, get_request_tracker

logger = structlog.get_logger()


class RateLimitMiddleware(BaseHTTPMiddleware):
    """API限流中间件"""

    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        self.max_requests = self.settings.rate_limit_requests
        self.window_size = self.settings.rate_limit_window

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 获取客户端标识
        client_id = self._get_client_identifier(request)

        # 获取Redis客户端
        try:
            redis_client = await get_redis()
        except Exception as e:
            logger.warning("Redis连接失败，跳过限流检查", error=str(e))
            return await call_next(request)

        # 检查限流
        try:
            is_allowed = await self._check_rate_limit(redis_client, client_id)
            if not is_allowed:
                # 记录限流命中
                monitor = get_middleware_monitor()
                await monitor.record_rate_limit_hit(client_id)

                logger.warning(
                    "API请求被限流",
                    client_id=client_id,
                    max_requests=self.max_requests,
                    window_size=self.window_size,
                    url=str(request.url),
                    method=request.method,
                )
                message = (
                    f"API请求频率超限，每{self.window_size}秒最多"
                    f"{self.max_requests}次请求"
                )
                return JSONResponse(
                    status_code=429,
                    content={
                        "error": "Too Many Requests",
                        "message": message,
                        "retry_after": self.window_size,
                    },
                    headers={"Retry-After": str(self.window_size)},
                )
        except Exception as e:
            logger.error("限流检查失败", error=str(e))
            # 限流检查失败时，允许请求通过但记录错误

        return await call_next(request)

    def _get_client_identifier(self, request: Request) -> str:
        """获取客户端唯一标识"""
        # 优先使用认证用户ID
        if hasattr(request.state, "user_id") and request.state.user_id:
            return f"user:{request.state.user_id}"

        # 获取真实客户端IP
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        else:
            client_ip = request.client.host if request.client else "unknown"

        return f"ip:{client_ip}"

    async def _check_rate_limit(self, redis_client, client_id: str) -> bool:
        """检查限流状态"""
        current_time = int(time.time())
        window_start = current_time - self.window_size
        rate_limit_key = f"rate_limit:{client_id}"

        # 使用Redis管道提高性能
        async with redis_client.pipeline() as pipe:
            # 移除过期的请求记录
            pipe.zremrangebyscore(rate_limit_key, 0, window_start)
            # 获取当前窗口内的请求数
            pipe.zcard(rate_limit_key)
            # 添加当前请求
            pipe.zadd(rate_limit_key, {str(current_time): current_time})
            # 设置过期时间
            pipe.expire(rate_limit_key, self.window_size)

            results = await pipe.execute()
            current_requests = results[1]

            # 检查是否超过限制
            return current_requests < self.max_requests


class IPWhitelistMiddleware(BaseHTTPMiddleware):
    """IP白名单中间件"""

    def __init__(self, app, whitelist: Optional[list] = None):
        super().__init__(app)
        self.whitelist = whitelist or []

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        if not self.whitelist:
            return await call_next(request)

        # 获取真实客户端IP
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        else:
            client_ip = request.client.host if request.client else "unknown"

        # 检查是否在白名单中
        if client_ip not in self.whitelist:
            logger.warning(
                "IP访问被拒绝",
                client_ip=client_ip,
                url=str(request.url),
                method=request.method,
            )
            return JSONResponse(
                status_code=403,
                content={"error": "Forbidden", "message": "IP地址未在访问白名单中"},
            )

        return await call_next(request)


class RequestSizeMiddleware(BaseHTTPMiddleware):
    """请求大小限制中间件"""

    def __init__(self, app, max_size: int = 10 * 1024 * 1024):  # 默认10MB
        super().__init__(app)
        self.max_size = max_size

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 检查Content-Length头
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                size = int(content_length)
                if size > self.max_size:
                    logger.warning(
                        "请求体过大被拒绝",
                        content_length=size,
                        max_size=self.max_size,
                        url=str(request.url),
                        method=request.method,
                    )
                    message = f"请求体大小超过限制，最大允许{self.max_size}字节"
                    return JSONResponse(
                        status_code=413,
                        content={
                            "error": "Request Entity Too Large",
                            "message": message,
                        },
                    )
            except ValueError:
                pass  # 无效的Content-Length，让后续处理

        return await call_next(request)


class ResponseTimeMiddleware(BaseHTTPMiddleware):
    """响应时间中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()

        response = await call_next(request)

        process_time = time.time() - start_time

        # 添加响应时间头
        response.headers["X-Process-Time"] = f"{process_time:.4f}"

        # 记录慢请求
        if process_time > 2.0:  # 超过2秒的请求
            logger.warning(
                "慢请求检测",
                method=request.method,
                url=str(request.url),
                process_time_seconds=process_time,
                status_code=response.status_code,
            )

        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())

        # 记录请求开始
        start_time = time.time()

        # 获取客户端信息
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")

        # 获取监控器
        monitor = get_middleware_monitor()
        tracker = get_request_tracker()

        # 开始追踪请求
        await tracker.start_request(request_id, request)

        logger.info(
            "请求开始",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            client_ip=client_ip,
            user_agent=user_agent,
        )

        # 将请求ID添加到请求上下文
        request.state.request_id = request_id

        try:
            # 处理请求
            response = await call_next(request)
        except Exception as e:
            # 结束请求追踪
            await tracker.end_request(request_id)

            # 记录异常
            process_time = time.time() - start_time
            logger.error(
                "请求异常",
                request_id=request_id,
                method=request.method,
                url=str(request.url),
                error=str(e),
                process_time_ms=round(process_time * 1000, 2),
                client_ip=client_ip,
                exc_info=True,
            )
            raise

        # 结束请求追踪
        await tracker.end_request(request_id)

        # 计算处理时间
        process_time = time.time() - start_time

        # 记录到监控器
        await monitor.record_request(request, response, process_time)

        # 记录请求完成
        logger.info(
            "请求完成",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            process_time_ms=round(process_time * 1000, 2),
            client_ip=client_ip,
        )

        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = str(round(process_time * 1000, 2))

        return response


class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()

        # 处理请求
        response = await call_next(request)

        # 计算性能指标
        process_time = time.time() - start_time

        # 如果处理时间过长，记录警告
        if process_time > 1.0:  # 超过1秒
            logger.warning(
                "请求处理时间过长",
                method=request.method,
                url=str(request.url),
                process_time_seconds=process_time,
                status_code=response.status_code,
            )

        # 记录性能指标
        logger.debug(
            "性能指标",
            method=request.method,
            url=str(request.url),
            process_time_ms=round(process_time * 1000, 2),
            status_code=response.status_code,
        )

        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)

        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

        # 在生产环境添加更严格的安全头
        settings = get_settings()
        if not settings.debug:
            response.headers["Strict-Transport-Security"] = (
                "max-age=31536000; includeSubDomains"
            )
            response.headers["Content-Security-Policy"] = "default-src 'self'"

        return response


def setup_middleware(app: FastAPI) -> None:
    """设置所有中间件"""
    settings = get_settings()

    # 1. 安全头中间件（最外层）
    app.add_middleware(SecurityHeadersMiddleware)

    # 2. 可信主机中间件
    if not settings.debug:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", "*.yourdomain.com"],
        )

    # 3. IP白名单中间件（可选）
    # 如果需要IP白名单，可以在这里启用
    # app.add_middleware(IPWhitelistMiddleware, whitelist=["127.0.0.1", "***********/24"])

    # 4. 请求大小限制中间件
    app.add_middleware(RequestSizeMiddleware, max_size=settings.max_file_size)

    # 5. 限流中间件
    app.add_middleware(RateLimitMiddleware)

    # 6. CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.get_cors_origins(),
        allow_credentials=settings.cors_credentials,
        allow_methods=settings.get_cors_methods(),
        allow_headers=settings.get_cors_headers(),
    )

    # 7. 响应时间中间件
    app.add_middleware(ResponseTimeMiddleware)

    # 8. 性能监控中间件
    app.add_middleware(PerformanceMonitoringMiddleware)

    # 9. 请求日志中间件（最内层）
    app.add_middleware(RequestLoggingMiddleware)

    logger.info(
        "中间件设置完成",
        rate_limit_requests=settings.rate_limit_requests,
        rate_limit_window=settings.rate_limit_window,
        max_file_size=settings.max_file_size,
    )
