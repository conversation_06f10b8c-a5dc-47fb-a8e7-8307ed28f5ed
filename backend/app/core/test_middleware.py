"""
中间件系统测试模块
测试各种中间件的功能和性能
"""

import asyncio
import time
from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi import FastAPI, Request
from fastapi.testclient import TestClient

from .config import get_settings
from .middleware import (
    PerformanceMonitoringMiddleware,
    RateLimitMiddleware,
    RequestLoggingMiddleware,
    RequestSizeMiddleware,
    ResponseTimeMiddleware,
    SecurityHeadersMiddleware,
    setup_middleware,
)
from .middleware_utils import MiddlewareMonitor, RequestTracker


class TestRateLimitMiddleware:
    """限流中间件测试"""

    def test_rate_limit_initialization(self):
        """测试限流中间件初始化"""
        app = FastAPI()
        middleware = RateLimitMiddleware(app)

        assert middleware.max_requests == get_settings().rate_limit_requests
        assert middleware.window_size == get_settings().rate_limit_window

    @pytest.mark.asyncio
    async def test_client_identifier_with_user(self):
        """测试客户端标识获取 - 有用户ID"""
        app = FastAPI()
        middleware = RateLimitMiddleware(app)

        # 模拟带用户ID的请求
        request = Mock(spec=Request)
        request.state.user_id = "user123"
        request.client.host = "***********"
        request.headers = {}

        client_id = middleware._get_client_identifier(request)
        assert client_id == "user:user123"

    @pytest.mark.asyncio
    async def test_client_identifier_with_ip(self):
        """测试客户端标识获取 - 仅IP地址"""
        app = FastAPI()
        middleware = RateLimitMiddleware(app)

        # 模拟仅有IP的请求
        request = Mock(spec=Request)
        request.state = Mock()
        request.state.user_id = None
        request.client.host = "***********"
        request.headers = {}

        client_id = middleware._get_client_identifier(request)
        assert client_id == "ip:***********"

    @pytest.mark.asyncio
    async def test_client_identifier_with_forwarded_ip(self):
        """测试客户端标识获取 - X-Forwarded-For"""
        app = FastAPI()
        middleware = RateLimitMiddleware(app)

        # 模拟带X-Forwarded-For的请求
        request = Mock(spec=Request)
        request.state = Mock()
        request.state.user_id = None
        request.client.host = "***********"
        request.headers = {"x-forwarded-for": "***********, ***********"}

        client_id = middleware._get_client_identifier(request)
        assert client_id == "ip:***********"


class TestRequestLoggingMiddleware:
    """请求日志中间件测试"""

    @pytest.mark.asyncio
    async def test_request_logging_basic(self):
        """测试基本请求日志功能"""
        app = FastAPI()
        middleware = RequestLoggingMiddleware(app)

        # 模拟请求
        request = Mock(spec=Request)
        request.method = "GET"
        request.url = Mock()
        request.url.__str__ = Mock(return_value="http://test.com/api")
        request.client.host = "***********"
        request.headers = {"user-agent": "test-client"}
        request.state = Mock()

        # 模拟响应
        response = Mock()
        response.status_code = 200
        response.headers = {}

        # 模拟call_next
        async def mock_call_next(req):
            return response

        with patch(
            "uuid.uuid4",
            return_value=Mock(spec=str, __str__=Mock(return_value="test-request-id")),
        ):
            result = await middleware.dispatch(request, mock_call_next)

        assert result == response
        assert "X-Request-ID" in response.headers
        assert "X-Process-Time" in response.headers


class TestPerformanceMonitoringMiddleware:
    """性能监控中间件测试"""

    @pytest.mark.asyncio
    async def test_performance_monitoring(self):
        """测试性能监控功能"""
        app = FastAPI()
        middleware = PerformanceMonitoringMiddleware(app)

        # 模拟请求
        request = Mock(spec=Request)
        request.method = "GET"
        request.url = Mock()
        request.url.__str__ = Mock(return_value="http://test.com/api")

        # 模拟响应
        response = Mock()
        response.status_code = 200

        # 模拟慢请求
        async def slow_call_next(req):
            await asyncio.sleep(0.1)  # 模拟处理时间
            return response

        result = await middleware.dispatch(request, slow_call_next)
        assert result == response


class TestSecurityHeadersMiddleware:
    """安全头中间件测试"""

    @pytest.mark.asyncio
    async def test_security_headers_added(self):
        """测试安全头添加"""
        app = FastAPI()
        middleware = SecurityHeadersMiddleware(app)

        # 模拟请求
        request = Mock(spec=Request)

        # 模拟响应
        response = Mock()
        response.headers = {}

        async def mock_call_next(req):
            return response

        result = await middleware.dispatch(request, mock_call_next)

        # 检查安全头
        assert "X-Content-Type-Options" in response.headers
        assert "X-Frame-Options" in response.headers
        assert "X-XSS-Protection" in response.headers
        assert "Referrer-Policy" in response.headers


class TestRequestSizeMiddleware:
    """请求大小限制中间件测试"""

    @pytest.mark.asyncio
    async def test_request_size_allowed(self):
        """测试允许的请求大小"""
        app = FastAPI()
        middleware = RequestSizeMiddleware(app, max_size=1000)

        # 模拟小请求
        request = Mock(spec=Request)
        request.headers = {"content-length": "500"}
        request.method = "POST"
        request.url = Mock()
        request.url.__str__ = Mock(return_value="http://test.com/api")

        response = Mock()

        async def mock_call_next(req):
            return response

        result = await middleware.dispatch(request, mock_call_next)
        assert result == response

    @pytest.mark.asyncio
    async def test_request_size_rejected(self):
        """测试拒绝的请求大小"""
        app = FastAPI()
        middleware = RequestSizeMiddleware(app, max_size=1000)

        # 模拟大请求
        request = Mock(spec=Request)
        request.headers = {"content-length": "2000"}
        request.method = "POST"
        request.url = Mock()
        request.url.__str__ = Mock(return_value="http://test.com/api")

        async def mock_call_next(req):
            return Mock()

        result = await middleware.dispatch(request, mock_call_next)

        # 应该返回413错误
        assert result.status_code == 413


class TestResponseTimeMiddleware:
    """响应时间中间件测试"""

    @pytest.mark.asyncio
    async def test_response_time_header(self):
        """测试响应时间头添加"""
        app = FastAPI()
        middleware = ResponseTimeMiddleware(app)

        request = Mock(spec=Request)
        response = Mock()
        response.headers = {}

        async def mock_call_next(req):
            await asyncio.sleep(0.1)  # 模拟处理时间
            return response

        result = await middleware.dispatch(request, mock_call_next)

        assert "X-Process-Time" in response.headers
        # 检查处理时间是否合理
        process_time = float(response.headers["X-Process-Time"])
        assert 0.09 <= process_time <= 0.15  # 允许一些误差


class TestMiddlewareIntegration:
    """中间件集成测试"""

    def test_middleware_setup(self):
        """测试中间件设置"""
        app = FastAPI()

        # 测试设置函数不抛出异常
        try:
            setup_middleware(app)
            assert True
        except Exception as e:
            pytest.fail(f"中间件设置失败: {e}")

    def test_middleware_order(self):
        """测试中间件顺序"""
        app = FastAPI()
        setup_middleware(app)

        # 检查是否有中间件被添加
        assert len(app.user_middleware) > 0

        # 检查特定中间件是否存在
        middleware_classes = [mw.cls.__name__ for mw in app.user_middleware]

        expected_middlewares = [
            "SecurityHeadersMiddleware",
            "RequestSizeMiddleware",
            "RateLimitMiddleware",
            "CORSMiddleware",
            "ResponseTimeMiddleware",
            "PerformanceMonitoringMiddleware",
            "RequestLoggingMiddleware",
        ]

        for expected in expected_middlewares:
            assert expected in middleware_classes


class TestMiddlewareMonitor:
    """中间件监控器测试"""

    def test_middleware_monitor_initialization(self):
        """测试监控器初始化"""
        monitor = MiddlewareMonitor()

        assert monitor.metrics.total_requests == 0
        assert monitor.metrics.error_count == 0
        assert monitor.metrics.rate_limit_hits == 0

    @pytest.mark.asyncio
    async def test_record_request(self):
        """测试请求记录"""
        monitor = MiddlewareMonitor()

        # 模拟请求和响应
        request = Mock(spec=Request)
        request.method = "GET"
        request.url = Mock()
        request.url.path = "/api/test"
        request.client.host = "***********"
        request.headers = {}

        response = Mock()
        response.status_code = 200

        await monitor.record_request(request, response, 0.5)

        assert monitor.metrics.total_requests == 1
        assert monitor.metrics.error_count == 0
        assert monitor.metrics.slow_requests == 0

    @pytest.mark.asyncio
    async def test_record_error_request(self):
        """测试错误请求记录"""
        monitor = MiddlewareMonitor()

        request = Mock(spec=Request)
        request.method = "GET"
        request.url = Mock()
        request.url.path = "/api/test"
        request.client.host = "***********"
        request.headers = {}

        response = Mock()
        response.status_code = 500

        await monitor.record_request(request, response, 0.5)

        assert monitor.metrics.total_requests == 1
        assert monitor.metrics.error_count == 1

    @pytest.mark.asyncio
    async def test_record_slow_request(self):
        """测试慢请求记录"""
        monitor = MiddlewareMonitor()

        request = Mock(spec=Request)
        request.method = "GET"
        request.url = Mock()
        request.url.path = "/api/test"
        request.client.host = "***********"
        request.headers = {}

        response = Mock()
        response.status_code = 200

        await monitor.record_request(request, response, 3.0)  # 慢请求

        assert monitor.metrics.total_requests == 1
        assert monitor.metrics.slow_requests == 1

    def test_get_metrics(self):
        """测试指标获取"""
        monitor = MiddlewareMonitor()
        monitor.metrics.total_requests = 100
        monitor.metrics.error_count = 5

        metrics = monitor.get_global_metrics()

        assert metrics["total_requests"] == 100
        assert metrics["error_count"] == 5
        assert metrics["error_rate"] == 5.0


class TestRequestTracker:
    """请求追踪器测试"""

    def test_request_tracker_initialization(self):
        """测试请求追踪器初始化"""
        tracker = RequestTracker()

        assert len(tracker.active_requests) == 0

    @pytest.mark.asyncio
    async def test_track_request(self):
        """测试请求追踪"""
        tracker = RequestTracker()

        request = Mock(spec=Request)
        request.method = "GET"
        request.url = Mock()
        request.url.__str__ = Mock(return_value="http://test.com/api")
        request.client.host = "***********"
        request.headers = {"user-agent": "test-client"}

        await tracker.start_request("test-id", request)

        assert len(tracker.active_requests) == 1
        assert "test-id" in tracker.active_requests

        await tracker.end_request("test-id")

        assert len(tracker.active_requests) == 0

    def test_get_active_requests(self):
        """测试获取活跃请求"""
        tracker = RequestTracker()

        # 手动添加一个活跃请求
        tracker.active_requests["test-id"] = {
            "start_time": time.time() - 1.0,
            "method": "GET",
            "url": "http://test.com/api",
            "client_ip": "***********",
            "user_agent": "test-client",
        }

        active = tracker.get_active_requests()

        assert len(active) == 1
        assert active[0]["request_id"] == "test-id"
        assert active[0]["duration"] >= 1.0


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
