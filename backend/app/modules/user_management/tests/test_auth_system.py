import os
# user_management/tests/test_auth_system.py - 认证系统测试用例
"""
认证系统集成测试

测试内容：
1. 用户注册和登录流程
2. JWT令牌生成和验证
3. 权限和角色验证
4. API接口安全测试
5. 边界条件和异常处理
"""

from datetime import datetime, timedelta

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from ....database import get_db_session
from ....main import app
from ....models.auth_models import AuthingUser, Permission, Role
from ..services.auth_service import AuthService
from ..services.jwt_service import JWTService
from ..services.permission_service import PermissionService
from ..services.role_service import RoleService
from ..services.user_service import UserService

# 测试数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """测试数据库依赖覆盖"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db_session] = override_get_db
client = TestClient(app)


class TestAuthenticationSystem:
    """认证系统测试类"""

    def setup_method(self):
        """测试方法设置"""
        # 创建测试数据库表
        from ....models.base import Base

        Base.metadata.create_all(bind=engine)

        self.db = TestingSessionLocal()
        self.auth_service = AuthService()
        self.jwt_service = JWTService()
        self.user_service = UserService()
        self.role_service = RoleService()
        self.permission_service = PermissionService()

        # 初始化测试数据
        self._init_test_data()

    def teardown_method(self):
        """测试方法清理"""
        self.db.close()
        # 清理测试数据库
        from ....models.base import Base

        Base.metadata.drop_all(bind=engine)

    def _init_test_data(self):
        """初始化测试数据"""
        # 创建默认角色和权限
        self.role_service.create_default_roles(self.db)
        self.permission_service.create_default_permissions(self.db)
        self.permission_service.assign_default_permissions_to_roles(self.db)

        # 创建测试用户
        self.test_user_data = {
            "email": "<EMAIL>",
            "name": "测试用户",
            "authing_user_id": "test_authing_id",
            "is_active": True,
            "is_email_verified": True,
        }
        self.test_user = self.user_service.create_user(self.db, self.test_user_data)

        # 为测试用户分配角色
        user_role = self.role_service.get_role_by_code(self.db, "user")
        if user_role:
            self.role_service.assign_role_to_user(
                self.db, self.test_user.id, user_role.id
            )


class TestJWTService(TestAuthenticationSystem):
    """JWT服务测试"""

    def test_create_access_token(self):
        """测试创建访问令牌"""
        user_data = {
            "email": self.test_user.email,
            "name": self.test_user.name,
            "roles": ["user"],
            "permissions": ["profile:read"],
        }

        token = self.jwt_service.create_access_token(
            user_id=self.test_user.id, user_data=user_data
        )

        assert token is not None
        assert isinstance(token, str)

        # 验证令牌
        is_valid, payload = self.jwt_service.verify_access_token(token)
        assert is_valid
        assert payload["sub"] == str(self.test_user.id)
        assert payload["type"] == "access"
        assert payload["email"] == self.test_user.email

    def test_create_refresh_token(self):
        """测试创建刷新令牌"""
        token = self.jwt_service.create_refresh_token(
            user_id=self.test_user.id, session_id="test_session"
        )

        assert token is not None
        assert isinstance(token, str)

        # 验证令牌
        is_valid, payload = self.jwt_service.verify_refresh_token(token)
        assert is_valid
        assert payload["sub"] == str(self.test_user.id)
        assert payload["type"] == "refresh"
        assert payload["session_id"] == "test_session"

    def test_token_expiration(self):
        """测试令牌过期"""
        # 创建过期时间很短的令牌
        short_expiry = timedelta(microseconds=1)
        token = self.jwt_service.create_access_token(
            user_id=self.test_user.id, expires_delta=short_expiry
        )

        # 等待令牌过期
        import time

        time.sleep(0.001)

        # 验证过期令牌
        is_valid, payload = self.jwt_service.verify_access_token(token)
        assert not is_valid
        assert "令牌已过期" in payload.get("error", "")

    def test_invalid_token(self):
        """测试无效令牌"""
        invalid_token = os.getenv("TEST_INVALID_TOKEN", "invalid.token.string")

        is_valid, payload = self.jwt_service.verify_access_token(invalid_token)
        assert not is_valid
        assert "令牌无效" in payload.get("error", "")

    def test_token_revocation(self):
        """测试令牌吊销"""
        token = self.jwt_service.create_access_token(user_id=self.test_user.id)

        # 验证令牌有效
        is_valid, _ = self.jwt_service.verify_access_token(token)
        assert is_valid

        # 吊销令牌
        success = self.jwt_service.revoke_token(token)
        assert success

        # 验证令牌被吊销
        is_valid, payload = self.jwt_service.verify_access_token(token)
        assert not is_valid
        assert "令牌已被吊销" in payload.get("error", "")


class TestUserService(TestAuthenticationSystem):
    """用户服务测试"""

    def test_create_user(self):
        """测试创建用户"""
        user_data = {
            "email": "<EMAIL>",
            "name": "新用户",
            "authing_user_id": "new_authing_id",
        }

        user = self.user_service.create_user(self.db, user_data)
        assert user is not None
        assert user.email == user_data["email"]
        assert user.name == user_data["name"]
        assert user.authing_user_id == user_data["authing_user_id"]

    def test_get_user_by_email(self):
        """测试根据邮箱获取用户"""
        user = self.user_service.get_user_by_email(self.db, self.test_user.email)
        assert user is not None
        assert user.id == self.test_user.id
        assert user.email == self.test_user.email

    def test_update_user(self):
        """测试更新用户"""
        updates = {"name": "更新后的名称", "phone": "13800138000"}

        updated_user = self.user_service.update_user(
            self.db, self.test_user.id, updates
        )
        assert updated_user is not None
        assert updated_user.name == updates["name"]
        assert updated_user.phone == updates["phone"]

    def test_block_user(self):
        """测试封禁用户"""
        success = self.user_service.block_user(self.db, self.test_user.id, "测试封禁")
        assert success

        # 验证用户被封禁
        user = self.user_service.get_user_by_id(self.db, self.test_user.id)
        assert user.is_blocked
        assert user.blocked_at is not None


class TestRolePermissionSystem(TestAuthenticationSystem):
    """角色权限系统测试"""

    def test_create_role(self):
        """测试创建角色"""
        from ..models.schemas import CreateRoleRequest

        request = CreateRoleRequest(
            code="test_role",
            name="测试角色",
            description="这是一个测试角色",
            permission_ids=[],
        )

        role = self.role_service.create_role(self.db, request)
        assert role is not None
        assert role.code == request.code
        assert role.name == request.name

    def test_create_permission(self):
        """测试创建权限"""
        permission = self.permission_service.create_permission(
            db=self.db,
            code="test:action",
            name="测试权限",
            resource="test",
            action="action",
            description="这是一个测试权限",
        )

        assert permission is not None
        assert permission.code == "test:action"
        assert permission.resource == "test"
        assert permission.action == "action"

    def test_assign_role_to_user(self):
        """测试为用户分配角色"""
        # 创建测试角色
        from ..models.schemas import CreateRoleRequest

        request = CreateRoleRequest(code="agent", name="客服代理", permission_ids=[])
        role = self.role_service.create_role(self.db, request)

        # 分配角色给用户
        success = self.role_service.assign_role_to_user(
            self.db, self.test_user.id, role.id
        )
        assert success

        # 验证用户有该角色
        has_role = self.role_service.check_user_has_role(
            self.db, self.test_user.id, "agent"
        )
        assert has_role

    def test_user_permissions(self):
        """测试用户权限"""
        # 获取用户权限
        permissions = self.permission_service.get_user_permissions(
            self.db, self.test_user.id
        )

        # 用户应该有基础权限
        permission_codes = [p.code for p in permissions]
        assert "profile:read" in permission_codes
        assert "profile:update" in permission_codes


class TestAPIEndpoints(TestAuthenticationSystem):
    """API端点测试"""

    def test_register_endpoint(self):
        """测试注册接口"""
        register_data = {
            "email": "<EMAIL>",
            "name": "API测试用户",
            "password": "TestPassword123",
            "username": "apitest",
        }

        # 模拟注册请求（需要mock Authing服务）
        # 这里只测试数据验证
        from ..models.schemas import RegisterRequest

        request = RegisterRequest(**register_data)
        assert request.email == register_data["email"]
        assert request.name == register_data["name"]

    def test_login_endpoint(self):
        """测试登录接口"""
        login_data = {"email": "<EMAIL>", "password": "TestPassword123"}

        # 模拟登录请求
        from ..models.schemas import LoginRequest

        request = LoginRequest(**login_data)
        assert request.email == login_data["email"]
        assert request.password == login_data["password"]

    def test_token_validation(self):
        """测试令牌验证"""
        # 创建有效令牌
        user_data = {
            "email": self.test_user.email,
            "name": self.test_user.name,
            "roles": self.test_user.get_roles(),
            "permissions": self.test_user.get_permissions(),
        }

        access_token = self.jwt_service.create_access_token(
            user_id=self.test_user.id, user_data=user_data
        )

        # 验证令牌
        is_valid, payload = self.jwt_service.verify_access_token(access_token)
        assert is_valid
        assert int(payload["sub"]) == self.test_user.id


class TestSecurityFeatures(TestAuthenticationSystem):
    """安全特性测试"""

    def test_password_validation(self):
        """测试密码验证"""
        from ..models.schemas import RegisterRequest

        # 测试弱密码
        with pytest.raises(ValueError):
            RegisterRequest(
                email="<EMAIL>", name="Test User", password="123"  # 太短
            )

        with pytest.raises(ValueError):
            RegisterRequest(
                email="<EMAIL>",
                name="Test User",
                password=os.getenv("TEST_PASSWORD_WEAK", "onlyletters"),  # 只有字母
            )

        # 测试强密码
        request = RegisterRequest(
            email="<EMAIL>", name="Test User", password=os.getenv("TEST_PASSWORD_STRONG", "StrongPassword123")
        )
        assert request.password == "StrongPassword123"

    def test_email_validation(self):
        """测试邮箱验证"""
        from ..models.schemas import RegisterRequest

        # 测试无效邮箱
        with pytest.raises(ValueError):
            RegisterRequest(
                email="invalid-email", name="Test User", password=os.getenv("TEST_PASSWORD_DEFAULT", "Password123")
            )

        # 测试有效邮箱
        request = RegisterRequest(
            email="<EMAIL>", name="Test User", password=os.getenv("TEST_PASSWORD_DEFAULT", "Password123")
        )
        assert request.email == "<EMAIL>"

    def test_token_tampering(self):
        """测试令牌篡改检测"""
        # 创建有效令牌
        token = self.jwt_service.create_access_token(user_id=self.test_user.id)

        # 篡改令牌
        tampered_token = token[:-10] + "tampered123"

        # 验证篡改后的令牌
        is_valid, payload = self.jwt_service.verify_access_token(tampered_token)
        assert not is_valid
        assert "令牌无效" in payload.get("error", "")

    def test_permission_boundary(self):
        """测试权限边界"""
        # 用户不应该有管理员权限
        has_admin_permission = self.permission_service.check_user_permission(
            self.db, self.test_user.id, "user:delete"
        )
        assert not has_admin_permission

        # 用户应该有基础权限
        has_basic_permission = self.permission_service.check_user_permission(
            self.db, self.test_user.id, "profile:read"
        )
        assert has_basic_permission


class TestErrorHandling(TestAuthenticationSystem):
    """错误处理测试"""

    def test_nonexistent_user(self):
        """测试不存在的用户"""
        user = self.user_service.get_user_by_id(self.db, 99999)
        assert user is None

        user = self.user_service.get_user_by_email(self.db, "<EMAIL>")
        assert user is None

    def test_duplicate_email(self):
        """测试重复邮箱"""
        # 尝试创建相同邮箱的用户
        duplicate_data = {
            "email": self.test_user.email,  # 重复邮箱
            "name": "重复用户",
            "authing_user_id": "duplicate_id",
        }

        # 数据库约束应该防止创建重复邮箱用户
        with pytest.raises(Exception):
            self.user_service.create_user(self.db, duplicate_data)

    def test_invalid_role_assignment(self):
        """测试无效角色分配"""
        # 尝试分配不存在的角色
        success = self.role_service.assign_role_to_user(
            self.db, self.test_user.id, 99999  # 不存在的角色ID
        )
        assert not success

    def test_session_cleanup(self):
        """测试会话清理"""
        # 创建过期会话
        session = self.user_service.create_user_session(
            self.db, self.test_user.id, {"device": "test"}
        )

        # 手动设置会话过期
        session.expires_at = datetime.now() - timedelta(days=1)
        self.db.commit()

        # 清理过期会话
        cleaned_count = self.user_service.cleanup_expired_sessions(self.db)
        assert cleaned_count >= 1


# 运行测试的辅助函数
def run_security_tests():
    """运行安全测试套件"""
    print("🔒 开始认证系统安全测试...")

    # 使用pytest运行测试
    pytest.main([__file__, "-v", "--tb=short", "--disable-warnings"])

    print("✅ 认证系统安全测试完成")


if __name__ == "__main__":
    run_security_tests()
