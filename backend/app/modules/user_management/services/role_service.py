# user_management/services/role_service.py - 角色管理服务
"""
角色管理服务

功能：
1. 角色CRUD操作
2. 角色权限管理
3. 用户角色分配
4. 角色层级管理
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session, joinedload

from ..models.auth_models import Role, RolePermission, UserRole
from ..models.schemas import AssignRoleRequest, CreateRoleRequest

logger = logging.getLogger(__name__)


class RoleService:
    """角色管理服务"""

    def create_role(
        self, db: Session, request: CreateRoleRequest, created_by: int = None
    ) -> Optional[Role]:
        """创建角色"""
        try:
            # 检查角色代码是否已存在
            existing_role = self.get_role_by_code(db, request.code)
            if existing_role:
                logger.warning(f"角色代码已存在: {request.code}")
                return None

            role = Role(
                code=request.code,
                name=request.name,
                description=request.description,
                is_active=True,
                is_system=False,
            )

            db.add(role)
            db.flush()  # 获取角色ID

            # 分配权限
            if request.permission_ids:
                for permission_id in request.permission_ids:
                    role_permission = RolePermission(
                        role_id=role.id,
                        permission_id=permission_id,
                        granted_by=created_by,
                    )
                    db.add(role_permission)

            db.commit()
            db.refresh(role)

            logger.info(f"角色创建成功: {role.code}")
            return role
        except Exception as e:
            logger.error(f"角色创建失败: {e}")
            db.rollback()
            return None

    def get_role_by_id(self, db: Session, role_id: int) -> Optional[Role]:
        """根据ID获取角色"""
        try:
            return (
                db.query(Role)
                .options(
                    joinedload(Role.role_permissions).joinedload(
                        RolePermission.permission
                    )
                )
                .filter(Role.id == role_id)
                .first()
            )
        except Exception as e:
            logger.error(f"获取角色失败: role_id={role_id}, 错误: {e}")
            return None

    def get_role_by_code(self, db: Session, code: str) -> Optional[Role]:
        """根据代码获取角色"""
        try:
            return (
                db.query(Role)
                .options(
                    joinedload(Role.role_permissions).joinedload(
                        RolePermission.permission
                    )
                )
                .filter(Role.code == code)
                .first()
            )
        except Exception as e:
            logger.error(f"获取角色失败: code={code}, 错误: {e}")
            return None

    def list_roles(
        self, db: Session, active_only: bool = True, include_system: bool = True
    ) -> List[Role]:
        """获取角色列表"""
        try:
            stmt = db.query(Role).options(
                joinedload(Role.role_permissions).joinedload(RolePermission.permission)
            )

            if active_only:
                stmt = stmt.filter(Role.is_active == True)

            if not include_system:
                stmt = stmt.filter(Role.is_system == False)

            return stmt.order_by(Role.level.asc(), Role.sort_order.asc()).all()
        except Exception as e:
            logger.error(f"获取角色列表失败: {e}")
            return []

    def update_role(self, db: Session, role_id: int, updates: Dict) -> Optional[Role]:
        """更新角色"""
        try:
            role = self.get_role_by_id(db, role_id)
            if not role:
                return None

            # 系统角色不允许修改某些字段
            if role.is_system:
                protected_fields = ["code", "is_system", "level"]
                for field in protected_fields:
                    updates.pop(field, None)

            for key, value in updates.items():
                if hasattr(role, key) and value is not None:
                    setattr(role, key, value)

            role.updated_at = datetime.now()
            db.commit()
            db.refresh(role)

            logger.info(f"角色更新成功: role_id={role_id}")
            return role
        except Exception as e:
            logger.error(f"角色更新失败: role_id={role_id}, 错误: {e}")
            db.rollback()
            return None

    def delete_role(self, db: Session, role_id: int) -> bool:
        """删除角色"""
        try:
            role = self.get_role_by_id(db, role_id)
            if not role:
                return False

            # 系统角色不允许删除
            if role.is_system:
                logger.warning(f"系统角色不允许删除: {role.code}")
                return False

            # 检查是否有用户使用该角色
            user_count = db.query(UserRole).filter(UserRole.role_id == role_id).count()
            if user_count > 0:
                logger.warning(f"角色仍被用户使用，不允许删除: {role.code}")
                return False

            # 删除角色权限关联
            db.query(RolePermission).filter(RolePermission.role_id == role_id).delete()

            # 删除角色
            db.delete(role)
            db.commit()

            logger.info(f"角色删除成功: {role.code}")
            return True
        except Exception as e:
            logger.error(f"角色删除失败: role_id={role_id}, 错误: {e}")
            db.rollback()
            return False

    def deactivate_role(self, db: Session, role_id: int) -> bool:
        """停用角色"""
        try:
            role = self.get_role_by_id(db, role_id)
            if not role:
                return False

            role.is_active = False
            role.updated_at = datetime.now()
            db.commit()

            logger.info(f"角色停用成功: {role.code}")
            return True
        except Exception as e:
            logger.error(f"角色停用失败: role_id={role_id}, 错误: {e}")
            db.rollback()
            return False

    def assign_permissions_to_role(
        self,
        db: Session,
        role_id: int,
        permission_ids: List[int],
        granted_by: int = None,
    ) -> bool:
        """为角色分配权限"""
        try:
            role = self.get_role_by_id(db, role_id)
            if not role:
                return False

            # 删除现有权限
            db.query(RolePermission).filter(RolePermission.role_id == role_id).delete()

            # 添加新权限
            for permission_id in permission_ids:
                role_permission = RolePermission(
                    role_id=role_id, permission_id=permission_id, granted_by=granted_by
                )
                db.add(role_permission)

            db.commit()
            logger.info(f"角色权限分配成功: role_id={role_id}")
            return True
        except Exception as e:
            logger.error(f"角色权限分配失败: role_id={role_id}, 错误: {e}")
            db.rollback()
            return False

    def add_permission_to_role(
        self, db: Session, role_id: int, permission_id: int, granted_by: int = None
    ) -> bool:
        """为角色添加单个权限"""
        try:
            # 检查权限是否已存在
            existing = (
                db.query(RolePermission)
                .filter(
                    and_(
                        RolePermission.role_id == role_id,
                        RolePermission.permission_id == permission_id,
                    )
                )
                .first()
            )

            if existing:
                logger.warning(
                    f"角色权限已存在: role_id={role_id}, permission_id={permission_id}"
                )
                return True

            role_permission = RolePermission(
                role_id=role_id, permission_id=permission_id, granted_by=granted_by
            )
            db.add(role_permission)
            db.commit()

            logger.info(
                f"角色权限添加成功: role_id={role_id}, permission_id={permission_id}"
            )
            return True
        except Exception as e:
            logger.error(
                f"角色权限添加失败: role_id={role_id}, permission_id={permission_id}, 错误: {e}"
            )
            db.rollback()
            return False

    def remove_permission_from_role(
        self, db: Session, role_id: int, permission_id: int
    ) -> bool:
        """从角色移除权限"""
        try:
            result = (
                db.query(RolePermission)
                .filter(
                    and_(
                        RolePermission.role_id == role_id,
                        RolePermission.permission_id == permission_id,
                    )
                )
                .delete()
            )

            db.commit()

            if result > 0:
                logger.info(
                    f"角色权限移除成功: role_id={role_id}, permission_id={permission_id}"
                )
                return True
            else:
                logger.warning(
                    f"角色权限不存在: role_id={role_id}, permission_id={permission_id}"
                )
                return False
        except Exception as e:
            logger.error(
                f"角色权限移除失败: role_id={role_id}, permission_id={permission_id}, 错误: {e}"
            )
            db.rollback()
            return False

    def assign_role_to_user(
        self,
        db: Session,
        user_id: int,
        role_id: int,
        granted_by: int = None,
        scope: str = None,
        expires_at: datetime = None,
    ) -> bool:
        """为用户分配角色"""
        try:
            # 检查角色分配是否已存在
            existing = (
                db.query(UserRole)
                .filter(and_(UserRole.user_id == user_id, UserRole.role_id == role_id))
                .first()
            )

            if existing:
                logger.warning(f"用户角色已存在: user_id={user_id}, role_id={role_id}")
                return True

            user_role = UserRole(
                user_id=user_id,
                role_id=role_id,
                scope=scope,
                granted_by=granted_by,
                expires_at=expires_at,
            )
            db.add(user_role)
            db.commit()

            logger.info(f"用户角色分配成功: user_id={user_id}, role_id={role_id}")
            return True
        except Exception as e:
            logger.error(
                f"用户角色分配失败: user_id={user_id}, role_id={role_id}, 错误: {e}"
            )
            db.rollback()
            return False

    def remove_role_from_user(self, db: Session, user_id: int, role_id: int) -> bool:
        """从用户移除角色"""
        try:
            result = (
                db.query(UserRole)
                .filter(and_(UserRole.user_id == user_id, UserRole.role_id == role_id))
                .delete()
            )

            db.commit()

            if result > 0:
                logger.info(f"用户角色移除成功: user_id={user_id}, role_id={role_id}")
                return True
            else:
                logger.warning(f"用户角色不存在: user_id={user_id}, role_id={role_id}")
                return False
        except Exception as e:
            logger.error(
                f"用户角色移除失败: user_id={user_id}, role_id={role_id}, 错误: {e}"
            )
            db.rollback()
            return False

    def assign_roles_to_user(
        self, db: Session, request: AssignRoleRequest, granted_by: int = None
    ) -> bool:
        """为用户分配多个角色"""
        try:
            # 删除用户现有角色
            db.query(UserRole).filter(UserRole.user_id == request.user_id).delete()

            # 分配新角色
            for role_id in request.role_ids:
                user_role = UserRole(
                    user_id=request.user_id,
                    role_id=role_id,
                    scope=request.scope,
                    granted_by=granted_by,
                    expires_at=request.expires_at,
                )
                db.add(user_role)

            db.commit()
            logger.info(f"用户角色批量分配成功: user_id={request.user_id}")
            return True
        except Exception as e:
            logger.error(f"用户角色批量分配失败: user_id={request.user_id}, 错误: {e}")
            db.rollback()
            return False

    def get_user_roles(
        self, db: Session, user_id: int, active_only: bool = True
    ) -> List[Role]:
        """获取用户的角色列表"""
        try:
            stmt = db.query(Role).join(UserRole).filter(UserRole.user_id == user_id)

            if active_only:
                stmt = stmt.filter(Role.is_active == True)
                # 检查角色是否过期
                stmt = stmt.filter(
                    or_(
                        UserRole.expires_at.is_(None),
                        UserRole.expires_at > datetime.now(),
                    )
                )

            return stmt.all()
        except Exception as e:
            logger.error(f"获取用户角色失败: user_id={user_id}, 错误: {e}")
            return []

    def get_role_users(self, db: Session, role_id: int) -> List[Dict]:
        """获取拥有指定角色的用户列表"""
        try:
            from ..models.auth_models import AuthingUser

            result = (
                db.query(AuthingUser, UserRole)
                .join(UserRole, AuthingUser.id == UserRole.user_id)
                .filter(UserRole.role_id == role_id)
                .all()
            )

            users = []
            for user, user_role in result:
                users.append(
                    {
                        "user": user,
                        "granted_at": user_role.granted_at,
                        "expires_at": user_role.expires_at,
                        "scope": user_role.scope,
                    }
                )

            return users
        except Exception as e:
            logger.error(f"获取角色用户失败: role_id={role_id}, 错误: {e}")
            return []

    def check_user_has_role(self, db: Session, user_id: int, role_code: str) -> bool:
        """检查用户是否有指定角色"""
        try:
            result = (
                db.query(UserRole)
                .join(Role)
                .filter(
                    and_(
                        UserRole.user_id == user_id,
                        Role.code == role_code,
                        Role.is_active == True,
                        or_(
                            UserRole.expires_at.is_(None),
                            UserRole.expires_at > datetime.now(),
                        ),
                    )
                )
                .first()
            )

            return result is not None
        except Exception as e:
            logger.error(
                f"检查用户角色失败: user_id={user_id}, role_code={role_code}, 错误: {e}"
            )
            return False

    def get_role_hierarchy(self, db: Session, role_id: int) -> Dict:
        """获取角色层级结构"""
        try:
            role = self.get_role_by_id(db, role_id)
            if not role:
                return {}

            # 获取父角色
            parent_roles = []
            current_role = role
            while current_role.parent_id:
                parent = self.get_role_by_id(db, current_role.parent_id)
                if parent:
                    parent_roles.append(parent)
                    current_role = parent
                else:
                    break

            # 获取子角色
            child_roles = db.query(Role).filter(Role.parent_id == role_id).all()

            return {
                "role": role,
                "parent_roles": parent_roles,
                "child_roles": child_roles,
            }
        except Exception as e:
            logger.error(f"获取角色层级失败: role_id={role_id}, 错误: {e}")
            return {}

    def create_default_roles(self, db: Session) -> bool:
        """创建默认系统角色"""
        try:
            default_roles = [
                {
                    "code": "super_admin",
                    "name": "超级管理员",
                    "description": "系统超级管理员，拥有所有权限",
                    "is_system": True,
                    "level": 0,
                },
                {
                    "code": "admin",
                    "name": "管理员",
                    "description": "系统管理员，拥有大部分管理权限",
                    "is_system": True,
                    "level": 1,
                },
                {
                    "code": "agent",
                    "name": "客服代理",
                    "description": "客服代理，处理客户咨询",
                    "is_system": True,
                    "level": 2,
                },
                {
                    "code": "user",
                    "name": "普通用户",
                    "description": "普通用户，基础权限",
                    "is_system": True,
                    "level": 3,
                },
            ]

            created_count = 0
            for role_data in default_roles:
                existing = self.get_role_by_code(db, role_data["code"])
                if not existing:
                    role = Role(**role_data)
                    db.add(role)
                    created_count += 1

            db.commit()
            logger.info(f"默认角色创建成功: {created_count}个")
            return True
        except Exception as e:
            logger.error(f"默认角色创建失败: {e}")
            db.rollback()
            return False
