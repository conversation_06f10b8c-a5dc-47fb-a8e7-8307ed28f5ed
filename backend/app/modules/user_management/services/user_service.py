# user_management/services/user_service.py - 用户服务
"""
用户数据管理服务

功能：
1. 用户CRUD操作
2. 用户查询和搜索
3. 会话管理
4. 用户状态管理
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session, joinedload

from ..models.auth_models import AuthingUser, Role, UserRole, UserSession
from ..models.schemas import UserListQuery, UserUpdateRequest

logger = logging.getLogger(__name__)


class UserService:
    """用户服务"""

    def create_user(self, db: Session, user_data: Dict) -> AuthingUser:
        """创建用户"""
        try:
            user = AuthingUser(**user_data)
            db.add(user)
            db.commit()
            db.refresh(user)
            logger.info(f"用户创建成功: {user.email}")
            return user
        except Exception as e:
            logger.error(f"用户创建失败: {e}")
            db.rollback()
            raise

    def get_user_by_id(self, db: Session, user_id: int) -> Optional[AuthingUser]:
        """根据ID获取用户"""
        try:
            return (
                db.query(AuthingUser)
                .options(joinedload(AuthingUser.user_roles).joinedload(UserRole.role))
                .filter(AuthingUser.id == user_id)
                .first()
            )
        except Exception as e:
            logger.error(f"获取用户失败: user_id={user_id}, 错误: {e}")
            return None

    def get_user_by_email(self, db: Session, email: str) -> Optional[AuthingUser]:
        """根据邮箱获取用户"""
        try:
            return (
                db.query(AuthingUser)
                .options(joinedload(AuthingUser.user_roles).joinedload(UserRole.role))
                .filter(AuthingUser.email == email)
                .first()
            )
        except Exception as e:
            logger.error(f"获取用户失败: email={email}, 错误: {e}")
            return None

    def get_user_by_username(self, db: Session, username: str) -> Optional[AuthingUser]:
        """根据用户名获取用户"""
        try:
            return (
                db.query(AuthingUser)
                .options(joinedload(AuthingUser.user_roles).joinedload(UserRole.role))
                .filter(AuthingUser.username == username)
                .first()
            )
        except Exception as e:
            logger.error(f"获取用户失败: username={username}, 错误: {e}")
            return None

    def get_user_by_phone(self, db: Session, phone: str) -> Optional[AuthingUser]:
        """根据手机号获取用户"""
        try:
            return (
                db.query(AuthingUser)
                .options(joinedload(AuthingUser.user_roles).joinedload(UserRole.role))
                .filter(AuthingUser.phone == phone)
                .first()
            )
        except Exception as e:
            logger.error(f"获取用户失败: phone={phone}, 错误: {e}")
            return None

    def get_user_by_authing_id(
        self, db: Session, authing_user_id: str
    ) -> Optional[AuthingUser]:
        """根据Authing用户ID获取用户"""
        try:
            return (
                db.query(AuthingUser)
                .options(joinedload(AuthingUser.user_roles).joinedload(UserRole.role))
                .filter(AuthingUser.authing_user_id == authing_user_id)
                .first()
            )
        except Exception as e:
            logger.error(f"获取用户失败: authing_user_id={authing_user_id}, 错误: {e}")
            return None

    def update_user(
        self, db: Session, user_id: int, updates: Dict
    ) -> Optional[AuthingUser]:
        """更新用户信息"""
        try:
            user = self.get_user_by_id(db, user_id)
            if not user:
                return None

            for key, value in updates.items():
                if hasattr(user, key) and value is not None:
                    setattr(user, key, value)

            user.updated_at = datetime.now()
            db.commit()
            db.refresh(user)

            logger.info(f"用户更新成功: user_id={user_id}")
            return user
        except Exception as e:
            logger.error(f"用户更新失败: user_id={user_id}, 错误: {e}")
            db.rollback()
            return None

    def update_user_profile(
        self, db: Session, user_id: int, request: UserUpdateRequest
    ) -> Optional[AuthingUser]:
        """更新用户资料"""
        updates = {}

        # 只更新非None的字段
        for field, value in request.dict(exclude_unset=True).items():
            if value is not None:
                updates[field] = value

        return self.update_user(db, user_id, updates)

    def delete_user(self, db: Session, user_id: int) -> bool:
        """删除用户（软删除）"""
        try:
            user = self.get_user_by_id(db, user_id)
            if not user:
                return False

            # 软删除：设置为非激活状态
            user.is_active = False
            user.updated_at = datetime.now()

            # 终止所有会话
            self.terminate_user_sessions(db, user_id)

            db.commit()
            logger.info(f"用户删除成功: user_id={user_id}")
            return True
        except Exception as e:
            logger.error(f"用户删除失败: user_id={user_id}, 错误: {e}")
            db.rollback()
            return False

    def activate_user(self, db: Session, user_id: int) -> bool:
        """激活用户"""
        try:
            user = self.get_user_by_id(db, user_id)
            if not user:
                return False

            user.is_active = True
            user.is_blocked = False
            user.is_suspended = False
            user.locked_until = None
            user.updated_at = datetime.now()

            db.commit()
            logger.info(f"用户激活成功: user_id={user_id}")
            return True
        except Exception as e:
            logger.error(f"用户激活失败: user_id={user_id}, 错误: {e}")
            db.rollback()
            return False

    def block_user(self, db: Session, user_id: int, reason: str = None) -> bool:
        """封禁用户"""
        try:
            user = self.get_user_by_id(db, user_id)
            if not user:
                return False

            user.is_blocked = True
            user.blocked_at = datetime.now()
            user.updated_at = datetime.now()

            # 如果提供了原因，可以存储在metadata中
            if reason:
                if not user.metadata:
                    user.metadata = {}
                user.metadata["block_reason"] = reason
                user.metadata["blocked_at"] = datetime.now().isoformat()

            # 终止所有会话
            self.terminate_user_sessions(db, user_id)

            db.commit()
            logger.info(f"用户封禁成功: user_id={user_id}")
            return True
        except Exception as e:
            logger.error(f"用户封禁失败: user_id={user_id}, 错误: {e}")
            db.rollback()
            return False

    def list_users(
        self, db: Session, query: UserListQuery
    ) -> Tuple[List[AuthingUser], int]:
        """获取用户列表"""
        try:
            # 构建查询
            stmt = db.query(AuthingUser).options(
                joinedload(AuthingUser.user_roles).joinedload(UserRole.role)
            )

            # 应用筛选条件
            if query.keyword:
                keyword = f"%{query.keyword}%"
                stmt = stmt.filter(
                    or_(
                        AuthingUser.email.ilike(keyword),
                        AuthingUser.name.ilike(keyword),
                        AuthingUser.username.ilike(keyword),
                        AuthingUser.phone.ilike(keyword),
                    )
                )

            if query.is_active is not None:
                stmt = stmt.filter(AuthingUser.is_active == query.is_active)

            if query.role:
                stmt = stmt.join(UserRole).join(Role).filter(Role.code == query.role)

            # 获取总数
            total = stmt.count()

            # 应用排序
            if hasattr(AuthingUser, query.sort_by):
                sort_column = getattr(AuthingUser, query.sort_by)
                if query.sort_order == "desc":
                    stmt = stmt.order_by(sort_column.desc())
                else:
                    stmt = stmt.order_by(sort_column.asc())

            # 应用分页
            offset = (query.page - 1) * query.page_size
            users = stmt.offset(offset).limit(query.page_size).all()

            return users, total
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return [], 0

    def search_users(
        self, db: Session, keyword: str, limit: int = 10
    ) -> List[AuthingUser]:
        """搜索用户"""
        try:
            keyword = f"%{keyword}%"
            return (
                db.query(AuthingUser)
                .filter(
                    and_(
                        AuthingUser.is_active == True,
                        or_(
                            AuthingUser.email.ilike(keyword),
                            AuthingUser.name.ilike(keyword),
                            AuthingUser.username.ilike(keyword),
                        ),
                    )
                )
                .limit(limit)
                .all()
            )
        except Exception as e:
            logger.error(f"搜索用户失败: {e}")
            return []

    # 会话管理相关方法
    def create_user_session(
        self, db: Session, user_id: int, device_info: Dict = None, **kwargs
    ) -> UserSession:
        """创建用户会话"""
        try:
            session = UserSession.create_session(
                user_id=user_id, device_info=device_info, **kwargs
            )
            db.add(session)
            db.commit()
            db.refresh(session)
            logger.info(f"会话创建成功: user_id={user_id}")
            return session
        except Exception as e:
            logger.error(f"会话创建失败: user_id={user_id}, 错误: {e}")
            db.rollback()
            raise

    def get_user_session(self, db: Session, session_id: str) -> Optional[UserSession]:
        """获取用户会话"""
        try:
            return (
                db.query(UserSession)
                .filter(UserSession.session_id == session_id)
                .first()
            )
        except Exception as e:
            logger.error(f"获取会话失败: session_id={session_id}, 错误: {e}")
            return None

    def get_user_sessions(
        self, db: Session, user_id: int, active_only: bool = True
    ) -> List[UserSession]:
        """获取用户的所有会话"""
        try:
            stmt = db.query(UserSession).filter(UserSession.user_id == user_id)
            if active_only:
                stmt = stmt.filter(UserSession.is_active == True)
            return stmt.order_by(UserSession.last_activity_at.desc()).all()
        except Exception as e:
            logger.error(f"获取用户会话失败: user_id={user_id}, 错误: {e}")
            return []

    def update_session_activity(self, db: Session, session_id: str) -> bool:
        """更新会话活动时间"""
        try:
            session = self.get_user_session(db, session_id)
            if session and session.is_active:
                session.last_activity_at = datetime.now()
                db.commit()
                return True
            return False
        except Exception as e:
            logger.error(f"更新会话活动失败: session_id={session_id}, 错误: {e}")
            return False

    def terminate_session(self, db: Session, session_id: str) -> bool:
        """终止会话"""
        try:
            session = self.get_user_session(db, session_id)
            if session:
                session.terminate()
                db.commit()
                logger.info(f"会话终止成功: session_id={session_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"会话终止失败: session_id={session_id}, 错误: {e}")
            db.rollback()
            return False

    def terminate_user_sessions(
        self, db: Session, user_id: int, exclude_session_id: str = None
    ) -> int:
        """终止用户的所有会话"""
        try:
            stmt = db.query(UserSession).filter(
                and_(UserSession.user_id == user_id, UserSession.is_active == True)
            )

            if exclude_session_id:
                stmt = stmt.filter(UserSession.session_id != exclude_session_id)

            sessions = stmt.all()
            terminated_count = 0

            for session in sessions:
                session.terminate()
                terminated_count += 1

            db.commit()
            logger.info(f"用户会话终止成功: user_id={user_id}, 数量={terminated_count}")
            return terminated_count
        except Exception as e:
            logger.error(f"用户会话终止失败: user_id={user_id}, 错误: {e}")
            db.rollback()
            return 0

    def cleanup_expired_sessions(self, db: Session) -> int:
        """清理过期会话"""
        try:
            expired_sessions = (
                db.query(UserSession)
                .filter(
                    and_(
                        UserSession.is_active == True,
                        UserSession.expires_at < datetime.now(),
                    )
                )
                .all()
            )

            cleaned_count = 0
            for session in expired_sessions:
                session.terminate()
                cleaned_count += 1

            db.commit()
            logger.info(f"过期会话清理完成: 数量={cleaned_count}")
            return cleaned_count
        except Exception as e:
            logger.error(f"清理过期会话失败: {e}")
            db.rollback()
            return 0

    # 用户统计方法
    def get_user_stats(self, db: Session) -> Dict:
        """获取用户统计信息"""
        try:
            total_users = db.query(AuthingUser).count()
            active_users = (
                db.query(AuthingUser).filter(AuthingUser.is_active == True).count()
            )
            blocked_users = (
                db.query(AuthingUser).filter(AuthingUser.is_blocked == True).count()
            )

            # 最近30天注册的用户
            thirty_days_ago = datetime.now().replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            thirty_days_ago = thirty_days_ago.replace(day=thirty_days_ago.day - 30)
            recent_users = (
                db.query(AuthingUser)
                .filter(AuthingUser.created_at >= thirty_days_ago)
                .count()
            )

            # 活跃会话数
            active_sessions = (
                db.query(UserSession)
                .filter(
                    and_(
                        UserSession.is_active == True,
                        UserSession.expires_at > datetime.now(),
                    )
                )
                .count()
            )

            return {
                "total_users": total_users,
                "active_users": active_users,
                "blocked_users": blocked_users,
                "recent_users": recent_users,
                "active_sessions": active_sessions,
                "inactive_users": total_users - active_users,
            }
        except Exception as e:
            logger.error(f"获取用户统计失败: {e}")
            return {}

    def verify_user_email(self, db: Session, user_id: int) -> bool:
        """验证用户邮箱"""
        try:
            user = self.get_user_by_id(db, user_id)
            if user:
                user.is_email_verified = True
                user.email_verified_at = datetime.now()
                db.commit()
                logger.info(f"用户邮箱验证成功: user_id={user_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"用户邮箱验证失败: user_id={user_id}, 错误: {e}")
            db.rollback()
            return False

    def verify_user_phone(self, db: Session, user_id: int) -> bool:
        """验证用户手机号"""
        try:
            user = self.get_user_by_id(db, user_id)
            if user:
                user.is_phone_verified = True
                user.phone_verified_at = datetime.now()
                db.commit()
                logger.info(f"用户手机验证成功: user_id={user_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"用户手机验证失败: user_id={user_id}, 错误: {e}")
            db.rollback()
            return False
