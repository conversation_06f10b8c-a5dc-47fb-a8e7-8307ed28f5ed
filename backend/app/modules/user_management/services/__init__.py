# user_management/services/__init__.py - 用户管理服务模块

from .auth_service import AuthService
from .authing_service import AuthingService
from .jwt_service import JWTService
from .permission_service import PermissionService
from .role_service import RoleService
from .user_service import UserService


def get_jwt_service(session_cache=None):
    """获取JWT服务实例"""
    return JWTService(session_cache=session_cache)


def get_permission_service(permission_cache=None):
    """获取权限服务实例"""
    return PermissionService(permission_cache=permission_cache)


__all__ = [
    "AuthService",
    "AuthingService",
    "JWTService",
    "PermissionService",
    "RoleService",
    "UserService",
    "get_jwt_service",
    "get_permission_service",
]
