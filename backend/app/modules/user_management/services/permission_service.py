# user_management/services/permission_service.py - 权限管理服务
"""
权限管理服务

功能：
1. 权限CRUD操作
2. 权限分组管理
3. 权限验证
4. 默认权限初始化
"""

import logging
from typing import Dict, List, Optional

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session

from ..models.auth_models import Permission, Role, RolePermission, UserRole

logger = logging.getLogger(__name__)


class PermissionService:
    """权限管理服务"""

    def __init__(self, permission_cache=None):
        self.permission_cache = permission_cache

    def create_permission(
        self,
        db: Session,
        code: str,
        name: str,
        resource: str,
        action: str,
        description: str = None,
        group: str = None,
        category: str = None,
    ) -> Optional[Permission]:
        """创建权限"""
        try:
            # 检查权限代码是否已存在
            existing = self.get_permission_by_code(db, code)
            if existing:
                logger.warning(f"权限代码已存在: {code}")
                return None

            permission = Permission(
                code=code,
                name=name,
                description=description,
                resource=resource,
                action=action,
                group=group,
                category=category,
                is_active=True,
            )

            db.add(permission)
            db.commit()
            db.refresh(permission)

            logger.info(f"权限创建成功: {code}")
            return permission
        except Exception as e:
            logger.error(f"权限创建失败: {e}")
            db.rollback()
            return None

    def get_permission_by_id(
        self, db: Session, permission_id: int
    ) -> Optional[Permission]:
        """根据ID获取权限"""
        try:
            return db.query(Permission).filter(Permission.id == permission_id).first()
        except Exception as e:
            logger.error(f"获取权限失败: permission_id={permission_id}, 错误: {e}")
            return None

    def get_permission_by_code(self, db: Session, code: str) -> Optional[Permission]:
        """根据代码获取权限"""
        try:
            return db.query(Permission).filter(Permission.code == code).first()
        except Exception as e:
            logger.error(f"获取权限失败: code={code}, 错误: {e}")
            return None

    def list_permissions(
        self,
        db: Session,
        active_only: bool = True,
        group: str = None,
        category: str = None,
    ) -> List[Permission]:
        """获取权限列表"""
        try:
            stmt = db.query(Permission)

            if active_only:
                stmt = stmt.filter(Permission.is_active == True)

            if group:
                stmt = stmt.filter(Permission.group == group)

            if category:
                stmt = stmt.filter(Permission.category == category)

            return stmt.order_by(
                Permission.group, Permission.category, Permission.code
            ).all()
        except Exception as e:
            logger.error(f"获取权限列表失败: {e}")
            return []

    def update_permission(
        self, db: Session, permission_id: int, updates: Dict
    ) -> Optional[Permission]:
        """更新权限"""
        try:
            permission = self.get_permission_by_id(db, permission_id)
            if not permission:
                return None

            for key, value in updates.items():
                if hasattr(permission, key) and value is not None:
                    setattr(permission, key, value)

            db.commit()
            db.refresh(permission)

            logger.info(f"权限更新成功: permission_id={permission_id}")
            return permission
        except Exception as e:
            logger.error(f"权限更新失败: permission_id={permission_id}, 错误: {e}")
            db.rollback()
            return None

    def delete_permission(self, db: Session, permission_id: int) -> bool:
        """删除权限"""
        try:
            permission = self.get_permission_by_id(db, permission_id)
            if not permission:
                return False

            # 检查是否有角色使用该权限
            role_count = (
                db.query(RolePermission)
                .filter(RolePermission.permission_id == permission_id)
                .count()
            )

            if role_count > 0:
                logger.warning(f"权限仍被角色使用，不允许删除: {permission.code}")
                return False

            db.delete(permission)
            db.commit()

            logger.info(f"权限删除成功: {permission.code}")
            return True
        except Exception as e:
            logger.error(f"权限删除失败: permission_id={permission_id}, 错误: {e}")
            db.rollback()
            return False

    def deactivate_permission(self, db: Session, permission_id: int) -> bool:
        """停用权限"""
        try:
            permission = self.get_permission_by_id(db, permission_id)
            if not permission:
                return False

            permission.is_active = False
            db.commit()

            logger.info(f"权限停用成功: {permission.code}")
            return True
        except Exception as e:
            logger.error(f"权限停用失败: permission_id={permission_id}, 错误: {e}")
            db.rollback()
            return False

    def get_permissions_by_group(self, db: Session, group: str) -> List[Permission]:
        """获取分组下的权限"""
        try:
            return (
                db.query(Permission)
                .filter(and_(Permission.group == group, Permission.is_active == True))
                .order_by(Permission.category, Permission.code)
                .all()
            )
        except Exception as e:
            logger.error(f"获取分组权限失败: group={group}, 错误: {e}")
            return []

    def get_permissions_by_resource(
        self, db: Session, resource: str
    ) -> List[Permission]:
        """获取资源的权限"""
        try:
            return (
                db.query(Permission)
                .filter(
                    and_(Permission.resource == resource, Permission.is_active == True)
                )
                .order_by(Permission.action)
                .all()
            )
        except Exception as e:
            logger.error(f"获取资源权限失败: resource={resource}, 错误: {e}")
            return []

    async def check_user_permission(
        self, db: Session, user_id: int, permission_code: str, resource: str = None
    ) -> bool:
        """检查用户是否有指定权限（支持缓存）"""
        try:
            # 先检查缓存
            if self.permission_cache:
                cached_result = await self.permission_cache.check_permission(
                    user_id, permission_code, resource
                )
                if cached_result is not None:
                    return cached_result

            # 通过用户角色查询权限
            query = (
                db.query(Permission)
                .join(RolePermission)
                .join(Role)
                .join(UserRole)
                .filter(
                    and_(
                        UserRole.user_id == user_id,
                        Permission.code == permission_code,
                        Permission.is_active == True,
                        Role.is_active == True,
                    )
                )
            )

            # 如果指定了资源，添加资源过滤
            if resource:
                query = query.filter(Permission.resource == resource)

            result = query.first()
            has_permission = result is not None

            # 缓存结果
            if self.permission_cache:
                await self.permission_cache.cache_permission_check(
                    user_id, permission_code, has_permission, resource
                )

            return has_permission
        except Exception as e:
            logger.error(
                f"检查用户权限失败: user_id={user_id}, permission_code={permission_code}, 错误: {e}"
            )
            return False

    def get_user_permissions(self, db: Session, user_id: int) -> List[Permission]:
        """获取用户的所有权限"""
        try:
            return (
                db.query(Permission)
                .join(RolePermission)
                .join(Role)
                .join(UserRole)
                .filter(
                    and_(
                        UserRole.user_id == user_id,
                        Permission.is_active == True,
                        Role.is_active == True,
                    )
                )
                .distinct()
                .order_by(Permission.group, Permission.category, Permission.code)
                .all()
            )
        except Exception as e:
            logger.error(f"获取用户权限失败: user_id={user_id}, 错误: {e}")
            return []

    def get_role_permissions(self, db: Session, role_id: int) -> List[Permission]:
        """获取角色的权限"""
        try:
            return (
                db.query(Permission)
                .join(RolePermission)
                .filter(
                    and_(
                        RolePermission.role_id == role_id, Permission.is_active == True
                    )
                )
                .order_by(Permission.group, Permission.category, Permission.code)
                .all()
            )
        except Exception as e:
            logger.error(f"获取角色权限失败: role_id={role_id}, 错误: {e}")
            return []

    def get_permission_groups(self, db: Session) -> List[Dict]:
        """获取权限分组"""
        try:
            from sqlalchemy import func

            result = (
                db.query(Permission.group, func.count(Permission.id).label("count"))
                .filter(Permission.is_active == True)
                .group_by(Permission.group)
                .all()
            )

            groups = []
            for group, count in result:
                if group:
                    groups.append({"group": group, "count": count})

            return groups
        except Exception as e:
            logger.error(f"获取权限分组失败: {e}")
            return []

    def get_permission_categories(self, db: Session, group: str = None) -> List[Dict]:
        """获取权限分类"""
        try:
            from sqlalchemy import func

            stmt = db.query(
                Permission.category, func.count(Permission.id).label("count")
            ).filter(Permission.is_active == True)

            if group:
                stmt = stmt.filter(Permission.group == group)

            result = stmt.group_by(Permission.category).all()

            categories = []
            for category, count in result:
                if category:
                    categories.append({"category": category, "count": count})

            return categories
        except Exception as e:
            logger.error(f"获取权限分类失败: {e}")
            return []

    def create_default_permissions(self, db: Session) -> bool:
        """创建默认权限"""
        try:
            default_permissions = [
                # 用户管理权限
                {
                    "code": "user:read",
                    "name": "查看用户",
                    "resource": "user",
                    "action": "read",
                    "group": "user_management",
                    "category": "basic",
                },
                {
                    "code": "user:create",
                    "name": "创建用户",
                    "resource": "user",
                    "action": "create",
                    "group": "user_management",
                    "category": "basic",
                },
                {
                    "code": "user:update",
                    "name": "更新用户",
                    "resource": "user",
                    "action": "update",
                    "group": "user_management",
                    "category": "basic",
                },
                {
                    "code": "user:delete",
                    "name": "删除用户",
                    "resource": "user",
                    "action": "delete",
                    "group": "user_management",
                    "category": "basic",
                },
                {
                    "code": "user:block",
                    "name": "封禁用户",
                    "resource": "user",
                    "action": "block",
                    "group": "user_management",
                    "category": "admin",
                },
                # 角色管理权限
                {
                    "code": "role:read",
                    "name": "查看角色",
                    "resource": "role",
                    "action": "read",
                    "group": "role_management",
                    "category": "basic",
                },
                {
                    "code": "role:create",
                    "name": "创建角色",
                    "resource": "role",
                    "action": "create",
                    "group": "role_management",
                    "category": "admin",
                },
                {
                    "code": "role:update",
                    "name": "更新角色",
                    "resource": "role",
                    "action": "update",
                    "group": "role_management",
                    "category": "admin",
                },
                {
                    "code": "role:delete",
                    "name": "删除角色",
                    "resource": "role",
                    "action": "delete",
                    "group": "role_management",
                    "category": "admin",
                },
                {
                    "code": "role:assign",
                    "name": "分配角色",
                    "resource": "role",
                    "action": "assign",
                    "group": "role_management",
                    "category": "admin",
                },
                # 权限管理权限
                {
                    "code": "permission:read",
                    "name": "查看权限",
                    "resource": "permission",
                    "action": "read",
                    "group": "permission_management",
                    "category": "basic",
                },
                {
                    "code": "permission:create",
                    "name": "创建权限",
                    "resource": "permission",
                    "action": "create",
                    "group": "permission_management",
                    "category": "admin",
                },
                {
                    "code": "permission:update",
                    "name": "更新权限",
                    "resource": "permission",
                    "action": "update",
                    "group": "permission_management",
                    "category": "admin",
                },
                {
                    "code": "permission:delete",
                    "name": "删除权限",
                    "resource": "permission",
                    "action": "delete",
                    "group": "permission_management",
                    "category": "admin",
                },
                # 消息管理权限
                {
                    "code": "message:read",
                    "name": "查看消息",
                    "resource": "message",
                    "action": "read",
                    "group": "message_management",
                    "category": "basic",
                },
                {
                    "code": "message:send",
                    "name": "发送消息",
                    "resource": "message",
                    "action": "send",
                    "group": "message_management",
                    "category": "basic",
                },
                {
                    "code": "message:delete",
                    "name": "删除消息",
                    "resource": "message",
                    "action": "delete",
                    "group": "message_management",
                    "category": "admin",
                },
                # 会话管理权限
                {
                    "code": "conversation:read",
                    "name": "查看会话",
                    "resource": "conversation",
                    "action": "read",
                    "group": "conversation_management",
                    "category": "basic",
                },
                {
                    "code": "conversation:assign",
                    "name": "分配会话",
                    "resource": "conversation",
                    "action": "assign",
                    "group": "conversation_management",
                    "category": "admin",
                },
                {
                    "code": "conversation:close",
                    "name": "关闭会话",
                    "resource": "conversation",
                    "action": "close",
                    "group": "conversation_management",
                    "category": "basic",
                },
                # 知识库权限
                {
                    "code": "knowledge:read",
                    "name": "查看知识库",
                    "resource": "knowledge",
                    "action": "read",
                    "group": "knowledge_management",
                    "category": "basic",
                },
                {
                    "code": "knowledge:create",
                    "name": "创建知识条目",
                    "resource": "knowledge",
                    "action": "create",
                    "group": "knowledge_management",
                    "category": "basic",
                },
                {
                    "code": "knowledge:update",
                    "name": "更新知识条目",
                    "resource": "knowledge",
                    "action": "update",
                    "group": "knowledge_management",
                    "category": "basic",
                },
                {
                    "code": "knowledge:delete",
                    "name": "删除知识条目",
                    "resource": "knowledge",
                    "action": "delete",
                    "group": "knowledge_management",
                    "category": "admin",
                },
                # 系统管理权限
                {
                    "code": "system:config",
                    "name": "系统配置",
                    "resource": "system",
                    "action": "config",
                    "group": "system_management",
                    "category": "admin",
                },
                {
                    "code": "system:logs",
                    "name": "查看日志",
                    "resource": "system",
                    "action": "logs",
                    "group": "system_management",
                    "category": "admin",
                },
                {
                    "code": "system:backup",
                    "name": "系统备份",
                    "resource": "system",
                    "action": "backup",
                    "group": "system_management",
                    "category": "admin",
                },
                # 渠道管理权限
                {
                    "code": "channel:read",
                    "name": "查看渠道",
                    "resource": "channel",
                    "action": "read",
                    "group": "channel_management",
                    "category": "basic",
                },
                {
                    "code": "channel:create",
                    "name": "创建渠道",
                    "resource": "channel",
                    "action": "create",
                    "group": "channel_management",
                    "category": "admin",
                },
                {
                    "code": "channel:update",
                    "name": "更新渠道",
                    "resource": "channel",
                    "action": "update",
                    "group": "channel_management",
                    "category": "admin",
                },
                {
                    "code": "channel:delete",
                    "name": "删除渠道",
                    "resource": "channel",
                    "action": "delete",
                    "group": "channel_management",
                    "category": "admin",
                },
                # AI管理权限
                {
                    "code": "ai:config",
                    "name": "AI配置",
                    "resource": "ai",
                    "action": "config",
                    "group": "ai_management",
                    "category": "admin",
                },
                {
                    "code": "ai:takeover",
                    "name": "人工接管",
                    "resource": "ai",
                    "action": "takeover",
                    "group": "ai_management",
                    "category": "basic",
                },
                # 基础权限
                {
                    "code": "profile:read",
                    "name": "查看个人资料",
                    "resource": "profile",
                    "action": "read",
                    "group": "basic",
                    "category": "profile",
                },
                {
                    "code": "profile:update",
                    "name": "更新个人资料",
                    "resource": "profile",
                    "action": "update",
                    "group": "basic",
                    "category": "profile",
                },
            ]

            created_count = 0
            for perm_data in default_permissions:
                existing = self.get_permission_by_code(db, perm_data["code"])
                if not existing:
                    permission = Permission(**perm_data)
                    db.add(permission)
                    created_count += 1

            db.commit()
            logger.info(f"默认权限创建成功: {created_count}个")
            return True
        except Exception as e:
            logger.error(f"默认权限创建失败: {e}")
            db.rollback()
            return False

    def assign_default_permissions_to_roles(self, db: Session) -> bool:
        """为默认角色分配权限"""
        try:
            from .role_service import RoleService

            role_service = RoleService()

            # 权限分配映射
            role_permissions = {
                "super_admin": [
                    # 超级管理员拥有所有权限
                    "user:read",
                    "user:create",
                    "user:update",
                    "user:delete",
                    "user:block",
                    "role:read",
                    "role:create",
                    "role:update",
                    "role:delete",
                    "role:assign",
                    "permission:read",
                    "permission:create",
                    "permission:update",
                    "permission:delete",
                    "message:read",
                    "message:send",
                    "message:delete",
                    "conversation:read",
                    "conversation:assign",
                    "conversation:close",
                    "knowledge:read",
                    "knowledge:create",
                    "knowledge:update",
                    "knowledge:delete",
                    "system:config",
                    "system:logs",
                    "system:backup",
                    "channel:read",
                    "channel:create",
                    "channel:update",
                    "channel:delete",
                    "ai:config",
                    "ai:takeover",
                    "profile:read",
                    "profile:update",
                ],
                "admin": [
                    # 管理员权限（除了系统级权限）
                    "user:read",
                    "user:create",
                    "user:update",
                    "user:block",
                    "role:read",
                    "role:assign",
                    "permission:read",
                    "message:read",
                    "message:send",
                    "message:delete",
                    "conversation:read",
                    "conversation:assign",
                    "conversation:close",
                    "knowledge:read",
                    "knowledge:create",
                    "knowledge:update",
                    "knowledge:delete",
                    "channel:read",
                    "channel:create",
                    "channel:update",
                    "ai:config",
                    "ai:takeover",
                    "profile:read",
                    "profile:update",
                ],
                "agent": [
                    # 客服代理权限
                    "user:read",
                    "message:read",
                    "message:send",
                    "conversation:read",
                    "conversation:close",
                    "knowledge:read",
                    "knowledge:create",
                    "knowledge:update",
                    "ai:takeover",
                    "profile:read",
                    "profile:update",
                ],
                "user": [
                    # 普通用户权限
                    "profile:read",
                    "profile:update",
                ],
            }

            for role_code, permission_codes in role_permissions.items():
                role = role_service.get_role_by_code(db, role_code)
                if role:
                    permission_ids = []
                    for perm_code in permission_codes:
                        permission = self.get_permission_by_code(db, perm_code)
                        if permission:
                            permission_ids.append(permission.id)

                    if permission_ids:
                        role_service.assign_permissions_to_role(
                            db, role.id, permission_ids
                        )

            logger.info("默认角色权限分配成功")
            return True
        except Exception as e:
            logger.error(f"默认角色权限分配失败: {e}")
            return False

    def search_permissions(self, db: Session, keyword: str) -> List[Permission]:
        """搜索权限"""
        try:
            keyword = f"%{keyword}%"
            return (
                db.query(Permission)
                .filter(
                    and_(
                        Permission.is_active == True,
                        or_(
                            Permission.code.ilike(keyword),
                            Permission.name.ilike(keyword),
                            Permission.description.ilike(keyword),
                        ),
                    )
                )
                .order_by(Permission.group, Permission.category, Permission.code)
                .all()
            )
        except Exception as e:
            logger.error(f"搜索权限失败: {e}")
            return []
