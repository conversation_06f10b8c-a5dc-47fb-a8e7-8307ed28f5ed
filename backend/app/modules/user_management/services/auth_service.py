# user_management/services/auth_service.py - 认证服务
"""
统一认证服务

整合Authing和本地JWT认证：
1. 用户注册和登录
2. 令牌管理
3. 用户数据同步
4. 会话管理
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional, Tuple

from sqlalchemy.orm import Session

from ..models.auth_models import AuthingUser, UserSession
from ..models.schemas import LoginRequest, RegisterRequest, UserProfileSchema
from .authing_service import AuthingService, get_authing_service
from .jwt_service import JWTService, get_jwt_service
from .user_service import UserService

logger = logging.getLogger(__name__)


class AuthService:
    """认证服务 - 整合Authing和本地认证"""

    def __init__(
        self,
        authing_service: AuthingService = None,
        jwt_service: JWTService = None,
        user_service: UserService = None,
    ):
        self.authing_service = authing_service
        self.jwt_service = jwt_service or get_jwt_service()
        self.user_service = user_service

    async def _get_authing_service(self) -> AuthingService:
        """获取Authing服务实例"""
        if self.authing_service is None:
            self.authing_service = await get_authing_service()
        return self.authing_service

    async def _get_user_service(self) -> UserService:
        """获取用户服务实例"""
        if self.user_service is None:
            from .user_service import UserService

            self.user_service = UserService()
        return self.user_service

    async def register_user(
        self, db: Session, request: RegisterRequest, device_info: Dict = None
    ) -> Tuple[bool, Dict]:
        """
        用户注册

        流程：
        1. 在Authing中注册用户
        2. 同步用户信息到本地数据库
        3. 创建本地会话
        4. 生成JWT令牌
        """
        try:
            # 1. 检查用户是否已存在
            user_service = await self._get_user_service()
            existing_user = user_service.get_user_by_email(db, request.email)
            if existing_user:
                return False, {"error": "用户已存在", "code": "USER_EXISTS"}

            # 2. 在Authing中注册用户
            authing_service = await self._get_authing_service()
            authing_result = await authing_service.register_user(
                email=request.email,
                password=request.password,
                name=request.name,
                phone=request.phone,
                username=request.username,
            )

            # 3. 同步用户数据到本地
            local_user_data = await authing_service.sync_user_to_local(authing_result)
            local_user = user_service.create_user(db, local_user_data)

            # 4. 创建用户会话
            session = UserSession.create_session(
                user_id=local_user.id, device_info=device_info or {}
            )
            db.add(session)
            db.commit()

            # 5. 生成JWT令牌
            user_data = {
                "email": local_user.email,
                "name": local_user.name,
                "roles": local_user.get_roles(),
                "permissions": local_user.get_permissions(),
            }
            tokens = self.jwt_service.create_token_pair(
                user_id=local_user.id,
                user_data=user_data,
                session_id=session.session_id,
            )

            # 6. 更新会话令牌信息
            session.access_token_hash = self.jwt_service._hash_token(
                tokens["access_token"]
            )
            session.refresh_token_hash = self.jwt_service._hash_token(
                tokens["refresh_token"]
            )
            db.commit()

            logger.info(f"用户注册成功: {request.email}")

            return True, {
                "user": UserProfileSchema.from_orm(local_user),
                "tokens": tokens,
                "session_id": session.session_id,
            }

        except Exception as e:
            logger.error(f"用户注册失败: {request.email}, 错误: {e}")
            db.rollback()
            return False, {
                "error": f"注册失败: {str(e)}",
                "code": "REGISTRATION_FAILED",
            }

    async def login_user(
        self, db: Session, request: LoginRequest, device_info: Dict = None
    ) -> Tuple[bool, Dict]:
        """
        用户登录

        流程：
        1. 在Authing中验证用户凭据
        2. 获取最新用户信息并同步到本地
        3. 创建本地会话
        4. 生成JWT令牌
        """
        try:
            # 1. 在Authing中验证用户凭据
            authing_service = await self._get_authing_service()
            login_result = await authing_service.login_user(
                email=request.email, password=request.password, device_info=device_info
            )

            # 2. 获取用户信息
            authing_user_info = login_result.get("user", {})
            if not authing_user_info:
                # 如果登录结果中没有用户信息，单独获取
                authing_user_info = await authing_service.get_user_info(
                    access_token=login_result.get("token")
                )

            # 3. 同步用户数据到本地
            user_service = await self._get_user_service()
            local_user = await self._sync_or_create_user(
                db, user_service, authing_service, authing_user_info
            )

            # 4. 更新登录记录
            local_user.last_login_at = datetime.now()
            local_user.login_count += 1
            if device_info:
                local_user.last_login_ip = device_info.get("ip")
            local_user.failed_login_attempts = 0  # 重置失败次数

            # 5. 创建新的用户会话
            session = UserSession.create_session(
                user_id=local_user.id,
                device_info=device_info or {},
                expires_in_days=7 if request.remember_me else 1,
            )
            if device_info:
                session.login_ip = device_info.get("ip")
                session.user_agent = device_info.get("user_agent")
                session.login_location = device_info.get("location")

            db.add(session)

            # 6. 生成JWT令牌
            user_data = {
                "email": local_user.email,
                "name": local_user.name,
                "roles": local_user.get_roles(),
                "permissions": local_user.get_permissions(),
            }
            tokens = self.jwt_service.create_token_pair(
                user_id=local_user.id,
                user_data=user_data,
                session_id=session.session_id,
            )

            # 7. 更新会话令牌信息
            session.access_token_hash = self.jwt_service._hash_token(
                tokens["access_token"]
            )
            session.refresh_token_hash = self.jwt_service._hash_token(
                tokens["refresh_token"]
            )

            db.commit()

            logger.info(f"用户登录成功: {request.email}")

            return True, {
                "user": UserProfileSchema.from_orm(local_user),
                "tokens": tokens,
                "session_id": session.session_id,
            }

        except Exception as e:
            logger.error(f"用户登录失败: {request.email}, 错误: {e}")

            # 更新失败登录尝试次数
            try:
                user_service = await self._get_user_service()
                user = user_service.get_user_by_email(db, request.email)
                if user:
                    user.failed_login_attempts += 1
                    # 如果失败次数过多，可以锁定账户
                    if user.failed_login_attempts >= 5:
                        user.locked_until = datetime.now().replace(
                            hour=23, minute=59, second=59
                        )
                    db.commit()
            except:
                pass

            return False, {"error": f"登录失败: {str(e)}", "code": "LOGIN_FAILED"}

    async def logout_user(self, db: Session, access_token: str) -> bool:
        """
        用户登出

        流程：
        1. 验证访问令牌
        2. 吊销本地令牌
        3. 终止会话
        4. 通知Authing（可选）
        """
        try:
            # 1. 验证访问令牌并获取用户信息
            is_valid, payload = self.jwt_service.verify_access_token(access_token)
            if not is_valid:
                return False

            user_id = int(payload.get("sub"))
            session_id = payload.get("session_id")

            # 2. 吊销令牌
            self.jwt_service.revoke_token(access_token)

            # 3. 终止会话
            if session_id:
                user_service = await self._get_user_service()
                session = user_service.get_user_session(db, session_id)
                if session and session.user_id == user_id:
                    session.terminate()
                    db.commit()

            # 4. 可选：通知Authing
            # authing_service = await self._get_authing_service()
            # await authing_service.logout_user(access_token)

            logger.info(f"用户登出成功: user_id={user_id}")
            return True

        except Exception as e:
            logger.error(f"用户登出失败: {e}")
            return False

    async def refresh_token(self, db: Session, refresh_token: str) -> Tuple[bool, Dict]:
        """
        刷新访问令牌

        流程：
        1. 验证刷新令牌
        2. 检查会话状态
        3. 生成新的访问令牌
        4. 更新会话信息
        """
        try:
            # 1. 验证刷新令牌
            is_valid, payload = self.jwt_service.verify_refresh_token(refresh_token)
            if not is_valid:
                return False, {
                    "error": payload.get("error"),
                    "code": "INVALID_REFRESH_TOKEN",
                }

            user_id = int(payload.get("sub"))
            session_id = payload.get("session_id")

            # 2. 检查会话状态
            user_service = await self._get_user_service()
            session = user_service.get_user_session(db, session_id)
            if not session or session.user_id != user_id or not session.is_active:
                return False, {"error": "会话无效", "code": "INVALID_SESSION"}

            if session.is_expired():
                session.terminate()
                db.commit()
                return False, {"error": "会话已过期", "code": "SESSION_EXPIRED"}

            # 3. 获取最新用户信息
            user = user_service.get_user_by_id(db, user_id)
            if not user or not user.is_active:
                return False, {"error": "用户无效", "code": "INVALID_USER"}

            # 4. 生成新的访问令牌
            user_data = {
                "email": user.email,
                "name": user.name,
                "roles": user.get_roles(),
                "permissions": user.get_permissions(),
            }

            new_tokens = self.jwt_service.refresh_access_token(refresh_token, user_data)

            # 5. 更新会话信息
            session.access_token_hash = self.jwt_service._hash_token(
                new_tokens["access_token"]
            )
            session.extend_session()
            db.commit()

            logger.info(f"令牌刷新成功: user_id={user_id}")

            return True, {
                "tokens": new_tokens,
                "user": UserProfileSchema.from_orm(user),
            }

        except Exception as e:
            logger.error(f"令牌刷新失败: {e}")
            return False, {"error": f"刷新失败: {str(e)}", "code": "REFRESH_FAILED"}

    async def validate_token(
        self, db: Session, access_token: str
    ) -> Tuple[bool, Optional[AuthingUser]]:
        """验证访问令牌并返回用户信息"""
        try:
            # 1. 验证JWT令牌
            is_valid, payload = self.jwt_service.verify_access_token(access_token)
            if not is_valid:
                return False, None

            user_id = int(payload.get("sub"))

            # 2. 获取用户信息
            user_service = await self._get_user_service()
            user = user_service.get_user_by_id(db, user_id)

            if not user or not user.is_active:
                return False, None

            return True, user

        except Exception as e:
            logger.error(f"令牌验证失败: {e}")
            return False, None

    async def _sync_or_create_user(
        self,
        db: Session,
        user_service: UserService,
        authing_service: AuthingService,
        authing_user: Dict,
    ) -> AuthingUser:
        """同步或创建用户"""
        authing_user_id = authing_user.get("id")

        # 尝试通过Authing用户ID查找本地用户
        local_user = user_service.get_user_by_authing_id(db, authing_user_id)

        if local_user:
            # 更新现有用户
            sync_data = await authing_service.sync_user_to_local(authing_user)
            user_service.update_user(db, local_user.id, sync_data)
            return local_user
        else:
            # 创建新用户
            sync_data = await authing_service.sync_user_to_local(authing_user)
            return user_service.create_user(db, sync_data)

    async def change_password(
        self, db: Session, user_id: int, old_password: str, new_password: str
    ) -> Tuple[bool, Dict]:
        """修改密码"""
        try:
            # 1. 获取用户信息
            user_service = await self._get_user_service()
            user = user_service.get_user_by_id(db, user_id)
            if not user:
                return False, {"error": "用户不存在", "code": "USER_NOT_FOUND"}

            # 2. 在Authing中修改密码
            authing_service = await self._get_authing_service()
            success = await authing_service.change_password(
                user_id=user.authing_user_id,
                old_password=old_password,
                new_password=new_password,
            )

            if not success:
                return False, {
                    "error": "密码修改失败",
                    "code": "PASSWORD_CHANGE_FAILED",
                }

            # 3. 吊销用户的所有令牌
            self.jwt_service.revoke_user_tokens(user_id)

            # 4. 终止用户的所有会话
            user_service.terminate_user_sessions(db, user_id)

            logger.info(f"密码修改成功: user_id={user_id}")
            return True, {"message": "密码修改成功"}

        except Exception as e:
            logger.error(f"密码修改失败: user_id={user_id}, 错误: {e}")
            return False, {
                "error": f"密码修改失败: {str(e)}",
                "code": "PASSWORD_CHANGE_ERROR",
            }

    async def get_user_profile(
        self, db: Session, user_id: int
    ) -> Optional[UserProfileSchema]:
        """获取用户资料"""
        try:
            user_service = await self._get_user_service()
            user = user_service.get_user_by_id(db, user_id)
            if user:
                return UserProfileSchema.from_orm(user)
            return None
        except Exception as e:
            logger.error(f"获取用户资料失败: user_id={user_id}, 错误: {e}")
            return None


# 创建全局认证服务实例
_auth_service = None


def get_auth_service() -> AuthService:
    """获取认证服务实例"""
    global _auth_service
    if _auth_service is None:
        _auth_service = AuthService()
    return _auth_service
