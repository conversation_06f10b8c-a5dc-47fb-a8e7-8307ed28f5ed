# user_management/api/router.py - 用户管理API路由
"""
用户管理API路由

包含的API模块：
1. 认证API（登录、注册、登出）
2. 用户管理API（用户CRUD）
3. 角色管理API（角色CRUD）
4. 权限管理API（权限CRUD）
"""

from fastapi import APIRouter

from .auth_endpoints import auth_router
from .permission_endpoints import permission_router
from .role_endpoints import role_router
from .user_endpoints import user_router

# 创建主路由
router = APIRouter(prefix="/api/v1", tags=["用户管理"])

# 包含子路由
router.include_router(auth_router, prefix="/auth", tags=["认证"])
router.include_router(user_router, prefix="/users", tags=["用户管理"])
router.include_router(role_router, prefix="/roles", tags=["角色管理"])
router.include_router(permission_router, prefix="/permissions", tags=["权限管理"])

# 导出主路由（向后兼容）
auth_router = router
