# user_management/api/auth_endpoints.py - 认证API端点
"""
认证相关API端点

功能：
1. 用户注册
2. 用户登录
3. 用户登出
4. 令牌刷新
5. 密码修改
6. 个人资料管理
"""

import logging
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session

from app.core.schemas import BaseResponse
from app.modules.user_management.models.auth_models import AuthingUser
from database.connection import get_db_session

from ..models.schemas import (
    LoginRequest,
    LoginResponse,
    PasswordChangeRequest,
    RegisterRequest,
    TokenRefreshRequest,
    TokenRefreshResponse,
    UserProfileSchema,
    UserUpdateRequest,
)
from ..services.auth_service import get_auth_service
from .dependencies import get_current_active_user, get_current_user_optional

logger = logging.getLogger(__name__)

auth_router = APIRouter()


@auth_router.post(
    "/register",
    response_model=BaseResponse[LoginResponse],
    summary="用户注册",
    description="通过Authing注册新用户并同步到本地数据库",
)
async def register(
    request: RegisterRequest,
    client_request: Request,
    db: Session = Depends(get_db_session),
):
    """用户注册"""
    try:
        # 获取设备信息
        device_info = {
            "ip": client_request.client.host if client_request.client else "unknown",
            "user_agent": client_request.headers.get("user-agent"),
            "device_type": "web",
        }

        auth_service = get_auth_service()
        success, result = await auth_service.register_user(db, request, device_info)

        if not success:
            error_code = result.get("code", "REGISTRATION_FAILED")
            error_message = result.get("error", "注册失败")

            # 根据错误类型设置HTTP状态码
            if error_code == "USER_EXISTS":
                status_code = status.HTTP_409_CONFLICT
            else:
                status_code = status.HTTP_400_BAD_REQUEST

            raise HTTPException(status_code=status_code, detail=error_message)

        return BaseResponse(
            success=True,
            message="注册成功",
            data=LoginResponse(
                access_token=result["tokens"]["access_token"],
                refresh_token=result["tokens"]["refresh_token"],
                token_type=result["tokens"]["token_type"],
                expires_in=result["tokens"]["expires_in"],
                refresh_expires_in=result["tokens"]["refresh_expires_in"],
                user=result["user"],
            ),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="注册服务错误"
        )


@auth_router.post(
    "/login",
    response_model=BaseResponse[LoginResponse],
    summary="用户登录",
    description="用户登录认证并获取访问令牌",
)
async def login(
    request: LoginRequest,
    client_request: Request,
    db: Session = Depends(get_db_session),
):
    """用户登录"""
    try:
        # 获取设备信息
        device_info = {
            "ip": client_request.client.host if client_request.client else "unknown",
            "user_agent": client_request.headers.get("user-agent"),
            "device_type": "web",
            "location": client_request.headers.get("x-forwarded-for", "unknown"),
        }

        auth_service = get_auth_service()
        success, result = await auth_service.login_user(db, request, device_info)

        if not success:
            error_message = result.get("error", "登录失败")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail=error_message
            )

        return BaseResponse(
            success=True,
            message="登录成功",
            data=LoginResponse(
                access_token=result["tokens"]["access_token"],
                refresh_token=result["tokens"]["refresh_token"],
                token_type=result["tokens"]["token_type"],
                expires_in=result["tokens"]["expires_in"],
                refresh_expires_in=result["tokens"]["refresh_expires_in"],
                user=result["user"],
            ),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="登录服务错误"
        )


@auth_router.post(
    "/logout",
    response_model=BaseResponse[Dict[str, Any]],
    summary="用户登出",
    description="用户登出并吊销访问令牌",
)
async def logout(
    client_request: Request,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(get_current_active_user),
):
    """用户登出"""
    try:
        # 从请求中获取访问令牌
        access_token = None
        auth_header = client_request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            access_token = auth_header[7:]

        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="缺少访问令牌"
            )

        auth_service = get_auth_service()
        success = await auth_service.logout_user(db, access_token)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="登出失败"
            )

        return BaseResponse(
            success=True, message="登出成功", data={"user_id": current_user.id}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登出失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="登出服务错误"
        )


@auth_router.post(
    "/refresh",
    response_model=BaseResponse[TokenRefreshResponse],
    summary="刷新访问令牌",
    description="使用刷新令牌获取新的访问令牌",
)
async def refresh_token(
    request: TokenRefreshRequest, db: Session = Depends(get_db_session)
):
    """刷新访问令牌"""
    try:
        auth_service = get_auth_service()
        success, result = await auth_service.refresh_token(db, request.refresh_token)

        if not success:
            error_message = result.get("error", "令牌刷新失败")
            error_code = result.get("code", "REFRESH_FAILED")

            if error_code in ["INVALID_REFRESH_TOKEN", "SESSION_EXPIRED"]:
                status_code = status.HTTP_401_UNAUTHORIZED
            else:
                status_code = status.HTTP_400_BAD_REQUEST

            raise HTTPException(status_code=status_code, detail=error_message)

        return BaseResponse(
            success=True,
            message="令牌刷新成功",
            data=TokenRefreshResponse(
                access_token=result["tokens"]["access_token"],
                expires_in=result["tokens"]["expires_in"],
            ),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"令牌刷新失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="令牌刷新服务错误"
        )


@auth_router.get(
    "/me",
    response_model=BaseResponse[UserProfileSchema],
    summary="获取当前用户信息",
    description="获取当前登录用户的详细信息",
)
async def get_current_user_profile(
    current_user: AuthingUser = Depends(get_current_active_user),
):
    """获取当前用户信息"""
    try:
        return BaseResponse(
            success=True,
            message="获取用户信息成功",
            data=UserProfileSchema.from_orm(current_user),
        )

    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取用户信息失败"
        )


@auth_router.put(
    "/me",
    response_model=BaseResponse[UserProfileSchema],
    summary="更新当前用户信息",
    description="更新当前登录用户的资料信息",
)
async def update_current_user_profile(
    request: UserUpdateRequest,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(get_current_active_user),
):
    """更新当前用户信息"""
    try:
        from ..services.user_service import UserService

        user_service = UserService()
        updated_user = user_service.update_user_profile(db, current_user.id, request)

        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="更新用户信息失败"
            )

        return BaseResponse(
            success=True,
            message="用户信息更新成功",
            data=UserProfileSchema.from_orm(updated_user),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新用户信息失败"
        )


@auth_router.post(
    "/change-password",
    response_model=BaseResponse[Dict[str, Any]],
    summary="修改密码",
    description="修改当前用户的登录密码",
)
async def change_password(
    request: PasswordChangeRequest,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(get_current_active_user),
):
    """修改密码"""
    try:
        auth_service = get_auth_service()
        success, result = await auth_service.change_password(
            db, current_user.id, request.old_password, request.new_password
        )

        if not success:
            error_message = result.get("error", "密码修改失败")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=error_message
            )

        return BaseResponse(
            success=True,
            message="密码修改成功，请重新登录",
            data={"user_id": current_user.id},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"密码修改失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="密码修改服务错误"
        )


@auth_router.get(
    "/profile",
    response_model=BaseResponse[UserProfileSchema],
    summary="获取用户资料",
    description="获取用户资料信息（支持未登录用户）",
)
async def get_user_profile(
    current_user: AuthingUser = Depends(get_current_user_optional),
):
    """获取用户资料（可选认证）"""
    if not current_user:
        return BaseResponse(success=True, message="未登录用户", data=None)

    try:
        return BaseResponse(
            success=True,
            message="获取用户资料成功",
            data=UserProfileSchema.from_orm(current_user),
        )

    except Exception as e:
        logger.error(f"获取用户资料失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取用户资料失败"
        )


@auth_router.post(
    "/verify-token",
    response_model=BaseResponse[Dict[str, Any]],
    summary="验证访问令牌",
    description="验证访问令牌的有效性",
)
async def verify_token(client_request: Request, db: Session = Depends(get_db_session)):
    """验证访问令牌"""
    try:
        # 从请求中获取访问令牌
        access_token = None
        auth_header = client_request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            access_token = auth_header[7:]

        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="缺少访问令牌"
            )

        auth_service = get_auth_service()
        is_valid, user = await auth_service.validate_token(db, access_token)

        if not is_valid or not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="令牌无效"
            )

        return BaseResponse(
            success=True,
            message="令牌验证成功",
            data={
                "valid": True,
                "user_id": user.id,
                "email": user.email,
                "roles": user.get_roles(),
                "permissions": user.get_permissions(),
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"令牌验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="令牌验证服务错误"
        )
