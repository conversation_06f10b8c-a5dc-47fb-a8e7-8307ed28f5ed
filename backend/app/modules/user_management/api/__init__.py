# user_management/api/__init__.py - 用户管理API模块

from .auth_middleware import AuthMiddleware, RequireAuth, RequirePermission, RequireRole
from .dependencies import get_current_user, get_current_user_optional
from .router import auth_router

__all__ = [
    "auth_router",
    "AuthMiddleware",
    "RequireAuth",
    "RequirePermission",
    "RequireRole",
    "get_current_user",
    "get_current_user_optional",
]
