# user_management/api/permission_endpoints.py - 权限管理API端点
"""
权限管理API端点

功能：
1. 权限列表查询
2. 权限详情查看
3. 权限创建和更新
4. 权限删除和停用
5. 权限分组管理
6. 权限搜索
"""

import logging
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from database import get_db_session

from ....core.schemas import BaseResponse
from ..models.auth_models import AuthingUser
from ..models.schemas import PermissionSchema
from ..services.permission_service import PermissionService
from .dependencies import (
    get_current_active_user,
    require_admin,
    require_permission_admin,
    require_permission_read,
)

logger = logging.getLogger(__name__)

permission_router = APIRouter()


@permission_router.get(
    "/",
    response_model=BaseResponse[List[PermissionSchema]],
    summary="获取权限列表",
    description="获取系统中的所有权限",
)
async def list_permissions(
    active_only: bool = Query(True, description="是否只显示激活的权限"),
    group: str = Query(None, description="权限分组筛选"),
    category: str = Query(None, description="权限分类筛选"),
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_permission_read),
):
    """获取权限列表"""
    try:
        permission_service = PermissionService()
        permissions = permission_service.list_permissions(
            db, active_only, group, category
        )

        permission_schemas = [PermissionSchema.from_orm(perm) for perm in permissions]

        return BaseResponse(
            success=True, message="获取权限列表成功", data=permission_schemas
        )

    except Exception as e:
        logger.error(f"获取权限列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取权限列表失败"
        )


@permission_router.post(
    "/",
    response_model=BaseResponse[PermissionSchema],
    summary="创建权限",
    description="创建新的权限",
)
async def create_permission(
    code: str,
    name: str,
    resource: str,
    action: str,
    description: str = None,
    group: str = None,
    category: str = None,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_permission_admin),
):
    """创建权限"""
    try:
        permission_service = PermissionService()
        permission = permission_service.create_permission(
            db, code, name, resource, action, description, group, category
        )

        if not permission:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="权限创建失败，可能是权限代码已存在",
            )

        return BaseResponse(
            success=True,
            message="权限创建成功",
            data=PermissionSchema.from_orm(permission),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建权限失败"
        )


@permission_router.get(
    "/{permission_id}",
    response_model=BaseResponse[PermissionSchema],
    summary="获取权限详情",
    description="根据权限ID获取权限详细信息",
)
async def get_permission(
    permission_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_permission_read),
):
    """获取权限详情"""
    try:
        permission_service = PermissionService()
        permission = permission_service.get_permission_by_id(db, permission_id)

        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="权限不存在"
            )

        return BaseResponse(
            success=True,
            message="获取权限详情成功",
            data=PermissionSchema.from_orm(permission),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取权限详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取权限详情失败"
        )


@permission_router.put(
    "/{permission_id}",
    response_model=BaseResponse[PermissionSchema],
    summary="更新权限",
    description="更新指定权限的信息",
)
async def update_permission(
    permission_id: int,
    updates: Dict[str, Any],
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_permission_admin),
):
    """更新权限"""
    try:
        permission_service = PermissionService()

        # 检查权限是否存在
        permission = permission_service.get_permission_by_id(db, permission_id)
        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="权限不存在"
            )

        # 更新权限
        updated_permission = permission_service.update_permission(
            db, permission_id, updates
        )

        if not updated_permission:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="更新权限失败"
            )

        return BaseResponse(
            success=True,
            message="权限更新成功",
            data=PermissionSchema.from_orm(updated_permission),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新权限失败"
        )


@permission_router.delete(
    "/{permission_id}",
    response_model=BaseResponse[Dict[str, Any]],
    summary="删除权限",
    description="删除指定权限",
)
async def delete_permission(
    permission_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_permission_admin),
):
    """删除权限"""
    try:
        permission_service = PermissionService()
        success = permission_service.delete_permission(db, permission_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="删除权限失败，可能仍有角色使用该权限",
            )

        return BaseResponse(
            success=True, message="权限删除成功", data={"permission_id": permission_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除权限失败"
        )


@permission_router.post(
    "/{permission_id}/deactivate",
    response_model=BaseResponse[Dict[str, Any]],
    summary="停用权限",
    description="停用指定权限",
)
async def deactivate_permission(
    permission_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_permission_admin),
):
    """停用权限"""
    try:
        permission_service = PermissionService()
        success = permission_service.deactivate_permission(db, permission_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="停用权限失败"
            )

        return BaseResponse(
            success=True, message="权限停用成功", data={"permission_id": permission_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停用权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="停用权限失败"
        )


@permission_router.get(
    "/groups/",
    response_model=BaseResponse[List[Dict[str, Any]]],
    summary="获取权限分组",
    description="获取权限分组列表和统计信息",
)
async def get_permission_groups(
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_permission_read),
):
    """获取权限分组"""
    try:
        permission_service = PermissionService()
        groups = permission_service.get_permission_groups(db)

        return BaseResponse(success=True, message="获取权限分组成功", data=groups)

    except Exception as e:
        logger.error(f"获取权限分组失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取权限分组失败"
        )


@permission_router.get(
    "/categories/",
    response_model=BaseResponse[List[Dict[str, Any]]],
    summary="获取权限分类",
    description="获取权限分类列表和统计信息",
)
async def get_permission_categories(
    group: str = Query(None, description="按分组筛选分类"),
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_permission_read),
):
    """获取权限分类"""
    try:
        permission_service = PermissionService()
        categories = permission_service.get_permission_categories(db, group)

        return BaseResponse(success=True, message="获取权限分类成功", data=categories)

    except Exception as e:
        logger.error(f"获取权限分类失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取权限分类失败"
        )


@permission_router.get(
    "/by-group/{group}",
    response_model=BaseResponse[List[PermissionSchema]],
    summary="按分组获取权限",
    description="获取指定分组下的权限列表",
)
async def get_permissions_by_group(
    group: str,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_permission_read),
):
    """按分组获取权限"""
    try:
        permission_service = PermissionService()
        permissions = permission_service.get_permissions_by_group(db, group)

        permission_schemas = [PermissionSchema.from_orm(perm) for perm in permissions]

        return BaseResponse(
            success=True, message="获取分组权限成功", data=permission_schemas
        )

    except Exception as e:
        logger.error(f"获取分组权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取分组权限失败"
        )


@permission_router.get(
    "/by-resource/{resource}",
    response_model=BaseResponse[List[PermissionSchema]],
    summary="按资源获取权限",
    description="获取指定资源的权限列表",
)
async def get_permissions_by_resource(
    resource: str,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_permission_read),
):
    """按资源获取权限"""
    try:
        permission_service = PermissionService()
        permissions = permission_service.get_permissions_by_resource(db, resource)

        permission_schemas = [PermissionSchema.from_orm(perm) for perm in permissions]

        return BaseResponse(
            success=True, message="获取资源权限成功", data=permission_schemas
        )

    except Exception as e:
        logger.error(f"获取资源权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取资源权限失败"
        )


@permission_router.get(
    "/search",
    response_model=BaseResponse[List[PermissionSchema]],
    summary="搜索权限",
    description="根据关键词搜索权限",
)
async def search_permissions(
    keyword: str = Query(..., min_length=2, description="搜索关键词"),
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_permission_read),
):
    """搜索权限"""
    try:
        permission_service = PermissionService()
        permissions = permission_service.search_permissions(db, keyword)

        permission_schemas = [PermissionSchema.from_orm(perm) for perm in permissions]

        return BaseResponse(
            success=True, message="权限搜索成功", data=permission_schemas
        )

    except Exception as e:
        logger.error(f"搜索权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="搜索权限失败"
        )


@permission_router.post(
    "/check",
    response_model=BaseResponse[Dict[str, Any]],
    summary="检查用户权限",
    description="检查当前用户是否有指定权限",
)
async def check_user_permission(
    permission_code: str,
    user_id: int = None,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(get_current_active_user),
):
    """检查用户权限"""
    try:
        # 如果没有指定用户ID，检查当前用户
        target_user_id = user_id if user_id else current_user.id

        # 如果要检查其他用户的权限，需要管理员权限
        if user_id and user_id != current_user.id:
            if not current_user.has_permission("user:read"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权检查其他用户的权限",
                )

        permission_service = PermissionService()
        has_permission = permission_service.check_user_permission(
            db, target_user_id, permission_code
        )

        return BaseResponse(
            success=True,
            message="权限检查完成",
            data={
                "user_id": target_user_id,
                "permission_code": permission_code,
                "has_permission": has_permission,
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查用户权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="检查用户权限失败"
        )


@permission_router.post(
    "/init-defaults",
    response_model=BaseResponse[Dict[str, Any]],
    summary="初始化默认权限",
    description="创建系统默认权限",
)
async def init_default_permissions(
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_admin),
):
    """初始化默认权限"""
    try:
        permission_service = PermissionService()
        success = permission_service.create_default_permissions(db)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="初始化默认权限失败"
            )

        return BaseResponse(
            success=True,
            message="默认权限初始化成功",
            data={"created_by": current_user.id},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"初始化默认权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="初始化默认权限失败",
        )


@permission_router.post(
    "/assign-defaults",
    response_model=BaseResponse[Dict[str, Any]],
    summary="分配默认权限",
    description="为默认角色分配权限",
)
async def assign_default_permissions(
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_admin),
):
    """分配默认权限"""
    try:
        permission_service = PermissionService()
        success = permission_service.assign_default_permissions_to_roles(db)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="分配默认权限失败"
            )

        return BaseResponse(
            success=True,
            message="默认权限分配成功",
            data={"assigned_by": current_user.id},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分配默认权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="分配默认权限失败"
        )
