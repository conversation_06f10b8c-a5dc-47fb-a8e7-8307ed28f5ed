# user_management/api/dependencies.py - FastAPI依赖注入
"""
认证和权限依赖注入

FastAPI依赖注入函数，用于：
1. 获取当前用户
2. 验证权限
3. 验证角色
4. 获取数据库会话
"""

from typing import List, Optional, Union

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.orm import Session

from app.modules.user_management.models.auth_models import AuthingUser
from database.connection import get_db_session

from ..services.auth_service import get_auth_service
from ..services.permission_service import PermissionService
from ..services.role_service import RoleService

# HTTP Bearer 认证方案
security = HTTPBearer(auto_error=False)
security_required = HTTPBearer(auto_error=True)


async def get_current_user_optional(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db_session),
) -> Optional[AuthingUser]:
    """
    获取当前用户（可选）

    如果没有提供令牌或令牌无效，返回None
    用于不强制要求认证的接口
    """
    # 首先尝试从中间件获取
    if hasattr(request.state, "current_user"):
        return request.state.current_user

    if not credentials:
        return None

    try:
        auth_service = get_auth_service()
        is_valid, user = await auth_service.validate_token(db, credentials.credentials)
        return user if is_valid else None
    except Exception:
        return None


async def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security_required),
    db: Session = Depends(get_db_session),
) -> AuthingUser:
    """
    获取当前用户（必需）

    如果没有提供令牌或令牌无效，抛出401异常
    用于强制要求认证的接口
    """
    # 首先尝试从中间件获取
    if hasattr(request.state, "current_user"):
        return request.state.current_user

    try:
        auth_service = get_auth_service()
        is_valid, user = await auth_service.validate_token(db, credentials.credentials)

        if not is_valid or not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌无效或已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="认证服务错误"
        )


async def get_current_active_user(
    current_user: AuthingUser = Depends(get_current_user),
) -> AuthingUser:
    """
    获取当前活跃用户

    验证用户是否处于活跃状态
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="用户账户已被禁用"
        )

    if current_user.is_blocked:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="用户账户已被封禁"
        )

    if current_user.is_suspended:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="用户账户已被暂停"
        )

    return current_user


def require_permissions(permissions: Union[str, List[str]], require_all: bool = True):
    """
    权限依赖注入工厂函数

    Args:
        permissions: 权限代码或权限代码列表
        require_all: 如果是多个权限，是否要求全部拥有

    Returns:
        FastAPI依赖注入函数
    """
    permission_list = [permissions] if isinstance(permissions, str) else permissions

    async def check_permissions(
        current_user: AuthingUser = Depends(get_current_active_user),
        db: Session = Depends(get_db_session),
    ) -> AuthingUser:
        permission_service = PermissionService()

        if require_all:
            # 要求所有权限
            for perm in permission_list:
                if not permission_service.check_user_permission(
                    db, current_user.id, perm
                ):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"缺少权限: {perm}",
                    )
        else:
            # 任一权限即可
            has_permission = False
            for perm in permission_list:
                if permission_service.check_user_permission(db, current_user.id, perm):
                    has_permission = True
                    break

            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {' 或 '.join(permission_list)}",
                )

        return current_user

    return check_permissions


def require_roles(roles: Union[str, List[str]], require_all: bool = False):
    """
    角色依赖注入工厂函数

    Args:
        roles: 角色代码或角色代码列表
        require_all: 如果是多个角色，是否要求全部拥有

    Returns:
        FastAPI依赖注入函数
    """
    role_list = [roles] if isinstance(roles, str) else roles

    async def check_roles(
        current_user: AuthingUser = Depends(get_current_active_user),
        db: Session = Depends(get_db_session),
    ) -> AuthingUser:
        role_service = RoleService()

        if require_all:
            # 要求所有角色
            for role in role_list:
                if not role_service.check_user_has_role(db, current_user.id, role):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"缺少角色: {role}",
                    )
        else:
            # 任一角色即可
            has_role = False
            for role in role_list:
                if role_service.check_user_has_role(db, current_user.id, role):
                    has_role = True
                    break

            if not has_role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少角色: {' 或 '.join(role_list)}",
                )

        return current_user

    return check_roles


# 常用依赖注入实例
require_admin = require_roles(["admin", "super_admin"], require_all=False)
require_super_admin = require_roles("super_admin")
require_agent = require_roles(["agent", "admin", "super_admin"], require_all=False)

# 常用权限依赖
require_user_read = require_permissions("user:read")
require_user_write = require_permissions(
    ["user:create", "user:update"], require_all=False
)
require_user_admin = require_permissions(
    ["user:delete", "user:block"], require_all=False
)

require_role_read = require_permissions("role:read")
require_role_write = require_permissions(
    ["role:create", "role:update"], require_all=False
)
require_role_admin = require_permissions(
    ["role:delete", "role:assign"], require_all=False
)

require_permission_read = require_permissions("permission:read")
require_permission_admin = require_permissions(
    ["permission:create", "permission:update", "permission:delete"], require_all=False
)

require_message_read = require_permissions("message:read")
require_message_send = require_permissions("message:send")
require_message_admin = require_permissions("message:delete")

require_conversation_read = require_permissions("conversation:read")
require_conversation_assign = require_permissions("conversation:assign")
require_conversation_close = require_permissions("conversation:close")

require_knowledge_read = require_permissions("knowledge:read")
require_knowledge_write = require_permissions(
    ["knowledge:create", "knowledge:update"], require_all=False
)
require_knowledge_admin = require_permissions("knowledge:delete")

require_channel_read = require_permissions("channel:read")
require_channel_write = require_permissions(
    ["channel:create", "channel:update"], require_all=False
)
require_channel_admin = require_permissions("channel:delete")

require_ai_config = require_permissions("ai:config")
require_ai_takeover = require_permissions("ai:takeover")

require_system_config = require_permissions("system:config")
require_system_logs = require_permissions("system:logs")
require_system_backup = require_permissions("system:backup")


def get_user_permissions(
    current_user: AuthingUser = Depends(get_current_active_user),
    db: Session = Depends(get_db_session),
) -> List[str]:
    """获取当前用户的权限列表"""
    return current_user.get_permissions()


def get_user_roles(
    current_user: AuthingUser = Depends(get_current_active_user),
    db: Session = Depends(get_db_session),
) -> List[str]:
    """获取当前用户的角色列表"""
    return current_user.get_roles()


async def get_access_token(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
) -> Optional[str]:
    """获取访问令牌"""
    # 首先尝试从中间件获取
    if hasattr(request.state, "access_token"):
        return request.state.access_token

    if credentials:
        return credentials.credentials

    return None


def is_admin_user(current_user: AuthingUser = Depends(get_current_active_user)) -> bool:
    """检查是否为管理员用户"""
    admin_roles = {"admin", "super_admin"}
    user_roles = set(current_user.get_roles())
    return bool(admin_roles.intersection(user_roles))


def is_super_admin_user(
    current_user: AuthingUser = Depends(get_current_active_user),
) -> bool:
    """检查是否为超级管理员用户"""
    return "super_admin" in current_user.get_roles()


async def verify_user_identity(
    user_id: int, current_user: AuthingUser = Depends(get_current_active_user)
) -> bool:
    """验证用户身份（用户只能访问自己的资源，除非是管理员）"""
    if current_user.id == user_id:
        return True

    # 管理员可以访问所有用户资源
    if is_admin_user(current_user):
        return True

    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN, detail="无权访问其他用户的资源"
    )
