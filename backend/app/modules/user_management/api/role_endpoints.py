# user_management/api/role_endpoints.py - 角色管理API端点
"""
角色管理API端点

功能：
1. 角色列表查询
2. 角色详情查看
3. 角色创建和更新
4. 角色删除和停用
5. 角色权限管理
6. 角色用户管理
"""

import logging
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from database import get_db_session

from ....core.schemas import BaseResponse
from ..models.auth_models import AuthingUser
from ..models.schemas import CreateRoleRequest, RoleSchema
from ..services.role_service import RoleService
from .dependencies import (
    get_current_active_user,
    require_admin,
    require_role_admin,
    require_role_read,
    require_role_write,
)

logger = logging.getLogger(__name__)

role_router = APIRouter()


@role_router.get(
    "/",
    response_model=BaseResponse[List[RoleSchema]],
    summary="获取角色列表",
    description="获取系统中的所有角色",
)
async def list_roles(
    active_only: bool = Query(True, description="是否只显示激活的角色"),
    include_system: bool = Query(True, description="是否包含系统角色"),
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_read),
):
    """获取角色列表"""
    try:
        role_service = RoleService()
        roles = role_service.list_roles(db, active_only, include_system)

        role_schemas = [RoleSchema.from_orm(role) for role in roles]

        return BaseResponse(success=True, message="获取角色列表成功", data=role_schemas)

    except Exception as e:
        logger.error(f"获取角色列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取角色列表失败"
        )


@role_router.post(
    "/",
    response_model=BaseResponse[RoleSchema],
    summary="创建角色",
    description="创建新的角色",
)
async def create_role(
    request: CreateRoleRequest,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_write),
):
    """创建角色"""
    try:
        role_service = RoleService()
        role = role_service.create_role(db, request, current_user.id)

        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色创建失败，可能是角色代码已存在",
            )

        return BaseResponse(
            success=True, message="角色创建成功", data=RoleSchema.from_orm(role)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建角色失败"
        )


@role_router.get(
    "/{role_id}",
    response_model=BaseResponse[RoleSchema],
    summary="获取角色详情",
    description="根据角色ID获取角色详细信息",
)
async def get_role(
    role_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_read),
):
    """获取角色详情"""
    try:
        role_service = RoleService()
        role = role_service.get_role_by_id(db, role_id)

        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在"
            )

        return BaseResponse(
            success=True, message="获取角色详情成功", data=RoleSchema.from_orm(role)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取角色详情失败"
        )


@role_router.put(
    "/{role_id}",
    response_model=BaseResponse[RoleSchema],
    summary="更新角色",
    description="更新指定角色的信息",
)
async def update_role(
    role_id: int,
    updates: Dict[str, Any],
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_write),
):
    """更新角色"""
    try:
        role_service = RoleService()

        # 检查角色是否存在
        role = role_service.get_role_by_id(db, role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在"
            )

        # 更新角色
        updated_role = role_service.update_role(db, role_id, updates)

        if not updated_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="更新角色失败"
            )

        return BaseResponse(
            success=True, message="角色更新成功", data=RoleSchema.from_orm(updated_role)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新角色失败"
        )


@role_router.delete(
    "/{role_id}",
    response_model=BaseResponse[Dict[str, Any]],
    summary="删除角色",
    description="删除指定角色",
)
async def delete_role(
    role_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_admin),
):
    """删除角色"""
    try:
        role_service = RoleService()
        success = role_service.delete_role(db, role_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="删除角色失败，可能是系统角色或仍有用户使用",
            )

        return BaseResponse(
            success=True, message="角色删除成功", data={"role_id": role_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除角色失败"
        )


@role_router.post(
    "/{role_id}/deactivate",
    response_model=BaseResponse[Dict[str, Any]],
    summary="停用角色",
    description="停用指定角色",
)
async def deactivate_role(
    role_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_admin),
):
    """停用角色"""
    try:
        role_service = RoleService()
        success = role_service.deactivate_role(db, role_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="停用角色失败"
            )

        return BaseResponse(
            success=True, message="角色停用成功", data={"role_id": role_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停用角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="停用角色失败"
        )


@role_router.get(
    "/{role_id}/permissions",
    response_model=BaseResponse[List[str]],
    summary="获取角色权限",
    description="获取指定角色的权限列表",
)
async def get_role_permissions(
    role_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_read),
):
    """获取角色权限"""
    try:
        from ..services.permission_service import PermissionService

        permission_service = PermissionService()
        permissions = permission_service.get_role_permissions(db, role_id)

        permission_codes = [perm.code for perm in permissions]

        return BaseResponse(
            success=True, message="获取角色权限成功", data=permission_codes
        )

    except Exception as e:
        logger.error(f"获取角色权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取角色权限失败"
        )


@role_router.post(
    "/{role_id}/permissions",
    response_model=BaseResponse[Dict[str, Any]],
    summary="分配角色权限",
    description="为指定角色分配权限",
)
async def assign_role_permissions(
    role_id: int,
    permission_ids: List[int],
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_admin),
):
    """分配角色权限"""
    try:
        role_service = RoleService()

        # 检查角色是否存在
        role = role_service.get_role_by_id(db, role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在"
            )

        success = role_service.assign_permissions_to_role(
            db, role_id, permission_ids, current_user.id
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="分配角色权限失败"
            )

        return BaseResponse(
            success=True,
            message="角色权限分配成功",
            data={"role_id": role_id, "permission_ids": permission_ids},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分配角色权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="分配角色权限失败"
        )


@role_router.post(
    "/{role_id}/permissions/{permission_id}",
    response_model=BaseResponse[Dict[str, Any]],
    summary="添加角色权限",
    description="为角色添加单个权限",
)
async def add_role_permission(
    role_id: int,
    permission_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_admin),
):
    """添加角色权限"""
    try:
        role_service = RoleService()
        success = role_service.add_permission_to_role(
            db, role_id, permission_id, current_user.id
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="添加角色权限失败"
            )

        return BaseResponse(
            success=True,
            message="角色权限添加成功",
            data={"role_id": role_id, "permission_id": permission_id},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加角色权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="添加角色权限失败"
        )


@role_router.delete(
    "/{role_id}/permissions/{permission_id}",
    response_model=BaseResponse[Dict[str, Any]],
    summary="移除角色权限",
    description="从角色移除指定权限",
)
async def remove_role_permission(
    role_id: int,
    permission_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_admin),
):
    """移除角色权限"""
    try:
        role_service = RoleService()
        success = role_service.remove_permission_from_role(db, role_id, permission_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="移除角色权限失败"
            )

        return BaseResponse(
            success=True,
            message="角色权限移除成功",
            data={"role_id": role_id, "permission_id": permission_id},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移除角色权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="移除角色权限失败"
        )


@role_router.get(
    "/{role_id}/users",
    response_model=BaseResponse[List[Dict[str, Any]]],
    summary="获取角色用户",
    description="获取拥有指定角色的用户列表",
)
async def get_role_users(
    role_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_read),
):
    """获取角色用户"""
    try:
        role_service = RoleService()
        users = role_service.get_role_users(db, role_id)

        user_data = []
        for user_info in users:
            user = user_info["user"]
            user_data.append(
                {
                    "user_id": user.id,
                    "email": user.email,
                    "name": user.name,
                    "is_active": user.is_active,
                    "granted_at": (
                        user_info["granted_at"].isoformat()
                        if user_info["granted_at"]
                        else None
                    ),
                    "expires_at": (
                        user_info["expires_at"].isoformat()
                        if user_info["expires_at"]
                        else None
                    ),
                    "scope": user_info["scope"],
                }
            )

        return BaseResponse(success=True, message="获取角色用户成功", data=user_data)

    except Exception as e:
        logger.error(f"获取角色用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取角色用户失败"
        )


@role_router.get(
    "/{role_id}/hierarchy",
    response_model=BaseResponse[Dict[str, Any]],
    summary="获取角色层级",
    description="获取指定角色的层级结构",
)
async def get_role_hierarchy(
    role_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_role_read),
):
    """获取角色层级"""
    try:
        role_service = RoleService()
        hierarchy = role_service.get_role_hierarchy(db, role_id)

        if not hierarchy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在"
            )

        # 转换为可序列化的格式
        result = {
            "role": {
                "id": hierarchy["role"].id,
                "code": hierarchy["role"].code,
                "name": hierarchy["role"].name,
                "level": hierarchy["role"].level,
            },
            "parent_roles": [
                {
                    "id": role.id,
                    "code": role.code,
                    "name": role.name,
                    "level": role.level,
                }
                for role in hierarchy["parent_roles"]
            ],
            "child_roles": [
                {
                    "id": role.id,
                    "code": role.code,
                    "name": role.name,
                    "level": role.level,
                }
                for role in hierarchy["child_roles"]
            ],
        }

        return BaseResponse(success=True, message="获取角色层级成功", data=result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色层级失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取角色层级失败"
        )


@role_router.post(
    "/init-defaults",
    response_model=BaseResponse[Dict[str, Any]],
    summary="初始化默认角色",
    description="创建系统默认角色",
)
async def init_default_roles(
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_admin),
):
    """初始化默认角色"""
    try:
        role_service = RoleService()
        success = role_service.create_default_roles(db)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="初始化默认角色失败"
            )

        return BaseResponse(
            success=True,
            message="默认角色初始化成功",
            data={"created_by": current_user.id},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"初始化默认角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="初始化默认角色失败",
        )
