# 消息队列系统

基于 RabbitMQ 的企业级消息队列系统，为柴管家项目提供可靠的异步消息处理能力。

## 🌟 系统特性

- **高可靠性**：基于 RabbitMQ，支持消息持久化和集群部署
- **智能路由**：支持直接路由、话题路由和条件路由
- **重试机制**：多种重试策略，自动处理失败消息
- **实时监控**：完整的指标收集和告警系统
- **高性能**：支持批量处理和并发消费
- **易于使用**：统一的管理接口和丰富的示例

## 📁 系统架构

```
shared/messaging/
├── __init__.py          # 模块入口
├── config.py           # RabbitMQ配置管理
├── models.py           # 消息数据模型
├── exceptions.py       # 异常定义
├── router.py           # 消息路由器
├── publisher.py        # 消息发布器
├── consumer.py         # 消息消费者
├── retry_handler.py    # 重试处理器
├── monitor.py          # 监控系统
├── manager.py          # 统一管理器
├── handlers.py         # 示例处理器
├── examples.py         # 使用示例
└── README.md          # 本文档
```

## 🚀 快速开始

### 1. 基本使用

```python
from app.shared.messaging import get_message_queue_manager
from app.shared.messaging.models import MessageType, MessagePriority

# 获取管理器
manager = get_message_queue_manager()

# 启动服务
manager.start()

# 发布消息
success = manager.publish_message(
    message_type=MessageType.WEBHOOK,
    payload={'platform': 'wechat', 'content': '用户消息'},
    priority=MessagePriority.HIGH,
    source='wechat_platform'
)

# 停止服务
manager.stop()
```

### 2. 注册消息处理器

```python
from app.shared.messaging.consumer import MessageHandler
from app.shared.messaging.config import QueueType

class MyHandler(MessageHandler):
    def __init__(self):
        super().__init__("my_handler")

    def process(self, message) -> bool:
        # 处理消息逻辑
        print(f"处理消息: {message.payload.data}")
        return True

# 注册处理器
handler = MyHandler()
manager.register_handler(QueueType.WEBHOOK, handler)
```

### 3. 批量发布消息

```python
messages = [
    {
        'type': MessageType.NOTIFICATION.value,
        'payload': {'recipient': '<EMAIL>', 'content': '通知1'},
        'priority': MessagePriority.NORMAL.value
    },
    {
        'type': MessageType.NOTIFICATION.value,
        'payload': {'recipient': '<EMAIL>', 'content': '通知2'},
        'priority': MessagePriority.HIGH.value
    }
]

result = manager.publish_batch_messages(messages)
print(f"批量发布结果: {result['successful']}/{result['total']}")
```

## 📊 系统监控

### 获取系统状态

```python
# 健康检查
health = manager.get_health_status()
print(f"系统状态: {health['status']}")

# 统计信息
stats = manager.get_statistics()
print(f"发布消息数: {stats['publisher']['messages_published']}")

# 队列信息
queue_info = manager.get_queue_info(QueueType.WEBHOOK)
print(f"队列消息数: {queue_info['message_count']}")
```

### 使用监控脚本

```bash
# 查看监控面板
python scripts/monitor_message_queue.py --command dashboard

# 持续监控
python scripts/monitor_message_queue.py --command watch --interval 10

# 查看告警
python scripts/monitor_message_queue.py --command alerts --hours 24

# 查看详细统计
python scripts/monitor_message_queue.py --command stats
```

## 🔧 配置说明

### 环境变量

```bash
# RabbitMQ连接
RABBITMQ_URL=amqp://admin:password@localhost:5672/chaiguanjia
RABBITMQ_POOL_SIZE=10
RABBITMQ_MAX_RETRIES=3
RABBITMQ_RETRY_DELAY=5
```

### 队列配置

系统预定义了以下队列：

| 队列名称            | 用途             | TTL     | 最大长度 |
| ------------------- | ---------------- | ------- | -------- |
| webhook_queue       | Webhook 消息处理 | 1 小时  | 1000     |
| ai_processing_queue | AI 处理任务      | 30 分钟 | 5000     |
| notification_queue  | 通知发送         | 10 分钟 | 2000     |
| message_queue       | 消息处理         | 2 小时  | 10000    |
| task_queue          | 后台任务         | 30 分钟 | 3000     |

### 重试策略

支持多种重试策略：

- **固定延迟**：每次重试使用相同延迟时间
- **指数退避**：延迟时间按指数增长
- **线性退避**：延迟时间线性增长
- **自定义策略**：使用自定义延迟计算函数

```python
from app.shared.messaging.retry_handler import RetryPolicy, RetryStrategy

# 创建指数退避策略
policy = RetryPolicy(
    strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
    initial_delay=5,
    max_delay=300,
    max_retries=3,
    multiplier=2.0
)

# 设置策略
retry_handler.set_policy('ai_processing', policy)
```

## 🛠️ 启动服务

### 使用启动脚本

```bash
# 启动完整服务
python scripts/start_message_queue.py

# 自定义RabbitMQ URL
python scripts/start_message_queue.py --rabbitmq-url amqp://user:pass@host:5672/vhost

# 禁用监控
python scripts/start_message_queue.py --no-monitoring

# 设置日志级别
python scripts/start_message_queue.py --log-level DEBUG --log-file /var/log/mq.log

# 运行测试
python scripts/start_message_queue.py --command test
```

### Docker 部署

```yaml
# docker-compose.yml
services:
  message-queue:
    build: .
    command: python scripts/start_message_queue.py
    environment:
      - RABBITMQ_URL=amqp://rabbitmq:5672
    depends_on:
      - rabbitmq
    volumes:
      - ./logs:/app/logs
```

## 🎯 使用场景

### 1. Webhook 处理

```python
# 微信消息处理
wechat_payload = {
    'platform': 'wechat',
    'message_type': 'text',
    'content': '用户询问',
    'user_id': 'wechat_user_123'
}

manager.publish_message(
    message_type=MessageType.WEBHOOK,
    payload=wechat_payload,
    priority=MessagePriority.HIGH,
    source='wechat_platform'
)
```

### 2. AI 处理任务

```python
# 聊天AI处理
ai_payload = {
    'ai_type': 'chat',
    'message': '用户问题',
    'user_id': 'user_123',
    'context': {'conversation_id': 'conv_456'}
}

manager.publish_message(
    message_type=MessageType.AI_PROCESSING,
    payload=ai_payload,
    priority=MessagePriority.HIGH
)
```

### 3. 通知发送

```python
# 邮件通知
notification_payload = {
    'type': 'email',
    'recipient': '<EMAIL>',
    'title': '订单确认',
    'content': '您的订单已确认'
}

manager.publish_message(
    message_type=MessageType.NOTIFICATION,
    payload=notification_payload,
    priority=MessagePriority.NORMAL
)
```

### 4. 后台任务

```python
# 数据同步任务
task_payload = {
    'task_type': 'background',
    'task_name': 'data_sync',
    'params': {'source': 'db_a', 'target': 'db_b'}
}

manager.publish_message(
    message_type=MessageType.TASK,
    payload=task_payload,
    priority=MessagePriority.LOW
)
```

## 🔍 故障排除

### 常见问题

1. **连接失败**

   ```
   错误: Failed to connect to RabbitMQ
   解决: 检查RabbitMQ服务是否启动，确认连接URL正确
   ```

2. **消息发布失败**

   ```
   错误: Message was not confirmed by broker
   解决: 检查队列是否存在，确认交换机配置正确
   ```

3. **消费者无法启动**
   ```
   错误: No consumers connected
   解决: 确认处理器已注册，检查队列权限
   ```

### 日志分析

```bash
# 查看错误日志
grep "ERROR" /var/log/chaiguanjia/message_queue.log

# 查看连接问题
grep "connection" /var/log/chaiguanjia/message_queue.log

# 查看重试情况
grep "retry" /var/log/chaiguanjia/message_queue.log
```

### 性能调优

1. **调整预取数量**

   ```python
   consumer.add_consumer(queue_name, handler, prefetch_count=10)
   ```

2. **使用连接池**

   ```python
   config.connection_pool_size = 20
   ```

3. **批量处理**
   ```python
   # 批量发布减少网络开销
   manager.publish_batch_messages(messages)
   ```

## 📈 监控指标

### 核心指标

- **吞吐量指标**

  - 消息发布速率 (messages/sec)
  - 消息消费速率 (messages/sec)
  - 队列积压数量

- **性能指标**

  - 消息处理延迟
  - 发布确认时间
  - 连接数和通道数

- **可靠性指标**

  - 消息成功率
  - 重试次数
  - 死信队列消息数

- **系统指标**
  - CPU 和内存使用率
  - 网络 I/O
  - 磁盘空间使用

### 告警规则

- 队列长度 > 1000 (警告)
- 队列长度 > 5000 (错误)
- 无消费者连接 (警告)
- 错误率 > 5% (错误)
- 连接数 > 100 (警告)

## 🔐 安全考虑

1. **连接安全**

   - 使用 SSL/TLS 加密连接
   - 配置强密码和用户权限

2. **消息安全**

   - 敏感数据加密
   - 消息签名验证

3. **访问控制**
   - RabbitMQ 用户权限管理
   - 网络防火墙配置

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 📞 支持

如有问题，请联系开发团队或提交 Issue。
