"""
消息队列监控系统

提供队列监控、告警和统计功能
"""

import json
import logging
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from .config import RabbitMQConfig
from .consumer import MessageConsumer
from .exceptions import MessagingException
from .publisher import MessagePublisher

logger = logging.getLogger(__name__)


class AlertLevel(Enum):
    """告警级别"""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MetricType(Enum):
    """指标类型"""

    GAUGE = "gauge"  # 瞬时值
    COUNTER = "counter"  # 计数器
    HISTOGRAM = "histogram"  # 直方图
    SUMMARY = "summary"  # 摘要


@dataclass
class Alert:
    """告警信息"""

    id: str
    level: AlertLevel
    title: str
    message: str
    timestamp: datetime
    metric_name: str
    metric_value: Any
    threshold: Any
    source: str
    tags: Dict[str, str]
    resolved: bool = False
    resolved_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data["level"] = self.level.value
        data["timestamp"] = self.timestamp.isoformat()
        if self.resolved_at:
            data["resolved_at"] = self.resolved_at.isoformat()
        return data


@dataclass
class Metric:
    """监控指标"""

    name: str
    type: MetricType
    value: Any
    timestamp: datetime
    labels: Dict[str, str]
    description: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "type": self.type.value,
            "value": self.value,
            "timestamp": self.timestamp.isoformat(),
            "labels": self.labels,
            "description": self.description,
        }


class ThresholdRule:
    """阈值规则"""

    def __init__(
        self,
        metric_name: str,
        operator: str,  # >, <, >=, <=, ==, !=
        threshold: Any,
        alert_level: AlertLevel = AlertLevel.WARNING,
        message_template: str = "Metric {metric} {operator} {threshold}",
        tags: Optional[Dict[str, str]] = None,
    ):
        self.metric_name = metric_name
        self.operator = operator
        self.threshold = threshold
        self.alert_level = alert_level
        self.message_template = message_template
        self.tags = tags or {}

    def evaluate(self, metric: Metric) -> Optional[Alert]:
        """评估指标是否触发告警"""
        if metric.name != self.metric_name:
            return None

        triggered = False
        value = metric.value

        try:
            if self.operator == ">":
                triggered = value > self.threshold
            elif self.operator == "<":
                triggered = value < self.threshold
            elif self.operator == ">=":
                triggered = value >= self.threshold
            elif self.operator == "<=":
                triggered = value <= self.threshold
            elif self.operator == "==":
                triggered = value == self.threshold
            elif self.operator == "!=":
                triggered = value != self.threshold
        except (TypeError, ValueError) as e:
            logger.warning(
                f"Failed to compare {value} {self.operator} {self.threshold}: {e}"
            )
            return None

        if triggered:
            alert_id = f"{self.metric_name}_{int(time.time())}"
            message = self.message_template.format(
                metric=self.metric_name,
                operator=self.operator,
                threshold=self.threshold,
                value=value,
            )

            return Alert(
                id=alert_id,
                level=self.alert_level,
                title=f"Metric Alert: {self.metric_name}",
                message=message,
                timestamp=datetime.now(),
                metric_name=self.metric_name,
                metric_value=value,
                threshold=self.threshold,
                source="threshold_monitor",
                tags={**self.tags, **metric.labels},
            )

        return None


class MessageQueueMonitor:
    """消息队列监控器"""

    def __init__(self, config: RabbitMQConfig):
        """
        初始化监控器

        Args:
            config: RabbitMQ配置
        """
        self.config = config
        self.metrics: Dict[str, List[Metric]] = {}
        self.alerts: List[Alert] = []
        self.threshold_rules: List[ThresholdRule] = []

        # 监控状态
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.monitor_interval = 30  # 30秒监控间隔

        # 告警回调
        self.alert_callbacks: List[Callable[[Alert], None]] = []

        # 统计信息
        self.stats = {
            "metrics_collected": 0,
            "alerts_generated": 0,
            "last_collection_time": None,
            "collection_errors": 0,
        }

        self._setup_default_rules()

    def _setup_default_rules(self) -> None:
        """设置默认监控规则"""
        # 队列长度告警
        self.add_threshold_rule(
            "queue_length",
            ">",
            1000,
            AlertLevel.WARNING,
            "Queue {metric} has {value} messages (threshold: {threshold})",
        )

        self.add_threshold_rule(
            "queue_length",
            ">",
            5000,
            AlertLevel.ERROR,
            "Queue {metric} is critically full with {value} messages",
        )

        # 消费者数量告警
        self.add_threshold_rule(
            "consumer_count",
            "==",
            0,
            AlertLevel.WARNING,
            "No consumers connected to queue {metric}",
        )

        # 消息处理速率告警
        self.add_threshold_rule(
            "processing_rate",
            "<",
            10,
            AlertLevel.WARNING,
            "Low message processing rate: {value}/min (threshold: {threshold}/min)",
        )

        # 错误率告警
        self.add_threshold_rule(
            "error_rate",
            ">",
            0.05,  # 5%
            AlertLevel.ERROR,
            "High error rate: {value:.2%} (threshold: {threshold:.2%})",
        )

        # 连接数告警
        self.add_threshold_rule(
            "connection_count",
            ">",
            100,
            AlertLevel.WARNING,
            "High connection count: {value} (threshold: {threshold})",
        )

    def add_threshold_rule(
        self,
        metric_name: str,
        operator: str,
        threshold: Any,
        alert_level: AlertLevel = AlertLevel.WARNING,
        message_template: str = "Metric {metric} {operator} {threshold}",
        tags: Optional[Dict[str, str]] = None,
    ) -> None:
        """添加阈值规则"""
        rule = ThresholdRule(
            metric_name=metric_name,
            operator=operator,
            threshold=threshold,
            alert_level=alert_level,
            message_template=message_template,
            tags=tags,
        )
        self.threshold_rules.append(rule)
        logger.info(f"Added threshold rule: {metric_name} {operator} {threshold}")

    def add_alert_callback(self, callback: Callable[[Alert], None]) -> None:
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)

    def collect_metrics(self) -> None:
        """收集监控指标"""
        try:
            timestamp = datetime.now()

            # 收集队列指标
            self._collect_queue_metrics(timestamp)

            # 收集连接指标
            self._collect_connection_metrics(timestamp)

            # 收集系统指标
            self._collect_system_metrics(timestamp)

            self.stats["metrics_collected"] += 1
            self.stats["last_collection_time"] = timestamp

        except Exception as e:
            self.stats["collection_errors"] += 1
            logger.error(f"Error collecting metrics: {e}")

    def _collect_queue_metrics(self, timestamp: datetime) -> None:
        """收集队列相关指标"""
        try:
            connection = self.config.create_connection()
            channel = connection.channel()

            for queue_name, queue_config in self.config.queues.items():
                try:
                    # 获取队列信息
                    method = channel.queue_declare(
                        queue=queue_config.name, passive=True
                    )
                    message_count = method.method.message_count
                    consumer_count = method.method.consumer_count

                    # 记录队列长度指标
                    self._add_metric(
                        Metric(
                            name="queue_length",
                            type=MetricType.GAUGE,
                            value=message_count,
                            timestamp=timestamp,
                            labels={"queue": queue_config.name},
                            description=f"Number of messages in {queue_config.name}",
                        )
                    )

                    # 记录消费者数量指标
                    self._add_metric(
                        Metric(
                            name="consumer_count",
                            type=MetricType.GAUGE,
                            value=consumer_count,
                            timestamp=timestamp,
                            labels={"queue": queue_config.name},
                            description=f"Number of consumers for {queue_config.name}",
                        )
                    )

                except Exception as e:
                    logger.warning(
                        f"Failed to collect metrics for queue {queue_name}: {e}"
                    )

            connection.close()

        except Exception as e:
            logger.error(f"Failed to collect queue metrics: {e}")

    def _collect_connection_metrics(self, timestamp: datetime) -> None:
        """收集连接相关指标"""
        # 这里可以通过RabbitMQ Management API收集更详细的连接信息
        # 简化版本，只记录基本连接状态
        try:
            connection = self.config.create_connection()

            # 连接成功，记录连接状态
            self._add_metric(
                Metric(
                    name="connection_status",
                    type=MetricType.GAUGE,
                    value=1,
                    timestamp=timestamp,
                    labels={"status": "connected"},
                    description="RabbitMQ connection status",
                )
            )

            connection.close()

        except Exception as e:
            self._add_metric(
                Metric(
                    name="connection_status",
                    type=MetricType.GAUGE,
                    value=0,
                    timestamp=timestamp,
                    labels={"status": "disconnected", "error": str(e)},
                    description="RabbitMQ connection status",
                )
            )

    def _collect_system_metrics(self, timestamp: datetime) -> None:
        """收集系统相关指标"""
        import psutil

        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent()
            self._add_metric(
                Metric(
                    name="cpu_usage",
                    type=MetricType.GAUGE,
                    value=cpu_percent,
                    timestamp=timestamp,
                    labels={"unit": "percent"},
                    description="CPU usage percentage",
                )
            )

            # 内存使用率
            memory = psutil.virtual_memory()
            self._add_metric(
                Metric(
                    name="memory_usage",
                    type=MetricType.GAUGE,
                    value=memory.percent,
                    timestamp=timestamp,
                    labels={"unit": "percent"},
                    description="Memory usage percentage",
                )
            )

            # 磁盘使用率
            disk = psutil.disk_usage("/")
            disk_percent = (disk.used / disk.total) * 100
            self._add_metric(
                Metric(
                    name="disk_usage",
                    type=MetricType.GAUGE,
                    value=disk_percent,
                    timestamp=timestamp,
                    labels={"unit": "percent", "mount": "/"},
                    description="Disk usage percentage",
                )
            )

        except Exception as e:
            logger.warning(f"Failed to collect system metrics: {e}")

    def _add_metric(self, metric: Metric) -> None:
        """添加指标"""
        if metric.name not in self.metrics:
            self.metrics[metric.name] = []

        self.metrics[metric.name].append(metric)

        # 保留最近的1000个指标点
        if len(self.metrics[metric.name]) > 1000:
            self.metrics[metric.name] = self.metrics[metric.name][-1000:]

        # 检查是否触发告警
        self._check_thresholds(metric)

    def _check_thresholds(self, metric: Metric) -> None:
        """检查阈值并生成告警"""
        for rule in self.threshold_rules:
            alert = rule.evaluate(metric)
            if alert:
                self._generate_alert(alert)

    def _generate_alert(self, alert: Alert) -> None:
        """生成告警"""
        self.alerts.append(alert)
        self.stats["alerts_generated"] += 1

        # 保留最近的1000个告警
        if len(self.alerts) > 1000:
            self.alerts = self.alerts[-1000:]

        logger.warning(f"Alert generated: {alert.title} - {alert.message}")

        # 调用告警回调
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")

    def start_monitoring(self) -> None:
        """开始监控"""
        if self.is_monitoring:
            logger.warning("Monitoring is already running")
            return

        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()

        logger.info("Started message queue monitoring")

    def stop_monitoring(self) -> None:
        """停止监控"""
        if not self.is_monitoring:
            return

        self.is_monitoring = False

        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        logger.info("Stopped message queue monitoring")

    def _monitor_loop(self) -> None:
        """监控循环"""
        while self.is_monitoring:
            try:
                self.collect_metrics()
                time.sleep(self.monitor_interval)
            except Exception as e:
                logger.error(f"Error in monitor loop: {e}")
                time.sleep(self.monitor_interval)

    def get_metrics(
        self,
        metric_name: Optional[str] = None,
        labels: Optional[Dict[str, str]] = None,
        since: Optional[datetime] = None,
    ) -> List[Metric]:
        """
        获取指标数据

        Args:
            metric_name: 指标名称
            labels: 标签过滤
            since: 时间过滤

        Returns:
            符合条件的指标列表
        """
        if metric_name:
            metrics = self.metrics.get(metric_name, [])
        else:
            metrics = []
            for metric_list in self.metrics.values():
                metrics.extend(metric_list)

        # 应用过滤器
        filtered_metrics = []
        for metric in metrics:
            # 时间过滤
            if since and metric.timestamp < since:
                continue

            # 标签过滤
            if labels:
                if not all(metric.labels.get(k) == v for k, v in labels.items()):
                    continue

            filtered_metrics.append(metric)

        return sorted(filtered_metrics, key=lambda m: m.timestamp)

    def get_alerts(
        self,
        level: Optional[AlertLevel] = None,
        resolved: Optional[bool] = None,
        since: Optional[datetime] = None,
    ) -> List[Alert]:
        """
        获取告警信息

        Args:
            level: 告警级别过滤
            resolved: 是否已解决过滤
            since: 时间过滤

        Returns:
            符合条件的告警列表
        """
        filtered_alerts = []
        for alert in self.alerts:
            # 级别过滤
            if level and alert.level != level:
                continue

            # 解决状态过滤
            if resolved is not None and alert.resolved != resolved:
                continue

            # 时间过滤
            if since and alert.timestamp < since:
                continue

            filtered_alerts.append(alert)

        return sorted(filtered_alerts, key=lambda a: a.timestamp, reverse=True)

    def resolve_alert(self, alert_id: str) -> bool:
        """
        解决告警

        Args:
            alert_id: 告警ID

        Returns:
            是否成功解决
        """
        for alert in self.alerts:
            if alert.id == alert_id and not alert.resolved:
                alert.resolved = True
                alert.resolved_at = datetime.now()
                logger.info(f"Resolved alert: {alert_id}")
                return True
        return False

    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取监控面板数据"""
        now = datetime.now()
        last_hour = now - timedelta(hours=1)

        # 最近一小时的指标
        recent_metrics = self.get_metrics(since=last_hour)

        # 活跃告警
        active_alerts = self.get_alerts(resolved=False)

        # 队列统计
        queue_stats = {}
        for queue_name in self.config.queues.keys():
            queue_metrics = self.get_metrics(
                metric_name="queue_length",
                labels={"queue": queue_name},
                since=last_hour,
            )
            if queue_metrics:
                latest = queue_metrics[-1]
                queue_stats[queue_name] = {
                    "message_count": latest.value,
                    "last_updated": latest.timestamp.isoformat(),
                }

        return {
            "timestamp": now.isoformat(),
            "stats": self.stats,
            "active_alerts": [alert.to_dict() for alert in active_alerts],
            "queue_stats": queue_stats,
            "recent_metrics_count": len(recent_metrics),
            "monitoring_status": "running" if self.is_monitoring else "stopped",
        }

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health = {
            "status": "healthy",
            "monitoring": self.is_monitoring,
            "last_collection": None,
            "collection_errors": self.stats["collection_errors"],
            "active_alerts": len(self.get_alerts(resolved=False)),
        }

        try:
            if self.stats["last_collection_time"]:
                health["last_collection"] = self.stats[
                    "last_collection_time"
                ].isoformat()

                # 检查是否长时间没有收集指标
                time_since_collection = (
                    datetime.now() - self.stats["last_collection_time"]
                )
                if time_since_collection > timedelta(minutes=5):
                    health["status"] = "degraded"
                    health["warning"] = "No recent metric collection"

            # 检查是否有关键告警
            critical_alerts = self.get_alerts(level=AlertLevel.CRITICAL, resolved=False)
            if critical_alerts:
                health["status"] = "unhealthy"
                health["critical_alerts"] = len(critical_alerts)

        except Exception as e:
            health["status"] = "unhealthy"
            health["error"] = str(e)

        return health
