"""
消息路由器

负责消息的路由和分发逻辑
"""

import logging
import re
from typing import Callable, Dict, List, Optional, Pattern

from .config import ExchangeType, QueueType, RabbitMQConfig
from .exceptions import ConfigurationError, MessagingException
from .models import Message, MessageType

logger = logging.getLogger(__name__)


class RoutingRule:
    """路由规则"""

    def __init__(
        self,
        pattern: str,
        target_queue: str,
        exchange: str = "chaiguanjia.main",
        priority: int = 0,
        condition: Optional[Callable[[Message], bool]] = None,
    ):
        """
        初始化路由规则

        Args:
            pattern: 路由模式（支持通配符）
            target_queue: 目标队列
            exchange: 交换机名称
            priority: 优先级（数字越大优先级越高）
            condition: 额外的路由条件函数
        """
        self.pattern = pattern
        self.target_queue = target_queue
        self.exchange = exchange
        self.priority = priority
        self.condition = condition

        # 编译正则表达式
        self._compile_pattern()

    def _compile_pattern(self) -> None:
        """编译路由模式为正则表达式"""
        # 将通配符转换为正则表达式
        # * 匹配一个单词
        # # 匹配零个或多个单词
        regex_pattern = self.pattern.replace(".", r"\.")
        regex_pattern = regex_pattern.replace("*", r"[^.]+")
        regex_pattern = regex_pattern.replace("#", r".*")
        regex_pattern = f"^{regex_pattern}$"

        try:
            self.compiled_pattern: Pattern = re.compile(regex_pattern)
        except re.error as e:
            raise ConfigurationError(f"Invalid routing pattern '{self.pattern}': {e}")

    def matches(self, routing_key: str, message: Message) -> bool:
        """检查路由键是否匹配"""
        # 检查模式匹配
        if not self.compiled_pattern.match(routing_key):
            return False

        # 检查额外条件
        if self.condition and not self.condition(message):
            return False

        return True

    def __str__(self) -> str:
        return f"RoutingRule({self.pattern} -> {self.target_queue}@{self.exchange})"


class MessageRouter:
    """消息路由器"""

    def __init__(self, config: RabbitMQConfig):
        """
        初始化路由器

        Args:
            config: RabbitMQ配置
        """
        self.config = config
        self.routing_rules: List[RoutingRule] = []
        self._setup_default_rules()

    def _setup_default_rules(self) -> None:
        """设置默认路由规则"""
        # 基础直接路由规则
        self.add_rule("webhook", "webhook_queue", priority=10)
        self.add_rule("ai.processing", "ai_processing_queue", priority=10)
        self.add_rule("notification", "notification_queue", priority=10)
        self.add_rule("message.process", "message_queue", priority=10)
        self.add_rule("task", "task_queue", priority=10)

        # 死信路由规则
        self.add_rule(
            "webhook.dead", "webhook_dead_queue", exchange="chaiguanjia.dlx", priority=5
        )
        self.add_rule(
            "ai.dead", "ai_dead_queue", exchange="chaiguanjia.dlx", priority=5
        )
        self.add_rule(
            "notification.dead",
            "notification_dead_queue",
            exchange="chaiguanjia.dlx",
            priority=5,
        )
        self.add_rule(
            "message.dead", "message_dead_queue", exchange="chaiguanjia.dlx", priority=5
        )
        self.add_rule(
            "task.dead", "task_dead_queue", exchange="chaiguanjia.dlx", priority=5
        )

        # 话题路由规则（使用话题交换机）
        self.add_rule(
            "platform.*.webhook",
            "webhook_queue",
            exchange="chaiguanjia.topic",
            priority=8,
        )
        self.add_rule(
            "ai.*.processing",
            "ai_processing_queue",
            exchange="chaiguanjia.topic",
            priority=8,
        )
        self.add_rule(
            "notification.*",
            "notification_queue",
            exchange="chaiguanjia.topic",
            priority=8,
        )
        self.add_rule(
            "message.*.process",
            "message_queue",
            exchange="chaiguanjia.topic",
            priority=8,
        )
        self.add_rule("task.*", "task_queue", exchange="chaiguanjia.topic", priority=8)

        # 基于消息类型的条件路由
        self.add_rule(
            "urgent.*",
            "ai_processing_queue",
            condition=lambda msg: msg.header.priority.value >= 8,
            priority=15,
        )

        # 基于来源的路由
        self.add_rule(
            "platform.wechat.*",
            "message_queue",
            condition=lambda msg: msg.header.source.startswith("wechat"),
            priority=12,
        )

        self.add_rule(
            "platform.taobao.*",
            "message_queue",
            condition=lambda msg: msg.header.source.startswith("taobao"),
            priority=12,
        )

        logger.info(
            f"Default routing rules configured: {len(self.routing_rules)} rules"
        )

    def add_rule(
        self,
        pattern: str,
        target_queue: str,
        exchange: str = "chaiguanjia.main",
        priority: int = 0,
        condition: Optional[Callable[[Message], bool]] = None,
    ) -> None:
        """
        添加路由规则

        Args:
            pattern: 路由模式
            target_queue: 目标队列
            exchange: 交换机名称
            priority: 优先级
            condition: 路由条件
        """
        rule = RoutingRule(pattern, target_queue, exchange, priority, condition)
        self.routing_rules.append(rule)

        # 按优先级排序
        self.routing_rules.sort(key=lambda r: r.priority, reverse=True)

        logger.debug(f"Added routing rule: {rule}")

    def remove_rule(self, pattern: str, target_queue: str) -> bool:
        """
        移除路由规则

        Args:
            pattern: 路由模式
            target_queue: 目标队列

        Returns:
            是否成功移除
        """
        for rule in self.routing_rules[:]:
            if rule.pattern == pattern and rule.target_queue == target_queue:
                self.routing_rules.remove(rule)
                logger.debug(f"Removed routing rule: {rule}")
                return True
        return False

    def route_message(self, message: Message) -> Dict[str, str]:
        """
        路由消息

        Args:
            message: 要路由的消息

        Returns:
            包含exchange和routing_key的字典

        Raises:
            MessagingException: 当找不到匹配的路由规则时
        """
        routing_key = message.get_routing_key()

        # 查找匹配的路由规则
        matching_rule = self._find_matching_rule(routing_key, message)

        if not matching_rule:
            # 使用默认路由
            default_routing = self._get_default_routing(message)
            logger.warning(
                f"No routing rule found for '{routing_key}', using default: {default_routing}"
            )
            return default_routing

        result = {
            "exchange": matching_rule.exchange,
            "routing_key": routing_key,
            "queue": matching_rule.target_queue,
        }

        logger.debug(f"Routed message {message.message_id}: {routing_key} -> {result}")
        return result

    def _find_matching_rule(
        self, routing_key: str, message: Message
    ) -> Optional[RoutingRule]:
        """查找匹配的路由规则"""
        for rule in self.routing_rules:
            if rule.matches(routing_key, message):
                return rule
        return None

    def _get_default_routing(self, message: Message) -> Dict[str, str]:
        """获取默认路由"""
        # 根据消息类型确定默认队列
        type_queue_mapping = {
            MessageType.WEBHOOK: "webhook_queue",
            MessageType.AI_PROCESSING: "ai_processing_queue",
            MessageType.NOTIFICATION: "notification_queue",
            MessageType.MESSAGE_PROCESS: "message_queue",
            MessageType.TASK: "task_queue",
            MessageType.SYSTEM: "task_queue",
        }

        default_queue = type_queue_mapping.get(
            message.header.message_type, "task_queue"
        )

        return {
            "exchange": "chaiguanjia.main",
            "routing_key": message.get_routing_key(),
            "queue": default_queue,
        }

    def get_queue_for_routing_key(self, routing_key: str) -> Optional[str]:
        """根据路由键获取目标队列"""
        # 创建一个虚拟消息用于路由测试
        from .models import MessagePriority, MessageType

        dummy_message = Message(
            message_type=MessageType.SYSTEM,
            payload={"test": True},
            priority=MessagePriority.NORMAL,
        )

        # 临时修改路由键
        original_get_routing_key = dummy_message.get_routing_key
        dummy_message.get_routing_key = lambda: routing_key

        try:
            routing_result = self.route_message(dummy_message)
            return routing_result.get("queue")
        finally:
            # 恢复原始方法
            dummy_message.get_routing_key = original_get_routing_key

    def list_routes(self) -> List[Dict[str, any]]:
        """列出所有路由规则"""
        routes = []
        for rule in self.routing_rules:
            routes.append(
                {
                    "pattern": rule.pattern,
                    "target_queue": rule.target_queue,
                    "exchange": rule.exchange,
                    "priority": rule.priority,
                    "has_condition": rule.condition is not None,
                }
            )
        return routes

    def validate_routing_config(self) -> List[str]:
        """验证路由配置"""
        errors = []

        # 检查是否有重复的高优先级规则
        high_priority_patterns = {}
        for rule in self.routing_rules:
            if rule.priority >= 10:
                if rule.pattern in high_priority_patterns:
                    errors.append(f"Duplicate high-priority pattern: {rule.pattern}")
                high_priority_patterns[rule.pattern] = rule

        # 检查目标队列是否存在于配置中
        configured_queues = set(self.config.queues.keys())
        for rule in self.routing_rules:
            if rule.target_queue not in configured_queues:
                errors.append(f"Target queue '{rule.target_queue}' not configured")

        # 检查交换机是否存在于配置中
        configured_exchanges = set(self.config.exchanges.keys())
        for rule in self.routing_rules:
            exchange_name = rule.exchange.split(".")[-1] + "_exchange"
            if exchange_name not in configured_exchanges:
                logger.warning(f"Exchange '{rule.exchange}' not found in configuration")

        return errors

    def get_statistics(self) -> Dict[str, any]:
        """获取路由统计信息"""
        stats = {
            "total_rules": len(self.routing_rules),
            "rules_by_exchange": {},
            "rules_by_priority": {},
            "conditional_rules": 0,
        }

        for rule in self.routing_rules:
            # 按交换机统计
            exchange = rule.exchange
            if exchange not in stats["rules_by_exchange"]:
                stats["rules_by_exchange"][exchange] = 0
            stats["rules_by_exchange"][exchange] += 1

            # 按优先级统计
            priority = rule.priority
            if priority not in stats["rules_by_priority"]:
                stats["rules_by_priority"][priority] = 0
            stats["rules_by_priority"][priority] += 1

            # 条件规则统计
            if rule.condition:
                stats["conditional_rules"] += 1

        return stats
