"""
消息队列管理器

提供统一的消息队列管理接口
"""

import logging
from contextlib import contextmanager
from typing import Any, Callable, Dict, List, Optional

from .config import QueueType, RabbitMQConfig
from .consumer import MessageConsumer, MessageHandler
from .exceptions import MessagingException
from .models import Message, MessagePriority, MessageType
from .monitor import Alert, MessageQueueMonitor
from .publisher import MessagePublisher
from .retry_handler import RetryHandler

logger = logging.getLogger(__name__)


class MessageQueueManager:
    """消息队列管理器"""

    def __init__(self, rabbitmq_url: Optional[str] = None):
        """
        初始化管理器

        Args:
            rabbitmq_url: RabbitMQ连接URL
        """
        # 初始化配置
        self.config = RabbitMQConfig()
        if rabbitmq_url:
            self.config.connection_url = rabbitmq_url
            from pika import URLParameters

            self.config.connection_params = URLParameters(rabbitmq_url)

        # 初始化组件
        self.publisher: Optional[MessagePublisher] = None
        self.consumer: Optional[MessageConsumer] = None
        self.retry_handler: Optional[RetryHandler] = None
        self.monitor: Optional[MessageQueueMonitor] = None

        # 管理器状态
        self.is_initialized = False
        self.is_running = False

        # 注册的处理器
        self.handlers: Dict[str, MessageHandler] = {}

    def initialize(self) -> None:
        """初始化所有组件"""
        try:
            logger.info("Initializing message queue manager...")

            # 初始化发布器
            self.publisher = MessagePublisher(self.config)

            # 初始化消费者
            self.consumer = MessageConsumer(self.config)

            # 初始化重试处理器
            self.retry_handler = RetryHandler(self.config, self.publisher)

            # 设置消费者的重试发布器
            self.consumer.set_retry_publisher(self.publisher)

            # 初始化监控器
            self.monitor = MessageQueueMonitor(self.config)

            # 设置默认告警回调
            self.monitor.add_alert_callback(self._default_alert_handler)

            self.is_initialized = True
            logger.info("Message queue manager initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize message queue manager: {e}")
            raise MessagingException(f"Initialization failed: {e}")

    def start(self, start_monitoring: bool = True) -> None:
        """
        启动消息队列管理器

        Args:
            start_monitoring: 是否启动监控
        """
        if not self.is_initialized:
            self.initialize()

        if self.is_running:
            logger.warning("Message queue manager is already running")
            return

        try:
            logger.info("Starting message queue manager...")

            # 连接发布器
            self.publisher.connect()

            # 启动消费者（如果有注册的处理器）
            if self.handlers:
                for queue_name, handler in self.handlers.items():
                    self.consumer.add_consumer(queue_name, handler)

                self.consumer.start_consuming(threaded=True)

            # 启动监控
            if start_monitoring and self.monitor:
                self.monitor.start_monitoring()

            self.is_running = True
            logger.info("Message queue manager started successfully")

        except Exception as e:
            logger.error(f"Failed to start message queue manager: {e}")
            raise MessagingException(f"Startup failed: {e}")

    def stop(self) -> None:
        """停止消息队列管理器"""
        if not self.is_running:
            return

        try:
            logger.info("Stopping message queue manager...")

            # 停止监控
            if self.monitor:
                self.monitor.stop_monitoring()

            # 停止消费者
            if self.consumer:
                self.consumer.stop_consuming()
                self.consumer.disconnect()

            # 断开发布器
            if self.publisher:
                self.publisher.disconnect()

            self.is_running = False
            logger.info("Message queue manager stopped successfully")

        except Exception as e:
            logger.error(f"Error stopping message queue manager: {e}")

    def register_handler(self, queue_type: QueueType, handler: MessageHandler) -> None:
        """
        注册消息处理器

        Args:
            queue_type: 队列类型
            handler: 消息处理器
        """
        queue_config = self.config.get_queue_config(queue_type)
        queue_name = queue_config.name

        self.handlers[queue_name] = handler

        # 如果消费者已经运行，动态添加消费者
        if self.is_running and self.consumer:
            self.consumer.add_consumer(queue_name, handler)

        logger.info(f"Registered handler for queue: {queue_name}")

    def unregister_handler(self, queue_type: QueueType) -> None:
        """
        取消注册消息处理器

        Args:
            queue_type: 队列类型
        """
        queue_config = self.config.get_queue_config(queue_type)
        queue_name = queue_config.name

        if queue_name in self.handlers:
            del self.handlers[queue_name]

            # 如果消费者已经运行，动态移除消费者
            if self.is_running and self.consumer:
                self.consumer.remove_consumer(queue_name)

            logger.info(f"Unregistered handler for queue: {queue_name}")

    def publish_message(
        self,
        message_type: MessageType,
        payload: Any,
        priority: MessagePriority = MessagePriority.NORMAL,
        **kwargs,
    ) -> bool:
        """
        发布消息

        Args:
            message_type: 消息类型
            payload: 消息载荷
            priority: 消息优先级
            **kwargs: 其他消息参数

        Returns:
            发布是否成功
        """
        if not self.is_running or not self.publisher:
            raise MessagingException("Message queue manager is not running")

        try:
            # 创建消息
            message = Message(
                message_type=message_type, payload=payload, priority=priority, **kwargs
            )

            # 发布消息
            return self.publisher.publish(message)

        except Exception as e:
            logger.error(f"Failed to publish message: {e}")
            return False

    def publish_batch_messages(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量发布消息

        Args:
            messages: 消息列表，每个消息包含type, payload等字段

        Returns:
            批量发布结果
        """
        if not self.is_running or not self.publisher:
            raise MessagingException("Message queue manager is not running")

        try:
            # 构建消息对象列表
            message_objects = []
            for msg_data in messages:
                message = Message(
                    message_type=MessageType(msg_data["type"]),
                    payload=msg_data["payload"],
                    priority=MessagePriority(
                        msg_data.get("priority", MessagePriority.NORMAL.value)
                    ),
                    source=msg_data.get("source", "system"),
                    target=msg_data.get("target"),
                    correlation_id=msg_data.get("correlation_id"),
                    max_retries=msg_data.get("max_retries", 3),
                )
                message_objects.append(message)

            # 批量发布
            return self.publisher.publish_batch(message_objects)

        except Exception as e:
            logger.error(f"Failed to publish batch messages: {e}")
            return {
                "total": len(messages),
                "successful": 0,
                "failed": len(messages),
                "errors": [{"error": str(e)}],
            }

    def get_queue_info(self, queue_type: QueueType) -> Optional[Dict[str, Any]]:
        """
        获取队列信息

        Args:
            queue_type: 队列类型

        Returns:
            队列信息
        """
        if not self.is_running or not self.publisher:
            return None

        queue_config = self.config.get_queue_config(queue_type)
        return self.publisher.get_queue_info(queue_config.name)

    def purge_queue(self, queue_type: QueueType) -> int:
        """
        清空队列

        Args:
            queue_type: 队列类型

        Returns:
            清除的消息数量
        """
        if not self.is_running or not self.publisher:
            raise MessagingException("Message queue manager is not running")

        queue_config = self.config.get_queue_config(queue_type)
        return self.publisher.purge_queue(queue_config.name)

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            "manager": {
                "initialized": self.is_initialized,
                "running": self.is_running,
                "registered_handlers": len(self.handlers),
            }
        }

        if self.publisher:
            stats["publisher"] = self.publisher.get_stats()

        if self.consumer:
            stats["consumer"] = self.consumer.get_stats()

        if self.retry_handler:
            stats["retry_handler"] = self.retry_handler.get_stats()

        if self.monitor:
            stats["monitor"] = self.monitor.get_dashboard_data()

        return stats

    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        health = {
            "status": "healthy",
            "manager": {"initialized": self.is_initialized, "running": self.is_running},
        }

        try:
            if self.publisher:
                publisher_health = self.publisher.health_check()
                health["publisher"] = publisher_health
                if publisher_health["status"] != "healthy":
                    health["status"] = "degraded"

            if self.consumer:
                consumer_health = self.consumer.health_check()
                health["consumer"] = consumer_health
                if consumer_health["status"] != "healthy":
                    health["status"] = "degraded"

            if self.retry_handler:
                retry_health = self.retry_handler.health_check()
                health["retry_handler"] = retry_health
                if retry_health["status"] != "healthy":
                    health["status"] = "degraded"

            if self.monitor:
                monitor_health = self.monitor.health_check()
                health["monitor"] = monitor_health
                if monitor_health["status"] != "healthy":
                    health["status"] = "degraded"

        except Exception as e:
            health["status"] = "unhealthy"
            health["error"] = str(e)

        return health

    def _default_alert_handler(self, alert: Alert) -> None:
        """默认告警处理器"""
        logger.warning(
            f"ALERT [{alert.level.value.upper()}]: {alert.title} - {alert.message}"
        )

    @contextmanager
    def get_publisher(self):
        """获取发布器的上下文管理器"""
        if not self.publisher:
            raise MessagingException("Publisher not available")

        try:
            yield self.publisher
        except Exception as e:
            logger.error(f"Error in publisher context: {e}")
            raise

    @contextmanager
    def get_consumer(self):
        """获取消费者的上下文管理器"""
        if not self.consumer:
            raise MessagingException("Consumer not available")

        try:
            yield self.consumer
        except Exception as e:
            logger.error(f"Error in consumer context: {e}")
            raise

    def __enter__(self):
        """进入上下文管理器"""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        self.stop()


# 全局消息队列管理器实例
_message_queue_manager: Optional[MessageQueueManager] = None


def get_message_queue_manager() -> MessageQueueManager:
    """获取全局消息队列管理器实例"""
    global _message_queue_manager

    if _message_queue_manager is None:
        _message_queue_manager = MessageQueueManager()
        _message_queue_manager.initialize()

    return _message_queue_manager


def init_message_queue_manager(
    rabbitmq_url: Optional[str] = None,
) -> MessageQueueManager:
    """
    初始化全局消息队列管理器

    Args:
        rabbitmq_url: RabbitMQ连接URL

    Returns:
        消息队列管理器实例
    """
    global _message_queue_manager

    if _message_queue_manager is not None:
        _message_queue_manager.stop()

    _message_queue_manager = MessageQueueManager(rabbitmq_url)
    _message_queue_manager.initialize()

    return _message_queue_manager
