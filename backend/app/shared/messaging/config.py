"""
RabbitMQ 配置管理

定义队列、交换机、路由键等配置
"""

import logging
import os
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional

import pika
from pika import URLParameters

logger = logging.getLogger(__name__)


class QueueType(Enum):
    """队列类型枚举"""

    WEBHOOK = "webhook"
    AI_PROCESSING = "ai_processing"
    NOTIFICATION = "notification"
    MESSAGE = "message"
    TASK = "task"


class ExchangeType(Enum):
    """交换机类型枚举"""

    DIRECT = "direct"
    TOPIC = "topic"
    FANOUT = "fanout"
    HEADERS = "headers"


@dataclass
class QueueConfig:
    """队列配置"""

    name: str
    durable: bool = True
    auto_delete: bool = False
    arguments: Optional[Dict] = None
    routing_key: str = ""
    priority: int = 0


@dataclass
class ExchangeConfig:
    """交换机配置"""

    name: str
    type: ExchangeType = ExchangeType.DIRECT
    durable: bool = True
    auto_delete: bool = False
    arguments: Optional[Dict] = None


class RabbitMQConfig:
    """RabbitMQ配置管理器"""

    def __init__(self):
        # 从环境变量读取连接配置
        self.connection_url = os.getenv(
            "RABBITMQ_URL", "amqp://admin:chaiguanjia2024@localhost:5672/chaiguanjia"
        )
        self.connection_params = URLParameters(self.connection_url)

        # 连接池配置
        self.connection_pool_size = int(os.getenv("RABBITMQ_POOL_SIZE", "10"))
        self.channel_max = int(os.getenv("RABBITMQ_CHANNEL_MAX", "2047"))

        # 重试配置
        self.max_retries = int(os.getenv("RABBITMQ_MAX_RETRIES", "3"))
        self.retry_delay = int(os.getenv("RABBITMQ_RETRY_DELAY", "5"))

        # 预定义的交换机配置
        self.exchanges = self._get_exchange_configs()

        # 预定义的队列配置
        self.queues = self._get_queue_configs()

        # 路由配置
        self.routing_table = self._get_routing_table()

    def _get_exchange_configs(self) -> Dict[str, ExchangeConfig]:
        """获取交换机配置"""
        return {
            # 主交换机 - 直接路由
            "main_exchange": ExchangeConfig(
                name="chaiguanjia.main", type=ExchangeType.DIRECT, durable=True
            ),
            # 话题交换机 - 话题路由
            "topic_exchange": ExchangeConfig(
                name="chaiguanjia.topic", type=ExchangeType.TOPIC, durable=True
            ),
            # 死信交换机
            "dlx_exchange": ExchangeConfig(
                name="chaiguanjia.dlx", type=ExchangeType.DIRECT, durable=True
            ),
            # 延迟交换机
            "delay_exchange": ExchangeConfig(
                name="chaiguanjia.delay",
                type=ExchangeType.DIRECT,
                durable=True,
                arguments={"x-delayed-type": "direct"},
            ),
        }

    def _get_queue_configs(self) -> Dict[str, QueueConfig]:
        """获取队列配置"""
        return {
            # Webhook消息队列
            "webhook_queue": QueueConfig(
                name="webhook_queue",
                durable=True,
                arguments={
                    "x-message-ttl": 3600000,  # 1小时TTL
                    "x-max-length": 1000,  # 最大长度
                    "x-overflow": "reject-publish",
                    "x-dead-letter-exchange": "chaiguanjia.dlx",
                    "x-dead-letter-routing-key": "webhook.dead",
                },
                routing_key="webhook",
                priority=5,
            ),
            # AI处理队列
            "ai_processing_queue": QueueConfig(
                name="ai_processing_queue",
                durable=True,
                arguments={
                    "x-message-ttl": 1800000,  # 30分钟TTL
                    "x-max-length": 5000,
                    "x-overflow": "reject-publish",
                    "x-dead-letter-exchange": "chaiguanjia.dlx",
                    "x-dead-letter-routing-key": "ai.dead",
                },
                routing_key="ai.processing",
                priority=8,
            ),
            # 通知队列
            "notification_queue": QueueConfig(
                name="notification_queue",
                durable=True,
                arguments={
                    "x-message-ttl": 600000,  # 10分钟TTL
                    "x-max-length": 2000,
                    "x-overflow": "drop-head",  # 丢弃最旧的消息
                    "x-dead-letter-exchange": "chaiguanjia.dlx",
                    "x-dead-letter-routing-key": "notification.dead",
                },
                routing_key="notification",
                priority=6,
            ),
            # 消息处理队列
            "message_queue": QueueConfig(
                name="message_queue",
                durable=True,
                arguments={
                    "x-message-ttl": 7200000,  # 2小时TTL
                    "x-max-length": 10000,
                    "x-overflow": "reject-publish",
                    "x-dead-letter-exchange": "chaiguanjia.dlx",
                    "x-dead-letter-routing-key": "message.dead",
                },
                routing_key="message.process",
                priority=7,
            ),
            # 任务队列
            "task_queue": QueueConfig(
                name="task_queue",
                durable=True,
                arguments={
                    "x-message-ttl": 1800000,  # 30分钟TTL
                    "x-max-length": 3000,
                    "x-overflow": "reject-publish",
                    "x-dead-letter-exchange": "chaiguanjia.dlx",
                    "x-dead-letter-routing-key": "task.dead",
                },
                routing_key="task",
                priority=4,
            ),
            # 死信队列
            "webhook_dead_queue": QueueConfig(
                name="webhook_queue.dead", durable=True, routing_key="webhook.dead"
            ),
            "ai_dead_queue": QueueConfig(
                name="ai_processing_queue.dead", durable=True, routing_key="ai.dead"
            ),
            "notification_dead_queue": QueueConfig(
                name="notification_queue.dead",
                durable=True,
                routing_key="notification.dead",
            ),
            "message_dead_queue": QueueConfig(
                name="message_queue.dead", durable=True, routing_key="message.dead"
            ),
            "task_dead_queue": QueueConfig(
                name="task_queue.dead", durable=True, routing_key="task.dead"
            ),
        }

    def _get_routing_table(self) -> Dict[str, Dict[str, str]]:
        """获取路由表配置"""
        return {
            # 直接路由
            "direct": {
                "webhook": "webhook_queue",
                "ai.processing": "ai_processing_queue",
                "notification": "notification_queue",
                "message.process": "message_queue",
                "task": "task_queue",
                "webhook.dead": "webhook_dead_queue",
                "ai.dead": "ai_dead_queue",
                "notification.dead": "notification_dead_queue",
                "message.dead": "message_dead_queue",
                "task.dead": "task_dead_queue",
            },
            # 话题路由
            "topic": {
                "platform.*.webhook": "webhook_queue",
                "ai.*.processing": "ai_processing_queue",
                "notification.*": "notification_queue",
                "message.*.process": "message_queue",
                "task.*": "task_queue",
            },
        }

    def get_queue_config(self, queue_type: QueueType) -> QueueConfig:
        """根据队列类型获取配置"""
        queue_mapping = {
            QueueType.WEBHOOK: "webhook_queue",
            QueueType.AI_PROCESSING: "ai_processing_queue",
            QueueType.NOTIFICATION: "notification_queue",
            QueueType.MESSAGE: "message_queue",
            QueueType.TASK: "task_queue",
        }

        queue_name = queue_mapping.get(queue_type)
        if not queue_name:
            raise ValueError(f"Unknown queue type: {queue_type}")

        return self.queues[queue_name]

    def get_connection_parameters(self) -> URLParameters:
        """获取连接参数"""
        return self.connection_params

    def create_connection(self) -> pika.BlockingConnection:
        """创建连接"""
        try:
            connection = pika.BlockingConnection(self.connection_params)
            logger.info("RabbitMQ connection established successfully")
            return connection
        except Exception as e:
            logger.error(f"Failed to create RabbitMQ connection: {e}")
            raise

    def declare_topology(self, channel: pika.channel.Channel) -> None:
        """声明所有的交换机和队列"""
        try:
            # 声明交换机
            for exchange_config in self.exchanges.values():
                channel.exchange_declare(
                    exchange=exchange_config.name,
                    exchange_type=exchange_config.type.value,
                    durable=exchange_config.durable,
                    auto_delete=exchange_config.auto_delete,
                    arguments=exchange_config.arguments,
                )
                logger.info(f"Exchange declared: {exchange_config.name}")

            # 声明队列
            for queue_config in self.queues.values():
                channel.queue_declare(
                    queue=queue_config.name,
                    durable=queue_config.durable,
                    auto_delete=queue_config.auto_delete,
                    arguments=queue_config.arguments,
                )
                logger.info(f"Queue declared: {queue_config.name}")

            # 绑定队列到交换机
            self._bind_queues(channel)

            logger.info("RabbitMQ topology declared successfully")

        except Exception as e:
            logger.error(f"Failed to declare RabbitMQ topology: {e}")
            raise

    def _bind_queues(self, channel: pika.channel.Channel) -> None:
        """绑定队列到交换机"""
        # 绑定普通队列到主交换机
        for queue_name, queue_config in self.queues.items():
            if not queue_name.endswith("_dead_queue"):
                channel.queue_bind(
                    exchange="chaiguanjia.main",
                    queue=queue_config.name,
                    routing_key=queue_config.routing_key,
                )
                logger.debug(
                    f"Queue bound: {queue_config.name} -> chaiguanjia.main:{queue_config.routing_key}"
                )

        # 绑定死信队列到死信交换机
        for queue_name, queue_config in self.queues.items():
            if queue_name.endswith("_dead_queue"):
                channel.queue_bind(
                    exchange="chaiguanjia.dlx",
                    queue=queue_config.name,
                    routing_key=queue_config.routing_key,
                )
                logger.debug(
                    f"Dead letter queue bound: {queue_config.name} -> chaiguanjia.dlx:{queue_config.routing_key}"
                )
