"""
消息队列数据模型

定义消息、优先级、状态等数据结构
"""

import json
import logging
import uuid
from dataclasses import asdict, dataclass
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, Optional, Union

logger = logging.getLogger(__name__)


class MessagePriority(Enum):
    """消息优先级枚举"""

    LOW = 1
    NORMAL = 5
    HIGH = 8
    URGENT = 10


class MessageStatus(Enum):
    """消息状态枚举"""

    PENDING = "pending"  # 等待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 处理失败
    RETRY = "retry"  # 重试中
    DEAD_LETTER = "dead_letter"  # 死信


class MessageType(Enum):
    """消息类型枚举"""

    WEBHOOK = "webhook"
    AI_PROCESSING = "ai_processing"
    NOTIFICATION = "notification"
    MESSAGE_PROCESS = "message_process"
    TASK = "task"
    SYSTEM = "system"


@dataclass
class MessageHeader:
    """消息头信息"""

    message_id: str
    message_type: MessageType
    priority: MessagePriority
    timestamp: datetime
    source: str
    target: Optional[str] = None
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    content_type: str = "application/json"
    encoding: str = "utf-8"
    retry_count: int = 0
    max_retries: int = 3
    delay_seconds: int = 0
    expires_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理特殊类型
        data["message_type"] = self.message_type.value
        data["priority"] = self.priority.value
        data["timestamp"] = self.timestamp.isoformat()
        if self.expires_at:
            data["expires_at"] = self.expires_at.isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MessageHeader":
        """从字典创建"""
        # 处理特殊类型
        data["message_type"] = MessageType(data["message_type"])
        data["priority"] = MessagePriority(data["priority"])
        data["timestamp"] = datetime.fromisoformat(data["timestamp"])
        if data.get("expires_at"):
            data["expires_at"] = datetime.fromisoformat(data["expires_at"])
        return cls(**data)


@dataclass
class MessagePayload:
    """消息载荷"""

    data: Any
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {"data": self.data, "metadata": self.metadata or {}}

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MessagePayload":
        """从字典创建"""
        return cls(data=data.get("data"), metadata=data.get("metadata"))


class Message:
    """消息类"""

    def __init__(
        self,
        message_type: MessageType,
        payload: Union[MessagePayload, Dict[str, Any], Any],
        priority: MessagePriority = MessagePriority.NORMAL,
        source: str = "system",
        target: Optional[str] = None,
        correlation_id: Optional[str] = None,
        reply_to: Optional[str] = None,
        max_retries: int = 3,
        delay_seconds: int = 0,
        expires_in_seconds: Optional[int] = None,
    ):
        """
        初始化消息

        Args:
            message_type: 消息类型
            payload: 消息载荷
            priority: 消息优先级
            source: 消息来源
            target: 消息目标
            correlation_id: 关联ID
            reply_to: 回复地址
            max_retries: 最大重试次数
            delay_seconds: 延迟秒数
            expires_in_seconds: 过期时间（秒）
        """
        # 生成消息ID
        self.message_id = str(uuid.uuid4())

        # 设置过期时间
        expires_at = None
        if expires_in_seconds:
            expires_at = datetime.now(timezone.utc) + timedelta(
                seconds=expires_in_seconds
            )

        # 创建消息头
        self.header = MessageHeader(
            message_id=self.message_id,
            message_type=message_type,
            priority=priority,
            timestamp=datetime.now(timezone.utc),
            source=source,
            target=target,
            correlation_id=correlation_id or str(uuid.uuid4()),
            reply_to=reply_to,
            max_retries=max_retries,
            delay_seconds=delay_seconds,
            expires_at=expires_at,
        )

        # 处理载荷
        if isinstance(payload, MessagePayload):
            self.payload = payload
        elif isinstance(payload, dict):
            self.payload = MessagePayload.from_dict(payload)
        else:
            self.payload = MessagePayload(data=payload)

        # 消息状态
        self.status = MessageStatus.PENDING

        # 处理历史
        self.processing_history: list = []

    def serialize(self) -> str:
        """序列化消息"""
        try:
            message_data = {
                "header": self.header.to_dict(),
                "payload": self.payload.to_dict(),
                "status": self.status.value,
                "processing_history": self.processing_history,
            }
            return json.dumps(message_data, ensure_ascii=False, default=str)
        except Exception as e:
            logger.error(f"Failed to serialize message {self.message_id}: {e}")
            raise

    @classmethod
    def deserialize(cls, data: Union[str, bytes]) -> "Message":
        """反序列化消息"""
        try:
            if isinstance(data, bytes):
                data = data.decode("utf-8")

            message_data = json.loads(data)

            # 创建消息实例
            message = cls.__new__(cls)
            message.message_id = message_data["header"]["message_id"]
            message.header = MessageHeader.from_dict(message_data["header"])
            message.payload = MessagePayload.from_dict(message_data["payload"])
            message.status = MessageStatus(message_data["status"])
            message.processing_history = message_data.get("processing_history", [])

            return message
        except Exception as e:
            logger.error(f"Failed to deserialize message: {e}")
            raise

    def add_processing_record(
        self, action: str, details: Optional[Dict] = None
    ) -> None:
        """添加处理记录"""
        record = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "action": action,
            "details": details or {},
        }
        self.processing_history.append(record)

    def increment_retry_count(self) -> None:
        """增加重试次数"""
        self.header.retry_count += 1
        self.add_processing_record("retry", {"retry_count": self.header.retry_count})

    def should_retry(self) -> bool:
        """判断是否应该重试"""
        return self.header.retry_count < self.header.max_retries

    def is_expired(self) -> bool:
        """判断消息是否过期"""
        if not self.header.expires_at:
            return False
        return datetime.now(timezone.utc) > self.header.expires_at

    def get_routing_key(self) -> str:
        """获取路由键"""
        type_mapping = {
            MessageType.WEBHOOK: "webhook",
            MessageType.AI_PROCESSING: "ai.processing",
            MessageType.NOTIFICATION: "notification",
            MessageType.MESSAGE_PROCESS: "message.process",
            MessageType.TASK: "task",
            MessageType.SYSTEM: "system",
        }
        return type_mapping.get(self.header.message_type, "default")

    def to_amqp_properties(self) -> Dict[str, Any]:
        """转换为AMQP属性"""
        return {
            "message_id": self.message_id,
            "correlation_id": self.header.correlation_id,
            "reply_to": self.header.reply_to,
            "priority": self.header.priority.value,
            "timestamp": int(self.header.timestamp.timestamp()),
            "content_type": self.header.content_type,
            "content_encoding": self.header.encoding,
            "headers": {
                "message_type": self.header.message_type.value,
                "source": self.header.source,
                "target": self.header.target,
                "retry_count": self.header.retry_count,
                "max_retries": self.header.max_retries,
            },
        }

    def __str__(self) -> str:
        """字符串表示"""
        return f"Message({self.message_id}, {self.header.message_type.value}, {self.status.value})"

    def __repr__(self) -> str:
        """调试表示"""
        return (
            f"Message(id={self.message_id}, type={self.header.message_type.value}, "
            f"status={self.status.value}, priority={self.header.priority.value})"
        )


# 导入必要的模块
from datetime import timedelta
