"""
消息队列异常定义

定义所有消息队列相关的异常类
"""

from typing import Optional


class MessagingException(Exception):
    """消息队列基础异常"""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[dict] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)

    def __str__(self) -> str:
        error_info = f"MessagingException: {self.message}"
        if self.error_code:
            error_info += f" (Error Code: {self.error_code})"
        return error_info


class MessageConnectionError(MessagingException):
    """消息连接错误"""

    def __init__(self, message: str = "Failed to connect to RabbitMQ", **kwargs):
        super().__init__(message, error_code="CONN_ERROR", **kwargs)


class PublishError(MessagingException):
    """发布消息错误"""

    def __init__(self, message: str = "Failed to publish message", **kwargs):
        super().__init__(message, error_code="PUBLISH_ERROR", **kwargs)


class ConsumeError(MessagingException):
    """消费消息错误"""

    def __init__(self, message: str = "Failed to consume message", **kwargs):
        super().__init__(message, error_code="CONSUME_ERROR", **kwargs)


class SerializationError(MessagingException):
    """序列化错误"""

    def __init__(
        self, message: str = "Failed to serialize/deserialize message", **kwargs
    ):
        super().__init__(message, error_code="SERIALIZE_ERROR", **kwargs)


class QueueNotFoundError(MessagingException):
    """队列不存在错误"""

    def __init__(self, queue_name: str, **kwargs):
        message = f"Queue '{queue_name}' not found"
        super().__init__(message, error_code="QUEUE_NOT_FOUND", **kwargs)


class ExchangeNotFoundError(MessagingException):
    """交换机不存在错误"""

    def __init__(self, exchange_name: str, **kwargs):
        message = f"Exchange '{exchange_name}' not found"
        super().__init__(message, error_code="EXCHANGE_NOT_FOUND", **kwargs)


class RetryExhaustedError(MessagingException):
    """重试次数耗尽错误"""

    def __init__(self, message_id: str, retry_count: int, **kwargs):
        message = f"Message '{message_id}' exhausted all {retry_count} retries"
        super().__init__(message, error_code="RETRY_EXHAUSTED", **kwargs)


class MessageExpiredError(MessagingException):
    """消息过期错误"""

    def __init__(self, message_id: str, **kwargs):
        message = f"Message '{message_id}' has expired"
        super().__init__(message, error_code="MESSAGE_EXPIRED", **kwargs)


class ChannelError(MessagingException):
    """通道错误"""

    def __init__(self, message: str = "Channel operation failed", **kwargs):
        super().__init__(message, error_code="CHANNEL_ERROR", **kwargs)


class ConfigurationError(MessagingException):
    """配置错误"""

    def __init__(self, message: str = "Invalid configuration", **kwargs):
        super().__init__(message, error_code="CONFIG_ERROR", **kwargs)
