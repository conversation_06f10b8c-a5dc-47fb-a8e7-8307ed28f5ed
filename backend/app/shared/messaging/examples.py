"""
消息队列使用示例

演示如何使用消息队列系统
"""

import asyncio
import logging
import time
from typing import Any, Dict

from .config import QueueType
from .handlers import (
    AIProcessingHandler,
    MessageProcessHandler,
    NotificationHandler,
    TaskHandler,
    WebhookHandler,
)
from .manager import MessageQueueManager, get_message_queue_manager
from .models import MessagePriority, MessageType

logger = logging.getLogger(__name__)


class MessageQueueExamples:
    """消息队列使用示例"""

    def __init__(self):
        self.manager = get_message_queue_manager()

    def setup_handlers(self) -> None:
        """设置消息处理器"""
        logger.info("Setting up message handlers...")

        # 注册Webhook处理器
        webhook_handler = WebhookHandler()
        self.manager.register_handler(QueueType.WEBHOOK, webhook_handler)

        # 注册AI处理器
        ai_handler = AIProcessingHandler()
        self.manager.register_handler(QueueType.AI_PROCESSING, ai_handler)

        # 注册通知处理器
        notification_handler = NotificationHandler()
        self.manager.register_handler(QueueType.NOTIFICATION, notification_handler)

        # 注册消息处理器
        message_handler = MessageProcessHandler()
        self.manager.register_handler(QueueType.MESSAGE, message_handler)

        # 注册任务处理器
        task_handler = TaskHandler()
        self.manager.register_handler(QueueType.TASK, task_handler)

        logger.info("Message handlers registered successfully")

    def example_webhook_processing(self) -> None:
        """Webhook处理示例"""
        logger.info("=== Webhook Processing Example ===")

        # 模拟微信消息
        wechat_payload = {
            "platform": "wechat",
            "message_type": "text",
            "content": "你好，我想了解一下产品信息",
            "user_id": "wechat_user_12345",
            "timestamp": time.time(),
        }

        success = self.manager.publish_message(
            message_type=MessageType.WEBHOOK,
            payload=wechat_payload,
            priority=MessagePriority.HIGH,
            source="wechat_platform",
            target="webhook_processor",
        )

        if success:
            logger.info("WeChat webhook message published successfully")
        else:
            logger.error("Failed to publish WeChat webhook message")

        # 模拟淘宝消息
        taobao_payload = {
            "platform": "taobao",
            "message_type": "text",
            "content": "这个商品还有库存吗？",
            "user_id": "taobao_user_67890",
            "shop_id": "shop_12345",
            "timestamp": time.time(),
        }

        success = self.manager.publish_message(
            message_type=MessageType.WEBHOOK,
            payload=taobao_payload,
            priority=MessagePriority.NORMAL,
            source="taobao_platform",
        )

        if success:
            logger.info("Taobao webhook message published successfully")
        else:
            logger.error("Failed to publish Taobao webhook message")

    def example_ai_processing(self) -> None:
        """AI处理示例"""
        logger.info("=== AI Processing Example ===")

        # 聊天AI处理
        chat_payload = {
            "ai_type": "chat",
            "message": "你好，请帮我推荐一款手机",
            "user_id": "user_12345",
            "context": {"conversation_id": "conv_67890", "previous_messages": []},
        }

        success = self.manager.publish_message(
            message_type=MessageType.AI_PROCESSING,
            payload=chat_payload,
            priority=MessagePriority.HIGH,
            source="chat_interface",
        )

        if success:
            logger.info("Chat AI processing message published successfully")
        else:
            logger.error("Failed to publish chat AI processing message")

        # 图像AI处理
        image_payload = {
            "ai_type": "image",
            "image_url": "https://example.com/image.jpg",
            "user_id": "user_12345",
            "analysis_type": "product_recognition",
        }

        success = self.manager.publish_message(
            message_type=MessageType.AI_PROCESSING,
            payload=image_payload,
            priority=MessagePriority.NORMAL,
            source="image_interface",
        )

        if success:
            logger.info("Image AI processing message published successfully")
        else:
            logger.error("Failed to publish image AI processing message")

    def example_notifications(self) -> None:
        """通知发送示例"""
        logger.info("=== Notification Example ===")

        # 邮件通知
        email_payload = {
            "type": "email",
            "recipient": "<EMAIL>",
            "title": "订单确认通知",
            "content": "您的订单已确认，预计3-5个工作日内发货。",
            "template": "order_confirmation",
        }

        success = self.manager.publish_message(
            message_type=MessageType.NOTIFICATION,
            payload=email_payload,
            priority=MessagePriority.NORMAL,
            source="order_system",
        )

        if success:
            logger.info("Email notification published successfully")

        # 短信通知
        sms_payload = {
            "type": "sms",
            "recipient": "+86138****1234",
            "title": "验证码",
            "content": "您的验证码是：123456，5分钟内有效。",
            "urgent": True,
        }

        success = self.manager.publish_message(
            message_type=MessageType.NOTIFICATION,
            payload=sms_payload,
            priority=MessagePriority.HIGH,
            source="auth_system",
        )

        if success:
            logger.info("SMS notification published successfully")

        # Webhook通知
        webhook_payload = {
            "type": "webhook",
            "recipient": "https://api.example.com/webhook",
            "title": "系统告警",
            "content": "检测到异常流量，请及时处理。",
            "webhook_url": "https://api.example.com/webhook",
            "headers": {"Authorization": "Bearer token123"},
        }

        success = self.manager.publish_message(
            message_type=MessageType.NOTIFICATION,
            payload=webhook_payload,
            priority=MessagePriority.URGENT,
            source="monitoring_system",
        )

        if success:
            logger.info("Webhook notification published successfully")

    def example_batch_processing(self) -> None:
        """批量处理示例"""
        logger.info("=== Batch Processing Example ===")

        # 批量消息
        messages = [
            {
                "type": MessageType.NOTIFICATION.value,
                "payload": {
                    "type": "email",
                    "recipient": f"user{i}@example.com",
                    "title": "系统维护通知",
                    "content": f"系统将于今晚进行维护，预计持续2小时。用户{i}",
                },
                "priority": MessagePriority.NORMAL.value,
                "source": "system_admin",
            }
            for i in range(1, 6)
        ]

        result = self.manager.publish_batch_messages(messages)

        logger.info(
            f"Batch processing result: {result['successful']}/{result['total']} successful"
        )

        if result["errors"]:
            for error in result["errors"]:
                logger.error(f"Batch error: {error}")

    def example_task_scheduling(self) -> None:
        """任务调度示例"""
        logger.info("=== Task Scheduling Example ===")

        # 后台任务
        background_task_payload = {
            "task_type": "background",
            "task_name": "data_sync",
            "params": {
                "source": "database_a",
                "target": "database_b",
                "table": "users",
            },
        }

        success = self.manager.publish_message(
            message_type=MessageType.TASK,
            payload=background_task_payload,
            priority=MessagePriority.LOW,
            source="data_sync_service",
        )

        if success:
            logger.info("Background task published successfully")

        # 定时任务
        scheduled_task_payload = {
            "task_type": "scheduled",
            "task_name": "daily_report",
            "schedule_time": "2024-01-01 09:00:00",
            "params": {"report_type": "sales", "date_range": "2024-01-01"},
        }

        success = self.manager.publish_message(
            message_type=MessageType.TASK,
            payload=scheduled_task_payload,
            priority=MessagePriority.NORMAL,
            source="report_service",
        )

        if success:
            logger.info("Scheduled task published successfully")

        # 清理任务
        cleanup_task_payload = {
            "task_type": "cleanup",
            "cleanup_type": "logs",
            "params": {"retention_days": 30, "log_level": "DEBUG"},
        }

        success = self.manager.publish_message(
            message_type=MessageType.TASK,
            payload=cleanup_task_payload,
            priority=MessagePriority.LOW,
            source="maintenance_service",
        )

        if success:
            logger.info("Cleanup task published successfully")

    def example_monitoring(self) -> None:
        """监控示例"""
        logger.info("=== Monitoring Example ===")

        # 获取统计信息
        stats = self.manager.get_statistics()
        logger.info(f"Manager statistics: {stats}")

        # 获取健康状态
        health = self.manager.get_health_status()
        logger.info(f"Health status: {health['status']}")

        # 获取队列信息
        for queue_type in QueueType:
            queue_info = self.manager.get_queue_info(queue_type)
            if queue_info:
                logger.info(
                    f"Queue {queue_type.value}: {queue_info['message_count']} messages, "
                    f"{queue_info['consumer_count']} consumers"
                )

    def run_all_examples(self) -> None:
        """运行所有示例"""
        logger.info("Starting message queue examples...")

        try:
            # 设置处理器
            self.setup_handlers()

            # 启动管理器
            self.manager.start()

            # 运行示例
            self.example_webhook_processing()
            time.sleep(1)

            self.example_ai_processing()
            time.sleep(1)

            self.example_notifications()
            time.sleep(1)

            self.example_batch_processing()
            time.sleep(1)

            self.example_task_scheduling()
            time.sleep(1)

            # 等待消息处理
            logger.info("Waiting for message processing...")
            time.sleep(5)

            # 监控示例
            self.example_monitoring()

            logger.info("All examples completed successfully")

        except Exception as e:
            logger.error(f"Error running examples: {e}")
        finally:
            # 停止管理器
            self.manager.stop()


def run_examples():
    """运行示例"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # 运行示例
    examples = MessageQueueExamples()
    examples.run_all_examples()


def simple_publish_example():
    """简单发布示例"""
    logger.info("=== Simple Publish Example ===")

    # 获取管理器
    manager = get_message_queue_manager()

    try:
        # 启动管理器
        manager.start()

        # 发布一条简单消息
        success = manager.publish_message(
            message_type=MessageType.SYSTEM,
            payload={"message": "Hello, Message Queue!"},
            priority=MessagePriority.NORMAL,
            source="example_script",
        )

        if success:
            logger.info("Message published successfully")
        else:
            logger.error("Failed to publish message")

    except Exception as e:
        logger.error(f"Error in simple publish example: {e}")
    finally:
        manager.stop()


def simple_consumer_example():
    """简单消费者示例"""
    logger.info("=== Simple Consumer Example ===")

    from .consumer import MessageHandler

    class SimpleHandler(MessageHandler):
        def __init__(self):
            super().__init__("simple_handler")

        def process(self, message) -> bool:
            logger.info(f"Processing message: {message.message_id}")
            logger.info(f"Payload: {message.payload.data}")
            return True

    # 获取管理器
    manager = get_message_queue_manager()

    try:
        # 注册处理器
        handler = SimpleHandler()
        manager.register_handler(QueueType.TASK, handler)

        # 启动管理器
        manager.start()

        # 发布测试消息
        for i in range(3):
            manager.publish_message(
                message_type=MessageType.TASK,
                payload={"test_id": i, "message": f"Test message {i}"},
                priority=MessagePriority.NORMAL,
                source="test_script",
            )

        # 等待处理
        logger.info("Waiting for message processing...")
        time.sleep(5)

        # 查看统计
        stats = manager.get_statistics()
        logger.info(f"Consumer stats: {stats}")

    except Exception as e:
        logger.error(f"Error in simple consumer example: {e}")
    finally:
        manager.stop()


if __name__ == "__main__":
    # 运行所有示例
    run_examples()

    # 或者运行简单示例
    # simple_publish_example()
    # simple_consumer_example()
