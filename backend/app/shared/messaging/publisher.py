"""
消息发布者

负责将消息发布到RabbitMQ队列
"""

import json
import logging
import time
from contextlib import contextmanager
from typing import Any, Dict, List, Optional

import pika
from pika.exceptions import AMQPChannelError, AMQPConnectionError, AMQPError

from .config import RabbitMQConfig
from .exceptions import (
    ChannelError,
    MessageConnectionError,
    MessagingException,
    PublishError,
    SerializationError,
)
from .models import Message, MessageStatus
from .router import MessageRouter

logger = logging.getLogger(__name__)


class MessagePublisher:
    """消息发布者"""

    def __init__(self, config: RabbitMQConfig):
        """
        初始化发布者

        Args:
            config: RabbitMQ配置
        """
        self.config = config
        self.router = MessageRouter(config)
        self.connection: Optional[pika.BlockingConnection] = None
        self.channel: Optional[pika.channel.Channel] = None
        self._is_connected = False

        # 发布统计
        self.stats = {
            "messages_published": 0,
            "messages_failed": 0,
            "bytes_published": 0,
            "last_publish_time": None,
            "connection_attempts": 0,
            "connection_failures": 0,
        }

    def connect(self) -> None:
        """建立连接"""
        try:
            self.stats["connection_attempts"] += 1

            if self._is_connected and self.connection and self.connection.is_open:
                return

            logger.info("Connecting to RabbitMQ...")
            self.connection = self.config.create_connection()
            self.channel = self.connection.channel()

            # 声明拓扑结构
            self.config.declare_topology(self.channel)

            # 启用发布确认
            self.channel.confirm_delivery()

            self._is_connected = True
            logger.info("RabbitMQ connection established successfully")

        except Exception as e:
            self.stats["connection_failures"] += 1
            logger.error(f"Failed to connect to RabbitMQ: {e}")
            raise MessageConnectionError(f"Failed to connect to RabbitMQ: {e}")

    def disconnect(self) -> None:
        """断开连接"""
        try:
            if self.channel and self.channel.is_open:
                self.channel.close()

            if self.connection and self.connection.is_open:
                self.connection.close()

            self._is_connected = False
            logger.info("Disconnected from RabbitMQ")

        except Exception as e:
            logger.warning(f"Error during disconnection: {e}")

    @contextmanager
    def _ensure_connection(self):
        """确保连接可用的上下文管理器"""
        if not self._is_connected:
            self.connect()

        try:
            yield
        except (AMQPConnectionError, AMQPChannelError) as e:
            logger.warning(f"Connection lost, attempting to reconnect: {e}")
            self._is_connected = False
            self.connect()
            yield

    def publish(
        self,
        message: Message,
        exchange: Optional[str] = None,
        routing_key: Optional[str] = None,
        mandatory: bool = True,
        immediate: bool = False,
        **kwargs,
    ) -> bool:
        """
        发布消息

        Args:
            message: 要发布的消息
            exchange: 指定交换机（可选，会覆盖路由结果）
            routing_key: 指定路由键（可选，会覆盖路由结果）
            mandatory: 是否必须有队列接收
            immediate: 是否立即投递
            **kwargs: 其他发布参数

        Returns:
            发布是否成功

        Raises:
            PublishError: 发布失败时抛出
        """
        try:
            with self._ensure_connection():
                # 检查消息是否过期
                if message.is_expired():
                    raise PublishError(f"Message {message.message_id} has expired")

                # 路由消息
                routing_info = self.router.route_message(message)

                # 使用参数覆盖路由结果
                target_exchange = exchange or routing_info["exchange"]
                target_routing_key = routing_key or routing_info["routing_key"]

                # 序列化消息
                try:
                    body = message.serialize()
                    self.stats["bytes_published"] += len(body.encode("utf-8"))
                except Exception as e:
                    raise SerializationError(f"Failed to serialize message: {e}")

                # 准备发布属性
                properties = pika.BasicProperties(
                    **message.to_amqp_properties(),
                    delivery_mode=2,  # 持久化消息
                    **kwargs,
                )

                # 记录发布尝试
                message.add_processing_record(
                    "publish_attempt",
                    {
                        "exchange": target_exchange,
                        "routing_key": target_routing_key,
                        "mandatory": mandatory,
                    },
                )

                # 发布消息
                try:
                    confirmed = self.channel.basic_publish(
                        exchange=target_exchange,
                        routing_key=target_routing_key,
                        body=body,
                        properties=properties,
                        mandatory=mandatory,
                        immediate=immediate,
                    )

                    if confirmed:
                        message.status = MessageStatus.PENDING
                        message.add_processing_record(
                            "published",
                            {
                                "exchange": target_exchange,
                                "routing_key": target_routing_key,
                            },
                        )

                        self.stats["messages_published"] += 1
                        self.stats["last_publish_time"] = time.time()

                        logger.debug(
                            f"Message {message.message_id} published successfully to {target_exchange}:{target_routing_key}"
                        )
                        return True
                    else:
                        raise PublishError(
                            f"Message {message.message_id} was not confirmed by broker"
                        )

                except AMQPError as e:
                    raise PublishError(f"AMQP error during publish: {e}")

        except Exception as e:
            self.stats["messages_failed"] += 1
            message.add_processing_record("publish_failed", {"error": str(e)})

            if isinstance(e, MessagingException):
                raise
            else:
                raise PublishError(
                    f"Failed to publish message {message.message_id}: {e}"
                )

    def publish_batch(
        self,
        messages: List[Message],
        exchange: Optional[str] = None,
        fail_fast: bool = False,
    ) -> Dict[str, Any]:
        """
        批量发布消息

        Args:
            messages: 消息列表
            exchange: 指定交换机
            fail_fast: 遇到错误时是否立即停止

        Returns:
            批量发布结果统计
        """
        results = {"total": len(messages), "successful": 0, "failed": 0, "errors": []}

        with self._ensure_connection():
            for i, message in enumerate(messages):
                try:
                    success = self.publish(message, exchange=exchange)
                    if success:
                        results["successful"] += 1
                    else:
                        results["failed"] += 1

                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append(
                        {"message_id": message.message_id, "index": i, "error": str(e)}
                    )

                    if fail_fast:
                        logger.error(f"Batch publish failed at message {i}: {e}")
                        break

        logger.info(
            f"Batch publish completed: {results['successful']}/{results['total']} successful"
        )
        return results

    def publish_delayed(
        self,
        message: Message,
        delay_seconds: int,
        exchange: Optional[str] = None,
        routing_key: Optional[str] = None,
    ) -> bool:
        """
        发布延迟消息

        Args:
            message: 要发布的消息
            delay_seconds: 延迟秒数
            exchange: 指定交换机
            routing_key: 指定路由键

        Returns:
            发布是否成功
        """
        # 设置延迟
        message.header.delay_seconds = delay_seconds

        # 使用延迟交换机
        delay_exchange = exchange or "chaiguanjia.delay"

        # 添加延迟头信息
        delay_headers = {"x-delay": delay_seconds * 1000}  # 毫秒

        return self.publish(
            message,
            exchange=delay_exchange,
            routing_key=routing_key,
            headers=delay_headers,
        )

    def publish_priority(
        self,
        message: Message,
        priority: int,
        exchange: Optional[str] = None,
        routing_key: Optional[str] = None,
    ) -> bool:
        """
        发布优先级消息

        Args:
            message: 要发布的消息
            priority: 优先级（0-255）
            exchange: 指定交换机
            routing_key: 指定路由键

        Returns:
            发布是否成功
        """
        # 限制优先级范围
        priority = max(0, min(255, priority))

        return self.publish(
            message, exchange=exchange, routing_key=routing_key, priority=priority
        )

    def get_queue_info(self, queue_name: str) -> Optional[Dict[str, Any]]:
        """
        获取队列信息

        Args:
            queue_name: 队列名称

        Returns:
            队列信息字典
        """
        try:
            with self._ensure_connection():
                method = self.channel.queue_declare(queue=queue_name, passive=True)
                return {
                    "queue": queue_name,
                    "message_count": method.method.message_count,
                    "consumer_count": method.method.consumer_count,
                }
        except Exception as e:
            logger.error(f"Failed to get queue info for {queue_name}: {e}")
            return None

    def purge_queue(self, queue_name: str) -> int:
        """
        清空队列

        Args:
            queue_name: 队列名称

        Returns:
            清除的消息数量
        """
        try:
            with self._ensure_connection():
                method = self.channel.queue_purge(queue=queue_name)
                message_count = method.method.message_count
                logger.info(f"Purged {message_count} messages from queue {queue_name}")
                return message_count
        except Exception as e:
            logger.error(f"Failed to purge queue {queue_name}: {e}")
            raise ChannelError(f"Failed to purge queue {queue_name}: {e}")

    def delete_queue(
        self, queue_name: str, if_unused: bool = False, if_empty: bool = False
    ) -> int:
        """
        删除队列

        Args:
            queue_name: 队列名称
            if_unused: 仅在无消费者时删除
            if_empty: 仅在队列为空时删除

        Returns:
            删除的消息数量
        """
        try:
            with self._ensure_connection():
                method = self.channel.queue_delete(
                    queue=queue_name, if_unused=if_unused, if_empty=if_empty
                )
                message_count = method.method.message_count
                logger.info(f"Deleted queue {queue_name} with {message_count} messages")
                return message_count
        except Exception as e:
            logger.error(f"Failed to delete queue {queue_name}: {e}")
            raise ChannelError(f"Failed to delete queue {queue_name}: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """获取发布统计信息"""
        return self.stats.copy()

    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = {
            "messages_published": 0,
            "messages_failed": 0,
            "bytes_published": 0,
            "last_publish_time": None,
            "connection_attempts": 0,
            "connection_failures": 0,
        }

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health = {
            "status": "healthy",
            "connected": self._is_connected,
            "connection_open": False,
            "channel_open": False,
            "can_publish": False,
        }

        try:
            if self.connection:
                health["connection_open"] = self.connection.is_open

            if self.channel:
                health["channel_open"] = self.channel.is_open

            # 尝试发布测试消息
            if self._is_connected:
                # 创建测试消息
                from .models import MessagePriority, MessageType

                test_message = Message(
                    message_type=MessageType.SYSTEM,
                    payload={"health_check": True, "timestamp": time.time()},
                    priority=MessagePriority.LOW,
                )

                # 发布到测试队列
                health["can_publish"] = self.publish(
                    test_message, routing_key="health.check"
                )

        except Exception as e:
            health["status"] = "unhealthy"
            health["error"] = str(e)

        return health

    def __enter__(self):
        """进入上下文管理器"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        self.disconnect()
