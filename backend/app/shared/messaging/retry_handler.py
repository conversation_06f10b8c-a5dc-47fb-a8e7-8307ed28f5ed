"""
消息重试处理器

实现消息失败重试机制
"""

import logging
import math
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from .config import RabbitMQConfig
from .exceptions import MessagingException, RetryExhaustedError
from .models import Message, MessagePriority, MessageStatus
from .publisher import MessagePublisher

logger = logging.getLogger(__name__)


class RetryStrategy(Enum):
    """重试策略枚举"""

    FIXED_DELAY = "fixed_delay"  # 固定延迟
    EXPONENTIAL_BACKOFF = "exponential"  # 指数退避
    LINEAR_BACKOFF = "linear"  # 线性退避
    CUSTOM = "custom"  # 自定义策略


class RetryPolicy:
    """重试策略配置"""

    def __init__(
        self,
        strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
        initial_delay: int = 5,
        max_delay: int = 300,
        max_retries: int = 3,
        multiplier: float = 2.0,
        jitter: bool = True,
        retry_on_exceptions: Optional[List[type]] = None,
        custom_delay_calculator: Optional[Callable[[int], int]] = None,
    ):
        """
        初始化重试策略

        Args:
            strategy: 重试策略
            initial_delay: 初始延迟（秒）
            max_delay: 最大延迟（秒）
            max_retries: 最大重试次数
            multiplier: 指数退避倍数
            jitter: 是否添加随机抖动
            retry_on_exceptions: 需要重试的异常类型列表
            custom_delay_calculator: 自定义延迟计算函数
        """
        self.strategy = strategy
        self.initial_delay = initial_delay
        self.max_delay = max_delay
        self.max_retries = max_retries
        self.multiplier = multiplier
        self.jitter = jitter
        self.retry_on_exceptions = retry_on_exceptions or [MessagingException]
        self.custom_delay_calculator = custom_delay_calculator

    def calculate_delay(self, retry_count: int) -> int:
        """
        计算延迟时间

        Args:
            retry_count: 当前重试次数

        Returns:
            延迟秒数
        """
        if self.strategy == RetryStrategy.FIXED_DELAY:
            delay = self.initial_delay

        elif self.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.initial_delay * (self.multiplier ** (retry_count - 1))

        elif self.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.initial_delay + (retry_count - 1) * self.initial_delay

        elif self.strategy == RetryStrategy.CUSTOM and self.custom_delay_calculator:
            delay = self.custom_delay_calculator(retry_count)

        else:
            delay = self.initial_delay

        # 限制最大延迟
        delay = min(delay, self.max_delay)

        # 添加随机抖动
        if self.jitter:
            import random

            jitter_range = delay * 0.1  # 10% 抖动
            delay += random.uniform(-jitter_range, jitter_range)
            delay = max(1, delay)  # 确保延迟至少1秒

        return int(delay)

    def should_retry(
        self, retry_count: int, exception: Optional[Exception] = None
    ) -> bool:
        """
        判断是否应该重试

        Args:
            retry_count: 当前重试次数
            exception: 发生的异常

        Returns:
            是否应该重试
        """
        # 检查重试次数
        if retry_count >= self.max_retries:
            return False

        # 检查异常类型
        if exception and self.retry_on_exceptions:
            return any(
                isinstance(exception, exc_type) for exc_type in self.retry_on_exceptions
            )

        return True


class RetryHandler:
    """重试处理器"""

    def __init__(self, config: RabbitMQConfig, publisher: MessagePublisher):
        """
        初始化重试处理器

        Args:
            config: RabbitMQ配置
            publisher: 消息发布器
        """
        self.config = config
        self.publisher = publisher

        # 默认重试策略
        self.default_policy = RetryPolicy()

        # 特定消息类型的重试策略
        self.policies: Dict[str, RetryPolicy] = {}

        # 重试统计
        self.stats = {
            "total_retries": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "exhausted_retries": 0,
            "average_retry_delay": 0.0,
            "retry_by_type": {},
        }

        self._setup_default_policies()

    def _setup_default_policies(self) -> None:
        """设置默认重试策略"""
        # 高优先级消息：快速重试
        self.policies["high_priority"] = RetryPolicy(
            strategy=RetryStrategy.FIXED_DELAY,
            initial_delay=2,
            max_delay=30,
            max_retries=5,
        )

        # AI处理消息：指数退避
        self.policies["ai_processing"] = RetryPolicy(
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            initial_delay=10,
            max_delay=600,  # 最大10分钟
            max_retries=3,
            multiplier=2.0,
        )

        # 通知消息：线性退避
        self.policies["notification"] = RetryPolicy(
            strategy=RetryStrategy.LINEAR_BACKOFF,
            initial_delay=5,
            max_delay=120,  # 最大2分钟
            max_retries=4,
        )

        # Webhook消息：快速重试，失败后快速放弃
        self.policies["webhook"] = RetryPolicy(
            strategy=RetryStrategy.FIXED_DELAY,
            initial_delay=3,
            max_delay=15,
            max_retries=2,
            jitter=False,
        )

    def set_policy(self, message_type: str, policy: RetryPolicy) -> None:
        """
        设置特定消息类型的重试策略

        Args:
            message_type: 消息类型
            policy: 重试策略
        """
        self.policies[message_type] = policy
        logger.info(f"Set retry policy for message type: {message_type}")

    def get_policy(self, message: Message) -> RetryPolicy:
        """
        获取消息的重试策略

        Args:
            message: 消息对象

        Returns:
            适用的重试策略
        """
        # 根据优先级选择策略
        if message.header.priority.value >= 8:
            return self.policies.get("high_priority", self.default_policy)

        # 根据消息类型选择策略
        message_type = message.header.message_type.value
        if message_type in self.policies:
            return self.policies[message_type]

        # 返回默认策略
        return self.default_policy

    def should_retry(
        self, message: Message, exception: Optional[Exception] = None
    ) -> bool:
        """
        判断消息是否应该重试

        Args:
            message: 消息对象
            exception: 发生的异常

        Returns:
            是否应该重试
        """
        policy = self.get_policy(message)
        return policy.should_retry(message.header.retry_count, exception)

    def schedule_retry(
        self, message: Message, exception: Optional[Exception] = None
    ) -> bool:
        """
        安排消息重试

        Args:
            message: 要重试的消息
            exception: 导致重试的异常

        Returns:
            是否成功安排重试

        Raises:
            RetryExhaustedError: 重试次数耗尽时抛出
        """
        if not self.should_retry(message, exception):
            self.stats["exhausted_retries"] += 1
            raise RetryExhaustedError(
                message.message_id,
                message.header.retry_count,
                details={"exception": str(exception) if exception else None},
            )

        policy = self.get_policy(message)

        # 增加重试次数
        message.increment_retry_count()

        # 计算延迟时间
        delay = policy.calculate_delay(message.header.retry_count)

        # 记录重试信息
        retry_info = {
            "retry_count": message.header.retry_count,
            "delay_seconds": delay,
            "strategy": policy.strategy.value,
            "exception": str(exception) if exception else None,
            "scheduled_at": datetime.now().isoformat(),
        }

        message.add_processing_record("retry_scheduled", retry_info)
        message.status = MessageStatus.RETRY

        try:
            # 使用延迟发布重新发送消息
            success = self.publisher.publish_delayed(message, delay)

            if success:
                self.stats["total_retries"] += 1

                # 更新统计信息
                message_type = message.header.message_type.value
                if message_type not in self.stats["retry_by_type"]:
                    self.stats["retry_by_type"][message_type] = 0
                self.stats["retry_by_type"][message_type] += 1

                # 更新平均延迟
                total_retries = self.stats["total_retries"]
                if total_retries > 0:
                    self.stats["average_retry_delay"] = (
                        self.stats["average_retry_delay"] * (total_retries - 1) + delay
                    ) / total_retries

                logger.info(
                    f"Scheduled retry {message.header.retry_count} for message {message.message_id} in {delay}s"
                )
                return True
            else:
                self.stats["failed_retries"] += 1
                logger.error(
                    f"Failed to schedule retry for message {message.message_id}"
                )
                return False

        except Exception as e:
            self.stats["failed_retries"] += 1
            logger.error(
                f"Error scheduling retry for message {message.message_id}: {e}"
            )
            return False

    def handle_retry_success(self, message: Message) -> None:
        """
        处理重试成功

        Args:
            message: 成功处理的消息
        """
        self.stats["successful_retries"] += 1
        message.add_processing_record(
            "retry_success",
            {
                "final_retry_count": message.header.retry_count,
                "succeeded_at": datetime.now().isoformat(),
            },
        )

        logger.info(
            f"Message {message.message_id} succeeded after {message.header.retry_count} retries"
        )

    def handle_retry_failure(
        self, message: Message, final_exception: Exception
    ) -> None:
        """
        处理重试最终失败

        Args:
            message: 失败的消息
            final_exception: 最终失败的异常
        """
        self.stats["exhausted_retries"] += 1
        message.status = MessageStatus.DEAD_LETTER
        message.add_processing_record(
            "retry_exhausted",
            {
                "final_retry_count": message.header.retry_count,
                "final_exception": str(final_exception),
                "failed_at": datetime.now().isoformat(),
            },
        )

        logger.error(
            f"Message {message.message_id} exhausted all retries: {final_exception}"
        )

    def get_retry_queue_name(self, original_queue: str) -> str:
        """
        获取重试队列名称

        Args:
            original_queue: 原始队列名称

        Returns:
            重试队列名称
        """
        return f"{original_queue}_retry"

    def create_retry_queues(self) -> None:
        """创建重试队列"""
        try:
            connection = self.config.create_connection()
            channel = connection.channel()

            # 为每个主要队列创建重试队列
            main_queues = [
                "webhook_queue",
                "ai_processing_queue",
                "notification_queue",
                "message_queue",
                "task_queue",
            ]

            for queue_name in main_queues:
                retry_queue_name = self.get_retry_queue_name(queue_name)

                # 声明重试队列
                channel.queue_declare(
                    queue=retry_queue_name,
                    durable=True,
                    arguments={
                        "x-message-ttl": 3600000,  # 1小时TTL
                        "x-dead-letter-exchange": "chaiguanjia.main",
                        "x-dead-letter-routing-key": self.config.queues[
                            queue_name
                        ].routing_key,
                    },
                )

                logger.info(f"Created retry queue: {retry_queue_name}")

            connection.close()

        except Exception as e:
            logger.error(f"Failed to create retry queues: {e}")
            raise

    def get_stats(self) -> Dict[str, Any]:
        """获取重试统计信息"""
        stats = self.stats.copy()

        # 添加策略信息
        stats["policies"] = {}
        for msg_type, policy in self.policies.items():
            stats["policies"][msg_type] = {
                "strategy": policy.strategy.value,
                "initial_delay": policy.initial_delay,
                "max_delay": policy.max_delay,
                "max_retries": policy.max_retries,
                "multiplier": policy.multiplier,
            }

        return stats

    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = {
            "total_retries": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "exhausted_retries": 0,
            "average_retry_delay": 0.0,
            "retry_by_type": {},
        }

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health = {
            "status": "healthy",
            "total_retries": self.stats["total_retries"],
            "success_rate": 0.0,
            "average_delay": self.stats["average_retry_delay"],
        }

        try:
            # 计算成功率
            total = self.stats["successful_retries"] + self.stats["exhausted_retries"]
            if total > 0:
                health["success_rate"] = self.stats["successful_retries"] / total

            # 检查发布器健康状态
            publisher_health = self.publisher.health_check()
            if publisher_health["status"] != "healthy":
                health["status"] = "degraded"
                health["publisher_status"] = publisher_health["status"]

        except Exception as e:
            health["status"] = "unhealthy"
            health["error"] = str(e)

        return health
