"""
消息队列模块

提供基于RabbitMQ的消息队列功能，包括：
- 消息发布
- 消息消费
- 路由管理
- 重试机制
- 监控告警
"""

from .config import RabbitMQConfig
from .consumer import MessageConsumer
from .exceptions import (
    ConsumeError,
    MessageConnectionError,
    MessagingException,
    PublishError,
)
from .models import Message, MessagePriority, MessageStatus
from .publisher import MessagePublisher
from .router import MessageRouter

__all__ = [
    "RabbitMQConfig",
    "MessagePublisher",
    "MessageConsumer",
    "MessageRouter",
    "Message",
    "MessagePriority",
    "MessageStatus",
    "MessagingException",
    "MessageConnectionError",
    "PublishError",
    "ConsumeError",
]
