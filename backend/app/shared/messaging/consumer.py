"""
消息消费者

负责从RabbitMQ队列消费消息并处理
"""

import json
import logging
import threading
import time
from concurrent.futures import Future, ThreadPoolExecutor
from contextlib import contextmanager
from dataclasses import dataclass
from typing import Any, Callable, Dict, List, Optional

import pika
from pika.exceptions import AMQPChannelError, AMQPConnectionError, AMQPError

from .config import QueueType, RabbitMQConfig
from .exceptions import (
    ConsumeError,
    MessageConnectionError,
    MessageExpiredError,
    MessagingException,
    RetryExhaustedError,
    SerializationError,
)
from .models import Message, MessageStatus

logger = logging.getLogger(__name__)


@dataclass
class ConsumerConfig:
    """消费者配置"""

    queue_name: str
    handler: Callable[[Message], bool]
    auto_ack: bool = False
    prefetch_count: int = 1
    exclusive: bool = False
    consumer_tag: str = ""
    no_local: bool = False
    arguments: Optional[Dict] = None


class MessageHandler:
    """消息处理器基类"""

    def __init__(self, name: str):
        self.name = name
        self.stats = {
            "messages_processed": 0,
            "messages_failed": 0,
            "average_processing_time": 0.0,
            "last_processing_time": None,
        }

    def handle(self, message: Message) -> bool:
        """
        处理消息

        Args:
            message: 要处理的消息

        Returns:
            处理是否成功
        """
        start_time = time.time()

        try:
            success = self.process(message)

            if success:
                self.stats["messages_processed"] += 1
                message.status = MessageStatus.COMPLETED
                message.add_processing_record(
                    "processed",
                    {"handler": self.name, "processing_time": time.time() - start_time},
                )
            else:
                self.stats["messages_failed"] += 1
                message.status = MessageStatus.FAILED
                message.add_processing_record(
                    "processing_failed",
                    {"handler": self.name, "reason": "Handler returned False"},
                )

            # 更新统计信息
            processing_time = time.time() - start_time
            self.stats["last_processing_time"] = processing_time

            # 计算平均处理时间
            total_processed = (
                self.stats["messages_processed"] + self.stats["messages_failed"]
            )
            if total_processed > 0:
                self.stats["average_processing_time"] = (
                    self.stats["average_processing_time"] * (total_processed - 1)
                    + processing_time
                ) / total_processed

            return success

        except Exception as e:
            self.stats["messages_failed"] += 1
            message.status = MessageStatus.FAILED
            message.add_processing_record(
                "processing_error",
                {
                    "handler": self.name,
                    "error": str(e),
                    "processing_time": time.time() - start_time,
                },
            )
            logger.error(
                f"Handler {self.name} failed to process message {message.message_id}: {e}"
            )
            return False

    def process(self, message: Message) -> bool:
        """
        处理消息的具体实现（需要子类重写）

        Args:
            message: 要处理的消息

        Returns:
            处理是否成功
        """
        raise NotImplementedError("Subclasses must implement process method")

    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return self.stats.copy()


class MessageConsumer:
    """消息消费者"""

    def __init__(self, config: RabbitMQConfig):
        """
        初始化消费者

        Args:
            config: RabbitMQ配置
        """
        self.config = config
        self.connection: Optional[pika.BlockingConnection] = None
        self.channel: Optional[pika.channel.Channel] = None
        self._is_connected = False
        self._is_consuming = False
        self._stop_consuming = False

        # 消费者配置
        self.consumers: Dict[str, ConsumerConfig] = {}
        self.handlers: Dict[str, MessageHandler] = {}

        # 线程池用于并发处理
        self.thread_pool: Optional[ThreadPoolExecutor] = None
        self.max_workers = 10

        # 消费统计
        self.stats = {
            "messages_consumed": 0,
            "messages_acked": 0,
            "messages_nacked": 0,
            "messages_rejected": 0,
            "processing_errors": 0,
            "last_consume_time": None,
            "active_consumers": 0,
        }

        # 重试机制
        self.retry_publisher = None

    def connect(self) -> None:
        """建立连接"""
        try:
            if self._is_connected and self.connection and self.connection.is_open:
                return

            logger.info("Connecting to RabbitMQ for consuming...")
            self.connection = self.config.create_connection()
            self.channel = self.connection.channel()

            # 声明拓扑结构
            self.config.declare_topology(self.channel)

            self._is_connected = True
            logger.info("RabbitMQ consumer connection established successfully")

        except Exception as e:
            logger.error(f"Failed to connect to RabbitMQ: {e}")
            raise MessageConnectionError(f"Failed to connect to RabbitMQ: {e}")

    def disconnect(self) -> None:
        """断开连接"""
        try:
            self.stop_consuming()

            if self.thread_pool:
                self.thread_pool.shutdown(wait=True)
                self.thread_pool = None

            if self.channel and self.channel.is_open:
                self.channel.close()

            if self.connection and self.connection.is_open:
                self.connection.close()

            self._is_connected = False
            logger.info("Disconnected from RabbitMQ")

        except Exception as e:
            logger.warning(f"Error during disconnection: {e}")

    def add_consumer(
        self,
        queue_name: str,
        handler: MessageHandler,
        auto_ack: bool = False,
        prefetch_count: int = 1,
        exclusive: bool = False,
        **kwargs,
    ) -> None:
        """
        添加消费者

        Args:
            queue_name: 队列名称
            handler: 消息处理器
            auto_ack: 是否自动确认
            prefetch_count: 预取数量
            exclusive: 是否排他
            **kwargs: 其他参数
        """
        consumer_config = ConsumerConfig(
            queue_name=queue_name,
            handler=handler.handle,
            auto_ack=auto_ack,
            prefetch_count=prefetch_count,
            exclusive=exclusive,
            consumer_tag=kwargs.get("consumer_tag", f"{queue_name}_consumer"),
            no_local=kwargs.get("no_local", False),
            arguments=kwargs.get("arguments"),
        )

        self.consumers[queue_name] = consumer_config
        self.handlers[queue_name] = handler

        logger.info(f"Added consumer for queue: {queue_name}")

    def remove_consumer(self, queue_name: str) -> None:
        """
        移除消费者

        Args:
            queue_name: 队列名称
        """
        if queue_name in self.consumers:
            # 如果正在消费，先停止
            if self._is_consuming:
                consumer_config = self.consumers[queue_name]
                self.channel.basic_cancel(consumer_config.consumer_tag)

            del self.consumers[queue_name]
            if queue_name in self.handlers:
                del self.handlers[queue_name]

            logger.info(f"Removed consumer for queue: {queue_name}")

    def start_consuming(self, threaded: bool = False) -> None:
        """
        开始消费消息

        Args:
            threaded: 是否在线程中运行
        """
        if not self._is_connected:
            self.connect()

        if self._is_consuming:
            logger.warning("Already consuming messages")
            return

        # 初始化线程池
        if not self.thread_pool:
            self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)

        # 设置QoS
        for consumer_config in self.consumers.values():
            self.channel.basic_qos(prefetch_count=consumer_config.prefetch_count)

        # 注册消费者
        for queue_name, consumer_config in self.consumers.items():
            self.channel.basic_consume(
                queue=queue_name,
                on_message_callback=self._on_message,
                auto_ack=consumer_config.auto_ack,
                exclusive=consumer_config.exclusive,
                consumer_tag=consumer_config.consumer_tag,
                arguments=consumer_config.arguments,
            )

            self.stats["active_consumers"] += 1
            logger.info(f"Started consuming from queue: {queue_name}")

        self._is_consuming = True
        self._stop_consuming = False

        logger.info("Started consuming messages")

        if threaded:
            # 在单独线程中运行消费循环
            consume_thread = threading.Thread(target=self._consume_loop, daemon=True)
            consume_thread.start()
        else:
            # 在当前线程中运行消费循环
            self._consume_loop()

    def stop_consuming(self) -> None:
        """停止消费消息"""
        if not self._is_consuming:
            return

        self._stop_consuming = True

        # 取消所有消费者
        for consumer_config in self.consumers.values():
            try:
                if self.channel and self.channel.is_open:
                    self.channel.basic_cancel(consumer_config.consumer_tag)
            except Exception as e:
                logger.warning(
                    f"Error canceling consumer {consumer_config.consumer_tag}: {e}"
                )

        self._is_consuming = False
        self.stats["active_consumers"] = 0

        logger.info("Stopped consuming messages")

    def _consume_loop(self) -> None:
        """消费循环"""
        try:
            while not self._stop_consuming and self.channel and self.channel.is_open:
                self.connection.process_data_events(time_limit=1)
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, stopping consumer")
            self.stop_consuming()
        except Exception as e:
            logger.error(f"Error in consume loop: {e}")
            self._is_consuming = False

    def _on_message(
        self,
        channel: pika.channel.Channel,
        method: pika.spec.Basic.Deliver,
        properties: pika.BasicProperties,
        body: bytes,
    ) -> None:
        """
        消息回调处理器

        Args:
            channel: 通道
            method: 投递方法
            properties: 消息属性
            body: 消息体
        """
        self.stats["messages_consumed"] += 1
        self.stats["last_consume_time"] = time.time()

        delivery_tag = method.delivery_tag
        queue_name = method.routing_key  # 这里简化处理，实际可能需要从method中获取

        # 查找对应的队列名称
        target_queue = None
        for q_name in self.consumers.keys():
            if method.routing_key in self.config.queues.get(q_name, {}).get(
                "routing_key", ""
            ):
                target_queue = q_name
                break

        if not target_queue:
            # 使用第一个匹配的队列
            target_queue = list(self.consumers.keys())[0] if self.consumers else None

        if not target_queue or target_queue not in self.consumers:
            logger.error(f"No consumer found for routing key: {method.routing_key}")
            channel.basic_nack(delivery_tag=delivery_tag, requeue=False)
            self.stats["messages_rejected"] += 1
            return

        consumer_config = self.consumers[target_queue]

        try:
            # 反序列化消息
            message = Message.deserialize(body)
            message.add_processing_record(
                "consumed",
                {
                    "queue": target_queue,
                    "delivery_tag": delivery_tag,
                    "routing_key": method.routing_key,
                },
            )

            # 检查消息是否过期
            if message.is_expired():
                logger.warning(f"Message {message.message_id} has expired, rejecting")
                channel.basic_nack(delivery_tag=delivery_tag, requeue=False)
                self.stats["messages_rejected"] += 1
                self._handle_expired_message(message)
                return

            # 设置消息状态
            message.status = MessageStatus.PROCESSING

            # 异步处理消息（如果使用线程池）
            if self.thread_pool:
                future = self.thread_pool.submit(
                    self._process_message,
                    message,
                    channel,
                    delivery_tag,
                    consumer_config,
                )
                # 可以选择等待或不等待处理完成
                # future.result()  # 等待处理完成
            else:
                # 同步处理消息
                self._process_message(message, channel, delivery_tag, consumer_config)

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            self.stats["processing_errors"] += 1

            # 根据错误类型决定是否重新排队
            requeue = not isinstance(e, SerializationError)
            channel.basic_nack(delivery_tag=delivery_tag, requeue=requeue)
            self.stats["messages_nacked"] += 1

    def _process_message(
        self,
        message: Message,
        channel: pika.channel.Channel,
        delivery_tag: int,
        consumer_config: ConsumerConfig,
    ) -> None:
        """
        处理单个消息

        Args:
            message: 消息对象
            channel: 通道
            delivery_tag: 投递标签
            consumer_config: 消费者配置
        """
        try:
            # 处理消息
            success = consumer_config.handler(message)

            if success:
                # 确认消息
                if not consumer_config.auto_ack:
                    channel.basic_ack(delivery_tag=delivery_tag)
                self.stats["messages_acked"] += 1

                logger.debug(f"Message {message.message_id} processed successfully")

            else:
                # 处理失败，检查是否需要重试
                if message.should_retry():
                    self._handle_retry(message, channel, delivery_tag)
                else:
                    # 重试次数耗尽，发送到死信队列
                    self._handle_dead_letter(message, channel, delivery_tag)

        except Exception as e:
            logger.error(f"Error in message processing: {e}")
            self.stats["processing_errors"] += 1

            # 处理异常，检查是否需要重试
            if message.should_retry():
                self._handle_retry(message, channel, delivery_tag, error=str(e))
            else:
                self._handle_dead_letter(message, channel, delivery_tag, error=str(e))

    def _handle_retry(
        self,
        message: Message,
        channel: pika.channel.Channel,
        delivery_tag: int,
        error: Optional[str] = None,
    ) -> None:
        """
        处理消息重试

        Args:
            message: 消息对象
            channel: 通道
            delivery_tag: 投递标签
            error: 错误信息
        """
        message.increment_retry_count()
        message.status = MessageStatus.RETRY

        if error:
            message.add_processing_record("retry_due_to_error", {"error": error})

        # 计算重试延迟
        retry_delay = min(
            self.config.retry_delay * (2 ** (message.header.retry_count - 1)), 300
        )  # 最大5分钟

        try:
            # 如果有重试发布器，使用延迟发布
            if self.retry_publisher:
                self.retry_publisher.publish_delayed(message, retry_delay)
                channel.basic_ack(delivery_tag=delivery_tag)
                self.stats["messages_acked"] += 1

                logger.info(
                    f"Message {message.message_id} scheduled for retry {message.header.retry_count} in {retry_delay}s"
                )
            else:
                # 简单重新排队
                channel.basic_nack(delivery_tag=delivery_tag, requeue=True)
                self.stats["messages_nacked"] += 1

                logger.info(
                    f"Message {message.message_id} requeued for retry {message.header.retry_count}"
                )

        except Exception as e:
            logger.error(
                f"Failed to handle retry for message {message.message_id}: {e}"
            )
            channel.basic_nack(delivery_tag=delivery_tag, requeue=False)
            self.stats["messages_nacked"] += 1

    def _handle_dead_letter(
        self,
        message: Message,
        channel: pika.channel.Channel,
        delivery_tag: int,
        error: Optional[str] = None,
    ) -> None:
        """
        处理死信消息

        Args:
            message: 消息对象
            channel: 通道
            delivery_tag: 投递标签
            error: 错误信息
        """
        message.status = MessageStatus.DEAD_LETTER
        message.add_processing_record(
            "dead_letter",
            {
                "reason": (
                    "retry_exhausted"
                    if message.header.retry_count >= message.header.max_retries
                    else "processing_failed"
                ),
                "error": error,
                "final_retry_count": message.header.retry_count,
            },
        )

        # 拒绝消息，不重新排队（会发送到死信队列）
        channel.basic_nack(delivery_tag=delivery_tag, requeue=False)
        self.stats["messages_rejected"] += 1

        logger.warning(
            f"Message {message.message_id} sent to dead letter queue after {message.header.retry_count} retries"
        )

    def _handle_expired_message(self, message: Message) -> None:
        """
        处理过期消息

        Args:
            message: 过期的消息
        """
        message.status = MessageStatus.DEAD_LETTER
        message.add_processing_record(
            "expired",
            {
                "expired_at": time.time(),
                "expires_at": (
                    message.header.expires_at.isoformat()
                    if message.header.expires_at
                    else None
                ),
            },
        )

        logger.warning(f"Message {message.message_id} expired and was rejected")

    def set_retry_publisher(self, publisher) -> None:
        """
        设置重试发布器

        Args:
            publisher: 消息发布器实例
        """
        self.retry_publisher = publisher

    def get_queue_info(self, queue_name: str) -> Optional[Dict[str, Any]]:
        """
        获取队列信息

        Args:
            queue_name: 队列名称

        Returns:
            队列信息
        """
        try:
            if not self._is_connected:
                self.connect()

            method = self.channel.queue_declare(queue=queue_name, passive=True)
            return {
                "queue": queue_name,
                "message_count": method.method.message_count,
                "consumer_count": method.method.consumer_count,
            }
        except Exception as e:
            logger.error(f"Failed to get queue info for {queue_name}: {e}")
            return None

    def get_stats(self) -> Dict[str, Any]:
        """获取消费统计信息"""
        stats = self.stats.copy()

        # 添加处理器统计信息
        stats["handlers"] = {}
        for queue_name, handler in self.handlers.items():
            stats["handlers"][queue_name] = handler.get_stats()

        return stats

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health = {
            "status": "healthy",
            "connected": self._is_connected,
            "consuming": self._is_consuming,
            "active_consumers": self.stats["active_consumers"],
            "connection_open": False,
            "channel_open": False,
        }

        try:
            if self.connection:
                health["connection_open"] = self.connection.is_open

            if self.channel:
                health["channel_open"] = self.channel.is_open

        except Exception as e:
            health["status"] = "unhealthy"
            health["error"] = str(e)

        return health

    def __enter__(self):
        """进入上下文管理器"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        self.disconnect()
