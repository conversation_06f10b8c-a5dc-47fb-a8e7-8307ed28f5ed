"""
安全日志记录器

记录系统安全相关事件，包括登录、权限、异常行为等
支持结构化日志输出和安全事件分析
"""

import asyncio
import json
import logging
import traceback
from datetime import datetime
from enum import Enum
from functools import wraps
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class SecurityEventType(Enum):
    """安全事件类型"""

    # 认证事件
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    LOGIN_BLOCKED = "login_blocked"
    LOGOUT = "logout"
    TOKEN_REFRESH = "token_refresh"
    TOKEN_REVOKED = "token_revoked"

    # 授权事件
    PERMISSION_GRANTED = "permission_granted"
    PERMISSION_DENIED = "permission_denied"
    ROLE_ASSIGNED = "role_assigned"
    ROLE_REMOVED = "role_removed"

    # 账户事件
    ACCOUNT_CREATED = "account_created"
    ACCOUNT_ACTIVATED = "account_activated"
    ACCOUNT_BLOCKED = "account_blocked"
    ACCOUNT_SUSPENDED = "account_suspended"
    PASSWORD_CHANGED = "password_changed"
    EMAIL_VERIFIED = "email_verified"

    # 系统事件
    API_ACCESS = "api_access"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    DATA_EXPORT = "data_export"
    CONFIGURATION_CHANGED = "configuration_changed"

    # 安全威胁
    BRUTE_FORCE_ATTACK = "brute_force_attack"
    SQL_INJECTION_ATTEMPT = "sql_injection_attempt"
    XSS_ATTEMPT = "xss_attempt"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    DATA_BREACH_ATTEMPT = "data_breach_attempt"


class SecurityRiskLevel(Enum):
    """安全风险级别"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SecurityEvent:
    """安全事件模型"""

    def __init__(
        self,
        event_type: SecurityEventType,
        risk_level: SecurityRiskLevel,
        user_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        resource: Optional[str] = None,
        action: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        self.timestamp = datetime.now()
        self.event_type = event_type
        self.risk_level = risk_level
        self.user_id = user_id
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.resource = resource
        self.action = action
        self.details = details or {}
        self.metadata = metadata or {}
        self.event_id = self._generate_event_id()

    def _generate_event_id(self) -> str:
        """生成事件ID"""
        import uuid

        return str(uuid.uuid4())

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "event_id": self.event_id,
            "timestamp": self.timestamp.isoformat(),
            "event_type": self.event_type.value,
            "risk_level": self.risk_level.value,
            "user_id": self.user_id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "resource": self.resource,
            "action": self.action,
            "details": self.details,
            "metadata": self.metadata,
        }

    def to_json(self) -> str:
        """转换为JSON格式"""
        return json.dumps(self.to_dict(), ensure_ascii=False)


class SecurityLogger:
    """安全日志记录器"""

    def __init__(self, redis_client=None, alert_manager=None):
        self.redis_client = redis_client
        self.alert_manager = alert_manager

        # 配置安全日志记录器
        self.security_logger = self._setup_security_logger()

        # 事件缓存队列（用于批量处理）
        self.event_queue = asyncio.Queue()
        self.batch_size = 50
        self.batch_timeout = 30  # 秒

        # 威胁检测配置
        self.threat_detection_enabled = True
        self.max_login_attempts = 5
        self.suspicious_ip_threshold = 10

        # 启动后台处理任务
        asyncio.create_task(self._process_event_queue())

    def _setup_security_logger(self) -> logging.Logger:
        """设置安全日志记录器"""
        security_logger = logging.getLogger("security")
        security_logger.setLevel(logging.INFO)

        # 文件处理器 - 安全日志
        import os

        log_dir = os.getenv("SECURITY_LOG_DIR", "logs")
        os.makedirs(log_dir, exist_ok=True)

        file_handler = logging.FileHandler(f"{log_dir}/security.log")
        file_handler.setLevel(logging.INFO)

        # JSON格式化器
        formatter = logging.Formatter(
            '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "message": %(message)s}'
        )
        file_handler.setFormatter(formatter)

        security_logger.addHandler(file_handler)

        return security_logger

    async def log_event(self, event: SecurityEvent):
        """记录安全事件"""
        try:
            # 记录到日志文件
            self.security_logger.info(event.to_json())

            # 添加到处理队列
            await self.event_queue.put(event)

            # 高风险事件立即处理
            if event.risk_level in [SecurityRiskLevel.HIGH, SecurityRiskLevel.CRITICAL]:
                await self._handle_high_risk_event(event)

        except Exception as e:
            logger.error(f"记录安全事件失败: {e}")

    async def _process_event_queue(self):
        """处理事件队列（后台任务）"""
        batch = []
        last_process_time = datetime.now()

        while True:
            try:
                # 获取事件（带超时）
                try:
                    event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                    batch.append(event)
                except asyncio.TimeoutError:
                    pass

                # 检查是否需要处理批次
                now = datetime.now()
                should_process = len(batch) >= self.batch_size or (
                    batch and (now - last_process_time).seconds >= self.batch_timeout
                )

                if should_process and batch:
                    await self._process_event_batch(batch)
                    batch = []
                    last_process_time = now

            except Exception as e:
                logger.error(f"处理事件队列失败: {e}")
                await asyncio.sleep(1)

    async def _process_event_batch(self, events: List[SecurityEvent]):
        """批量处理安全事件"""
        try:
            # 存储到Redis
            if self.redis_client:
                await self._store_events_to_redis(events)

            # 威胁检测
            if self.threat_detection_enabled:
                await self._detect_threats(events)

            # 统计分析
            await self._update_security_metrics(events)

        except Exception as e:
            logger.error(f"批量处理安全事件失败: {e}")

    async def _store_events_to_redis(self, events: List[SecurityEvent]):
        """将事件存储到Redis"""
        try:
            for event in events:
                # 按日期分组存储
                date_key = event.timestamp.strftime("%Y-%m-%d")
                key = f"security:events:{date_key}"

                await self.redis_client.sadd(key, event.to_json())
                await self.redis_client.expire(key, 86400 * 30)  # 保留30天

                # 存储高风险事件索引
                if event.risk_level in [
                    SecurityRiskLevel.HIGH,
                    SecurityRiskLevel.CRITICAL,
                ]:
                    risk_key = f"security:high_risk:{date_key}"
                    await self.redis_client.sadd(risk_key, event.to_json())
                    await self.redis_client.expire(risk_key, 86400 * 90)  # 保留90天

        except Exception as e:
            logger.error(f"存储安全事件到Redis失败: {e}")

    async def _detect_threats(self, events: List[SecurityEvent]):
        """威胁检测"""
        try:
            # 按IP地址分组
            ip_events = {}
            user_events = {}

            for event in events:
                if event.ip_address:
                    if event.ip_address not in ip_events:
                        ip_events[event.ip_address] = []
                    ip_events[event.ip_address].append(event)

                if event.user_id:
                    if event.user_id not in user_events:
                        user_events[event.user_id] = []
                    user_events[event.user_id].append(event)

            # 检测暴力破解攻击
            await self._detect_brute_force(ip_events, user_events)

            # 检测可疑活动
            await self._detect_suspicious_activity(ip_events)

        except Exception as e:
            logger.error(f"威胁检测失败: {e}")

    async def _detect_brute_force(self, ip_events: Dict, user_events: Dict):
        """检测暴力破解攻击"""
        try:
            # 检测IP级别的暴力破解
            for ip_address, events in ip_events.items():
                failed_logins = [
                    e for e in events if e.event_type == SecurityEventType.LOGIN_FAILED
                ]

                if len(failed_logins) >= self.max_login_attempts:
                    await self.log_event(
                        SecurityEvent(
                            event_type=SecurityEventType.BRUTE_FORCE_ATTACK,
                            risk_level=SecurityRiskLevel.HIGH,
                            ip_address=ip_address,
                            details={
                                "failed_attempts": len(failed_logins),
                                "time_window": "recent_batch",
                            },
                        )
                    )

            # 检测用户级别的暴力破解
            for user_id, events in user_events.items():
                failed_logins = [
                    e for e in events if e.event_type == SecurityEventType.LOGIN_FAILED
                ]

                if len(failed_logins) >= self.max_login_attempts:
                    await self.log_event(
                        SecurityEvent(
                            event_type=SecurityEventType.BRUTE_FORCE_ATTACK,
                            risk_level=SecurityRiskLevel.HIGH,
                            user_id=user_id,
                            details={
                                "failed_attempts": len(failed_logins),
                                "time_window": "recent_batch",
                            },
                        )
                    )

        except Exception as e:
            logger.error(f"暴力破解检测失败: {e}")

    async def _detect_suspicious_activity(self, ip_events: Dict):
        """检测可疑活动"""
        try:
            for ip_address, events in ip_events.items():
                # 检测异常高频访问
                if len(events) >= self.suspicious_ip_threshold:
                    await self.log_event(
                        SecurityEvent(
                            event_type=SecurityEventType.SUSPICIOUS_ACTIVITY,
                            risk_level=SecurityRiskLevel.MEDIUM,
                            ip_address=ip_address,
                            details={
                                "event_count": len(events),
                                "reason": "high_frequency_access",
                            },
                        )
                    )

                # 检测多种事件类型（可能的扫描行为）
                event_types = set(e.event_type for e in events)
                if len(event_types) >= 5:
                    await self.log_event(
                        SecurityEvent(
                            event_type=SecurityEventType.SUSPICIOUS_ACTIVITY,
                            risk_level=SecurityRiskLevel.MEDIUM,
                            ip_address=ip_address,
                            details={
                                "event_types": list(et.value for et in event_types),
                                "reason": "diverse_event_pattern",
                            },
                        )
                    )

        except Exception as e:
            logger.error(f"可疑活动检测失败: {e}")

    async def _handle_high_risk_event(self, event: SecurityEvent):
        """处理高风险事件"""
        try:
            # 发送即时告警
            if self.alert_manager:
                await self.alert_manager.send_security_alert(event)

            # 记录到高优先级日志
            critical_logger = logging.getLogger("security.critical")
            critical_logger.critical(event.to_json())

        except Exception as e:
            logger.error(f"处理高风险事件失败: {e}")

    async def _update_security_metrics(self, events: List[SecurityEvent]):
        """更新安全指标"""
        try:
            if not self.redis_client:
                return

            for event in events:
                # 按事件类型统计
                type_key = f"security:metrics:event_type:{event.event_type.value}"
                await self.redis_client.incr(type_key)
                await self.redis_client.expire(type_key, 86400 * 7)  # 7天

                # 按风险级别统计
                risk_key = f"security:metrics:risk_level:{event.risk_level.value}"
                await self.redis_client.incr(risk_key)
                await self.redis_client.expire(risk_key, 86400 * 7)  # 7天

                # 按小时统计
                hour_key = (
                    f"security:metrics:hourly:{event.timestamp.strftime('%Y-%m-%d-%H')}"
                )
                await self.redis_client.incr(hour_key)
                await self.redis_client.expire(hour_key, 86400 * 3)  # 3天

        except Exception as e:
            logger.error(f"更新安全指标失败: {e}")

    async def get_security_metrics(self, days: int = 7) -> Dict[str, Any]:
        """获取安全指标"""
        try:
            if not self.redis_client:
                return {}

            metrics = {}

            # 获取事件类型统计
            event_types = {}
            for event_type in SecurityEventType:
                key = f"security:metrics:event_type:{event_type.value}"
                count = await self.redis_client.get(key) or "0"
                event_types[event_type.value] = int(count)
            metrics["event_types"] = event_types

            # 获取风险级别统计
            risk_levels = {}
            for risk_level in SecurityRiskLevel:
                key = f"security:metrics:risk_level:{risk_level.value}"
                count = await self.redis_client.get(key) or "0"
                risk_levels[risk_level.value] = int(count)
            metrics["risk_levels"] = risk_levels

            return metrics

        except Exception as e:
            logger.error(f"获取安全指标失败: {e}")
            return {}

    async def search_security_events(
        self,
        start_date: datetime,
        end_date: datetime,
        event_types: List[SecurityEventType] = None,
        risk_levels: List[SecurityRiskLevel] = None,
        user_id: int = None,
        ip_address: str = None,
        limit: int = 100,
    ) -> List[SecurityEvent]:
        """搜索安全事件"""
        try:
            if not self.redis_client:
                return []

            # 搜索指定日期范围内的事件
            events = []
            current_date = start_date.date()
            end_date_only = end_date.date()

            while current_date <= end_date_only:
                date_key = current_date.strftime("%Y-%m-%d")
                key = f"security:events:{date_key}"

                event_data_list = await self.redis_client.smembers(key)
                for event_data in event_data_list:
                    event_dict = json.loads(event_data)

                    # 过滤条件
                    if event_types and event_dict["event_type"] not in [
                        et.value for et in event_types
                    ]:
                        continue
                    if risk_levels and event_dict["risk_level"] not in [
                        rl.value for rl in risk_levels
                    ]:
                        continue
                    if user_id and event_dict["user_id"] != user_id:
                        continue
                    if ip_address and event_dict["ip_address"] != ip_address:
                        continue

                    events.append(event_dict)

                    if len(events) >= limit:
                        break

                if len(events) >= limit:
                    break

                current_date += timedelta(days=1)

            return events[:limit]

        except Exception as e:
            logger.error(f"搜索安全事件失败: {e}")
            return []


def security_event_logger(
    event_type: SecurityEventType,
    risk_level: SecurityRiskLevel = SecurityRiskLevel.LOW,
    include_request_info: bool = True,
):
    """安全事件记录装饰器"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                # 执行原函数
                result = await func(*args, **kwargs)

                # 记录成功事件
                event = SecurityEvent(
                    event_type=event_type,
                    risk_level=risk_level,
                    details={"function": func.__name__, "status": "success"},
                )

                await get_security_logger().log_event(event)
                return result

            except Exception as e:
                # 记录失败事件
                event = SecurityEvent(
                    event_type=SecurityEventType.SUSPICIOUS_ACTIVITY,
                    risk_level=SecurityRiskLevel.MEDIUM,
                    details={
                        "function": func.__name__,
                        "status": "error",
                        "error": str(e),
                        "traceback": traceback.format_exc(),
                    },
                )

                await get_security_logger().log_event(event)
                raise

        return wrapper

    return decorator


# 全局安全日志记录器实例
_security_logger = None


async def get_security_logger() -> SecurityLogger:
    """获取安全日志记录器实例"""
    global _security_logger
    if _security_logger is None:
        from ..cache import redis_client
        from .alert_manager import get_alert_manager

        _security_logger = SecurityLogger(
            redis_client=redis_client, alert_manager=await get_alert_manager()
        )
    return _security_logger
