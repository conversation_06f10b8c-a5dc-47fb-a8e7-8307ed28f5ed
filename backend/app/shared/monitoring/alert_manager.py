"""
告警管理器

负责系统告警的生成、发送和管理
支持多种通知渠道（邮件、钉钉、企业微信等）
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

import httpx

logger = logging.getLogger(__name__)


class AlertLevel(Enum):
    """告警级别"""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertChannel(Enum):
    """告警渠道"""

    EMAIL = "email"
    DINGTALK = "dingtalk"
    WECHAT_WORK = "wechat_work"
    SLACK = "slack"
    WEBHOOK = "webhook"


class Alert:
    """告警模型"""

    def __init__(
        self,
        title: str,
        message: str,
        level: AlertLevel,
        source: str,
        tags: List[str] = None,
        metadata: Dict[str, Any] = None,
    ):
        self.id = self._generate_alert_id()
        self.timestamp = datetime.now()
        self.title = title
        self.message = message
        self.level = level
        self.source = source
        self.tags = tags or []
        self.metadata = metadata or {}
        self.status = "active"
        self.resolved_at = None

    def _generate_alert_id(self) -> str:
        """生成告警ID"""
        import uuid

        return str(uuid.uuid4())

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "title": self.title,
            "message": self.message,
            "level": self.level.value,
            "source": self.source,
            "tags": self.tags,
            "metadata": self.metadata,
            "status": self.status,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
        }


class AlertManager:
    """告警管理器"""

    def __init__(self, redis_client=None):
        self.redis_client = redis_client

        # 告警配置
        import os

        self.enabled_channels = self._parse_alert_channels()
        self.alert_retention_days = int(os.getenv("ALERT_RETENTION_DAYS", "30"))
        self.rate_limit_window = int(
            os.getenv("ALERT_RATE_LIMIT_WINDOW", "300")
        )  # 5分钟
        self.max_alerts_per_window = int(os.getenv("MAX_ALERTS_PER_WINDOW", "10"))

        # 通知渠道配置
        self.notification_configs = {
            AlertChannel.EMAIL: {
                "smtp_server": os.getenv("SMTP_SERVER"),
                "smtp_port": int(os.getenv("SMTP_PORT", "587")),
                "smtp_username": os.getenv("SMTP_USERNAME"),
                "smtp_password": os.getenv("SMTP_PASSWORD"),
                "from_email": os.getenv("ALERT_FROM_EMAIL"),
                "to_emails": os.getenv("ALERT_TO_EMAILS", "").split(","),
            },
            AlertChannel.DINGTALK: {
                "webhook_url": os.getenv("DINGTALK_WEBHOOK_URL"),
                "secret": os.getenv("DINGTALK_SECRET"),
            },
            AlertChannel.WECHAT_WORK: {
                "webhook_url": os.getenv("WECHAT_WORK_WEBHOOK_URL")
            },
            AlertChannel.WEBHOOK: {
                "url": os.getenv("ALERT_WEBHOOK_URL"),
                "headers": json.loads(os.getenv("ALERT_WEBHOOK_HEADERS", "{}")),
            },
        }

        # 告警规则
        self.alert_rules = self._load_alert_rules()

        # HTTP客户端
        self.http_client = httpx.AsyncClient(timeout=30.0)

    def _parse_alert_channels(self) -> List[AlertChannel]:
        """解析启用的告警渠道"""
        import os

        channels_str = os.getenv("ALERT_CHANNELS", "email")
        channels = []

        for channel_name in channels_str.split(","):
            channel_name = channel_name.strip()
            try:
                channel = AlertChannel(channel_name)
                channels.append(channel)
            except ValueError:
                logger.warning(f"未知的告警渠道: {channel_name}")

        return channels

    def _load_alert_rules(self) -> Dict[str, Dict]:
        """加载告警规则"""
        # 默认告警规则
        rules = {
            "security_high_risk": {
                "pattern": "security.*high|critical",
                "channels": [AlertChannel.EMAIL, AlertChannel.DINGTALK],
                "level": AlertLevel.CRITICAL,
                "immediate": True,
            },
            "authentication_failure": {
                "pattern": "login_failed",
                "channels": [AlertChannel.EMAIL],
                "level": AlertLevel.WARNING,
                "rate_limit": True,
            },
            "system_error": {
                "pattern": "error|exception",
                "channels": [AlertChannel.EMAIL, AlertChannel.DINGTALK],
                "level": AlertLevel.ERROR,
                "rate_limit": True,
            },
            "performance_degradation": {
                "pattern": "performance|slow",
                "channels": [AlertChannel.EMAIL],
                "level": AlertLevel.WARNING,
                "rate_limit": True,
            },
        }

        # 从环境变量或配置文件加载自定义规则
        try:
            import os

            custom_rules_file = os.getenv("ALERT_RULES_FILE")
            if custom_rules_file and os.path.exists(custom_rules_file):
                with open(custom_rules_file, "r", encoding="utf-8") as f:
                    custom_rules = json.load(f)
                    rules.update(custom_rules)
        except Exception as e:
            logger.warning(f"加载自定义告警规则失败: {e}")

        return rules

    async def send_alert(
        self,
        title: str,
        message: str,
        level: AlertLevel = AlertLevel.INFO,
        source: str = "system",
        tags: List[str] = None,
        channels: List[AlertChannel] = None,
        immediate: bool = False,
    ) -> bool:
        """发送告警"""
        try:
            # 创建告警对象
            alert = Alert(
                title=title,
                message=message,
                level=level,
                source=source,
                tags=tags or [],
            )

            # 检查频率限制
            if not immediate and not await self._check_rate_limit(alert):
                logger.info(f"告警被频率限制拒绝: {alert.title}")
                return False

            # 存储告警
            await self._store_alert(alert)

            # 确定发送渠道
            if channels is None:
                channels = self._get_channels_for_alert(alert)

            # 发送到各个渠道
            success = True
            for channel in channels:
                if channel in self.enabled_channels:
                    channel_success = await self._send_to_channel(alert, channel)
                    success = success and channel_success

            return success

        except Exception as e:
            logger.error(f"发送告警失败: {e}")
            return False

    async def send_security_alert(self, security_event) -> bool:
        """发送安全告警"""
        try:
            title = f"安全事件告警: {security_event.event_type.value}"
            message = f"""
安全事件详情:
- 事件类型: {security_event.event_type.value}
- 风险级别: {security_event.risk_level.value}
- 时间: {security_event.timestamp.isoformat()}
- 用户ID: {security_event.user_id or 'N/A'}
- IP地址: {security_event.ip_address or 'N/A'}
- 资源: {security_event.resource or 'N/A'}
- 操作: {security_event.action or 'N/A'}
- 详情: {json.dumps(security_event.details, ensure_ascii=False, indent=2)}
            """.strip()

            # 根据风险级别确定告警级别
            alert_level = AlertLevel.INFO
            if security_event.risk_level.value == "medium":
                alert_level = AlertLevel.WARNING
            elif security_event.risk_level.value == "high":
                alert_level = AlertLevel.ERROR
            elif security_event.risk_level.value == "critical":
                alert_level = AlertLevel.CRITICAL

            return await self.send_alert(
                title=title,
                message=message,
                level=alert_level,
                source="security",
                tags=["security", security_event.event_type.value],
                immediate=(security_event.risk_level.value in ["high", "critical"]),
            )

        except Exception as e:
            logger.error(f"发送安全告警失败: {e}")
            return False

    async def _check_rate_limit(self, alert: Alert) -> bool:
        """检查频率限制"""
        try:
            if not self.redis_client:
                return True  # 无Redis时不限制

            # 生成频率限制键
            rate_key = f"alert:rate_limit:{alert.source}:{alert.level.value}"

            # 检查当前窗口内的告警数量
            current_count = await self.redis_client.get(rate_key) or "0"
            current_count = int(current_count)

            if current_count >= self.max_alerts_per_window:
                return False

            # 增加计数并设置过期时间
            await self.redis_client.incr(rate_key)
            await self.redis_client.expire(rate_key, self.rate_limit_window)

            return True

        except Exception as e:
            logger.error(f"检查频率限制失败: {e}")
            return True  # 出错时允许发送

    async def _store_alert(self, alert: Alert):
        """存储告警"""
        try:
            if not self.redis_client:
                return

            # 存储告警详情
            alert_key = f"alert:details:{alert.id}"
            await self.redis_client.set(
                alert_key, alert.to_dict(), ttl=self.alert_retention_days * 24 * 3600
            )

            # 添加到告警索引
            date_key = alert.timestamp.strftime("%Y-%m-%d")
            index_key = f"alert:index:{date_key}"
            await self.redis_client.sadd(index_key, alert.id)
            await self.redis_client.expire(
                index_key, self.alert_retention_days * 24 * 3600
            )

            # 按级别索引
            level_key = f"alert:level:{alert.level.value}:{date_key}"
            await self.redis_client.sadd(level_key, alert.id)
            await self.redis_client.expire(
                level_key, self.alert_retention_days * 24 * 3600
            )

        except Exception as e:
            logger.error(f"存储告警失败: {e}")

    def _get_channels_for_alert(self, alert: Alert) -> List[AlertChannel]:
        """根据告警获取发送渠道"""
        # 根据告警规则确定渠道
        for rule_name, rule_config in self.alert_rules.items():
            pattern = rule_config.get("pattern", "")
            if any(pattern in tag for tag in alert.tags) or pattern in alert.source:
                return rule_config.get("channels", [AlertChannel.EMAIL])

        # 默认渠道
        if alert.level in [AlertLevel.ERROR, AlertLevel.CRITICAL]:
            return [AlertChannel.EMAIL, AlertChannel.DINGTALK]
        else:
            return [AlertChannel.EMAIL]

    async def _send_to_channel(self, alert: Alert, channel: AlertChannel) -> bool:
        """发送告警到指定渠道"""
        try:
            if channel == AlertChannel.EMAIL:
                return await self._send_email_alert(alert)
            elif channel == AlertChannel.DINGTALK:
                return await self._send_dingtalk_alert(alert)
            elif channel == AlertChannel.WECHAT_WORK:
                return await self._send_wechat_work_alert(alert)
            elif channel == AlertChannel.WEBHOOK:
                return await self._send_webhook_alert(alert)
            else:
                logger.warning(f"不支持的告警渠道: {channel}")
                return False

        except Exception as e:
            logger.error(f"发送告警到{channel.value}失败: {e}")
            return False

    async def _send_email_alert(self, alert: Alert) -> bool:
        """发送邮件告警"""
        try:
            import smtplib
            from email.mime.multipart import MIMEMultipart
            from email.mime.text import MIMEText

            config = self.notification_configs[AlertChannel.EMAIL]

            if not all(
                [config["smtp_server"], config["from_email"], config["to_emails"]]
            ):
                logger.warning("邮件告警配置不完整")
                return False

            # 创建邮件
            msg = MIMEMultipart()
            msg["From"] = config["from_email"]
            msg["To"] = ", ".join(config["to_emails"])
            msg["Subject"] = f"[{alert.level.value.upper()}] {alert.title}"

            # 邮件正文
            body = f"""
告警详情:

标题: {alert.title}
级别: {alert.level.value.upper()}
时间: {alert.timestamp.isoformat()}
来源: {alert.source}
标签: {', '.join(alert.tags)}

消息:
{alert.message}

元数据:
{json.dumps(alert.metadata, ensure_ascii=False, indent=2)}
            """.strip()

            msg.attach(MIMEText(body, "plain", "utf-8"))

            # 发送邮件
            server = smtplib.SMTP(config["smtp_server"], config["smtp_port"])
            server.starttls()
            server.login(config["smtp_username"], config["smtp_password"])
            server.send_message(msg)
            server.quit()

            logger.info(f"邮件告警发送成功: {alert.title}")
            return True

        except Exception as e:
            logger.error(f"发送邮件告警失败: {e}")
            return False

    async def _send_dingtalk_alert(self, alert: Alert) -> bool:
        """发送钉钉告警"""
        try:
            config = self.notification_configs[AlertChannel.DINGTALK]

            if not config["webhook_url"]:
                logger.warning("钉钉告警配置不完整")
                return False

            # 钉钉消息格式
            content = f"""
**{alert.title}**

> 级别: {alert.level.value.upper()}
> 时间: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
> 来源: {alert.source}

{alert.message}
            """.strip()

            data = {
                "msgtype": "markdown",
                "markdown": {"title": alert.title, "text": content},
            }

            # 添加签名（如果配置了secret）
            if config.get("secret"):
                import base64
                import hashlib
                import hmac
                import time
                import urllib.parse

                timestamp = str(round(time.time() * 1000))
                secret_enc = config["secret"].encode("utf-8")
                string_to_sign = f'{timestamp}\n{config["secret"]}'
                string_to_sign_enc = string_to_sign.encode("utf-8")
                hmac_code = hmac.new(
                    secret_enc, string_to_sign_enc, digestmod=hashlib.sha256
                ).digest()
                sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))

                webhook_url = (
                    f'{config["webhook_url"]}&timestamp={timestamp}&sign={sign}'
                )
            else:
                webhook_url = config["webhook_url"]

            # 发送请求
            response = await self.http_client.post(webhook_url, json=data)
            response.raise_for_status()

            result = response.json()
            if result.get("errcode") == 0:
                logger.info(f"钉钉告警发送成功: {alert.title}")
                return True
            else:
                logger.error(f"钉钉告警发送失败: {result}")
                return False

        except Exception as e:
            logger.error(f"发送钉钉告警失败: {e}")
            return False

    async def _send_wechat_work_alert(self, alert: Alert) -> bool:
        """发送企业微信告警"""
        try:
            config = self.notification_configs[AlertChannel.WECHAT_WORK]

            if not config["webhook_url"]:
                logger.warning("企业微信告警配置不完整")
                return False

            # 企业微信消息格式
            content = f"""
{alert.title}

级别: {alert.level.value.upper()}
时间: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
来源: {alert.source}

{alert.message}
            """.strip()

            data = {"msgtype": "text", "text": {"content": content}}

            # 发送请求
            response = await self.http_client.post(config["webhook_url"], json=data)
            response.raise_for_status()

            result = response.json()
            if result.get("errcode") == 0:
                logger.info(f"企业微信告警发送成功: {alert.title}")
                return True
            else:
                logger.error(f"企业微信告警发送失败: {result}")
                return False

        except Exception as e:
            logger.error(f"发送企业微信告警失败: {e}")
            return False

    async def _send_webhook_alert(self, alert: Alert) -> bool:
        """发送Webhook告警"""
        try:
            config = self.notification_configs[AlertChannel.WEBHOOK]

            if not config["url"]:
                logger.warning("Webhook告警配置不完整")
                return False

            # 发送告警数据
            headers = config.get("headers", {})
            headers["Content-Type"] = "application/json"

            response = await self.http_client.post(
                config["url"], json=alert.to_dict(), headers=headers
            )
            response.raise_for_status()

            logger.info(f"Webhook告警发送成功: {alert.title}")
            return True

        except Exception as e:
            logger.error(f"发送Webhook告警失败: {e}")
            return False

    async def get_alerts(
        self,
        start_date: datetime = None,
        end_date: datetime = None,
        level: AlertLevel = None,
        source: str = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """获取告警列表"""
        try:
            if not self.redis_client:
                return []

            # 默认查询最近24小时
            if not start_date:
                start_date = datetime.now() - timedelta(hours=24)
            if not end_date:
                end_date = datetime.now()

            alerts = []
            current_date = start_date.date()
            end_date_only = end_date.date()

            while current_date <= end_date_only:
                date_key = current_date.strftime("%Y-%m-%d")

                if level:
                    index_key = f"alert:level:{level.value}:{date_key}"
                else:
                    index_key = f"alert:index:{date_key}"

                alert_ids = await self.redis_client.smembers(index_key)

                for alert_id in alert_ids:
                    alert_key = f"alert:details:{alert_id}"
                    alert_data = await self.redis_client.get(alert_key)

                    if alert_data:
                        alert_dict = json.loads(alert_data)

                        # 过滤条件
                        if source and alert_dict.get("source") != source:
                            continue

                        alerts.append(alert_dict)

                        if len(alerts) >= limit:
                            break

                if len(alerts) >= limit:
                    break

                current_date += timedelta(days=1)

            # 按时间倒序排列
            alerts.sort(key=lambda x: x["timestamp"], reverse=True)
            return alerts[:limit]

        except Exception as e:
            logger.error(f"获取告警列表失败: {e}")
            return []

    async def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        try:
            if not self.redis_client:
                return False

            alert_key = f"alert:details:{alert_id}"
            alert_data = await self.redis_client.get(alert_key)

            if not alert_data:
                logger.warning(f"告警不存在: {alert_id}")
                return False

            alert_dict = json.loads(alert_data)
            alert_dict["status"] = "resolved"
            alert_dict["resolved_at"] = datetime.now().isoformat()

            await self.redis_client.set(alert_key, alert_dict)

            logger.info(f"告警已解决: {alert_id}")
            return True

        except Exception as e:
            logger.error(f"解决告警失败: {e}")
            return False

    async def get_alert_stats(self) -> Dict[str, Any]:
        """获取告警统计"""
        try:
            if not self.redis_client:
                return {}

            stats = {
                "total": 0,
                "by_level": {},
                "by_source": {},
                "resolved": 0,
                "active": 0,
            }

            # 获取最近7天的告警统计
            for i in range(7):
                date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                index_key = f"alert:index:{date}"

                alert_ids = await self.redis_client.smembers(index_key)
                stats["total"] += len(alert_ids)

                for alert_id in alert_ids:
                    alert_key = f"alert:details:{alert_id}"
                    alert_data = await self.redis_client.get(alert_key)

                    if alert_data:
                        alert_dict = json.loads(alert_data)

                        # 按级别统计
                        level = alert_dict.get("level", "unknown")
                        stats["by_level"][level] = stats["by_level"].get(level, 0) + 1

                        # 按来源统计
                        source = alert_dict.get("source", "unknown")
                        stats["by_source"][source] = (
                            stats["by_source"].get(source, 0) + 1
                        )

                        # 按状态统计
                        if alert_dict.get("status") == "resolved":
                            stats["resolved"] += 1
                        else:
                            stats["active"] += 1

            return stats

        except Exception as e:
            logger.error(f"获取告警统计失败: {e}")
            return {}


# 全局告警管理器实例
_alert_manager = None


async def get_alert_manager() -> AlertManager:
    """获取告警管理器实例"""
    global _alert_manager
    if _alert_manager is None:
        from ..cache import redis_client

        _alert_manager = AlertManager(redis_client=redis_client)
    return _alert_manager
