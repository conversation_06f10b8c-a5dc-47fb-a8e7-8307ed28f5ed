"""
系统健康监控器

监控系统各组件的健康状态，包括数据库、Redis、外部服务等
提供健康检查接口和异常告警
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

import httpx
import psutil

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """健康状态"""

    HEALTHY = "healthy"
    WARNING = "warning"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class ComponentType(Enum):
    """组件类型"""

    DATABASE = "database"
    CACHE = "cache"
    EXTERNAL_API = "external_api"
    SYSTEM = "system"
    APPLICATION = "application"


class HealthCheck:
    """健康检查项"""

    def __init__(
        self,
        name: str,
        component_type: ComponentType,
        check_func: Callable,
        timeout: int = 30,
        interval: int = 60,
        critical: bool = False,
    ):
        self.name = name
        self.component_type = component_type
        self.check_func = check_func
        self.timeout = timeout
        self.interval = interval
        self.critical = critical
        self.last_check = None
        self.last_status = HealthStatus.UNKNOWN
        self.last_error = None
        self.response_time = 0


class HealthMonitor:
    """系统健康监控器"""

    def __init__(self, redis_client=None, alert_manager=None):
        self.redis_client = redis_client
        self.alert_manager = alert_manager

        # 健康检查项
        self.health_checks = {}

        # 监控配置
        import os

        self.monitoring_enabled = (
            os.getenv("HEALTH_MONITORING_ENABLED", "true").lower() == "true"
        )
        self.check_interval = int(os.getenv("HEALTH_CHECK_INTERVAL", "60"))  # 60秒
        self.alert_threshold = int(
            os.getenv("HEALTH_ALERT_THRESHOLD", "3")
        )  # 连续失败3次告警
        self.history_retention_hours = int(
            os.getenv("HEALTH_HISTORY_RETENTION_HOURS", "24")
        )

        # 失败计数器
        self.failure_counts = {}

        # HTTP客户端
        self.http_client = httpx.AsyncClient(timeout=30.0)

        # 注册默认健康检查
        self._register_default_checks()

        # 启动监控任务
        if self.monitoring_enabled:
            asyncio.create_task(self._monitoring_loop())

    def _register_default_checks(self):
        """注册默认健康检查"""
        # 数据库健康检查
        self.register_check(
            "database_connection",
            ComponentType.DATABASE,
            self._check_database,
            timeout=10,
            critical=True,
        )

        # Redis健康检查
        self.register_check(
            "redis_connection",
            ComponentType.CACHE,
            self._check_redis,
            timeout=5,
            critical=True,
        )

        # 系统资源检查
        self.register_check(
            "system_resources",
            ComponentType.SYSTEM,
            self._check_system_resources,
            timeout=5,
            critical=False,
        )

        # 磁盘空间检查
        self.register_check(
            "disk_space",
            ComponentType.SYSTEM,
            self._check_disk_space,
            timeout=5,
            critical=False,
        )

        # 应用程序健康检查
        self.register_check(
            "application_health",
            ComponentType.APPLICATION,
            self._check_application_health,
            timeout=10,
            critical=False,
        )

    def register_check(
        self,
        name: str,
        component_type: ComponentType,
        check_func: Callable,
        timeout: int = 30,
        interval: int = 60,
        critical: bool = False,
    ):
        """注册健康检查项"""
        self.health_checks[name] = HealthCheck(
            name=name,
            component_type=component_type,
            check_func=check_func,
            timeout=timeout,
            interval=interval,
            critical=critical,
        )

        # 初始化失败计数器
        self.failure_counts[name] = 0

        logger.info(f"注册健康检查: {name}")

    async def _monitoring_loop(self):
        """监控主循环"""
        logger.info("健康监控服务启动")

        while True:
            try:
                await self._run_health_checks()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"健康监控循环异常: {e}")
                await asyncio.sleep(5)

    async def _run_health_checks(self):
        """运行所有健康检查"""
        tasks = []

        for check_name, health_check in self.health_checks.items():
            # 检查是否需要运行
            if self._should_run_check(health_check):
                task = asyncio.create_task(self._run_single_check(health_check))
                tasks.append(task)

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

    def _should_run_check(self, health_check: HealthCheck) -> bool:
        """判断是否需要运行检查"""
        if not health_check.last_check:
            return True

        elapsed = (datetime.now() - health_check.last_check).total_seconds()
        return elapsed >= health_check.interval

    async def _run_single_check(self, health_check: HealthCheck):
        """运行单个健康检查"""
        try:
            start_time = time.time()

            # 执行检查（带超时）
            result = await asyncio.wait_for(
                health_check.check_func(), timeout=health_check.timeout
            )

            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # 毫秒

            # 更新检查状态
            health_check.last_check = datetime.now()
            health_check.response_time = response_time

            if result.get("status") == "healthy":
                await self._handle_check_success(health_check, result)
            else:
                await self._handle_check_failure(health_check, result)

        except asyncio.TimeoutError:
            await self._handle_check_timeout(health_check)
        except Exception as e:
            await self._handle_check_error(health_check, e)

    async def _handle_check_success(self, health_check: HealthCheck, result: Dict):
        """处理检查成功"""
        health_check.last_status = HealthStatus.HEALTHY
        health_check.last_error = None

        # 重置失败计数器
        self.failure_counts[health_check.name] = 0

        # 记录结果
        await self._record_check_result(health_check, result)

        logger.debug(f"健康检查成功: {health_check.name}")

    async def _handle_check_failure(self, health_check: HealthCheck, result: Dict):
        """处理检查失败"""
        error_msg = result.get("error", "检查失败")

        health_check.last_status = HealthStatus.UNHEALTHY
        health_check.last_error = error_msg

        # 增加失败计数器
        self.failure_counts[health_check.name] += 1

        # 记录结果
        await self._record_check_result(health_check, result)

        # 检查是否需要告警
        await self._check_alert_threshold(health_check)

        logger.warning(f"健康检查失败: {health_check.name} - {error_msg}")

    async def _handle_check_timeout(self, health_check: HealthCheck):
        """处理检查超时"""
        health_check.last_status = HealthStatus.UNHEALTHY
        health_check.last_error = f"检查超时 (>{health_check.timeout}s)"
        health_check.last_check = datetime.now()

        # 增加失败计数器
        self.failure_counts[health_check.name] += 1

        # 记录结果
        result = {
            "status": "unhealthy",
            "error": health_check.last_error,
            "timeout": True,
        }
        await self._record_check_result(health_check, result)

        # 检查是否需要告警
        await self._check_alert_threshold(health_check)

        logger.warning(f"健康检查超时: {health_check.name}")

    async def _handle_check_error(self, health_check: HealthCheck, error: Exception):
        """处理检查异常"""
        health_check.last_status = HealthStatus.UNHEALTHY
        health_check.last_error = str(error)
        health_check.last_check = datetime.now()

        # 增加失败计数器
        self.failure_counts[health_check.name] += 1

        # 记录结果
        result = {"status": "unhealthy", "error": str(error), "exception": True}
        await self._record_check_result(health_check, result)

        # 检查是否需要告警
        await self._check_alert_threshold(health_check)

        logger.error(f"健康检查异常: {health_check.name} - {error}")

    async def _record_check_result(self, health_check: HealthCheck, result: Dict):
        """记录检查结果"""
        try:
            if not self.redis_client:
                return

            # 记录历史数据
            timestamp = datetime.now().isoformat()
            record = {
                "timestamp": timestamp,
                "status": health_check.last_status.value,
                "response_time": health_check.response_time,
                "error": health_check.last_error,
                "details": result,
            }

            # 按小时分组存储
            hour_key = datetime.now().strftime("%Y-%m-%d-%H")
            history_key = f"health:history:{health_check.name}:{hour_key}"

            await self.redis_client.sadd(history_key, json.dumps(record))
            await self.redis_client.expire(
                history_key, self.history_retention_hours * 3600
            )

            # 存储最新状态
            status_key = f"health:status:{health_check.name}"
            await self.redis_client.set(status_key, record, ttl=self.check_interval * 2)

        except Exception as e:
            logger.error(f"记录健康检查结果失败: {e}")

    async def _check_alert_threshold(self, health_check: HealthCheck):
        """检查告警阈值"""
        failure_count = self.failure_counts.get(health_check.name, 0)

        if failure_count >= self.alert_threshold and self.alert_manager:
            # 发送告警
            title = f"健康检查失败: {health_check.name}"
            message = f"""
组件: {health_check.name}
类型: {health_check.component_type.value}
状态: {health_check.last_status.value}
连续失败次数: {failure_count}
最后错误: {health_check.last_error or 'N/A'}
响应时间: {health_check.response_time:.2f}ms
是否关键: {'是' if health_check.critical else '否'}
            """.strip()

            from .alert_manager import AlertLevel

            alert_level = (
                AlertLevel.CRITICAL if health_check.critical else AlertLevel.ERROR
            )

            await self.alert_manager.send_alert(
                title=title,
                message=message,
                level=alert_level,
                source="health_monitor",
                tags=["health", health_check.component_type.value, health_check.name],
                immediate=health_check.critical,
            )

    # 默认健康检查实现
    async def _check_database(self) -> Dict[str, Any]:
        """检查数据库连接"""
        try:
            # 这里应该注入实际的数据库连接
            # 简化实现，实际应该执行一个简单的查询
            await asyncio.sleep(0.1)  # 模拟数据库查询

            return {
                "status": "healthy",
                "details": {
                    "connection": "active",
                    "pool_size": 10,
                    "active_connections": 2,
                },
            }
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    async def _check_redis(self) -> Dict[str, Any]:
        """检查Redis连接"""
        try:
            if not self.redis_client:
                return {"status": "unhealthy", "error": "Redis客户端未配置"}

            # 执行PING命令
            await self.redis_client.redis.ping()

            # 获取Redis信息
            info = await self.redis_client.redis.info()

            return {
                "status": "healthy",
                "details": {
                    "version": info.get("redis_version"),
                    "connected_clients": info.get("connected_clients"),
                    "used_memory_human": info.get("used_memory_human"),
                    "uptime_in_seconds": info.get("uptime_in_seconds"),
                },
            }
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    async def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存使用率
            memory = psutil.virtual_memory()

            # 负载平均值
            load_avg = (
                psutil.getloadavg() if hasattr(psutil, "getloadavg") else (0, 0, 0)
            )

            # 判断健康状态
            status = "healthy"
            warnings = []

            if cpu_percent > 80:
                status = "warning"
                warnings.append(f"CPU使用率过高: {cpu_percent:.1f}%")

            if memory.percent > 85:
                status = "warning"
                warnings.append(f"内存使用率过高: {memory.percent:.1f}%")

            if cpu_percent > 95 or memory.percent > 95:
                status = "unhealthy"

            return {
                "status": status,
                "warnings": warnings,
                "details": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_total": memory.total,
                    "memory_available": memory.available,
                    "load_avg": load_avg,
                },
            }
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    async def _check_disk_space(self) -> Dict[str, Any]:
        """检查磁盘空间"""
        try:
            disk_usage = psutil.disk_usage("/")

            usage_percent = (disk_usage.used / disk_usage.total) * 100

            # 判断健康状态
            status = "healthy"
            warnings = []

            if usage_percent > 80:
                status = "warning"
                warnings.append(f"磁盘使用率过高: {usage_percent:.1f}%")

            if usage_percent > 95:
                status = "unhealthy"

            return {
                "status": status,
                "warnings": warnings,
                "details": {
                    "usage_percent": usage_percent,
                    "total": disk_usage.total,
                    "used": disk_usage.used,
                    "free": disk_usage.free,
                },
            }
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    async def _check_application_health(self) -> Dict[str, Any]:
        """检查应用程序健康状态"""
        try:
            # 检查应用程序特定的健康指标
            # 这里可以添加业务逻辑相关的检查

            return {
                "status": "healthy",
                "details": {
                    "uptime": time.time() - psutil.Process().create_time(),
                    "threads": len(psutil.Process().threads()),
                    "open_files": len(psutil.Process().open_files()),
                },
            }
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    async def get_health_status(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            overall_status = HealthStatus.HEALTHY
            components = {}

            for name, health_check in self.health_checks.items():
                component_info = {
                    "status": health_check.last_status.value,
                    "last_check": (
                        health_check.last_check.isoformat()
                        if health_check.last_check
                        else None
                    ),
                    "response_time": health_check.response_time,
                    "error": health_check.last_error,
                    "critical": health_check.critical,
                    "component_type": health_check.component_type.value,
                }

                components[name] = component_info

                # 更新整体状态
                if health_check.last_status == HealthStatus.UNHEALTHY:
                    if health_check.critical:
                        overall_status = HealthStatus.UNHEALTHY
                    elif overall_status != HealthStatus.UNHEALTHY:
                        overall_status = HealthStatus.WARNING
                elif (
                    health_check.last_status == HealthStatus.WARNING
                    and overall_status == HealthStatus.HEALTHY
                ):
                    overall_status = HealthStatus.WARNING

            return {
                "status": overall_status.value,
                "timestamp": datetime.now().isoformat(),
                "components": components,
            }

        except Exception as e:
            logger.error(f"获取健康状态失败: {e}")
            return {
                "status": HealthStatus.UNKNOWN.value,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "components": {},
            }

    async def get_health_history(
        self, component: str, hours: int = 24
    ) -> List[Dict[str, Any]]:
        """获取健康检查历史"""
        try:
            if not self.redis_client:
                return []

            history = []

            # 获取指定时间范围内的历史数据
            for i in range(hours):
                hour = datetime.now() - timedelta(hours=i)
                hour_key = hour.strftime("%Y-%m-%d-%H")
                history_key = f"health:history:{component}:{hour_key}"

                records = await self.redis_client.smembers(history_key)
                for record_str in records:
                    try:
                        record = json.loads(record_str)
                        history.append(record)
                    except json.JSONDecodeError:
                        continue

            # 按时间排序
            history.sort(key=lambda x: x["timestamp"], reverse=True)

            return history

        except Exception as e:
            logger.error(f"获取健康检查历史失败: {e}")
            return []

    async def run_manual_check(self, component_name: str) -> Dict[str, Any]:
        """手动运行健康检查"""
        try:
            if component_name not in self.health_checks:
                return {
                    "status": "error",
                    "error": f"健康检查项不存在: {component_name}",
                }

            health_check = self.health_checks[component_name]

            # 运行检查
            await self._run_single_check(health_check)

            return {
                "status": "success",
                "component": component_name,
                "result": {
                    "status": health_check.last_status.value,
                    "response_time": health_check.response_time,
                    "error": health_check.last_error,
                    "timestamp": (
                        health_check.last_check.isoformat()
                        if health_check.last_check
                        else None
                    ),
                },
            }

        except Exception as e:
            logger.error(f"手动运行健康检查失败: {e}")
            return {"status": "error", "error": str(e)}


# 全局健康监控器实例
_health_monitor = None


async def get_health_monitor() -> HealthMonitor:
    """获取健康监控器实例"""
    global _health_monitor
    if _health_monitor is None:
        from ..cache import redis_client
        from .alert_manager import get_alert_manager

        _health_monitor = HealthMonitor(
            redis_client=redis_client, alert_manager=await get_alert_manager()
        )
    return _health_monitor
