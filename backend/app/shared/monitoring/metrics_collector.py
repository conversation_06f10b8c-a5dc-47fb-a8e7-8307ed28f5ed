"""
性能指标收集器

收集系统性能指标，包括API响应时间、请求频率、错误率等
支持自定义指标和时间窗口统计
"""

import asyncio
import json
import logging
import threading
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from functools import wraps
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class MetricType:
    """指标类型"""

    COUNTER = "counter"  # 计数器（只增不减）
    GAUGE = "gauge"  # 仪表（可增可减）
    HISTOGRAM = "histogram"  # 直方图（分布统计）
    TIMER = "timer"  # 计时器（执行时间）


class Metric:
    """指标数据模型"""

    def __init__(
        self,
        name: str,
        metric_type: str,
        value: Union[int, float],
        labels: Dict[str, str] = None,
        timestamp: datetime = None,
    ):
        self.name = name
        self.metric_type = metric_type
        self.value = value
        self.labels = labels or {}
        self.timestamp = timestamp or datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "type": self.metric_type,
            "value": self.value,
            "labels": self.labels,
            "timestamp": self.timestamp.isoformat(),
        }


class TimeWindowStats:
    """时间窗口统计"""

    def __init__(self, window_size: int = 300):  # 5分钟窗口
        self.window_size = window_size
        self.data_points = deque()
        self.lock = threading.Lock()

    def add_value(self, value: Union[int, float], timestamp: datetime = None):
        """添加数据点"""
        if timestamp is None:
            timestamp = datetime.now()

        with self.lock:
            # 添加新数据点
            self.data_points.append((timestamp, value))

            # 清理过期数据
            cutoff_time = datetime.now() - timedelta(seconds=self.window_size)
            while self.data_points and self.data_points[0][0] < cutoff_time:
                self.data_points.popleft()

    def get_stats(self) -> Dict[str, Union[int, float]]:
        """获取统计信息"""
        with self.lock:
            if not self.data_points:
                return {
                    "count": 0,
                    "sum": 0,
                    "avg": 0,
                    "min": 0,
                    "max": 0,
                    "rate": 0,  # 每秒速率
                }

            values = [point[1] for point in self.data_points]
            count = len(values)
            sum_val = sum(values)
            avg_val = sum_val / count
            min_val = min(values)
            max_val = max(values)

            # 计算速率（每秒）
            if count > 1:
                time_span = (
                    self.data_points[-1][0] - self.data_points[0][0]
                ).total_seconds()
                rate = count / max(time_span, 1)
            else:
                rate = 0

            return {
                "count": count,
                "sum": sum_val,
                "avg": avg_val,
                "min": min_val,
                "max": max_val,
                "rate": rate,
            }


class MetricsCollector:
    """性能指标收集器"""

    def __init__(self, redis_client=None):
        self.redis_client = redis_client

        # 内存中的指标存储
        self.counters = defaultdict(int)
        self.gauges = defaultdict(float)
        self.histograms = defaultdict(list)
        self.timers = defaultdict(lambda: TimeWindowStats())

        # 线程锁
        self.lock = threading.Lock()

        # 配置
        import os

        self.enabled = os.getenv("METRICS_COLLECTION_ENABLED", "true").lower() == "true"
        self.flush_interval = int(os.getenv("METRICS_FLUSH_INTERVAL", "60"))  # 60秒
        self.retention_hours = int(os.getenv("METRICS_RETENTION_HOURS", "24"))  # 24小时

        # 预定义的系统指标
        self.system_metrics = [
            "api_requests_total",
            "api_request_duration",
            "api_errors_total",
            "database_queries_total",
            "database_query_duration",
            "cache_hits_total",
            "cache_misses_total",
            "user_sessions_active",
            "system_memory_usage",
            "system_cpu_usage",
        ]

        # 启动后台任务
        if self.enabled:
            asyncio.create_task(self._flush_metrics_loop())

    def increment_counter(
        self, name: str, value: Union[int, float] = 1, labels: Dict[str, str] = None
    ):
        """增加计数器"""
        if not self.enabled:
            return

        key = self._make_metric_key(name, labels)

        with self.lock:
            self.counters[key] += value

        # 异步刷新到Redis
        if self.redis_client:
            asyncio.create_task(self._update_counter_in_redis(key, value))

    def set_gauge(
        self, name: str, value: Union[int, float], labels: Dict[str, str] = None
    ):
        """设置仪表值"""
        if not self.enabled:
            return

        key = self._make_metric_key(name, labels)

        with self.lock:
            self.gauges[key] = value

        # 异步刷新到Redis
        if self.redis_client:
            asyncio.create_task(self._update_gauge_in_redis(key, value))

    def record_histogram(
        self, name: str, value: Union[int, float], labels: Dict[str, str] = None
    ):
        """记录直方图数据"""
        if not self.enabled:
            return

        key = self._make_metric_key(name, labels)

        with self.lock:
            self.histograms[key].append(value)

            # 限制内存中的数据量
            if len(self.histograms[key]) > 1000:
                self.histograms[key] = self.histograms[key][-1000:]

    def record_timer(self, name: str, duration: float, labels: Dict[str, str] = None):
        """记录计时器数据"""
        if not self.enabled:
            return

        key = self._make_metric_key(name, labels)
        self.timers[key].add_value(duration)

    def _make_metric_key(self, name: str, labels: Dict[str, str] = None) -> str:
        """生成指标键"""
        if not labels:
            return name

        label_str = ",".join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{name}{{{label_str}}}"

    async def _update_counter_in_redis(self, key: str, value: Union[int, float]):
        """更新Redis中的计数器"""
        try:
            redis_key = f"metrics:counter:{key}"
            await self.redis_client.incr(redis_key, int(value))
            await self.redis_client.expire(redis_key, self.retention_hours * 3600)
        except Exception as e:
            logger.error(f"更新Redis计数器失败: {e}")

    async def _update_gauge_in_redis(self, key: str, value: Union[int, float]):
        """更新Redis中的仪表"""
        try:
            redis_key = f"metrics:gauge:{key}"
            await self.redis_client.set(
                redis_key, str(value), ttl=self.retention_hours * 3600
            )
        except Exception as e:
            logger.error(f"更新Redis仪表失败: {e}")

    async def _flush_metrics_loop(self):
        """定期刷新指标到Redis"""
        while True:
            try:
                await asyncio.sleep(self.flush_interval)
                if self.redis_client:
                    await self._flush_histograms_to_redis()
                    await self._flush_timers_to_redis()
            except Exception as e:
                logger.error(f"刷新指标到Redis失败: {e}")

    async def _flush_histograms_to_redis(self):
        """刷新直方图数据到Redis"""
        try:
            with self.lock:
                histograms_copy = dict(self.histograms)
                # 清空内存中的直方图数据
                self.histograms.clear()

            for key, values in histograms_copy.items():
                if not values:
                    continue

                # 计算统计信息
                stats = {
                    "count": len(values),
                    "sum": sum(values),
                    "avg": sum(values) / len(values),
                    "min": min(values),
                    "max": max(values),
                    "p50": self._percentile(values, 50),
                    "p90": self._percentile(values, 90),
                    "p95": self._percentile(values, 95),
                    "p99": self._percentile(values, 99),
                }

                # 存储到Redis
                redis_key = f"metrics:histogram:{key}:{int(time.time())}"
                await self.redis_client.set(
                    redis_key, json.dumps(stats), ttl=self.retention_hours * 3600
                )

        except Exception as e:
            logger.error(f"刷新直方图数据失败: {e}")

    async def _flush_timers_to_redis(self):
        """刷新计时器数据到Redis"""
        try:
            for key, timer in self.timers.items():
                stats = timer.get_stats()

                if stats["count"] > 0:
                    redis_key = f"metrics:timer:{key}:{int(time.time())}"
                    await self.redis_client.set(
                        redis_key, json.dumps(stats), ttl=self.retention_hours * 3600
                    )

        except Exception as e:
            logger.error(f"刷新计时器数据失败: {e}")

    def _percentile(self, values: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not values:
            return 0

        sorted_values = sorted(values)
        index = int((percentile / 100) * len(sorted_values))
        index = min(index, len(sorted_values) - 1)
        return sorted_values[index]

    async def get_metrics(
        self,
        metric_types: List[str] = None,
        name_pattern: str = None,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> Dict[str, Any]:
        """获取指标数据"""
        try:
            metrics = {"counters": {}, "gauges": {}, "histograms": {}, "timers": {}}

            # 获取内存中的数据
            if not metric_types or "counter" in metric_types:
                with self.lock:
                    metrics["counters"] = dict(self.counters)

            if not metric_types or "gauge" in metric_types:
                with self.lock:
                    metrics["gauges"] = dict(self.gauges)

            # 从Redis获取历史数据
            if self.redis_client:
                if not metric_types or "histogram" in metric_types:
                    metrics["histograms"] = (
                        await self._get_histogram_metrics_from_redis(
                            name_pattern, start_time, end_time
                        )
                    )

                if not metric_types or "timer" in metric_types:
                    metrics["timers"] = await self._get_timer_metrics_from_redis(
                        name_pattern, start_time, end_time
                    )

            return metrics

        except Exception as e:
            logger.error(f"获取指标数据失败: {e}")
            return {}

    async def _get_histogram_metrics_from_redis(
        self,
        name_pattern: str = None,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> Dict[str, List[Dict]]:
        """从Redis获取直方图指标"""
        try:
            pattern = "metrics:histogram:*"
            if name_pattern:
                pattern = f"metrics:histogram:{name_pattern}:*"

            histograms = {}

            async for key in self.redis_client.redis.scan_iter(match=pattern):
                key_str = key.decode("utf-8")
                data = await self.redis_client.get(key_str)

                if data:
                    stats = json.loads(data)

                    # 提取指标名称和时间戳
                    parts = key_str.split(":")
                    if len(parts) >= 4:
                        metric_name = ":".join(parts[2:-1])
                        timestamp = int(parts[-1])

                        # 时间过滤
                        metric_time = datetime.fromtimestamp(timestamp)
                        if start_time and metric_time < start_time:
                            continue
                        if end_time and metric_time > end_time:
                            continue

                        if metric_name not in histograms:
                            histograms[metric_name] = []

                        stats["timestamp"] = metric_time.isoformat()
                        histograms[metric_name].append(stats)

            return histograms

        except Exception as e:
            logger.error(f"从Redis获取直方图指标失败: {e}")
            return {}

    async def _get_timer_metrics_from_redis(
        self,
        name_pattern: str = None,
        start_time: datetime = None,
        end_time: datetime = None,
    ) -> Dict[str, List[Dict]]:
        """从Redis获取计时器指标"""
        try:
            pattern = "metrics:timer:*"
            if name_pattern:
                pattern = f"metrics:timer:{name_pattern}:*"

            timers = {}

            async for key in self.redis_client.redis.scan_iter(match=pattern):
                key_str = key.decode("utf-8")
                data = await self.redis_client.get(key_str)

                if data:
                    stats = json.loads(data)

                    # 提取指标名称和时间戳
                    parts = key_str.split(":")
                    if len(parts) >= 4:
                        metric_name = ":".join(parts[2:-1])
                        timestamp = int(parts[-1])

                        # 时间过滤
                        metric_time = datetime.fromtimestamp(timestamp)
                        if start_time and metric_time < start_time:
                            continue
                        if end_time and metric_time > end_time:
                            continue

                        if metric_name not in timers:
                            timers[metric_name] = []

                        stats["timestamp"] = metric_time.isoformat()
                        timers[metric_name].append(stats)

            return timers

        except Exception as e:
            logger.error(f"从Redis获取计时器指标失败: {e}")
            return {}

    async def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标摘要"""
        try:
            import psutil

            # 系统资源
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage("/")

            # 网络统计
            network = psutil.net_io_counters()

            # 进程信息
            process = psutil.Process()
            process_memory = process.memory_info()

            return {
                "system": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_total": memory.total,
                    "memory_available": memory.available,
                    "disk_percent": (disk.used / disk.total) * 100,
                    "disk_total": disk.total,
                    "disk_free": disk.free,
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv,
                },
                "process": {
                    "memory_rss": process_memory.rss,
                    "memory_vms": process_memory.vms,
                    "cpu_percent": process.cpu_percent(),
                    "num_threads": process.num_threads(),
                    "num_fds": process.num_fds() if hasattr(process, "num_fds") else 0,
                },
            }

        except Exception as e:
            logger.error(f"获取系统指标失败: {e}")
            return {}

    def clear_metrics(self, metric_type: str = None):
        """清空指标数据"""
        with self.lock:
            if not metric_type or metric_type == "counter":
                self.counters.clear()
            if not metric_type or metric_type == "gauge":
                self.gauges.clear()
            if not metric_type or metric_type == "histogram":
                self.histograms.clear()
            if not metric_type or metric_type == "timer":
                self.timers.clear()

        logger.info(f"清空指标数据: {metric_type or 'all'}")


def metrics_timer(metric_name: str, labels: Dict[str, str] = None):
    """计时器装饰器"""

    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = (time.time() - start_time) * 1000  # 毫秒
                get_metrics_collector().record_timer(metric_name, duration, labels)
                return result
            except Exception as e:
                duration = (time.time() - start_time) * 1000
                error_labels = (labels or {}).copy()
                error_labels["status"] = "error"
                get_metrics_collector().record_timer(
                    metric_name, duration, error_labels
                )
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = (time.time() - start_time) * 1000
                get_metrics_collector().record_timer(metric_name, duration, labels)
                return result
            except Exception as e:
                duration = (time.time() - start_time) * 1000
                error_labels = (labels or {}).copy()
                error_labels["status"] = "error"
                get_metrics_collector().record_timer(
                    metric_name, duration, error_labels
                )
                raise

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


def metrics_counter(metric_name: str, labels: Dict[str, str] = None):
    """计数器装饰器"""

    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                get_metrics_collector().increment_counter(metric_name, 1, labels)
                return result
            except Exception as e:
                error_labels = (labels or {}).copy()
                error_labels["status"] = "error"
                get_metrics_collector().increment_counter(metric_name, 1, error_labels)
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                get_metrics_collector().increment_counter(metric_name, 1, labels)
                return result
            except Exception as e:
                error_labels = (labels or {}).copy()
                error_labels["status"] = "error"
                get_metrics_collector().increment_counter(metric_name, 1, error_labels)
                raise

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


# 全局指标收集器实例
_metrics_collector = None


def get_metrics_collector() -> MetricsCollector:
    """获取指标收集器实例"""
    global _metrics_collector
    if _metrics_collector is None:
        from ..cache import redis_client

        _metrics_collector = MetricsCollector(redis_client=redis_client)
    return _metrics_collector
