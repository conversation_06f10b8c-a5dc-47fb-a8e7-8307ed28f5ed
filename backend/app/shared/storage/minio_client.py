"""
MinIO客户端模块

提供MinIO对象存储的异步客户端实现
"""

import asyncio
import logging
from datetime import timed<PERSON><PERSON>
from typing import BinaryIO, Dict, List, Optional

from app.core.config import get_settings
from minio import Minio
from minio.commonconfig import CopySource
from minio.error import S3Error

logger = logging.getLogger(__name__)


class MinIOClient:
    """MinIO异步客户端"""

    def __init__(self):
        self.client: Optional[Minio] = None
        self.bucket_name: str = ""
        self.is_connected: bool = False
        self.settings = get_settings()

    async def connect(self) -> bool:
        """连接到MinIO服务"""
        try:
            self.client = Minio(
                endpoint=self.settings.minio_endpoint,
                access_key=self.settings.minio_access_key,
                secret_key=self.settings.minio_secret_key,
                secure=self.settings.minio_secure,
                region=self.settings.minio_region,
            )

            self.bucket_name = self.settings.minio_bucket_name

            # 检查连接并创建bucket
            await self._ensure_bucket_exists()

            self.is_connected = True
            logger.info("MinIO client connected successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to connect to MinIO: {e}")
            self.is_connected = False
            return False

    async def disconnect(self):
        """断开连接"""
        if self.client:
            self.client = None
            self.is_connected = False
            logger.info("MinIO client disconnected")

    async def _ensure_bucket_exists(self):
        """确保bucket存在"""
        try:
            # 在线程池中执行阻塞操作
            loop = asyncio.get_event_loop()

            # 检查bucket是否存在
            bucket_exists = await loop.run_in_executor(
                None, self.client.bucket_exists, self.bucket_name
            )

            if not bucket_exists:
                # 创建bucket
                await loop.run_in_executor(
                    None, self.client.make_bucket, self.bucket_name
                )
                logger.info(f"Created bucket: {self.bucket_name}")
            else:
                logger.info(f"Bucket already exists: {self.bucket_name}")

        except Exception as e:
            logger.error(f"Error ensuring bucket exists: {e}")
            raise

    async def upload_file(
        self,
        file_path: str,
        file_data: BinaryIO,
        content_type: str = "application/octet-stream",
        metadata: Optional[Dict[str, str]] = None,
    ) -> str:
        """
        上传文件到MinIO

        Args:
            file_path: 文件在bucket中的路径
            file_data: 文件数据流
            content_type: 内容类型
            metadata: 额外元数据

        Returns:
            文件的ETag
        """
        if not self.is_connected:
            raise RuntimeError("MinIO client not connected")

        try:
            loop = asyncio.get_event_loop()

            # 上传文件
            result = await loop.run_in_executor(
                None,
                self.client.put_object,
                self.bucket_name,
                file_path,
                file_data,
                -1,  # 让MinIO自动计算大小
                content_type,
                metadata,
            )

            logger.info(f"File uploaded successfully: {file_path}")
            return result.etag

        except S3Error as e:
            logger.error(f"S3 error uploading file {file_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"Error uploading file {file_path}: {e}")
            raise

    async def download_file(self, file_path: str) -> bytes:
        """
        从MinIO下载文件

        Args:
            file_path: 文件在bucket中的路径

        Returns:
            文件内容字节
        """
        if not self.is_connected:
            raise RuntimeError("MinIO client not connected")

        try:
            loop = asyncio.get_event_loop()

            # 下载文件
            response = await loop.run_in_executor(
                None, self.client.get_object, self.bucket_name, file_path
            )

            # 读取数据
            data = response.data
            response.close()
            response.release_conn()

            logger.info(f"File downloaded successfully: {file_path}")
            return data

        except S3Error as e:
            logger.error(f"S3 error downloading file {file_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"Error downloading file {file_path}: {e}")
            raise

    async def delete_file(self, file_path: str) -> bool:
        """
        删除文件

        Args:
            file_path: 文件在bucket中的路径

        Returns:
            是否删除成功
        """
        if not self.is_connected:
            raise RuntimeError("MinIO client not connected")

        try:
            loop = asyncio.get_event_loop()

            await loop.run_in_executor(
                None, self.client.remove_object, self.bucket_name, file_path
            )

            logger.info(f"File deleted successfully: {file_path}")
            return True

        except S3Error as e:
            logger.error(f"S3 error deleting file {file_path}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            return False

    async def file_exists(self, file_path: str) -> bool:
        """
        检查文件是否存在

        Args:
            file_path: 文件在bucket中的路径

        Returns:
            文件是否存在
        """
        if not self.is_connected:
            raise RuntimeError("MinIO client not connected")

        try:
            loop = asyncio.get_event_loop()

            await loop.run_in_executor(
                None, self.client.stat_object, self.bucket_name, file_path
            )
            return True

        except S3Error:
            return False
        except Exception as e:
            logger.error(f"Error checking file existence {file_path}: {e}")
            return False

    async def get_file_info(self, file_path: str) -> Optional[Dict]:
        """
        获取文件信息

        Args:
            file_path: 文件在bucket中的路径

        Returns:
            文件信息字典
        """
        if not self.is_connected:
            raise RuntimeError("MinIO client not connected")

        try:
            loop = asyncio.get_event_loop()

            stat = await loop.run_in_executor(
                None, self.client.stat_object, self.bucket_name, file_path
            )

            return {
                "size": stat.size,
                "etag": stat.etag,
                "last_modified": stat.last_modified,
                "content_type": stat.content_type,
                "metadata": stat.metadata,
            }

        except S3Error as e:
            logger.error(f"S3 error getting file info {file_path}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error getting file info {file_path}: {e}")
            return None

    async def list_files(
        self, prefix: str = "", recursive: bool = False, max_keys: int = 1000
    ) -> List[Dict]:
        """
        列出文件

        Args:
            prefix: 文件路径前缀
            recursive: 是否递归列出
            max_keys: 最大返回数量

        Returns:
            文件信息列表
        """
        if not self.is_connected:
            raise RuntimeError("MinIO client not connected")

        try:
            loop = asyncio.get_event_loop()

            objects = await loop.run_in_executor(
                None,
                lambda: list(
                    self.client.list_objects(
                        self.bucket_name,
                        prefix=prefix,
                        recursive=recursive,
                        max_keys=max_keys,
                    )
                ),
            )

            files = []
            for obj in objects:
                files.append(
                    {
                        "object_name": obj.object_name,
                        "size": obj.size,
                        "etag": obj.etag,
                        "last_modified": obj.last_modified,
                        "content_type": getattr(obj, "content_type", None),
                    }
                )

            return files

        except S3Error as e:
            logger.error(f"S3 error listing files with prefix {prefix}: {e}")
            return []
        except Exception as e:
            logger.error(f"Error listing files with prefix {prefix}: {e}")
            return []

    async def copy_file(
        self,
        source_path: str,
        destination_path: str,
        metadata: Optional[Dict[str, str]] = None,
    ) -> bool:
        """
        复制文件

        Args:
            source_path: 源文件路径
            destination_path: 目标文件路径
            metadata: 新的元数据

        Returns:
            是否复制成功
        """
        if not self.is_connected:
            raise RuntimeError("MinIO client not connected")

        try:
            loop = asyncio.get_event_loop()

            copy_source = CopySource(self.bucket_name, source_path)

            await loop.run_in_executor(
                None,
                self.client.copy_object,
                self.bucket_name,
                destination_path,
                copy_source,
                metadata,
            )

            logger.info(f"File copied: {source_path} -> {destination_path}")
            return True

        except S3Error as e:
            logger.error(f"S3 error copying file {source_path}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error copying file {source_path}: {e}")
            return False

    async def generate_presigned_url(
        self,
        file_path: str,
        method: str = "GET",
        expires: timedelta = timedelta(hours=1),
    ) -> str:
        """
        生成预签名URL

        Args:
            file_path: 文件路径
            method: HTTP方法
            expires: 过期时间

        Returns:
            预签名URL
        """
        if not self.is_connected:
            raise RuntimeError("MinIO client not connected")

        try:
            loop = asyncio.get_event_loop()

            url = await loop.run_in_executor(
                None,
                self.client.presigned_url,
                method,
                self.bucket_name,
                file_path,
                expires,
            )

            return url

        except S3Error as e:
            logger.error(f"S3 error generating presigned URL for {file_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"Error generating presigned URL for {file_path}: {e}")
            raise

    async def get_bucket_stats(self) -> Dict:
        """
        获取bucket统计信息

        Returns:
            统计信息字典
        """
        if not self.is_connected:
            raise RuntimeError("MinIO client not connected")

        try:
            files = await self.list_files(recursive=True)

            total_size = sum(file["size"] for file in files)
            file_count = len(files)

            # 按类型统计
            type_stats = {}
            for file in files:
                content_type = file.get("content_type", "unknown")
                type_stats[content_type] = type_stats.get(content_type, 0) + 1

            return {
                "total_files": file_count,
                "total_size": total_size,
                "bucket_name": self.bucket_name,
                "files_by_type": type_stats,
            }

        except Exception as e:
            logger.error(f"Error getting bucket stats: {e}")
            return {
                "total_files": 0,
                "total_size": 0,
                "bucket_name": self.bucket_name,
                "files_by_type": {},
            }


# 全局客户端实例
minio_client: Optional[MinIOClient] = None


async def init_minio() -> MinIOClient:
    """初始化MinIO客户端"""
    global minio_client

    if minio_client is None:
        minio_client = MinIOClient()

    if not minio_client.is_connected:
        await minio_client.connect()

    return minio_client


async def close_minio():
    """关闭MinIO客户端"""
    global minio_client

    if minio_client:
        await minio_client.disconnect()
        minio_client = None


def get_minio_client() -> MinIOClient:
    """获取MinIO客户端实例"""
    if minio_client is None or not minio_client.is_connected:
        raise RuntimeError("MinIO client not initialized. Call init_minio() first.")
    return minio_client
