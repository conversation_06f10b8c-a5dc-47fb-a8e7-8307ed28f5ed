"""
文件存储系统模块

本模块提供完整的文件存储管理功能，支持：
- MinIO对象存储
- 文件上传下载
- 图片处理和缩略图生成
- 文件权限控制
- 文件元数据管理
"""

from .file_manager import FileManager, file_manager, get_file_manager
from .file_processor import FileProcessor, file_processor, get_file_processor
from .minio_client import (
    MinIOClient,
    close_minio,
    get_minio_client,
    init_minio,
    minio_client,
)
from .storage_models import (
    BulkFileOperation,
    FileAccessType,
    FileDownloadResponse,
    FileMetadata,
    FileOperationResult,
    FilePermission,
    FileSearchRequest,
    FileSearchResponse,
    FileStorageStats,
    FileThumbnailRequest,
    FileThumbnailResponse,
    FileUploadRequest,
    FileUploadResponse,
)

__all__ = [
    # MinIO客户端
    "MinIOClient",
    "minio_client",
    "get_minio_client",
    "init_minio",
    "close_minio",
    # 文件管理器
    "FileManager",
    "file_manager",
    "get_file_manager",
    # 文件处理器
    "FileProcessor",
    "file_processor",
    "get_file_processor",
    # 数据模型
    "FileMetadata",
    "FileUploadRequest",
    "FileUploadResponse",
    "FileDownloadResponse",
    "FileThumbnailRequest",
    "FileThumbnailResponse",
    "FileSearchRequest",
    "FileSearchResponse",
    "FileStorageStats",
    "FilePermission",
    "FileAccessType",
    "BulkFileOperation",
    "FileOperationResult",
]
