"""
文件存储系统数据模型

定义文件存储相关的Pydantic模型和枚举类型
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


class FileAccessType(str, Enum):
    """文件访问类型"""

    PRIVATE = "private"
    PUBLIC = "public"
    SHARED = "shared"
    TEMPORARY = "temporary"


class FileOperationType(str, Enum):
    """文件操作类型"""

    UPLOAD = "upload"
    DOWNLOAD = "download"
    DELETE = "delete"
    COPY = "copy"
    MOVE = "move"
    THUMBNAIL = "thumbnail"


class FileStatus(str, Enum):
    """文件状态"""

    UPLOADING = "uploading"
    PROCESSING = "processing"
    READY = "ready"
    ERROR = "error"
    DELETED = "deleted"


class FileMetadata(BaseModel):
    """文件元数据模型"""

    id: str = Field(..., description="文件唯一标识")
    filename: str = Field(..., description="原始文件名")
    file_path: str = Field(..., description="存储路径")
    file_size: int = Field(..., description="文件大小（字节）")
    file_type: str = Field(..., description="MIME类型")
    file_extension: str = Field(..., description="文件扩展名")
    content_hash: str = Field(..., description="文件内容哈希")

    # 访问控制
    access_type: FileAccessType = Field(
        default=FileAccessType.PRIVATE, description="访问类型"
    )
    owner_id: Optional[str] = Field(None, description="文件所有者ID")
    permissions: Dict[str, List[str]] = Field(
        default_factory=dict, description="权限设置"
    )

    # 文件状态
    status: FileStatus = Field(default=FileStatus.UPLOADING, description="文件状态")
    upload_progress: float = Field(default=0.0, description="上传进度")

    # 图片信息（如果是图片）
    image_width: Optional[int] = Field(None, description="图片宽度")
    image_height: Optional[int] = Field(None, description="图片高度")
    has_thumbnail: bool = Field(default=False, description="是否有缩略图")
    thumbnail_path: Optional[str] = Field(None, description="缩略图路径")

    # 时间戳
    created_at: datetime = Field(
        default_factory=datetime.utcnow, description="创建时间"
    )
    updated_at: datetime = Field(
        default_factory=datetime.utcnow, description="更新时间"
    )
    expires_at: Optional[datetime] = Field(None, description="过期时间")

    # 额外元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")

    class Config:
        from_attributes = True
        json_encoders = {datetime: lambda v: v.isoformat()}


class FileUploadRequest(BaseModel):
    """文件上传请求模型"""

    filename: str = Field(..., description="文件名")
    content_type: Optional[str] = Field(None, description="内容类型")
    file_size: Optional[int] = Field(None, description="文件大小")
    access_type: FileAccessType = Field(
        default=FileAccessType.PRIVATE, description="访问类型"
    )
    folder_path: Optional[str] = Field(None, description="文件夹路径")
    permissions: Optional[Dict[str, List[str]]] = Field(None, description="权限设置")
    expires_in: Optional[int] = Field(None, description="过期时间（秒）")
    generate_thumbnail: bool = Field(default=True, description="是否生成缩略图")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")

    @validator("filename")
    def validate_filename(cls, v):
        if not v or "/" in v or "\\" in v:
            raise ValueError("Invalid filename")
        return v


class FileUploadResponse(BaseModel):
    """文件上传响应模型"""

    file_id: str = Field(..., description="文件ID")
    upload_url: str = Field(..., description="上传URL")
    expires_at: datetime = Field(..., description="URL过期时间")
    file_metadata: FileMetadata = Field(..., description="文件元数据")
    chunk_size: Optional[int] = Field(None, description="分片大小")
    multipart_upload_id: Optional[str] = Field(None, description="分片上传ID")


class FileDownloadResponse(BaseModel):
    """文件下载响应模型"""

    file_id: str = Field(..., description="文件ID")
    download_url: str = Field(..., description="下载URL")
    expires_at: datetime = Field(..., description="URL过期时间")
    file_metadata: FileMetadata = Field(..., description="文件元数据")


class FileThumbnailRequest(BaseModel):
    """缩略图请求模型"""

    file_id: str = Field(..., description="文件ID")
    width: int = Field(default=200, description="缩略图宽度")
    height: int = Field(default=200, description="缩略图高度")
    quality: int = Field(default=85, description="图片质量")
    format: str = Field(default="JPEG", description="输出格式")


class FileThumbnailResponse(BaseModel):
    """缩略图响应模型"""

    file_id: str = Field(..., description="原文件ID")
    thumbnail_id: str = Field(..., description="缩略图ID")
    thumbnail_url: str = Field(..., description="缩略图URL")
    expires_at: datetime = Field(..., description="URL过期时间")
    width: int = Field(..., description="缩略图宽度")
    height: int = Field(..., description="缩略图高度")


class FileSearchRequest(BaseModel):
    """文件搜索请求模型"""

    query: Optional[str] = Field(None, description="搜索关键词")
    file_type: Optional[str] = Field(None, description="文件类型")
    access_type: Optional[FileAccessType] = Field(None, description="访问类型")
    owner_id: Optional[str] = Field(None, description="所有者ID")
    folder_path: Optional[str] = Field(None, description="文件夹路径")
    created_after: Optional[datetime] = Field(None, description="创建时间晚于")
    created_before: Optional[datetime] = Field(None, description="创建时间早于")
    min_size: Optional[int] = Field(None, description="最小文件大小")
    max_size: Optional[int] = Field(None, description="最大文件大小")
    page: int = Field(default=1, description="页码")
    page_size: int = Field(default=20, description="每页数量")
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", description="排序方向")


class FileSearchResponse(BaseModel):
    """文件搜索响应模型"""

    files: List[FileMetadata] = Field(..., description="文件列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")


class FilePermission(BaseModel):
    """文件权限模型"""

    user_id: str = Field(..., description="用户ID")
    permissions: List[str] = Field(..., description="权限列表")
    granted_by: str = Field(..., description="授权者ID")
    granted_at: datetime = Field(
        default_factory=datetime.utcnow, description="授权时间"
    )
    expires_at: Optional[datetime] = Field(None, description="过期时间")


class BulkFileOperation(BaseModel):
    """批量文件操作模型"""

    operation: FileOperationType = Field(..., description="操作类型")
    file_ids: List[str] = Field(..., description="文件ID列表")
    target_folder: Optional[str] = Field(None, description="目标文件夹")
    new_access_type: Optional[FileAccessType] = Field(None, description="新的访问类型")
    metadata_updates: Optional[Dict[str, Any]] = Field(None, description="元数据更新")


class FileOperationResult(BaseModel):
    """文件操作结果模型"""

    file_id: str = Field(..., description="文件ID")
    operation: FileOperationType = Field(..., description="操作类型")
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="结果消息")
    data: Optional[Dict[str, Any]] = Field(None, description="额外数据")


class FileStorageStats(BaseModel):
    """文件存储统计模型"""

    total_files: int = Field(..., description="总文件数")
    total_size: int = Field(..., description="总大小")
    files_by_type: Dict[str, int] = Field(..., description="按类型统计")
    files_by_access_type: Dict[str, int] = Field(..., description="按访问类型统计")
    storage_usage: Dict[str, int] = Field(..., description="存储使用情况")
    upload_statistics: Dict[str, int] = Field(..., description="上传统计")
    download_statistics: Dict[str, int] = Field(..., description="下载统计")
