"""
文件管理器模块

提供高级文件管理功能，协调MinIO客户端和文件处理器
"""

import io
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Optional

from app.core.config import get_settings
from fastapi import HTTPException, UploadFile

from .file_processor import get_file_processor
from .minio_client import get_minio_client
from .storage_models import (
    FileAccessType,
    FileDownloadResponse,
    FileMetadata,
    FileSearchRequest,
    FileSearchResponse,
    FileStatus,
    FileStorageStats,
    FileThumbnailRequest,
    FileThumbnailResponse,
    FileUploadRequest,
    FileUploadResponse,
)

logger = logging.getLogger(__name__)


class FileManager:
    """文件管理器"""

    def __init__(self):
        self.settings = get_settings()
        self._file_metadata_cache: Dict[str, FileMetadata] = {}

    def _get_minio_client(self):
        """获取MinIO客户端"""
        return get_minio_client()

    def _get_file_processor(self):
        """获取文件处理器"""
        return get_file_processor()

    def _generate_file_id(self) -> str:
        """生成文件ID"""
        return str(uuid.uuid4())

    def _generate_file_path(
        self, file_id: str, filename: str, folder_path: Optional[str] = None
    ) -> str:
        """
        生成文件存储路径

        Args:
            file_id: 文件ID
            filename: 文件名
            folder_path: 文件夹路径

        Returns:
            完整的存储路径
        """
        processor = self._get_file_processor()
        extension = processor.get_file_extension(filename)

        # 按日期分组
        date_path = datetime.utcnow().strftime("%Y/%m/%d")

        # 构建路径
        path_parts = []
        if folder_path:
            path_parts.append(folder_path.strip("/"))
        path_parts.append(date_path)
        path_parts.append(f"{file_id}.{extension}" if extension else file_id)

        return "/".join(path_parts)

    def _generate_thumbnail_path(self, file_path: str) -> str:
        """生成缩略图路径"""
        return f"thumbnails/{file_path}"

    async def upload_file(
        self,
        file: UploadFile,
        request: FileUploadRequest,
        owner_id: Optional[str] = None,
    ) -> FileUploadResponse:
        """
        上传文件

        Args:
            file: 上传的文件
            request: 上传请求参数
            owner_id: 文件所有者ID

        Returns:
            上传响应
        """
        try:
            # 验证文件
            await self._validate_upload_file(file, request)

            # 读取文件数据
            file_data = await file.read()

            # 生成文件ID和路径
            file_id = self._generate_file_id()
            file_path = self._generate_file_path(
                file_id, request.filename, request.folder_path
            )

            # 获取处理器和客户端
            processor = self._get_file_processor()
            minio_client = self._get_minio_client()

            # 创建文件元数据
            metadata = processor.create_file_metadata(
                file_id=file_id,
                filename=request.filename,
                file_data=file_data,
                file_path=file_path,
                owner_id=owner_id,
                access_type=request.access_type,
                metadata=request.metadata or {},
            )

            # 处理图片
            processed_data = file_data
            if processor.is_image_file(request.filename):
                try:
                    processed_data, img_metadata = processor.process_image(file_data)
                    metadata.metadata.update(img_metadata)
                except Exception as e:
                    logger.warning(f"Image processing failed: {e}")

            # 上传到MinIO
            content_type = request.content_type or processor.get_mime_type(
                request.filename, processed_data
            )

            etag = await minio_client.upload_file(
                file_path=file_path,
                file_data=io.BytesIO(processed_data),
                content_type=content_type,
                metadata={
                    "file_id": file_id,
                    "original_filename": request.filename,
                    "owner_id": owner_id or "",
                    "access_type": request.access_type.value,
                },
            )

            # 更新元数据
            metadata.status = FileStatus.READY
            metadata.upload_progress = 100.0
            metadata.metadata["etag"] = etag

            # 生成缩略图（如果是图片）
            if processor.is_image_file(request.filename) and request.generate_thumbnail:
                try:
                    await self._generate_and_upload_thumbnail(
                        file_id, file_data, metadata
                    )
                except Exception as e:
                    logger.warning(f"Thumbnail generation failed: {e}")

            # 缓存元数据
            self._file_metadata_cache[file_id] = metadata

            # 生成预签名URL
            upload_url = await minio_client.generate_presigned_url(
                file_path, method="GET", expires=timedelta(hours=1)
            )

            return FileUploadResponse(
                file_id=file_id,
                upload_url=upload_url,
                expires_at=datetime.utcnow() + timedelta(hours=1),
                file_metadata=metadata,
            )

        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

    async def _validate_upload_file(self, file: UploadFile, request: FileUploadRequest):
        """验证上传文件"""
        processor = self._get_file_processor()

        # 验证文件名
        if not request.filename or request.filename != file.filename:
            raise HTTPException(status_code=400, detail="Filename mismatch")

        # 验证文件扩展名
        if not processor.validate_file_extension(request.filename):
            allowed_ext = self.settings.get_allowed_extensions()
            raise HTTPException(
                status_code=400,
                detail=f"File type not allowed. Allowed: {', '.join(allowed_ext)}",
            )

        # 验证文件大小
        if request.file_size and not processor.validate_file_size(request.file_size):
            max_size_mb = self.settings.max_file_size / (1024 * 1024)
            raise HTTPException(
                status_code=400, detail=f"File too large. Max size: {max_size_mb}MB"
            )

    async def _generate_and_upload_thumbnail(
        self, file_id: str, file_data: bytes, metadata: FileMetadata
    ):
        """生成并上传缩略图"""
        try:
            processor = self._get_file_processor()
            minio_client = self._get_minio_client()

            # 生成缩略图
            thumbnail_data = processor.generate_thumbnail(file_data)

            # 上传缩略图
            thumbnail_path = self._generate_thumbnail_path(metadata.file_path)

            await minio_client.upload_file(
                file_path=thumbnail_path,
                file_data=io.BytesIO(thumbnail_data),
                content_type="image/jpeg",
                metadata={"parent_file_id": file_id, "thumbnail": "true"},
            )

            # 更新元数据
            metadata.has_thumbnail = True
            metadata.thumbnail_path = thumbnail_path

        except Exception as e:
            logger.error(f"Error generating thumbnail: {e}")
            raise

    async def download_file(
        self, file_id: str, user_id: Optional[str] = None
    ) -> FileDownloadResponse:
        """
        下载文件

        Args:
            file_id: 文件ID
            user_id: 用户ID（用于权限检查）

        Returns:
            下载响应
        """
        try:
            # 获取文件元数据
            metadata = await self.get_file_metadata(file_id)
            if not metadata:
                raise HTTPException(status_code=404, detail="File not found")

            # 检查权限
            if not await self._check_file_access(metadata, user_id, "read"):
                raise HTTPException(status_code=403, detail="Access denied")

            # 生成下载URL
            minio_client = self._get_minio_client()
            download_url = await minio_client.generate_presigned_url(
                metadata.file_path, method="GET", expires=timedelta(hours=1)
            )

            return FileDownloadResponse(
                file_id=file_id,
                download_url=download_url,
                expires_at=datetime.utcnow() + timedelta(hours=1),
                file_metadata=metadata,
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error downloading file {file_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Download failed: {str(e)}")

    async def get_file_thumbnail(
        self, file_id: str, request: FileThumbnailRequest, user_id: Optional[str] = None
    ) -> FileThumbnailResponse:
        """
        获取文件缩略图

        Args:
            file_id: 文件ID
            request: 缩略图请求
            user_id: 用户ID

        Returns:
            缩略图响应
        """
        try:
            # 获取文件元数据
            metadata = await self.get_file_metadata(file_id)
            if not metadata:
                raise HTTPException(status_code=404, detail="File not found")

            # 检查权限
            if not await self._check_file_access(metadata, user_id, "read"):
                raise HTTPException(status_code=403, detail="Access denied")

            # 检查是否是图片文件
            processor = self._get_file_processor()
            if not processor.is_image_file(metadata.filename):
                raise HTTPException(status_code=400, detail="Not an image file")

            minio_client = self._get_minio_client()

            # 检查是否已有缩略图
            if metadata.has_thumbnail and metadata.thumbnail_path:
                thumbnail_url = await minio_client.generate_presigned_url(
                    metadata.thumbnail_path, method="GET", expires=timedelta(hours=1)
                )

                return FileThumbnailResponse(
                    file_id=file_id,
                    thumbnail_id=f"{file_id}_thumb",
                    thumbnail_url=thumbnail_url,
                    expires_at=datetime.utcnow() + timedelta(hours=1),
                    width=request.width,
                    height=request.height,
                )

            # 动态生成缩略图
            file_data = await minio_client.download_file(metadata.file_path)

            thumbnail_data = processor.generate_thumbnail(
                file_data, size=(request.width, request.height), quality=request.quality
            )

            # 生成临时缩略图路径
            thumbnail_path = (
                f"temp_thumbnails/{file_id}_{request.width}x{request.height}.jpg"
            )

            # 上传临时缩略图
            await minio_client.upload_file(
                file_path=thumbnail_path,
                file_data=io.BytesIO(thumbnail_data),
                content_type="image/jpeg",
            )

            # 生成URL
            thumbnail_url = await minio_client.generate_presigned_url(
                thumbnail_path, method="GET", expires=timedelta(hours=1)
            )

            return FileThumbnailResponse(
                file_id=file_id,
                thumbnail_id=f"{file_id}_thumb_{request.width}x{request.height}",
                thumbnail_url=thumbnail_url,
                expires_at=datetime.utcnow() + timedelta(hours=1),
                width=request.width,
                height=request.height,
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting thumbnail for {file_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Thumbnail failed: {str(e)}")

    async def delete_file(self, file_id: str, user_id: Optional[str] = None) -> bool:
        """
        删除文件

        Args:
            file_id: 文件ID
            user_id: 用户ID

        Returns:
            是否删除成功
        """
        try:
            # 获取文件元数据
            metadata = await self.get_file_metadata(file_id)
            if not metadata:
                return False

            # 检查权限
            if not await self._check_file_access(metadata, user_id, "delete"):
                raise HTTPException(status_code=403, detail="Access denied")

            minio_client = self._get_minio_client()

            # 删除主文件
            await minio_client.delete_file(metadata.file_path)

            # 删除缩略图
            if metadata.has_thumbnail and metadata.thumbnail_path:
                await minio_client.delete_file(metadata.thumbnail_path)

            # 更新元数据状态
            metadata.status = FileStatus.DELETED
            metadata.updated_at = datetime.utcnow()

            # 从缓存中移除
            if file_id in self._file_metadata_cache:
                del self._file_metadata_cache[file_id]

            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting file {file_id}: {e}")
            return False

    async def get_file_metadata(self, file_id: str) -> Optional[FileMetadata]:
        """
        获取文件元数据

        Args:
            file_id: 文件ID

        Returns:
            文件元数据或None
        """
        # 先从缓存查找
        if file_id in self._file_metadata_cache:
            return self._file_metadata_cache[file_id]

        # 从存储查找（这里应该从数据库查找，暂时返回None）
        # TODO: 实现数据库查询
        return None

    async def search_files(
        self, request: FileSearchRequest, user_id: Optional[str] = None
    ) -> FileSearchResponse:
        """
        搜索文件

        Args:
            request: 搜索请求
            user_id: 用户ID

        Returns:
            搜索结果
        """
        try:
            # TODO: 实现文件搜索功能
            # 这里应该从数据库或搜索引擎查询

            # 临时实现：从缓存中搜索
            all_files = list(self._file_metadata_cache.values())

            # 过滤文件
            filtered_files = []
            for file_metadata in all_files:
                # 权限检查
                if not await self._check_file_access(file_metadata, user_id, "read"):
                    continue

                # 应用搜索条件
                if (
                    request.query
                    and request.query.lower() not in file_metadata.filename.lower()
                ):
                    continue

                if request.file_type and request.file_type != file_metadata.file_type:
                    continue

                if (
                    request.access_type
                    and request.access_type != file_metadata.access_type
                ):
                    continue

                if request.owner_id and request.owner_id != file_metadata.owner_id:
                    continue

                # 大小过滤
                if request.min_size and file_metadata.file_size < request.min_size:
                    continue

                if request.max_size and file_metadata.file_size > request.max_size:
                    continue

                # 时间过滤
                if (
                    request.created_after
                    and file_metadata.created_at < request.created_after
                ):
                    continue

                if (
                    request.created_before
                    and file_metadata.created_at > request.created_before
                ):
                    continue

                filtered_files.append(file_metadata)

            # 排序
            reverse = request.sort_order.lower() == "desc"
            filtered_files.sort(
                key=lambda x: getattr(x, request.sort_by, x.created_at), reverse=reverse
            )

            # 分页
            total = len(filtered_files)
            start_idx = (request.page - 1) * request.page_size
            end_idx = start_idx + request.page_size
            page_files = filtered_files[start_idx:end_idx]

            total_pages = (total + request.page_size - 1) // request.page_size

            return FileSearchResponse(
                files=page_files,
                total=total,
                page=request.page,
                page_size=request.page_size,
                total_pages=total_pages,
            )

        except Exception as e:
            logger.error(f"Error searching files: {e}")
            raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

    async def _check_file_access(
        self, metadata: FileMetadata, user_id: Optional[str], operation: str
    ) -> bool:
        """
        检查文件访问权限

        Args:
            metadata: 文件元数据
            user_id: 用户ID
            operation: 操作类型（read, write, delete）

        Returns:
            是否有权限
        """
        # 公开文件允许读取
        if metadata.access_type == FileAccessType.PUBLIC and operation == "read":
            return True

        # 文件所有者有所有权限
        if user_id and metadata.owner_id == user_id:
            return True

        # 检查权限列表
        if user_id and user_id in metadata.permissions:
            user_perms = metadata.permissions[user_id]
            if operation in user_perms or "all" in user_perms:
                return True

        return False

    async def get_storage_stats(self) -> FileStorageStats:
        """获取存储统计信息"""
        try:
            minio_client = self._get_minio_client()
            bucket_stats = await minio_client.get_bucket_stats()

            # 从缓存统计更详细信息
            all_files = list(self._file_metadata_cache.values())

            files_by_type = {}
            files_by_access_type = {}

            for file_metadata in all_files:
                # 按类型统计
                file_type = file_metadata.file_extension or "unknown"
                files_by_type[file_type] = files_by_type.get(file_type, 0) + 1

                # 按访问类型统计
                access_type = file_metadata.access_type.value
                files_by_access_type[access_type] = (
                    files_by_access_type.get(access_type, 0) + 1
                )

            return FileStorageStats(
                total_files=bucket_stats["total_files"],
                total_size=bucket_stats["total_size"],
                files_by_type=files_by_type,
                files_by_access_type=files_by_access_type,
                storage_usage=bucket_stats,
                upload_statistics={},  # TODO: 实现上传统计
                download_statistics={},  # TODO: 实现下载统计
            )

        except Exception as e:
            logger.error(f"Error getting storage stats: {e}")
            raise HTTPException(status_code=500, detail=f"Stats failed: {str(e)}")


# 全局文件管理器实例
file_manager: Optional[FileManager] = None


def get_file_manager() -> FileManager:
    """获取文件管理器实例"""
    global file_manager

    if file_manager is None:
        file_manager = FileManager()

    return file_manager
