"""
文件存储系统使用示例

展示如何使用文件存储系统的各项功能
"""

import asyncio
import io
import logging
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from PIL import Image

from .file_manager import get_file_manager
from .file_processor import get_file_processor
from .minio_client import get_minio_client, init_minio
from .storage_models import (
    FileAccessType,
    FileSearchRequest,
    FileThumbnailRequest,
    FileUploadRequest,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def basic_file_operations_example():
    """基本文件操作示例"""
    logger.info("=== 基本文件操作示例 ===")

    try:
        # 初始化MinIO客户端
        minio_client = await init_minio()

        # 1. 上传文件
        logger.info("1. 上传文件示例")
        file_content = b"Hello, World! This is a test file."
        file_path = "examples/hello.txt"

        etag = await minio_client.upload_file(
            file_path=file_path,
            file_data=io.BytesIO(file_content),
            content_type="text/plain",
            metadata={"example": "basic_upload", "author": "system"},
        )

        logger.info(f"文件上传成功，ETag: {etag}")

        # 2. 检查文件是否存在
        logger.info("2. 检查文件存在")
        exists = await minio_client.file_exists(file_path)
        logger.info(f"文件存在: {exists}")

        # 3. 获取文件信息
        logger.info("3. 获取文件信息")
        file_info = await minio_client.get_file_info(file_path)
        logger.info(f"文件信息: {file_info}")

        # 4. 下载文件
        logger.info("4. 下载文件")
        downloaded_data = await minio_client.download_file(file_path)
        logger.info(f"下载内容: {downloaded_data.decode()}")

        # 5. 生成预签名URL
        logger.info("5. 生成预签名URL")
        download_url = await minio_client.generate_presigned_url(file_path)
        logger.info(f"下载URL: {download_url}")

        # 6. 复制文件
        logger.info("6. 复制文件")
        copy_path = "examples/hello_copy.txt"
        copy_success = await minio_client.copy_file(file_path, copy_path)
        logger.info(f"文件复制: {'成功' if copy_success else '失败'}")

        # 7. 列出文件
        logger.info("7. 列出文件")
        files = await minio_client.list_files(prefix="examples/")
        for file in files:
            logger.info(f"  - {file['object_name']} ({file['size']} bytes)")

        # 8. 删除文件
        logger.info("8. 删除文件")
        await minio_client.delete_file(file_path)
        await minio_client.delete_file(copy_path)
        logger.info("文件删除完成")

    except Exception as e:
        logger.error(f"基本文件操作示例失败: {e}")


async def image_processing_example():
    """图片处理示例"""
    logger.info("=== 图片处理示例 ===")

    try:
        # 创建测试图片
        image = Image.new("RGB", (800, 600), color="red")

        # 添加一些图形
        from PIL import ImageDraw

        draw = ImageDraw.Draw(image)
        draw.rectangle([100, 100, 700, 500], fill="blue")
        draw.ellipse([200, 200, 600, 400], fill="yellow")

        # 保存为字节
        buffer = io.BytesIO()
        image.save(buffer, format="JPEG")
        image_data = buffer.getvalue()

        logger.info(f"原始图片大小: {len(image_data)} bytes")

        # 获取文件处理器
        processor = get_file_processor()

        # 1. 图片压缩处理
        logger.info("1. 图片压缩处理")
        processed_data, metadata = processor.process_image(
            image_data, max_dimension=400, quality=70
        )

        logger.info(f"压缩后大小: {len(processed_data)} bytes")
        logger.info(f"压缩比: {len(processed_data)/len(image_data)*100:.1f}%")
        logger.info(f"图片元数据: {metadata}")

        # 2. 生成缩略图
        logger.info("2. 生成缩略图")
        thumbnail_data = processor.generate_thumbnail(
            image_data, size=(150, 150), quality=80
        )

        logger.info(f"缩略图大小: {len(thumbnail_data)} bytes")

        # 3. 上传原图和缩略图
        logger.info("3. 上传图片和缩略图")
        minio_client = get_minio_client()

        # 上传原图
        original_path = "examples/test_image.jpg"
        await minio_client.upload_file(
            file_path=original_path,
            file_data=io.BytesIO(processed_data),
            content_type="image/jpeg",
            metadata={"type": "original", "processed": "true"},
        )

        # 上传缩略图
        thumbnail_path = "examples/test_image_thumb.jpg"
        await minio_client.upload_file(
            file_path=thumbnail_path,
            file_data=io.BytesIO(thumbnail_data),
            content_type="image/jpeg",
            metadata={"type": "thumbnail", "parent": original_path},
        )

        logger.info("图片上传完成")

        # 4. 生成图片访问URL
        logger.info("4. 生成图片访问URL")
        original_url = await minio_client.generate_presigned_url(original_path)
        thumbnail_url = await minio_client.generate_presigned_url(thumbnail_path)

        logger.info(f"原图URL: {original_url[:50]}...")
        logger.info(f"缩略图URL: {thumbnail_url[:50]}...")

        # 清理
        await minio_client.delete_file(original_path)
        await minio_client.delete_file(thumbnail_path)

    except Exception as e:
        logger.error(f"图片处理示例失败: {e}")


async def file_manager_example():
    """文件管理器示例"""
    logger.info("=== 文件管理器示例 ===")

    try:
        # 获取文件管理器
        file_manager = get_file_manager()

        # 模拟UploadFile对象
        class MockUploadFile:
            def __init__(self, filename: str, content: bytes, content_type: str = None):
                self.filename = filename
                self.content = content
                self.content_type = content_type

            async def read(self) -> bytes:
                return self.content

        # 1. 上传文档文件
        logger.info("1. 上传文档文件")
        doc_content = """
# 示例文档

这是一个示例文档，用于测试文件管理器功能。

## 特性

- 支持多种文件类型
- 自动元数据提取
- 权限控制
- 搜索功能

创建时间: {timestamp}
        """.format(
            timestamp=datetime.utcnow().isoformat()
        ).encode()

        upload_file = MockUploadFile("example_doc.txt", doc_content, "text/plain")
        upload_request = FileUploadRequest(
            filename="example_doc.txt",
            content_type="text/plain",
            access_type=FileAccessType.PUBLIC,
            folder_path="documents",
            generate_thumbnail=False,
            metadata={"category": "example", "language": "zh"},
        )

        upload_result = await file_manager.upload_file(
            file=upload_file, request=upload_request, owner_id="example_user"
        )

        logger.info(f"文档上传成功，文件ID: {upload_result.file_id}")
        doc_file_id = upload_result.file_id

        # 2. 上传图片文件
        logger.info("2. 上传图片文件")
        image = Image.new("RGB", (300, 200), color="green")
        image_buffer = io.BytesIO()
        image.save(image_buffer, format="PNG")
        image_data = image_buffer.getvalue()

        image_file = MockUploadFile("example_image.png", image_data, "image/png")
        image_request = FileUploadRequest(
            filename="example_image.png",
            content_type="image/png",
            access_type=FileAccessType.PRIVATE,
            folder_path="images",
            generate_thumbnail=True,
            metadata={"category": "example", "width": 300, "height": 200},
        )

        image_result = await file_manager.upload_file(
            file=image_file, request=image_request, owner_id="example_user"
        )

        logger.info(f"图片上传成功，文件ID: {image_result.file_id}")
        image_file_id = image_result.file_id

        # 3. 获取文件元数据
        logger.info("3. 获取文件元数据")
        doc_metadata = await file_manager.get_file_metadata(doc_file_id)
        image_metadata = await file_manager.get_file_metadata(image_file_id)

        if doc_metadata:
            logger.info(
                f"文档元数据: {doc_metadata.filename}, {doc_metadata.file_size} bytes"
            )

        if image_metadata:
            logger.info(
                f"图片元数据: {image_metadata.filename}, {image_metadata.image_width}x{image_metadata.image_height}"
            )

        # 4. 生成缩略图
        if image_metadata and image_metadata.has_thumbnail:
            logger.info("4. 获取图片缩略图")
            thumbnail_request = FileThumbnailRequest(
                file_id=image_file_id, width=100, height=100, quality=90
            )

            thumbnail_result = await file_manager.get_file_thumbnail(
                file_id=image_file_id, request=thumbnail_request, user_id="example_user"
            )

            logger.info(f"缩略图URL: {thumbnail_result.thumbnail_url[:50]}...")

        # 5. 搜索文件
        logger.info("5. 搜索文件")
        search_request = FileSearchRequest(
            query="example", owner_id="example_user", page=1, page_size=10
        )

        search_result = await file_manager.search_files(
            request=search_request, user_id="example_user"
        )

        logger.info(f"搜索结果: 找到 {search_result.total} 个文件")
        for file in search_result.files:
            logger.info(f"  - {file.filename} ({file.file_type})")

        # 6. 下载文件
        logger.info("6. 下载文件")
        download_result = await file_manager.download_file(
            file_id=doc_file_id, user_id="example_user"
        )

        logger.info(f"下载URL: {download_result.download_url[:50]}...")

        # 7. 获取存储统计
        logger.info("7. 获取存储统计")
        stats = await file_manager.get_storage_stats()
        logger.info(
            f"存储统计: {stats.total_files} 个文件, {stats.total_size/1024/1024:.1f} MB"
        )

        # 8. 删除文件
        logger.info("8. 删除文件")
        doc_deleted = await file_manager.delete_file(doc_file_id, "example_user")
        image_deleted = await file_manager.delete_file(image_file_id, "example_user")

        logger.info(f"删除结果: 文档={doc_deleted}, 图片={image_deleted}")

    except Exception as e:
        logger.error(f"文件管理器示例失败: {e}")


async def permission_and_security_example():
    """权限和安全示例"""
    logger.info("=== 权限和安全示例 ===")

    try:
        file_manager = get_file_manager()

        # 模拟不同用户
        users = {
            "owner": "user_001",
            "viewer": "user_002",
            "editor": "user_003",
            "guest": "user_004",
        }

        # 创建测试文件
        class MockUploadFile:
            def __init__(self, filename: str, content: bytes):
                self.filename = filename
                self.content = content
                self.content_type = "text/plain"

            async def read(self) -> bytes:
                return self.content

        # 1. 创建私有文件
        logger.info("1. 创建私有文件")
        private_content = b"This is a private document with sensitive information."
        private_file = MockUploadFile("private_doc.txt", private_content)

        private_request = FileUploadRequest(
            filename="private_doc.txt",
            access_type=FileAccessType.PRIVATE,
            permissions={users["viewer"]: ["read"], users["editor"]: ["read", "write"]},
        )

        private_result = await file_manager.upload_file(
            file=private_file, request=private_request, owner_id=users["owner"]
        )

        private_file_id = private_result.file_id
        logger.info(f"私有文件创建成功: {private_file_id}")

        # 2. 创建公开文件
        logger.info("2. 创建公开文件")
        public_content = b"This is a public document that everyone can access."
        public_file = MockUploadFile("public_doc.txt", public_content)

        public_request = FileUploadRequest(
            filename="public_doc.txt", access_type=FileAccessType.PUBLIC
        )

        public_result = await file_manager.upload_file(
            file=public_file, request=public_request, owner_id=users["owner"]
        )

        public_file_id = public_result.file_id
        logger.info(f"公开文件创建成功: {public_file_id}")

        # 3. 测试权限访问
        logger.info("3. 测试权限访问")

        # 所有者访问
        try:
            owner_download = await file_manager.download_file(
                private_file_id, users["owner"]
            )
            logger.info("✅ 所有者可以访问私有文件")
        except Exception as e:
            logger.error(f"❌ 所有者访问失败: {e}")

        # 有权限用户访问
        try:
            viewer_download = await file_manager.download_file(
                private_file_id, users["viewer"]
            )
            logger.info("✅ 有权限用户可以访问私有文件")
        except Exception as e:
            logger.error(f"❌ 有权限用户访问失败: {e}")

        # 无权限用户访问
        try:
            guest_download = await file_manager.download_file(
                private_file_id, users["guest"]
            )
            logger.error("❌ 无权限用户不应该能访问私有文件")
        except Exception as e:
            logger.info(f"✅ 无权限用户正确被拒绝访问: {type(e).__name__}")

        # 公开文件访问
        try:
            guest_public = await file_manager.download_file(
                public_file_id, users["guest"]
            )
            logger.info("✅ 任何用户都可以访问公开文件")
        except Exception as e:
            logger.error(f"❌ 公开文件访问失败: {e}")

        # 4. 测试搜索权限
        logger.info("4. 测试搜索权限")

        # 所有者搜索
        owner_search = await file_manager.search_files(
            FileSearchRequest(owner_id=users["owner"]), users["owner"]
        )
        logger.info(f"所有者搜索结果: {owner_search.total} 个文件")

        # 访客搜索
        guest_search = await file_manager.search_files(
            FileSearchRequest(), users["guest"]
        )
        logger.info(f"访客搜索结果: {guest_search.total} 个文件（只能看到公开文件）")

        # 清理
        await file_manager.delete_file(private_file_id, users["owner"])
        await file_manager.delete_file(public_file_id, users["owner"])

    except Exception as e:
        logger.error(f"权限和安全示例失败: {e}")


async def bulk_operations_example():
    """批量操作示例"""
    logger.info("=== 批量操作示例 ===")

    try:
        minio_client = get_minio_client()

        # 1. 批量上传文件
        logger.info("1. 批量上传文件")

        files_to_upload = []
        for i in range(5):
            file_content = (
                f"这是批量上传的第 {i+1} 个文件\n创建时间: {datetime.utcnow()}"
            )
            file_path = f"bulk/file_{i+1:03d}.txt"

            await minio_client.upload_file(
                file_path=file_path,
                file_data=io.BytesIO(file_content.encode()),
                content_type="text/plain",
                metadata={"batch": "example", "index": str(i + 1)},
            )

            files_to_upload.append(file_path)

        logger.info(f"批量上传完成: {len(files_to_upload)} 个文件")

        # 2. 批量列出文件
        logger.info("2. 批量列出文件")
        bulk_files = await minio_client.list_files(prefix="bulk/", recursive=True)

        for file in bulk_files:
            logger.info(f"  - {file['object_name']} ({file['size']} bytes)")

        # 3. 批量复制文件
        logger.info("3. 批量复制文件")
        copied_files = []

        for original_path in files_to_upload:
            copy_path = original_path.replace("bulk/", "bulk_copy/")
            success = await minio_client.copy_file(original_path, copy_path)

            if success:
                copied_files.append(copy_path)
                logger.info(f"  复制成功: {original_path} -> {copy_path}")

        logger.info(f"批量复制完成: {len(copied_files)} 个文件")

        # 4. 获取批量文件信息
        logger.info("4. 获取批量文件信息")
        total_size = 0

        for file_path in files_to_upload + copied_files:
            file_info = await minio_client.get_file_info(file_path)
            if file_info:
                total_size += file_info["size"]

        logger.info(f"批量文件总大小: {total_size} bytes")

        # 5. 批量删除文件
        logger.info("5. 批量删除文件")
        deleted_count = 0

        for file_path in files_to_upload + copied_files:
            success = await minio_client.delete_file(file_path)
            if success:
                deleted_count += 1

        logger.info(f"批量删除完成: {deleted_count} 个文件")

    except Exception as e:
        logger.error(f"批量操作示例失败: {e}")


async def advanced_features_example():
    """高级功能示例"""
    logger.info("=== 高级功能示例 ===")

    try:
        minio_client = get_minio_client()
        processor = get_file_processor()

        # 1. 文件类型检测和验证
        logger.info("1. 文件类型检测和验证")

        test_files = {
            "image.jpg": b"\xff\xd8\xff\xe0",  # JPEG头部
            "document.pdf": b"%PDF-1.4",  # PDF头部
            "text.txt": b"Hello, World!",  # 纯文本
            "executable.exe": b"MZ",  # 可执行文件头部
        }

        for filename, content in test_files.items():
            mime_type = processor.get_mime_type(filename, content)
            is_valid = processor.validate_file_extension(filename)

            logger.info(
                f"  {filename}: {mime_type}, 验证: {'通过' if is_valid else '失败'}"
            )

        # 2. 元数据提取
        logger.info("2. 元数据提取")

        # 创建带EXIF的图片
        test_image = Image.new("RGB", (640, 480), color="blue")
        image_buffer = io.BytesIO()
        test_image.save(image_buffer, format="JPEG", quality=95)
        image_data = image_buffer.getvalue()

        # 创建文件元数据
        metadata = processor.create_file_metadata(
            file_id="advanced_example_001",
            filename="test_metadata.jpg",
            file_data=image_data,
            file_path="advanced/test_metadata.jpg",
            owner_id="advanced_user",
            access_type=FileAccessType.PRIVATE,
        )

        logger.info(f"  文件元数据:")
        logger.info(f"    - 文件大小: {metadata.file_size} bytes")
        logger.info(f"    - 文件类型: {metadata.file_type}")
        logger.info(f"    - 图片尺寸: {metadata.image_width}x{metadata.image_height}")
        logger.info(f"    - 内容哈希: {metadata.content_hash[:16]}...")

        # 3. 图片压缩和优化
        logger.info("3. 图片压缩和优化")

        # 创建大图片
        large_image = Image.new("RGB", (2048, 1536), color="red")
        large_buffer = io.BytesIO()
        large_image.save(large_buffer, format="JPEG", quality=100)
        large_data = large_buffer.getvalue()

        logger.info(f"  原始图片: {len(large_data)} bytes")

        # 压缩图片
        compressed_data, img_metadata = processor.process_image(
            large_data, max_dimension=1024, quality=75
        )

        logger.info(f"  压缩后: {len(compressed_data)} bytes")
        logger.info(f"  压缩比: {len(compressed_data)/len(large_data)*100:.1f}%")
        logger.info(
            f"  新尺寸: {img_metadata.get('processed_width')}x{img_metadata.get('processed_height')}"
        )

        # 4. 预签名URL的高级用法
        logger.info("4. 预签名URL的高级用法")

        # 上传测试文件
        test_path = "advanced/url_test.txt"
        test_content = b"This is for URL testing"

        await minio_client.upload_file(
            file_path=test_path,
            file_data=io.BytesIO(test_content),
            content_type="text/plain",
        )

        # 生成不同类型的URL
        from datetime import timedelta

        # 短期URL（1小时）
        short_url = await minio_client.generate_presigned_url(
            test_path, expires=timedelta(hours=1)
        )

        # 长期URL（7天）
        long_url = await minio_client.generate_presigned_url(
            test_path, expires=timedelta(days=7)
        )

        logger.info(f"  短期URL: {short_url[:50]}...")
        logger.info(f"  长期URL: {long_url[:50]}...")

        # 5. 存储统计和分析
        logger.info("5. 存储统计和分析")

        bucket_stats = await minio_client.get_bucket_stats()

        logger.info(f"  存储统计:")
        logger.info(f"    - 总文件数: {bucket_stats['total_files']}")
        logger.info(f"    - 总大小: {bucket_stats['total_size']/1024/1024:.1f} MB")
        logger.info(f"    - 文件类型分布: {bucket_stats['files_by_type']}")

        # 清理
        await minio_client.delete_file(test_path)

    except Exception as e:
        logger.error(f"高级功能示例失败: {e}")


async def error_handling_example():
    """错误处理示例"""
    logger.info("=== 错误处理示例 ===")

    try:
        minio_client = get_minio_client()
        processor = get_file_processor()

        # 1. 文件不存在错误
        logger.info("1. 处理文件不存在错误")

        try:
            await minio_client.download_file("non_existent_file.txt")
            logger.error("  ❌ 应该抛出异常")
        except Exception as e:
            logger.info(f"  ✅ 正确处理文件不存在: {type(e).__name__}")

        # 2. 无效文件类型错误
        logger.info("2. 处理无效文件类型错误")

        invalid_files = ["malware.exe", "script.bat", "dangerous.php"]

        for filename in invalid_files:
            is_valid = processor.validate_file_extension(filename)
            logger.info(f"  {filename}: {'允许' if is_valid else '拒绝'}")

        # 3. 文件大小超限错误
        logger.info("3. 处理文件大小超限错误")

        from app.core.config import get_settings

        settings = get_settings()

        oversized_file = settings.max_file_size + 1000
        valid_size = settings.max_file_size - 1000

        logger.info(
            f"  超大文件 ({oversized_file} bytes): {'允许' if processor.validate_file_size(oversized_file) else '拒绝'}"
        )
        logger.info(
            f"  正常文件 ({valid_size} bytes): {'允许' if processor.validate_file_size(valid_size) else '拒绝'}"
        )

        # 4. 网络连接错误模拟
        logger.info("4. 网络连接状态检查")

        if minio_client.is_connected:
            logger.info("  ✅ MinIO连接正常")
        else:
            logger.warning("  ⚠️ MinIO连接异常")

        # 5. 权限错误处理
        logger.info("5. 权限错误处理")

        file_manager = get_file_manager()

        # 模拟权限检查
        fake_metadata = type(
            "FileMetadata",
            (),
            {
                "access_type": FileAccessType.PRIVATE,
                "owner_id": "user_001",
                "permissions": {"user_002": ["read"]},
            },
        )()

        # 测试不同用户的访问权限
        test_cases = [
            ("user_001", "read", True),  # 所有者
            ("user_002", "read", True),  # 有权限用户
            ("user_003", "read", False),  # 无权限用户
            ("user_002", "delete", False),  # 权限不足
        ]

        for user_id, operation, expected in test_cases:
            result = await file_manager._check_file_access(
                fake_metadata, user_id, operation
            )
            status = "✅" if result == expected else "❌"
            logger.info(
                f"  {status} 用户 {user_id} {operation}: {'允许' if result else '拒绝'}"
            )

    except Exception as e:
        logger.error(f"错误处理示例失败: {e}")


async def run_all_examples():
    """运行所有示例"""
    logger.info("🚀 开始运行文件存储系统示例")
    logger.info("=" * 60)

    try:
        # 初始化系统
        await init_minio()

        # 运行各个示例
        examples = [
            ("基本文件操作", basic_file_operations_example),
            ("图片处理", image_processing_example),
            ("文件管理器", file_manager_example),
            ("权限和安全", permission_and_security_example),
            ("批量操作", bulk_operations_example),
            ("高级功能", advanced_features_example),
            ("错误处理", error_handling_example),
        ]

        for name, example_func in examples:
            try:
                logger.info(f"\n{'='*50}")
                logger.info(f"运行示例: {name}")
                logger.info("=" * 50)

                await example_func()
                logger.info(f"✅ {name} 示例完成")

            except Exception as e:
                logger.error(f"❌ {name} 示例失败: {e}")

        logger.info("\n" + "=" * 60)
        logger.info("🎉 所有示例运行完成!")

    except Exception as e:
        logger.error(f"运行示例时发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(run_all_examples())
