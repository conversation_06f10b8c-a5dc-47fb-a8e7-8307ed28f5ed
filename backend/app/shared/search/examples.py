"""
搜索系统使用示例

演示如何使用 Elasticsearch 搜索系统的各种功能。
"""

import asyncio
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Elasticsearch 基本操作示例
# ============================================================================


async def elasticsearch_basic_examples():
    """Elasticsearch 基本操作示例"""
    from . import (
        close_elasticsearch,
        get_es_client,
        get_index_manager,
        init_elasticsearch,
    )

    print("=" * 50)
    print("Elasticsearch 基本操作示例")
    print("=" * 50)

    try:
        # 初始化连接
        await init_elasticsearch()
        es_client = get_es_client()
        index_manager = get_index_manager()

        print("\n1. 连接和集群信息:")

        # 检查连接
        if await es_client.ping():
            print("  ✅ Elasticsearch 连接正常")
        else:
            print("  ❌ Elasticsearch 连接失败")
            return

        # 获取集群信息
        cluster_info = await es_client.get_cluster_info()
        print(f"  集群名称: {cluster_info.get('cluster_name')}")
        print(f"  版本: {cluster_info.get('version')}")
        print(f"  状态: {cluster_info.get('status')}")
        print(f"  节点数: {cluster_info.get('number_of_nodes')}")

        print("\n2. 索引管理:")

        # 创建测试索引
        test_index = "example_index"
        await es_client.delete_index(test_index)  # 清理可能存在的索引

        mapping = index_manager.get_default_chinese_mapping()
        settings = index_manager.get_default_settings()

        if await index_manager.create_index(test_index, mapping, settings):
            print(f"  ✅ 索引创建成功: {test_index}")
        else:
            print(f"  ❌ 索引创建失败: {test_index}")

        # 检查索引是否存在
        if await es_client.index_exists(test_index):
            print(f"  ✅ 索引存在: {test_index}")

        # 获取索引信息
        index_info = await index_manager.get_index_info(test_index)
        if index_info:
            print(f"  索引设置: {len(index_info.get('settings', {}))} 项配置")
            print(
                f"  索引映射: {len(index_info.get('mappings', {}).get('properties', {}))} 个字段"
            )

        print("\n3. 文档操作:")

        # 索引文档
        test_doc = {
            "title": "示例文档",
            "content": "这是一个示例文档，用于演示 Elasticsearch 的基本功能",
            "category": "示例",
            "tags": ["演示", "教程", "搜索"],
            "created_at": datetime.now().isoformat(),
            "status": "published",
        }

        doc_id = await es_client.index_document(test_index, test_doc)
        if doc_id:
            print(f"  ✅ 文档索引成功: {doc_id}")
        else:
            print("  ❌ 文档索引失败")

        # 刷新索引
        await es_client.refresh_index(test_index)

        # 获取文档
        retrieved_doc = await es_client.get_document(test_index, doc_id)
        if retrieved_doc:
            print(f"  ✅ 文档获取成功: {retrieved_doc.get('title')}")
        else:
            print("  ❌ 文档获取失败")

        # 更新文档
        update_data = {"priority": 1, "updated_at": datetime.now().isoformat()}
        if await es_client.update_document(test_index, doc_id, update_data):
            print("  ✅ 文档更新成功")
        else:
            print("  ❌ 文档更新失败")

        # 删除文档
        if await es_client.delete_document(test_index, doc_id):
            print("  ✅ 文档删除成功")
        else:
            print("  ❌ 文档删除失败")

        # 清理测试索引
        await es_client.delete_index(test_index)
        print(f"  🧹 清理测试索引: {test_index}")

    except Exception as e:
        logger.error(f"基本操作示例失败: {e}")
    finally:
        await close_elasticsearch()


# 搜索功能示例
# ============================================================================


async def search_functionality_examples():
    """搜索功能示例"""
    from . import (
        SearchFilter,
        SearchHighlight,
        SearchQuery,
        SearchSort,
        close_elasticsearch,
        get_index_manager,
        get_search_service,
        init_elasticsearch,
    )

    print("=" * 50)
    print("搜索功能示例")
    print("=" * 50)

    try:
        # 初始化
        await init_elasticsearch()
        search_service = get_search_service()
        index_manager = get_index_manager()

        # 创建测试索引和数据
        test_index = "search_examples"
        await index_manager.delete_index(test_index)

        mapping = index_manager.get_default_chinese_mapping()
        await index_manager.create_index(test_index, mapping)

        # 准备测试数据
        test_docs = [
            {
                "title": "柴管家智能客服系统介绍",
                "content": "柴管家是一个基于人工智能的多平台聚合智能客服系统，支持微信、钉钉、企业微信等多种平台的消息处理",
                "category": "产品介绍",
                "tags": ["智能客服", "多平台", "AI", "聊天机器人"],
                "created_at": datetime.now().isoformat(),
                "priority": 1,
                "status": "published",
            },
            {
                "title": "Elasticsearch 搜索引擎配置指南",
                "content": "本文档详细介绍如何配置和优化 Elasticsearch 搜索引擎，包括索引设置、映射配置和性能调优",
                "category": "技术文档",
                "tags": ["搜索引擎", "Elasticsearch", "配置", "优化"],
                "created_at": (datetime.now() - timedelta(days=1)).isoformat(),
                "priority": 2,
                "status": "published",
            },
            {
                "title": "中文分词技术在搜索中的应用",
                "content": "中文分词是中文信息处理的基础技术，本文介绍 IK 分词器在 Elasticsearch 中的应用和配置方法",
                "category": "技术文档",
                "tags": ["中文分词", "IK分词器", "自然语言处理", "搜索"],
                "created_at": (datetime.now() - timedelta(days=2)).isoformat(),
                "priority": 3,
                "status": "published",
            },
            {
                "title": "API 接口开发规范",
                "content": "本文档定义了 RESTful API 的开发规范和最佳实践，包括接口设计、错误处理和文档编写",
                "category": "开发规范",
                "tags": ["API", "RESTful", "开发规范", "最佳实践"],
                "created_at": (datetime.now() - timedelta(days=3)).isoformat(),
                "priority": 2,
                "status": "draft",
            },
        ]

        # 批量索引测试数据
        await search_service.bulk_index_documents(test_index, test_docs, refresh=True)
        print(f"✅ 索引了 {len(test_docs)} 个测试文档")

        print("\n1. 基本关键词搜索:")

        # 简单关键词搜索
        query = SearchQuery(query="柴管家", size=5)
        result = await search_service.search(test_index, query)

        print(f"  搜索 '柴管家' 找到 {result.total} 个结果:")
        for doc in result.documents:
            print(f"    - {doc.source.get('title')} (得分: {doc.score:.2f})")

        print("\n2. 多字段搜索:")

        # 多字段搜索，title 字段权重更高
        query = SearchQuery(
            query="搜索配置", fields=["title^2", "content", "tags"], size=5
        )
        result = await search_service.search(test_index, query)

        print(f"  多字段搜索 '搜索配置' 找到 {result.total} 个结果:")
        for doc in result.documents:
            print(f"    - {doc.source.get('title')} (得分: {doc.score:.2f})")

        print("\n3. 过滤搜索:")

        # 带过滤条件的搜索
        query = SearchQuery(
            query="技术",
            size=5,
            filters=[
                SearchFilter(field="category", values=["技术文档"]),
                SearchFilter(field="status", values=["published"]),
            ],
        )
        result = await search_service.search(test_index, query)

        print(f"  过滤搜索找到 {result.total} 个结果:")
        for doc in result.documents:
            print(
                f"    - {doc.source.get('title')} (分类: {doc.source.get('category')})"
            )

        print("\n4. 排序搜索:")

        # 按优先级和创建时间排序
        query = SearchQuery(
            query="",  # 匹配所有文档
            size=5,
            sort=[
                SearchSort(field="priority", order="asc"),
                SearchSort(field="created_at", order="desc"),
            ],
        )
        result = await search_service.search(test_index, query)

        print(f"  排序搜索结果:")
        for doc in result.documents:
            print(
                f"    - {doc.source.get('title')} (优先级: {doc.source.get('priority')})"
            )

        print("\n5. 高亮搜索:")

        # 带高亮的搜索
        query = SearchQuery(
            query="分词技术",
            fields=["title", "content"],
            highlight=SearchHighlight(
                fields=["title", "content"], fragment_size=100, number_of_fragments=2
            ),
            size=3,
        )
        result = await search_service.search(test_index, query)

        print(f"  高亮搜索结果:")
        for doc in result.documents:
            print(f"    标题: {doc.source.get('title')}")
            if doc.highlight:
                for field, highlights in doc.highlight.items():
                    print(f"    高亮 {field}: {highlights}")

        print("\n6. 范围查询:")

        # 时间范围查询
        end_date = datetime.now()
        start_date = end_date - timedelta(days=2)

        query = SearchQuery(
            query="",
            size=5,
            filters=[
                SearchFilter(
                    field="created_at",
                    values=[start_date.isoformat(), end_date.isoformat()],
                    operator="range",
                )
            ],
        )
        result = await search_service.search(test_index, query)

        print(f"  时间范围查询找到 {result.total} 个结果:")
        for doc in result.documents:
            created_at = doc.source.get("created_at", "").split("T")[0]
            print(f"    - {doc.source.get('title')} (创建: {created_at})")

        # 清理测试索引
        await index_manager.delete_index(test_index)
        print(f"\n🧹 清理测试索引: {test_index}")

    except Exception as e:
        logger.error(f"搜索功能示例失败: {e}")
    finally:
        await close_elasticsearch()


# 中文分词示例
# ============================================================================


async def chinese_analysis_examples():
    """中文分词示例"""
    from . import close_elasticsearch, get_search_service, init_elasticsearch

    print("=" * 50)
    print("中文分词示例")
    print("=" * 50)

    try:
        # 初始化
        await init_elasticsearch()
        search_service = get_search_service()

        # 测试文本
        test_texts = [
            "柴管家是一个智能客服系统",
            "基于人工智能的多平台聚合解决方案",
            "支持微信、钉钉、企业微信等平台",
            "提供自然语言处理和机器学习功能",
        ]

        print("\n1. IK 最大粒度分词 (ik_max_word):")

        for text in test_texts:
            tokens = await search_service.analyze_text(text, "ik_max_word")
            print(f"  原文: {text}")
            print(f"  分词: {' | '.join(tokens)}")
            print()

        print("\n2. IK 智能分词 (ik_smart):")

        for text in test_texts:
            tokens = await search_service.analyze_text(text, "ik_smart")
            print(f"  原文: {text}")
            print(f"  分词: {' | '.join(tokens)}")
            print()

        print("\n3. 标准分词器 (standard):")

        english_text = "Elasticsearch is a distributed search engine"
        tokens = await search_service.analyze_text(english_text, "standard")
        print(f"  原文: {english_text}")
        print(f"  分词: {' | '.join(tokens)}")

    except Exception as e:
        logger.error(f"中文分词示例失败: {e}")
    finally:
        await close_elasticsearch()


# 批量操作示例
# ============================================================================


async def bulk_operations_examples():
    """批量操作示例"""
    from . import (
        close_elasticsearch,
        get_index_manager,
        get_search_service,
        init_elasticsearch,
    )

    print("=" * 50)
    print("批量操作示例")
    print("=" * 50)

    try:
        # 初始化
        await init_elasticsearch()
        search_service = get_search_service()
        index_manager = get_index_manager()

        # 创建测试索引
        test_index = "bulk_examples"
        await index_manager.delete_index(test_index)

        mapping = index_manager.get_default_chinese_mapping()
        await index_manager.create_index(test_index, mapping)

        print("\n1. 批量索引文档:")

        # 生成大量测试文档
        import time

        start_time = time.time()

        docs_count = 1000
        test_docs = []

        for i in range(docs_count):
            doc = {
                "title": f"批量文档 {i}",
                "content": f"这是第 {i} 个批量生成的文档，用于测试批量索引功能和性能",
                "category": f"分类{i % 10}",
                "tags": [f"标签{i % 20}", "批量", "测试"],
                "created_at": (datetime.now() - timedelta(days=i % 365)).isoformat(),
                "priority": i % 5,
                "status": "published",
            }
            test_docs.append(doc)

        generation_time = time.time() - start_time
        print(f"  生成 {docs_count} 个文档耗时: {generation_time:.2f}s")

        # 批量索引
        start_time = time.time()
        result = await search_service.bulk_index_documents(
            test_index, test_docs, refresh=True
        )
        index_time = time.time() - start_time

        print(f"  批量索引耗时: {index_time:.2f}s")
        print(f"  索引速率: {docs_count/index_time:.1f} docs/s")
        print(f"  错误数量: {len(result.get('errors', []))}")

        print("\n2. 文档统计:")

        # 统计文档数量
        total_count = await search_service.count_documents(test_index)
        print(f"  总文档数: {total_count}")

        # 按分类统计
        from . import SearchFilter, SearchQuery

        category_counts = {}
        for i in range(10):
            category = f"分类{i}"
            query = SearchQuery(
                query="",
                filters=[SearchFilter(field="category", values=[category])],
                size=0,
            )
            result = await search_service.search(test_index, query)
            category_counts[category] = result.total

        print(f"  分类统计:")
        for category, count in category_counts.items():
            print(f"    {category}: {count} 个文档")

        print("\n3. 批量搜索性能测试:")

        # 测试搜索性能
        search_queries = ["批量文档", "测试功能", "分类标签", "索引性能"]

        start_time = time.time()
        search_results = []

        for query_text in search_queries:
            query = SearchQuery(query=query_text, size=10)
            result = await search_service.search(test_index, query)
            search_results.append(result)

        search_time = time.time() - start_time

        print(f"  执行 {len(search_queries)} 个搜索查询耗时: {search_time:.3f}s")
        print(f"  平均搜索时间: {search_time/len(search_queries):.3f}s")

        for i, result in enumerate(search_results):
            print(f"    查询 '{search_queries[i]}': {result.total} 个结果")

        print("\n4. 滚动搜索示例:")

        # 滚动搜索大量数据
        start_time = time.time()
        all_docs = await search_service.scroll_search(
            test_index, {"match_all": {}}, size=100
        )
        scroll_time = time.time() - start_time

        print(f"  滚动搜索获取 {len(all_docs)} 个文档耗时: {scroll_time:.2f}s")
        print(f"  滚动搜索速率: {len(all_docs)/scroll_time:.1f} docs/s")

        # 清理测试索引
        await index_manager.delete_index(test_index)
        print(f"\n🧹 清理测试索引: {test_index}")

    except Exception as e:
        logger.error(f"批量操作示例失败: {e}")
    finally:
        await close_elasticsearch()


# 业务场景示例
# ============================================================================


async def business_scenarios_examples():
    """业务场景示例"""
    from . import (
        close_elasticsearch,
        get_index_manager,
        get_search_service,
        init_elasticsearch,
    )

    print("=" * 50)
    print("业务场景示例")
    print("=" * 50)

    try:
        # 初始化
        await init_elasticsearch()
        search_service = get_search_service()
        index_manager = get_index_manager()

        # 设置默认索引
        await index_manager.setup_default_indices()

        print("\n场景1: 智能客服消息搜索")

        # 模拟客服消息数据
        messages = [
            {
                "content": "你好，我想了解一下产品的价格信息",
                "sender_name": "张三",
                "sender_id": "user_001",
                "channel": "微信",
                "category": "销售咨询",
                "created_at": datetime.now().isoformat(),
                "status": "handled",
            },
            {
                "content": "系统登录出现问题，提示密码错误",
                "sender_name": "李四",
                "sender_id": "user_002",
                "channel": "钉钉",
                "category": "技术支持",
                "created_at": (datetime.now() - timedelta(hours=1)).isoformat(),
                "status": "pending",
            },
            {
                "content": "如何设置自动回复功能？",
                "sender_name": "王五",
                "sender_id": "user_003",
                "channel": "企业微信",
                "category": "功能咨询",
                "created_at": (datetime.now() - timedelta(hours=2)).isoformat(),
                "status": "handled",
            },
        ]

        # 索引消息
        await search_service.bulk_index_documents("messages", messages, refresh=True)
        print(f"  索引了 {len(messages)} 条消息")

        # 搜索价格相关消息
        result = await search_service.search_messages(
            query="价格", filters={"category": ["销售咨询"]}, size=10
        )

        print(f"  搜索 '价格' 相关消息: {result.total} 条")
        for doc in result.documents:
            source = doc.source
            print(f"    {source.get('sender_name')}: {source.get('content')[:30]}...")

        print("\n场景2: 知识库智能检索")

        # 模拟知识库数据
        knowledge_items = [
            {
                "title": "产品价格说明",
                "content": "我们的产品采用阶梯定价模式，基础版本99元/月，专业版本199元/月，企业版本299元/月",
                "category": "产品信息",
                "tags": ["价格", "定价", "收费"],
                "author": "产品经理",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "status": "published",
            },
            {
                "title": "系统登录问题排查",
                "content": "用户登录失败的常见原因：1.密码错误 2.账号被锁定 3.网络连接问题 4.浏览器缓存问题",
                "category": "技术支持",
                "tags": ["登录", "故障排查", "密码"],
                "author": "技术支持",
                "created_at": (datetime.now() - timedelta(days=1)).isoformat(),
                "updated_at": datetime.now().isoformat(),
                "status": "published",
            },
            {
                "title": "自动回复配置指南",
                "content": "设置自动回复：进入管理后台 -> 自动化设置 -> 自动回复 -> 添加规则。支持关键词匹配和智能回复",
                "category": "使用指南",
                "tags": ["自动回复", "配置", "设置"],
                "author": "客服主管",
                "created_at": (datetime.now() - timedelta(days=2)).isoformat(),
                "updated_at": datetime.now().isoformat(),
                "status": "published",
            },
        ]

        # 索引知识库
        await search_service.bulk_index_documents(
            "knowledge", knowledge_items, refresh=True
        )
        print(f"  索引了 {len(knowledge_items)} 个知识库条目")

        # 智能知识检索
        search_queries = [
            ("产品多少钱", "产品信息"),
            ("登录不了", "技术支持"),
            ("怎么设置自动回复", None),
        ]

        for query_text, category in search_queries:
            result = await search_service.search_knowledge(
                query=query_text, category=category, size=3
            )

            print(f"  查询 '{query_text}': {result.total} 个结果")
            for doc in result.documents:
                source = doc.source
                score = doc.score or 0
                print(f"    {source.get('title')} (相关性: {score:.2f})")
                if doc.highlight:
                    for field, highlights in doc.highlight.items():
                        print(f"      高亮: {highlights[0] if highlights else ''}")

        print("\n场景3: 多维度搜索分析")

        # 聚合查询 - 按渠道统计消息数量
        aggregations = {
            "channels": {"terms": {"field": "channel.keyword", "size": 10}},
            "categories": {"terms": {"field": "category.keyword", "size": 10}},
        }

        agg_result = await search_service.aggregate("messages", aggregations)

        print(f"  消息渠道分布:")
        for bucket in agg_result.get("channels", {}).get("buckets", []):
            print(f"    {bucket['key']}: {bucket['doc_count']} 条")

        print(f"  消息分类分布:")
        for bucket in agg_result.get("categories", {}).get("buckets", []):
            print(f"    {bucket['key']}: {bucket['doc_count']} 条")

        print("\n场景4: 实时搜索建议")

        # 获取搜索建议
        suggest_queries = ["价格", "登录", "设置"]

        for query in suggest_queries:
            suggestions = await search_service.suggest(
                "knowledge", query, field="title", size=3
            )
            print(f"  '{query}' 的建议: {suggestions}")

        print("\n🎉 业务场景演示完成！")

    except Exception as e:
        logger.error(f"业务场景示例失败: {e}")
    finally:
        await close_elasticsearch()


# 主函数 - 运行所有示例
# ============================================================================


async def run_all_examples():
    """运行所有示例"""
    print("🚀 开始运行搜索系统示例...")

    examples = [
        ("Elasticsearch 基本操作", elasticsearch_basic_examples),
        ("搜索功能演示", search_functionality_examples),
        ("中文分词演示", chinese_analysis_examples),
        ("批量操作演示", bulk_operations_examples),
        ("业务场景演示", business_scenarios_examples),
    ]

    for name, example_func in examples:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            await example_func()
            print(f"✅ {name} 完成")
        except Exception as e:
            print(f"❌ {name} 失败: {e}")

        # 等待一下，避免连接过于频繁
        await asyncio.sleep(1)

    print("\n🎉 所有示例运行完成！")


if __name__ == "__main__":
    import os
    import sys

    # 添加项目根目录到 Python 路径
    sys.path.append(os.path.join(os.path.dirname(__file__), "../../.."))

    asyncio.run(run_all_examples())
