"""
搜索服务模块

提供高级搜索功能，包括全文搜索、多字段搜索、聚合查询等。
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from .elasticsearch_client import ElasticsearchClient, get_es_client
from .index_manager import IndexManager, get_index_manager
from .search_models import (
    SearchFilter,
    SearchHighlight,
    SearchQuery,
    SearchResult,
    SearchSort,
)

logger = logging.getLogger(__name__)


class SearchService:
    """搜索服务"""

    def __init__(
        self,
        es_client: Optional[ElasticsearchClient] = None,
        index_manager: Optional[IndexManager] = None,
    ):
        """
        初始化搜索服务

        Args:
            es_client: Elasticsearch 客户端
            index_manager: 索引管理器
        """
        self.es_client = es_client or get_es_client()
        self.index_manager = index_manager or get_index_manager()

    async def index_document(
        self,
        index: str,
        document: Dict[str, Any],
        doc_id: Optional[str] = None,
        refresh: bool = False,
    ) -> Optional[str]:
        """
        索引单个文档

        Args:
            index: 索引名称
            document: 文档内容
            doc_id: 文档ID
            refresh: 是否立即刷新索引

        Returns:
            Optional[str]: 文档ID
        """
        try:
            # 添加时间戳
            if "indexed_at" not in document:
                document["indexed_at"] = datetime.now().isoformat()

            doc_id = await self.es_client.index_document(index, document, doc_id)

            if refresh and doc_id:
                await self.es_client.refresh_index(index)

            return doc_id

        except Exception as e:
            logger.error(f"索引文档失败 {index}: {e}")
            return None

    async def bulk_index_documents(
        self,
        index: str,
        documents: List[Dict[str, Any]],
        doc_ids: Optional[List[str]] = None,
        refresh: bool = False,
    ) -> Dict[str, Any]:
        """
        批量索引文档

        Args:
            index: 索引名称
            documents: 文档列表
            doc_ids: 文档ID列表
            refresh: 是否立即刷新索引

        Returns:
            Dict: 批量操作结果
        """
        try:
            # 添加时间戳
            for document in documents:
                if "indexed_at" not in document:
                    document["indexed_at"] = datetime.now().isoformat()

            result = await self.es_client.bulk_index(index, documents, doc_ids)

            if refresh and not result.get("errors"):
                await self.es_client.refresh_index(index)

            return result

        except Exception as e:
            logger.error(f"批量索引文档失败 {index}: {e}")
            return {"errors": True, "items": []}

    async def update_document(
        self, index: str, doc_id: str, document: Dict[str, Any], refresh: bool = False
    ) -> bool:
        """
        更新文档

        Args:
            index: 索引名称
            doc_id: 文档ID
            document: 更新内容
            refresh: 是否立即刷新索引

        Returns:
            bool: 更新是否成功
        """
        try:
            # 添加更新时间戳
            document["updated_at"] = datetime.now().isoformat()

            success = await self.es_client.update_document(index, doc_id, document)

            if refresh and success:
                await self.es_client.refresh_index(index)

            return success

        except Exception as e:
            logger.error(f"更新文档失败 {index}/{doc_id}: {e}")
            return False

    async def delete_document(
        self, index: str, doc_id: str, refresh: bool = False
    ) -> bool:
        """
        删除文档

        Args:
            index: 索引名称
            doc_id: 文档ID
            refresh: 是否立即刷新索引

        Returns:
            bool: 删除是否成功
        """
        try:
            success = await self.es_client.delete_document(index, doc_id)

            if refresh and success:
                await self.es_client.refresh_index(index)

            return success

        except Exception as e:
            logger.error(f"删除文档失败 {index}/{doc_id}: {e}")
            return False

    async def get_document(self, index: str, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        获取文档

        Args:
            index: 索引名称
            doc_id: 文档ID

        Returns:
            Optional[Dict]: 文档内容
        """
        return await self.es_client.get_document(index, doc_id)

    async def search(self, index: str, query: SearchQuery) -> SearchResult:
        """
        搜索文档

        Args:
            index: 索引名称
            query: 搜索查询

        Returns:
            SearchResult: 搜索结果
        """
        try:
            # 构建 Elasticsearch 查询
            es_query = query.to_elasticsearch_query()

            # 构建排序
            sort_list = None
            if query.sort:
                sort_list = [sort.to_query() for sort in query.sort]

            # 构建高亮
            highlight = None
            if query.highlight:
                highlight = query.highlight.to_query()

            # 执行搜索
            response = await self.es_client.search(
                index=index,
                query=es_query,
                size=query.size,
                from_=query.from_,
                sort=sort_list,
                highlight=highlight,
                source=query.include_source,
            )

            # 转换结果
            return SearchResult.from_elasticsearch_response(response, index)

        except Exception as e:
            logger.error(f"搜索失败 {index}: {e}")
            return SearchResult()

    async def multi_search(
        self, queries: List[tuple[str, SearchQuery]]
    ) -> List[SearchResult]:
        """
        多索引搜索

        Args:
            queries: (索引名, 搜索查询) 列表

        Returns:
            List[SearchResult]: 搜索结果列表
        """
        try:
            if not self.es_client.client:
                return [SearchResult() for _ in queries]

            # 构建多搜索请求
            body = []
            for index, query in queries:
                # 搜索头部
                header = {"index": index}
                body.append(header)

                # 搜索体
                search_body = {"query": query.to_elasticsearch_query()}

                if query.sort:
                    search_body["sort"] = [sort.to_query() for sort in query.sort]
                if query.highlight:
                    search_body["highlight"] = query.highlight.to_query()
                if query.include_source is not True:
                    search_body["_source"] = query.include_source

                search_body["size"] = query.size
                search_body["from"] = query.from_

                body.append(search_body)

            # 执行多搜索
            response = await self.es_client.client.msearch(body=body)

            # 转换结果
            results = []
            for i, resp in enumerate(response.get("responses", [])):
                if "error" in resp:
                    logger.error(f"多搜索错误 {queries[i][0]}: {resp['error']}")
                    results.append(SearchResult())
                else:
                    results.append(
                        SearchResult.from_elasticsearch_response(resp, queries[i][0])
                    )

            return results

        except Exception as e:
            logger.error(f"多搜索失败: {e}")
            return [SearchResult() for _ in queries]

    async def suggest(
        self, index: str, text: str, field: str = "title", size: int = 5
    ) -> List[str]:
        """
        搜索建议

        Args:
            index: 索引名称
            text: 搜索文本
            field: 建议字段
            size: 建议数量

        Returns:
            List[str]: 建议列表
        """
        try:
            if not self.es_client.client:
                return []

            body = {
                "suggest": {
                    "text": text,
                    "term_suggestion": {"term": {"field": field, "size": size}},
                }
            }

            response = await self.es_client.client.search(
                index=index, body=body, size=0
            )

            suggestions = []
            suggest_result = response.get("suggest", {}).get("term_suggestion", [])

            for suggestion in suggest_result:
                for option in suggestion.get("options", []):
                    suggestions.append(option.get("text", ""))

            return suggestions[:size]

        except Exception as e:
            logger.error(f"搜索建议失败 {index}: {e}")
            return []

    async def aggregate(
        self,
        index: str,
        aggregations: Dict[str, Any],
        query: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        聚合查询

        Args:
            index: 索引名称
            aggregations: 聚合配置
            query: 查询条件

        Returns:
            Dict: 聚合结果
        """
        try:
            if not self.es_client.client:
                return {}

            body = {"aggs": aggregations, "size": 0}

            if query:
                body["query"] = query
            else:
                body["query"] = {"match_all": {}}

            response = await self.es_client.client.search(index=index, body=body)

            return response.get("aggregations", {})

        except Exception as e:
            logger.error(f"聚合查询失败 {index}: {e}")
            return {}

    async def count_documents(
        self, index: str, query: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        统计文档数量

        Args:
            index: 索引名称
            query: 查询条件

        Returns:
            int: 文档数量
        """
        try:
            if not self.es_client.client:
                return 0

            body = query if query else {"match_all": {}}

            response = await self.es_client.client.count(
                index=index, body={"query": body}
            )

            return response.get("count", 0)

        except Exception as e:
            logger.error(f"统计文档数量失败 {index}: {e}")
            return 0

    async def delete_by_query(
        self, index: str, query: Dict[str, Any], refresh: bool = False
    ) -> int:
        """
        按查询删除文档

        Args:
            index: 索引名称
            query: 删除查询
            refresh: 是否立即刷新索引

        Returns:
            int: 删除的文档数量
        """
        try:
            if not self.es_client.client:
                return 0

            response = await self.es_client.client.delete_by_query(
                index=index, body={"query": query}, refresh=refresh
            )

            return response.get("deleted", 0)

        except Exception as e:
            logger.error(f"按查询删除文档失败 {index}: {e}")
            return 0

    async def scroll_search(
        self,
        index: str,
        query: Dict[str, Any],
        size: int = 1000,
        scroll_timeout: str = "5m",
    ) -> List[Dict[str, Any]]:
        """
        滚动搜索（用于大量数据）

        Args:
            index: 索引名称
            query: 搜索查询
            size: 每次返回文档数量
            scroll_timeout: 滚动超时时间

        Returns:
            List[Dict]: 所有文档
        """
        try:
            if not self.es_client.client:
                return []

            documents = []

            # 初始搜索
            response = await self.es_client.client.search(
                index=index, body={"query": query}, size=size, scroll=scroll_timeout
            )

            scroll_id = response.get("_scroll_id")
            hits = response.get("hits", {}).get("hits", [])

            # 处理第一批结果
            for hit in hits:
                documents.append(hit.get("_source", {}))

            # 继续滚动
            while hits:
                response = await self.es_client.client.scroll(
                    scroll_id=scroll_id, scroll=scroll_timeout
                )

                hits = response.get("hits", {}).get("hits", [])

                for hit in hits:
                    documents.append(hit.get("_source", {}))

            # 清理滚动
            if scroll_id:
                await self.es_client.client.clear_scroll(scroll_id=scroll_id)

            return documents

        except Exception as e:
            logger.error(f"滚动搜索失败 {index}: {e}")
            return []

    async def analyze_text(
        self, text: str, analyzer: str = "ik_max_word", index: Optional[str] = None
    ) -> List[str]:
        """
        分析文本

        Args:
            text: 待分析文本
            analyzer: 分析器
            index: 索引名称

        Returns:
            List[str]: 分词结果
        """
        try:
            if not self.es_client.client:
                return []

            body = {"analyzer": analyzer, "text": text}

            if index:
                response = await self.es_client.client.indices.analyze(
                    index=index, body=body
                )
            else:
                response = await self.es_client.client.indices.analyze(body=body)

            tokens = []
            for token in response.get("tokens", []):
                tokens.append(token.get("token", ""))

            return tokens

        except Exception as e:
            logger.error(f"文本分析失败: {e}")
            return []

    # 业务相关的搜索方法
    async def search_messages(
        self,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        size: int = 10,
        from_: int = 0,
    ) -> SearchResult:
        """
        搜索消息

        Args:
            query: 搜索关键词
            filters: 过滤条件
            size: 返回数量
            from_: 起始位置

        Returns:
            SearchResult: 搜索结果
        """
        search_query = SearchQuery(
            query=query,
            fields=["content", "title", "sender_name"],
            size=size,
            from_=from_,
            highlight=SearchHighlight(
                fields=["content", "title"], fragment_size=200, number_of_fragments=3
            ),
        )

        if filters:
            for field, values in filters.items():
                if isinstance(values, list):
                    search_query.filters.append(
                        SearchFilter(field=field, values=values)
                    )
                else:
                    search_query.filters.append(
                        SearchFilter(field=field, values=[values])
                    )

        return await self.search("messages", search_query)

    async def search_knowledge(
        self, query: str, category: Optional[str] = None, size: int = 10
    ) -> SearchResult:
        """
        搜索知识库

        Args:
            query: 搜索关键词
            category: 分类过滤
            size: 返回数量

        Returns:
            SearchResult: 搜索结果
        """
        search_query = SearchQuery(
            query=query,
            fields=["title^2", "content", "tags"],  # title 权重更高
            size=size,
            highlight=SearchHighlight(
                fields=["title", "content"], fragment_size=150, number_of_fragments=2
            ),
        )

        if category:
            search_query.filters.append(
                SearchFilter(field="category", values=[category])
            )

        # 按相关性和更新时间排序
        search_query.sort = [
            SearchSort(field="_score", order="desc"),
            SearchSort(field="updated_at", order="desc"),
        ]

        return await self.search("knowledge", search_query)


# 全局搜索服务实例
search_service = SearchService()


def get_search_service() -> SearchService:
    """
    获取搜索服务实例

    Returns:
        SearchService: 搜索服务
    """
    return search_service
