"""
索引管理模块

负责管理 Elasticsearch 索引的创建、更新、删除和配置。
"""

import logging
from typing import Any, Dict, List, Optional

from .elasticsearch_client import ElasticsearchClient, get_es_client
from .search_models import IndexMapping, IndexSettings, SearchFieldType

logger = logging.getLogger(__name__)


class IndexManager:
    """索引管理器"""

    def __init__(self, es_client: Optional[ElasticsearchClient] = None):
        """
        初始化索引管理器

        Args:
            es_client: Elasticsearch 客户端
        """
        self.es_client = es_client or get_es_client()
        self._index_mappings: Dict[str, IndexMapping] = {}
        self._index_settings: Dict[str, IndexSettings] = {}

    def register_index(
        self,
        index_name: str,
        mapping: IndexMapping,
        settings: Optional[IndexSettings] = None,
    ):
        """
        注册索引配置

        Args:
            index_name: 索引名称
            mapping: 索引映射
            settings: 索引设置
        """
        self._index_mappings[index_name] = mapping
        if settings:
            self._index_settings[index_name] = settings
        logger.info(f"注册索引配置: {index_name}")

    async def create_index(
        self,
        index_name: str,
        mapping: Optional[IndexMapping] = None,
        settings: Optional[IndexSettings] = None,
        recreate: bool = False,
    ) -> bool:
        """
        创建索引

        Args:
            index_name: 索引名称
            mapping: 索引映射
            settings: 索引设置
            recreate: 是否重新创建

        Returns:
            bool: 创建是否成功
        """
        try:
            # 检查索引是否存在
            exists = await self.es_client.index_exists(index_name)

            if exists and recreate:
                logger.info(f"删除现有索引: {index_name}")
                await self.es_client.delete_index(index_name)
                exists = False

            if exists:
                logger.info(f"索引已存在: {index_name}")
                return True

            # 使用注册的配置或传入的配置
            index_mapping = mapping or self._index_mappings.get(index_name)
            index_settings = settings or self._index_settings.get(index_name)

            # 构建索引配置
            mapping_dict = None
            if index_mapping:
                mapping_dict = {"properties": index_mapping.properties}

            settings_dict = None
            if index_settings:
                settings_dict = index_settings.to_elasticsearch_settings()

            # 创建索引
            success = await self.es_client.create_index(
                index=index_name, mapping=mapping_dict, settings=settings_dict
            )

            if success:
                logger.info(f"索引创建成功: {index_name}")
            else:
                logger.error(f"索引创建失败: {index_name}")

            return success

        except Exception as e:
            logger.error(f"创建索引异常 {index_name}: {e}")
            return False

    async def delete_index(self, index_name: str) -> bool:
        """
        删除索引

        Args:
            index_name: 索引名称

        Returns:
            bool: 删除是否成功
        """
        return await self.es_client.delete_index(index_name)

    async def update_mapping(self, index_name: str, mapping: IndexMapping) -> bool:
        """
        更新索引映射

        Args:
            index_name: 索引名称
            mapping: 新的映射配置

        Returns:
            bool: 更新是否成功
        """
        try:
            if not self.es_client.client:
                return False

            mapping_dict = {"properties": mapping.properties}

            await self.es_client.client.indices.put_mapping(
                index=index_name, body=mapping_dict
            )

            # 更新注册的映射
            self._index_mappings[index_name] = mapping

            logger.info(f"索引映射更新成功: {index_name}")
            return True

        except Exception as e:
            logger.error(f"更新索引映射失败 {index_name}: {e}")
            return False

    async def get_index_info(self, index_name: str) -> Optional[Dict[str, Any]]:
        """
        获取索引信息

        Args:
            index_name: 索引名称

        Returns:
            Optional[Dict]: 索引信息
        """
        try:
            if not self.es_client.client:
                return None

            # 获取索引设置和映射
            settings_response = await self.es_client.client.indices.get_settings(
                index=index_name
            )
            mapping_response = await self.es_client.client.indices.get_mapping(
                index=index_name
            )

            # 获取索引统计信息
            stats_response = await self.es_client.client.indices.stats(index=index_name)

            index_info = {
                "name": index_name,
                "settings": settings_response.get(index_name, {}).get("settings", {}),
                "mappings": mapping_response.get(index_name, {}).get("mappings", {}),
                "stats": stats_response.get("indices", {}).get(index_name, {}),
            }

            return index_info

        except Exception as e:
            logger.error(f"获取索引信息失败 {index_name}: {e}")
            return None

    async def list_indices(self) -> List[str]:
        """
        列出所有索引

        Returns:
            List[str]: 索引名称列表
        """
        try:
            if not self.es_client.client:
                return []

            response = await self.es_client.client.cat.indices(format="json")
            return [index["index"] for index in response]

        except Exception as e:
            logger.error(f"列出索引失败: {e}")
            return []

    async def optimize_index(self, index_name: str) -> bool:
        """
        优化索引

        Args:
            index_name: 索引名称

        Returns:
            bool: 优化是否成功
        """
        try:
            if not self.es_client.client:
                return False

            await self.es_client.client.indices.forcemerge(
                index=index_name, max_num_segments=1
            )

            logger.info(f"索引优化成功: {index_name}")
            return True

        except Exception as e:
            logger.error(f"索引优化失败 {index_name}: {e}")
            return False

    async def reindex(
        self, source_index: str, dest_index: str, query: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        重建索引

        Args:
            source_index: 源索引
            dest_index: 目标索引
            query: 查询条件

        Returns:
            bool: 重建是否成功
        """
        try:
            if not self.es_client.client:
                return False

            body = {"source": {"index": source_index}, "dest": {"index": dest_index}}

            if query:
                body["source"]["query"] = query

            response = await self.es_client.client.reindex(body=body)

            if response.get("failures"):
                logger.error(f"重建索引有失败: {response['failures']}")
                return False

            logger.info(f"重建索引成功: {source_index} -> {dest_index}")
            return True

        except Exception as e:
            logger.error(f"重建索引失败 {source_index} -> {dest_index}: {e}")
            return False

    def get_default_chinese_mapping(self) -> IndexMapping:
        """
        获取默认中文映射配置

        Returns:
            IndexMapping: 中文映射配置
        """
        mapping = IndexMapping()

        # 添加常用字段
        mapping.add_field(
            "title",
            SearchFieldType.TEXT,
            analyzer="ik_max_word",
            search_analyzer="ik_smart",
        )

        mapping.add_field(
            "content",
            SearchFieldType.TEXT,
            analyzer="ik_max_word",
            search_analyzer="ik_smart",
        )

        mapping.add_field("category", SearchFieldType.KEYWORD)

        mapping.add_field("tags", SearchFieldType.KEYWORD)

        mapping.add_field(
            "created_at",
            SearchFieldType.DATE,
            format="strict_date_optional_time||epoch_millis",
        )

        mapping.add_field(
            "updated_at",
            SearchFieldType.DATE,
            format="strict_date_optional_time||epoch_millis",
        )

        mapping.add_field("status", SearchFieldType.KEYWORD)

        mapping.add_field("priority", SearchFieldType.INTEGER)

        return mapping

    def get_default_settings(self) -> IndexSettings:
        """
        获取默认索引设置

        Returns:
            IndexSettings: 默认设置
        """
        return IndexSettings(
            number_of_shards=1,
            number_of_replicas=0,
            refresh_interval="1s",
            max_result_window=10000,
            analysis={
                "analyzer": {
                    "ik_max_word": {"type": "ik_max_word"},
                    "ik_smart": {"type": "ik_smart"},
                }
            },
        )

    async def setup_default_indices(self) -> bool:
        """
        设置默认索引

        Returns:
            bool: 设置是否成功
        """
        try:
            default_mapping = self.get_default_chinese_mapping()
            default_settings = self.get_default_settings()

            # 默认索引列表
            default_indices = [
                "messages",  # 消息索引
                "users",  # 用户索引
                "knowledge",  # 知识库索引
                "conversations",  # 对话索引
                "logs",  # 日志索引
            ]

            success_count = 0
            for index_name in default_indices:
                self.register_index(index_name, default_mapping, default_settings)
                if await self.create_index(index_name):
                    success_count += 1

            logger.info(f"默认索引设置完成: {success_count}/{len(default_indices)}")
            return success_count == len(default_indices)

        except Exception as e:
            logger.error(f"设置默认索引失败: {e}")
            return False


# 全局索引管理器实例
index_manager = IndexManager()


def get_index_manager() -> IndexManager:
    """
    获取索引管理器实例

    Returns:
        IndexManager: 索引管理器
    """
    return index_manager
