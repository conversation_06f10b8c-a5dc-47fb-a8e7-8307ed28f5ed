"""
搜索系统模块

提供基于 Elasticsearch 的全文搜索功能，包括：
- 索引管理和映射定义
- 文档索引和更新
- 多字段搜索查询
- 搜索结果排序和高亮
- 中文分词支持
- 性能监控和分析
"""

from .elasticsearch_client import (
    ElasticsearchClient,
    close_elasticsearch,
    es_client,
    get_es_client,
    init_elasticsearch,
)
from .index_manager import IndexManager, get_index_manager, index_manager
from .search_models import (
    IndexMapping,
    SearchDocument,
    SearchFilter,
    SearchHighlight,
    SearchQuery,
    SearchResult,
    SearchSort,
)
from .search_service import SearchService, get_search_service, search_service

__all__ = [
    # Elasticsearch 客户端
    "ElasticsearchClient",
    "es_client",
    "get_es_client",
    "init_elasticsearch",
    "close_elasticsearch",
    # 索引管理
    "IndexManager",
    "index_manager",
    "get_index_manager",
    # 搜索服务
    "SearchService",
    "search_service",
    "get_search_service",
    # 数据模型
    "SearchDocument",
    "SearchQuery",
    "SearchResult",
    "SearchFilter",
    "SearchHighlight",
    "SearchSort",
    "IndexMapping",
]
