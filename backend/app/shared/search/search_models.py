"""
搜索系统数据模型

定义搜索相关的数据结构和模型。
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class SearchFieldType(str, Enum):
    """搜索字段类型"""

    TEXT = "text"
    KEYWORD = "keyword"
    INTEGER = "integer"
    LONG = "long"
    FLOAT = "float"
    DOUBLE = "double"
    BOOLEAN = "boolean"
    DATE = "date"
    OBJECT = "object"
    NESTED = "nested"


class SearchSortOrder(str, Enum):
    """搜索排序方向"""

    ASC = "asc"
    DESC = "desc"


class IndexMapping(BaseModel):
    """索引映射定义"""

    properties: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict, description="字段属性定义"
    )

    def add_field(
        self,
        name: str,
        field_type: SearchFieldType,
        analyzer: Optional[str] = None,
        index: bool = True,
        store: bool = False,
        **kwargs,
    ):
        """
        添加字段映射

        Args:
            name: 字段名称
            field_type: 字段类型
            analyzer: 分析器
            index: 是否索引
            store: 是否存储
            **kwargs: 其他属性
        """
        field_config = {
            "type": field_type.value,
            "index": index,
            "store": store,
        }

        if analyzer:
            field_config["analyzer"] = analyzer

        field_config.update(kwargs)
        self.properties = {**self.properties, name: field_config}


class SearchDocument(BaseModel):
    """搜索文档模型"""

    id: Optional[str] = Field(None, description="文档ID")
    index: str = Field(..., description="索引名称")
    source: Dict[str, Any] = Field(..., description="文档内容")
    score: Optional[float] = Field(None, description="相关性得分")
    highlight: Optional[Dict[str, List[str]]] = Field(None, description="高亮内容")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class SearchFilter(BaseModel):
    """搜索过滤条件"""

    field: str = Field(..., description="过滤字段")
    values: List[Any] = Field(..., description="过滤值列表")
    operator: str = Field("terms", description="过滤操作符")

    def to_query(self) -> Dict[str, Any]:
        """转换为 Elasticsearch 查询"""
        if self.operator == "terms":
            return {"terms": {self.field: self.values}}
        elif self.operator == "range":
            range_query = {}
            values_list = list(self.values)
            if len(values_list) >= 1 and values_list[0] is not None:
                range_query["gte"] = values_list[0]
            if len(values_list) >= 2 and values_list[1] is not None:
                range_query["lte"] = values_list[1]
            return {"range": {self.field: range_query}}
        elif self.operator == "exists":
            return {"exists": {"field": self.field}}
        elif self.operator == "missing":
            return {"bool": {"must_not": {"exists": {"field": self.field}}}}
        else:
            values_list = list(self.values)
            return {"term": {self.field: values_list[0] if values_list else ""}}


class SearchSort(BaseModel):
    """搜索排序"""

    field: str = Field(..., description="排序字段")
    order: SearchSortOrder = Field(SearchSortOrder.DESC, description="排序方向")
    missing: Optional[str] = Field(None, description="缺失值处理")

    def to_query(self) -> Dict[str, Any]:
        """转换为 Elasticsearch 排序"""
        sort_config = {"order": self.order.value}
        if self.missing:
            sort_config["missing"] = self.missing
        return {self.field: sort_config}


class SearchHighlight(BaseModel):
    """搜索高亮配置"""

    fields: List[str] = Field(..., description="高亮字段")
    pre_tags: List[str] = Field(["<mark>"], description="高亮前缀标签")
    post_tags: List[str] = Field(["</mark>"], description="高亮后缀标签")
    fragment_size: int = Field(150, description="片段大小")
    number_of_fragments: int = Field(3, description="片段数量")

    def to_query(self) -> Dict[str, Any]:
        """转换为 Elasticsearch 高亮配置"""
        return {
            "pre_tags": self.pre_tags,
            "post_tags": self.post_tags,
            "fields": {
                field: {
                    "fragment_size": self.fragment_size,
                    "number_of_fragments": self.number_of_fragments,
                }
                for field in self.fields
            },
        }


class SearchQuery(BaseModel):
    """搜索查询模型"""

    query: str = Field("", description="搜索关键词")
    fields: List[str] = Field(default_factory=list, description="搜索字段")
    filters: List[SearchFilter] = Field(default_factory=list, description="过滤条件")
    sort: List[SearchSort] = Field(default_factory=list, description="排序规则")
    highlight: Optional[SearchHighlight] = Field(None, description="高亮配置")
    size: int = Field(10, ge=1, le=100, description="返回结果数量")
    from_: int = Field(0, ge=0, alias="from", description="起始位置")
    include_source: Union[bool, List[str]] = Field(True, description="返回字段")
    min_score: Optional[float] = Field(None, description="最小相关性得分")

    def to_elasticsearch_query(self) -> Dict[str, Any]:
        """转换为 Elasticsearch 查询"""
        # 构建主查询
        if self.query.strip():
            if self.fields:
                # 多字段搜索
                main_query = {
                    "multi_match": {
                        "query": self.query,
                        "fields": self.fields,
                        "type": "best_fields",
                        "fuzziness": "AUTO",
                        "operator": "and",
                    }
                }
            else:
                # 简单查询
                main_query = {
                    "query_string": {
                        "query": self.query,
                        "default_operator": "AND",
                        "fuzziness": "AUTO",
                    }
                }
        else:
            # 匹配所有文档
            main_query = {"match_all": {}}

        # 构建过滤条件
        filter_queries = [f.to_query() for f in self.filters]

        # 组合查询
        if filter_queries:
            query = {"bool": {"must": [main_query], "filter": filter_queries}}
        else:
            query = main_query

        # 添加最小得分
        if self.min_score:
            if "bool" not in query:
                query = {"bool": {"must": [query]}}
            if "bool" not in query:
                query = {"bool": {"must": [query]}}
            query["bool"]["min_score"] = self.min_score

        return query


class SearchResult(BaseModel):
    """搜索结果模型"""

    total: int = Field(0, description="总结果数量")
    max_score: Optional[float] = Field(None, description="最高得分")
    documents: List[SearchDocument] = Field(
        default_factory=list, description="文档列表"
    )
    took: int = Field(0, description="搜索耗时（毫秒）")
    timed_out: bool = Field(False, description="是否超时")
    aggregations: Optional[Dict[str, Any]] = Field(None, description="聚合结果")

    @classmethod
    def from_elasticsearch_response(
        cls, response: Dict[str, Any], index: str
    ) -> "SearchResult":
        """
        从 Elasticsearch 响应创建搜索结果

        Args:
            response: Elasticsearch 响应
            index: 索引名称

        Returns:
            SearchResult: 搜索结果
        """
        hits = response.get("hits", {})
        total_hits = hits.get("total", {})

        # 处理不同版本的 total 格式
        if isinstance(total_hits, dict):
            total = total_hits.get("value", 0)
        else:
            total = total_hits

        documents = []
        for hit in hits.get("hits", []):
            doc = SearchDocument(
                id=hit.get("_id"),
                index=hit.get("_index", index),
                source=hit.get("_source", {}),
                score=hit.get("_score"),
                highlight=hit.get("highlight"),
            )
            documents.append(doc)

        return cls(
            total=total,
            max_score=hits.get("max_score"),
            documents=documents,
            took=response.get("took", 0),
            timed_out=response.get("timed_out", False),
            aggregations=response.get("aggregations"),
        )


class BulkOperation(BaseModel):
    """批量操作模型"""

    operation: str = Field(..., description="操作类型: index, update, delete")
    document: SearchDocument = Field(..., description="操作文档")

    def to_elasticsearch_action(self) -> Dict[str, Any]:
        """转换为 Elasticsearch 批量操作格式"""
        action = {
            self.operation: {
                "_index": self.document.index,
            }
        }

        if self.document.id:
            action[self.operation]["_id"] = self.document.id

        if self.operation in ["index", "update"]:
            return action, self.document.source
        else:
            return action


class IndexSettings(BaseModel):
    """索引设置"""

    number_of_shards: int = Field(1, description="分片数量")
    number_of_replicas: int = Field(0, description="副本数量")
    refresh_interval: str = Field("1s", description="刷新间隔")
    max_result_window: int = Field(10000, description="最大结果窗口")
    analysis: Optional[Dict[str, Any]] = Field(None, description="分析器配置")

    def to_elasticsearch_settings(self) -> Dict[str, Any]:
        """转换为 Elasticsearch 设置格式"""
        settings = {
            "number_of_shards": self.number_of_shards,
            "number_of_replicas": self.number_of_replicas,
            "refresh_interval": self.refresh_interval,
            "max_result_window": self.max_result_window,
        }

        if self.analysis:
            settings["analysis"] = self.analysis

        return settings
