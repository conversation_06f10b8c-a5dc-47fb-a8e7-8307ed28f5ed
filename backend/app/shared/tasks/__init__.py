"""
柴管家任务处理系统

基于Celery实现的异步任务处理和定时任务调度系统，支持：
- 异步任务执行
- 定时任务调度
- 任务重试机制
- 任务监控和管理
- 任务结果存储
"""

from .app import create_celery_app
from .config import CeleryConfig
from .decorators import task
from .manager import TaskManager
from .models import Task, TaskResult, TaskStatus
from .monitor import TaskMonitor
from .registry import TaskRegistry
from .scheduler import TaskScheduler

__all__ = [
    "create_celery_app",
    "CeleryConfig",
    "task",
    "TaskManager",
    "Task",
    "TaskResult",
    "TaskStatus",
    "TaskMonitor",
    "TaskRegistry",
    "TaskScheduler",
]
