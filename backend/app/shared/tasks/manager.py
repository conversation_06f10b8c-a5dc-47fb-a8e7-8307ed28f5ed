"""
任务管理器模块

提供高级任务管理接口，包括：
- 任务的创建和执行
- 任务状态查询和管理
- 批量任务操作
- 任务调度管理
"""

import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

from celery import Celery
from celery.result import AsyncResult

from .app import get_celery_app
from .models import Task, TaskPriority, TaskResult, TaskStatus, TaskType
from .monitor import get_task_monitor
from .registry import TaskRegistry
from .scheduler import TaskScheduler

logger = logging.getLogger(__name__)


class TaskManager:
    """任务管理器"""

    def __init__(self, celery_app: Optional[Celery] = None):
        self.celery_app = celery_app or get_celery_app()
        self.registry = TaskRegistry.get_instance()
        self.scheduler = TaskScheduler.get_instance()
        self.monitor = get_task_monitor()

    def submit_task(
        self,
        task_name: str,
        args: tuple = (),
        kwargs: Dict[str, Any] = None,
        queue: str = None,
        priority: TaskPriority = None,
        countdown: int = None,
        eta: datetime = None,
        expires: datetime = None,
        retry: bool = True,
        **options,
    ) -> Optional[str]:
        """
        提交任务

        Args:
            task_name: 任务名称
            args: 位置参数
            kwargs: 关键字参数
            queue: 队列名称
            priority: 任务优先级
            countdown: 延迟执行秒数
            eta: 指定执行时间
            expires: 过期时间
            retry: 是否重试
            **options: 其他选项

        Returns:
            任务ID或None
        """
        try:
            # 获取任务对象
            task_obj = self.registry.get_task(task_name)
            if not task_obj:
                logger.error(f"Task {task_name} not found in registry")
                return None

            # 准备参数
            kwargs = kwargs or {}

            # 准备选项
            apply_options = {}
            if queue:
                apply_options["queue"] = queue
            if priority:
                apply_options["priority"] = priority.value
            if countdown:
                apply_options["countdown"] = countdown
            if eta:
                apply_options["eta"] = eta
            if expires:
                apply_options["expires"] = expires
            if not retry:
                apply_options["retry"] = False

            apply_options.update(options)

            # 提交任务
            result = task_obj.apply_async(args=args, kwargs=kwargs, **apply_options)

            logger.info(f"Submitted task {task_name}[{result.id}]")
            return result.id

        except Exception as e:
            logger.error(f"Failed to submit task {task_name}: {e}")
            return None

    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """
        获取任务结果

        Args:
            task_id: 任务ID

        Returns:
            任务结果或None
        """
        try:
            async_result = AsyncResult(task_id, app=self.celery_app)

            # 构建任务结果
            result = TaskResult(
                task_id=task_id,
                status=TaskStatus(async_result.status),
                result=async_result.result if async_result.successful() else None,
                error=str(async_result.result) if async_result.failed() else None,
                traceback=async_result.traceback,
                created_at=datetime.now(timezone.utc),  # 近似值
                completed_at=(
                    datetime.now(timezone.utc) if async_result.ready() else None
                ),
                retry_count=0,  # Celery不直接提供重试次数
            )

            return result

        except Exception as e:
            logger.error(f"Failed to get task result for {task_id}: {e}")
            return None

    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态或None
        """
        try:
            async_result = AsyncResult(task_id, app=self.celery_app)
            return TaskStatus(async_result.status)
        except Exception as e:
            logger.error(f"Failed to get task status for {task_id}: {e}")
            return None

    def cancel_task(self, task_id: str, terminate: bool = False) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID
            terminate: 是否强制终止

        Returns:
            是否成功取消
        """
        try:
            if terminate:
                self.celery_app.control.terminate(task_id)
            else:
                self.celery_app.control.revoke(task_id, terminate=False)

            logger.info(f"Cancelled task {task_id} (terminate={terminate})")
            return True

        except Exception as e:
            logger.error(f"Failed to cancel task {task_id}: {e}")
            return False

    def retry_task(self, task_id: str, countdown: int = 60) -> Optional[str]:
        """
        重试任务

        Args:
            task_id: 原任务ID
            countdown: 延迟秒数

        Returns:
            新任务ID或None
        """
        try:
            # 获取原任务信息
            async_result = AsyncResult(task_id, app=self.celery_app)

            if not async_result.failed():
                logger.warning(f"Task {task_id} is not in failed state")
                return None

            # 获取原任务参数（这里简化处理）
            # 实际实现中可能需要从任务元数据中获取参数
            task_name = async_result.name

            # 重新提交任务
            new_task_id = self.submit_task(task_name=task_name, countdown=countdown)

            if new_task_id:
                logger.info(f"Retried task {task_id} as {new_task_id}")

            return new_task_id

        except Exception as e:
            logger.error(f"Failed to retry task {task_id}: {e}")
            return None

    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """获取活跃任务列表"""
        try:
            inspect = self.celery_app.control.inspect()
            active_tasks = inspect.active()

            if not active_tasks:
                return []

            # 合并所有worker的活跃任务
            all_tasks = []
            for worker, tasks in active_tasks.items():
                for task in tasks:
                    task["worker"] = worker
                    all_tasks.append(task)

            return all_tasks

        except Exception as e:
            logger.error(f"Failed to get active tasks: {e}")
            return []

    def get_scheduled_tasks(self) -> List[Dict[str, Any]]:
        """获取已调度的定时任务"""
        try:
            inspect = self.celery_app.control.inspect()
            scheduled_tasks = inspect.scheduled()

            if not scheduled_tasks:
                return []

            # 合并所有worker的调度任务
            all_tasks = []
            for worker, tasks in scheduled_tasks.items():
                for task in tasks:
                    task["worker"] = worker
                    all_tasks.append(task)

            return all_tasks

        except Exception as e:
            logger.error(f"Failed to get scheduled tasks: {e}")
            return []

    def get_reserved_tasks(self) -> List[Dict[str, Any]]:
        """获取预留任务列表"""
        try:
            inspect = self.celery_app.control.inspect()
            reserved_tasks = inspect.reserved()

            if not reserved_tasks:
                return []

            # 合并所有worker的预留任务
            all_tasks = []
            for worker, tasks in reserved_tasks.items():
                for task in tasks:
                    task["worker"] = worker
                    all_tasks.append(task)

            return all_tasks

        except Exception as e:
            logger.error(f"Failed to get reserved tasks: {e}")
            return []

    def purge_queue(self, queue_name: str) -> int:
        """
        清空队列

        Args:
            queue_name: 队列名称

        Returns:
            清除的任务数量
        """
        try:
            with self.celery_app.connection() as conn:
                queue = conn.SimpleQueue(queue_name)
                count = queue.qsize()
                queue.clear()

            logger.info(f"Purged {count} tasks from queue {queue_name}")
            return count

        except Exception as e:
            logger.error(f"Failed to purge queue {queue_name}: {e}")
            return 0

    def get_queue_length(self, queue_name: str) -> int:
        """
        获取队列长度

        Args:
            queue_name: 队列名称

        Returns:
            队列长度
        """
        try:
            with self.celery_app.connection() as conn:
                queue = conn.SimpleQueue(queue_name)
                return queue.qsize()

        except Exception as e:
            logger.debug(f"Failed to get queue length for {queue_name}: {e}")
            return 0

    def get_worker_stats(self) -> Dict[str, Any]:
        """获取worker统计信息"""
        try:
            inspect = self.celery_app.control.inspect()
            stats = inspect.stats()

            if not stats:
                return {}

            return stats

        except Exception as e:
            logger.error(f"Failed to get worker stats: {e}")
            return {}

    def broadcast_command(self, command: str, **kwargs) -> Dict[str, Any]:
        """
        广播命令到所有worker

        Args:
            command: 命令名称
            **kwargs: 命令参数

        Returns:
            执行结果
        """
        try:
            control = self.celery_app.control

            if hasattr(control, command):
                method = getattr(control, command)
                result = method(**kwargs)
                logger.info(f"Broadcasted command {command}")
                return result or {}
            else:
                logger.error(f"Unknown command: {command}")
                return {}

        except Exception as e:
            logger.error(f"Failed to broadcast command {command}: {e}")
            return {}

    def submit_batch_tasks(
        self, tasks: List[Dict[str, Any]], batch_size: int = 100
    ) -> List[str]:
        """
        批量提交任务

        Args:
            tasks: 任务列表，每个任务包含name, args, kwargs等
            batch_size: 批次大小

        Returns:
            任务ID列表
        """
        task_ids = []

        for i in range(0, len(tasks), batch_size):
            batch = tasks[i : i + batch_size]

            for task_config in batch:
                task_id = self.submit_task(
                    task_name=task_config.get("name"),
                    args=task_config.get("args", ()),
                    kwargs=task_config.get("kwargs", {}),
                    queue=task_config.get("queue"),
                    priority=task_config.get("priority"),
                    countdown=task_config.get("countdown"),
                    eta=task_config.get("eta"),
                    expires=task_config.get("expires"),
                )

                if task_id:
                    task_ids.append(task_id)

        logger.info(f"Submitted {len(task_ids)} tasks in batches")
        return task_ids

    def wait_for_tasks(
        self, task_ids: List[str], timeout: int = None, interval: float = 0.5
    ) -> Dict[str, TaskResult]:
        """
        等待任务完成

        Args:
            task_ids: 任务ID列表
            timeout: 超时秒数
            interval: 检查间隔

        Returns:
            任务结果字典
        """
        import time

        results = {}
        start_time = time.time()

        while task_ids:
            completed_ids = []

            for task_id in task_ids:
                result = self.get_task_result(task_id)
                if result and result.is_finished():
                    results[task_id] = result
                    completed_ids.append(task_id)

            # 移除已完成的任务
            for task_id in completed_ids:
                task_ids.remove(task_id)

            # 检查超时
            if timeout and (time.time() - start_time) > timeout:
                logger.warning(f"Timeout waiting for {len(task_ids)} tasks")
                break

            # 等待间隔
            if task_ids:
                time.sleep(interval)

        return results

    def get_task_summary(self) -> Dict[str, Any]:
        """获取任务总览信息"""
        try:
            # 获取注册任务信息
            registry_summary = self.registry.get_registry_summary()

            # 获取调度任务信息
            scheduler_status = self.scheduler.get_scheduler_status()

            # 获取监控统计信息
            monitor_stats = self.monitor.get_statistics()

            # 获取队列信息
            queue_info = {}
            for queue_name in self.registry.get_queue_names():
                queue_info[queue_name] = self.get_queue_length(queue_name)

            # 获取worker信息
            worker_stats = self.get_worker_stats()

            return {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "registry": registry_summary,
                "scheduler": scheduler_status,
                "monitor": (
                    monitor_stats.dict()
                    if hasattr(monitor_stats, "dict")
                    else str(monitor_stats)
                ),
                "queues": queue_info,
                "workers": {
                    "count": len(worker_stats),
                    "stats": worker_stats,
                },
                "active_tasks": len(self.get_active_tasks()),
                "scheduled_tasks": len(self.get_scheduled_tasks()),
                "reserved_tasks": len(self.get_reserved_tasks()),
            }

        except Exception as e:
            logger.error(f"Failed to get task summary: {e}")
            return {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": str(e),
            }


# 全局任务管理器实例
_task_manager: Optional[TaskManager] = None


def get_task_manager() -> TaskManager:
    """获取全局任务管理器实例"""
    global _task_manager

    if _task_manager is None:
        _task_manager = TaskManager()

    return _task_manager


def submit_task(task_name: str, *args, **kwargs) -> Optional[str]:
    """便捷函数：提交任务"""
    manager = get_task_manager()
    return manager.submit_task(task_name, args=args, **kwargs)


def get_task_result(task_id: str) -> Optional[TaskResult]:
    """便捷函数：获取任务结果"""
    manager = get_task_manager()
    return manager.get_task_result(task_id)


def cancel_task(task_id: str, terminate: bool = False) -> bool:
    """便捷函数：取消任务"""
    manager = get_task_manager()
    return manager.cancel_task(task_id, terminate)
