"""
任务注册表模块

管理所有注册的任务，包括：
- 任务注册和取消注册
- 任务查找和获取
- 任务元数据管理
- 任务统计信息
"""

import logging
from threading import RLock
from typing import Any, Dict, List, Optional, Set

from .models import TaskMetadata, TaskPriority, TaskStatistics, TaskType

logger = logging.getLogger(__name__)


class TaskRegistry:
    """任务注册表"""

    _instance: Optional["TaskRegistry"] = None
    _lock = RLock()

    def __init__(self):
        self._tasks: Dict[str, Any] = {}  # 任务名称 -> Celery任务对象
        self._metadata: Dict[str, TaskMetadata] = {}  # 任务名称 -> 元数据
        self._task_types: Dict[TaskType, Set[str]] = {}  # 任务类型 -> 任务名称集合
        self._task_queues: Dict[str, Set[str]] = {}  # 队列名称 -> 任务名称集合
        self._task_priorities: Dict[TaskPriority, Set[str]] = (
            {}
        )  # 优先级 -> 任务名称集合

        # 初始化分类字典
        for task_type in TaskType:
            self._task_types[task_type] = set()
        for priority in TaskPriority:
            self._task_priorities[priority] = set()

    @classmethod
    def get_instance(cls) -> "TaskRegistry":
        """获取单例实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    @classmethod
    def register_task(cls, name: str, task_obj: Any, metadata: TaskMetadata) -> None:
        """
        注册任务

        Args:
            name: 任务名称
            task_obj: Celery任务对象
            metadata: 任务元数据
        """
        registry = cls.get_instance()

        with cls._lock:
            # 注册任务
            registry._tasks[name] = task_obj
            registry._metadata[name] = metadata

            # 按类型分类
            registry._task_types[metadata.task_type].add(name)

            # 按队列分类
            if metadata.queue_name not in registry._task_queues:
                registry._task_queues[metadata.queue_name] = set()
            registry._task_queues[metadata.queue_name].add(name)

            # 按优先级分类
            registry._task_priorities[metadata.priority].add(name)

            logger.info(
                f"Registered task: {name} (type={metadata.task_type.value}, queue={metadata.queue_name})"
            )

    @classmethod
    def unregister_task(cls, name: str) -> bool:
        """
        取消注册任务

        Args:
            name: 任务名称

        Returns:
            是否成功取消注册
        """
        registry = cls.get_instance()

        with cls._lock:
            if name not in registry._tasks:
                return False

            metadata = registry._metadata[name]

            # 从各个分类中移除
            registry._task_types[metadata.task_type].discard(name)
            registry._task_queues[metadata.queue_name].discard(name)
            registry._task_priorities[metadata.priority].discard(name)

            # 移除主记录
            del registry._tasks[name]
            del registry._metadata[name]

            logger.info(f"Unregistered task: {name}")
            return True

    @classmethod
    def get_task(cls, name: str) -> Optional[Any]:
        """
        获取任务对象

        Args:
            name: 任务名称

        Returns:
            任务对象或None
        """
        registry = cls.get_instance()
        return registry._tasks.get(name)

    @classmethod
    def get_metadata(cls, name: str) -> Optional[TaskMetadata]:
        """
        获取任务元数据

        Args:
            name: 任务名称

        Returns:
            任务元数据或None
        """
        registry = cls.get_instance()
        return registry._metadata.get(name)

    @classmethod
    def get_all_tasks(cls) -> Dict[str, Any]:
        """获取所有注册的任务"""
        registry = cls.get_instance()
        return registry._tasks.copy()

    @classmethod
    def get_all_metadata(cls) -> Dict[str, TaskMetadata]:
        """获取所有任务元数据"""
        registry = cls.get_instance()
        return registry._metadata.copy()

    @classmethod
    def get_task_names(cls) -> List[str]:
        """获取所有任务名称"""
        registry = cls.get_instance()
        return list(registry._tasks.keys())

    @classmethod
    def get_tasks_by_type(cls, task_type: TaskType) -> List[str]:
        """
        根据任务类型获取任务列表

        Args:
            task_type: 任务类型

        Returns:
            任务名称列表
        """
        registry = cls.get_instance()
        return list(registry._task_types.get(task_type, set()))

    @classmethod
    def get_tasks_by_queue(cls, queue_name: str) -> List[str]:
        """
        根据队列名称获取任务列表

        Args:
            queue_name: 队列名称

        Returns:
            任务名称列表
        """
        registry = cls.get_instance()
        return list(registry._task_queues.get(queue_name, set()))

    @classmethod
    def get_tasks_by_priority(cls, priority: TaskPriority) -> List[str]:
        """
        根据优先级获取任务列表

        Args:
            priority: 任务优先级

        Returns:
            任务名称列表
        """
        registry = cls.get_instance()
        return list(registry._task_priorities.get(priority, set()))

    @classmethod
    def get_queue_names(cls) -> List[str]:
        """获取所有队列名称"""
        registry = cls.get_instance()
        return list(registry._task_queues.keys())

    @classmethod
    def get_task_count(cls) -> int:
        """获取注册任务总数"""
        registry = cls.get_instance()
        return len(registry._tasks)

    @classmethod
    def get_task_count_by_type(cls) -> Dict[str, int]:
        """获取按类型分组的任务数量"""
        registry = cls.get_instance()
        return {
            task_type.value: len(task_names)
            for task_type, task_names in registry._task_types.items()
        }

    @classmethod
    def get_task_count_by_queue(cls) -> Dict[str, int]:
        """获取按队列分组的任务数量"""
        registry = cls.get_instance()
        return {
            queue_name: len(task_names)
            for queue_name, task_names in registry._task_queues.items()
        }

    @classmethod
    def get_task_count_by_priority(cls) -> Dict[str, int]:
        """获取按优先级分组的任务数量"""
        registry = cls.get_instance()
        return {
            priority.name: len(task_names)
            for priority, task_names in registry._task_priorities.items()
        }

    @classmethod
    def is_task_registered(cls, name: str) -> bool:
        """
        检查任务是否已注册

        Args:
            name: 任务名称

        Returns:
            是否已注册
        """
        registry = cls.get_instance()
        return name in registry._tasks

    @classmethod
    def find_tasks(cls, pattern: str) -> List[str]:
        """
        根据模式查找任务

        Args:
            pattern: 搜索模式（支持通配符）

        Returns:
            匹配的任务名称列表
        """
        import fnmatch

        registry = cls.get_instance()

        matching_tasks = []
        for task_name in registry._tasks.keys():
            if fnmatch.fnmatch(task_name, pattern):
                matching_tasks.append(task_name)

        return matching_tasks

    @classmethod
    def get_task_info(cls, name: str) -> Optional[Dict[str, Any]]:
        """
        获取任务详细信息

        Args:
            name: 任务名称

        Returns:
            任务信息字典或None
        """
        registry = cls.get_instance()

        if name not in registry._tasks:
            return None

        task_obj = registry._tasks[name]
        metadata = registry._metadata[name]

        return {
            "name": name,
            "function": task_obj.name,
            "metadata": metadata.to_dict(),
            "rate_limit": getattr(task_obj, "rate_limit", None),
            "max_retries": task_obj.max_retries,
            "default_retry_delay": task_obj.default_retry_delay,
            "time_limit": getattr(task_obj, "time_limit", None),
            "soft_time_limit": getattr(task_obj, "soft_time_limit", None),
        }

    @classmethod
    def get_registry_summary(cls) -> Dict[str, Any]:
        """获取注册表摘要信息"""
        registry = cls.get_instance()

        return {
            "total_tasks": len(registry._tasks),
            "tasks_by_type": cls.get_task_count_by_type(),
            "tasks_by_queue": cls.get_task_count_by_queue(),
            "tasks_by_priority": cls.get_task_count_by_priority(),
            "queue_names": cls.get_queue_names(),
        }

    @classmethod
    def clear_registry(cls) -> None:
        """清空注册表（主要用于测试）"""
        registry = cls.get_instance()

        with cls._lock:
            registry._tasks.clear()
            registry._metadata.clear()

            for task_type in TaskType:
                registry._task_types[task_type].clear()
            for priority in TaskPriority:
                registry._task_priorities[priority].clear()
            registry._task_queues.clear()

            logger.info("Task registry cleared")

    @classmethod
    def validate_registry(cls) -> List[str]:
        """
        验证注册表的一致性

        Returns:
            发现的问题列表
        """
        registry = cls.get_instance()
        issues = []

        # 检查元数据和任务对象的一致性
        for name in registry._tasks.keys():
            if name not in registry._metadata:
                issues.append(f"Task {name} has no metadata")

        for name in registry._metadata.keys():
            if name not in registry._tasks:
                issues.append(f"Metadata for {name} has no corresponding task")

        # 检查分类索引的一致性
        all_categorized_tasks = set()

        for task_names in registry._task_types.values():
            all_categorized_tasks.update(task_names)

        for task_names in registry._task_queues.values():
            all_categorized_tasks.update(task_names)

        for task_names in registry._task_priorities.values():
            all_categorized_tasks.update(task_names)

        registered_tasks = set(registry._tasks.keys())

        # 检查是否有任务没有被正确分类
        uncategorized = registered_tasks - all_categorized_tasks
        if uncategorized:
            issues.append(f"Uncategorized tasks: {uncategorized}")

        # 检查是否有分类中的任务没有注册
        unregistered = all_categorized_tasks - registered_tasks
        if unregistered:
            issues.append(f"Categorized but unregistered tasks: {unregistered}")

        return issues
