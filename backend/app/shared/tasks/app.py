"""
Celery应用程序创建和配置模块

创建和配置Celery应用实例，包括：
- 应用实例化
- 配置加载
- 任务自动发现
- 信号处理
"""

import logging
import os
from typing import Optional

from celery import Celery
from celery.signals import (
    task_failure,
    task_postrun,
    task_prerun,
    task_success,
    worker_ready,
    worker_shutdown,
)

from .config import get_celery_config

# 全局Celery实例
celery_app: Optional[Celery] = None


def create_celery_app(app_name: str = "chaiguanjia-tasks") -> Celery:
    """
    创建Celery应用实例

    Args:
        app_name: 应用名称

    Returns:
        Celery应用实例
    """
    global celery_app

    if celery_app is not None:
        return celery_app

    # 创建Celery实例
    celery_app = Celery(app_name)

    # 加载配置
    config = get_celery_config()
    celery_app.config_from_object(config.get_celery_config())

    # 自动发现任务
    celery_app.autodiscover_tasks(
        [
            "app.shared.tasks.handlers",
            "app.modules.ai_services.tasks",
            "app.modules.message_processing.tasks",
            "app.modules.platform_adapters.tasks",
            "app.modules.knowledge_management.tasks",
            "app.modules.workflow_engine.tasks",
            "app.modules.human_collaboration.tasks",
            "app.modules.user_management.tasks",
        ],
        force=True,
    )

    # 注册信号处理器
    _register_signal_handlers(celery_app)

    # 配置日志
    _configure_logging()

    return celery_app


def get_celery_app() -> Celery:
    """获取Celery应用实例"""
    if celery_app is None:
        return create_celery_app()
    return celery_app


def _register_signal_handlers(app: Celery) -> None:
    """注册Celery信号处理器"""

    @task_prerun.connect
    def task_prerun_handler(
        sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds
    ):
        """任务开始前处理"""
        logger = logging.getLogger("celery.task")
        logger.info(
            f"Task {task.name}[{task_id}] started",
            extra={
                "task_id": task_id,
                "task_name": task.name,
                "args": args,
                "kwargs": kwargs,
            },
        )

    @task_postrun.connect
    def task_postrun_handler(
        sender=None,
        task_id=None,
        task=None,
        args=None,
        kwargs=None,
        retval=None,
        state=None,
        **kwds,
    ):
        """任务完成后处理"""
        logger = logging.getLogger("celery.task")
        logger.info(
            f"Task {task.name}[{task_id}] finished with state: {state}",
            extra={
                "task_id": task_id,
                "task_name": task.name,
                "state": state,
                "retval": str(retval)[:200] if retval else None,
            },
        )

    @task_success.connect
    def task_success_handler(sender=None, result=None, **kwds):
        """任务成功处理"""
        logger = logging.getLogger("celery.task")
        logger.info(
            f"Task {sender.name} succeeded",
            extra={
                "task_name": sender.name,
                "result": str(result)[:200] if result else None,
            },
        )

    @task_failure.connect
    def task_failure_handler(
        sender=None, task_id=None, exception=None, einfo=None, **kwds
    ):
        """任务失败处理"""
        logger = logging.getLogger("celery.task")
        logger.error(
            f"Task {sender.name}[{task_id}] failed: {exception}",
            extra={
                "task_id": task_id,
                "task_name": sender.name,
                "exception": str(exception),
                "traceback": str(einfo),
            },
        )

    @worker_ready.connect
    def worker_ready_handler(sender=None, **kwds):
        """Worker就绪处理"""
        logger = logging.getLogger("celery.worker")
        logger.info("Celery worker is ready", extra={"hostname": sender.hostname})

    @worker_shutdown.connect
    def worker_shutdown_handler(sender=None, **kwds):
        """Worker关闭处理"""
        logger = logging.getLogger("celery.worker")
        logger.info(
            "Celery worker is shutting down", extra={"hostname": sender.hostname}
        )


def _configure_logging() -> None:
    """配置Celery日志"""
    # 设置日志级别
    log_level = os.getenv("CELERY_LOG_LEVEL", "INFO")

    # 配置根日志器
    logging.basicConfig(
        level=getattr(logging, log_level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # 配置Celery相关日志器
    for logger_name in ["celery", "celery.task", "celery.worker"]:
        logger = logging.getLogger(logger_name)
        logger.setLevel(getattr(logging, log_level))


# 创建默认实例
app = create_celery_app()


# 提供给外部使用的快捷方式
def task(*args, **kwargs):
    """任务装饰器的快捷方式"""
    return app.task(*args, **kwargs)
