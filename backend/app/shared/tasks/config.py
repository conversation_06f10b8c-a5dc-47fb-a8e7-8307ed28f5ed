"""
Celery配置模块

配置Celery任务处理器的各项参数，包括：
- 消息代理配置
- 结果后端配置
- 任务路由规则
- 序列化设置
- 重试策略
"""

import os
from typing import Any, Dict, Optional

from celery import Celery
from kombu import Exchange, Queue


class CeleryConfig:
    """Celery配置类"""

    def __init__(self):
        # Redis连接配置
        self.redis_host = os.getenv("REDIS_HOST", "localhost")
        self.redis_port = int(os.getenv("REDIS_PORT", "6379"))
        self.redis_db = int(os.getenv("REDIS_DB", "0"))
        self.redis_password = os.getenv("REDIS_PASSWORD")

        # RabbitMQ连接配置（消息代理）
        self.rabbitmq_host = os.getenv("RABBITMQ_HOST", "localhost")
        self.rabbitmq_port = int(os.getenv("RABBITMQ_PORT", "5672"))
        self.rabbitmq_user = os.getenv("RABBITMQ_DEFAULT_USER", "admin")
        self.rabbitmq_password = os.getenv("RABBITMQ_DEFAULT_PASS", "admin123")
        self.rabbitmq_vhost = os.getenv("RABBITMQ_DEFAULT_VHOST", "/")

    @property
    def broker_url(self) -> str:
        """消息代理URL - 使用RabbitMQ"""
        return (
            f"pyamqp://{self.rabbitmq_user}:{self.rabbitmq_password}@"
            f"{self.rabbitmq_host}:{self.rabbitmq_port}/{self.rabbitmq_vhost}"
        )

    @property
    def result_backend(self) -> str:
        """结果后端URL - 使用Redis"""
        auth_part = f":{self.redis_password}@" if self.redis_password else ""
        return f"redis://{auth_part}{self.redis_host}:{self.redis_port}/{self.redis_db}"

    def get_celery_config(self) -> Dict[str, Any]:
        """获取Celery配置字典"""
        return {
            # 基础配置
            "broker_url": self.broker_url,
            "result_backend": self.result_backend,
            "timezone": "Asia/Shanghai",
            "enable_utc": True,
            # 任务序列化
            "task_serializer": "json",
            "result_serializer": "json",
            "accept_content": ["json"],
            # 结果配置
            "result_expires": 3600,  # 结果保存1小时
            "result_persistent": True,
            "result_compression": "gzip",
            # 任务配置
            "task_track_started": True,
            "task_time_limit": 300,  # 任务超时5分钟
            "task_soft_time_limit": 240,  # 软超时4分钟
            "task_acks_late": True,
            "worker_prefetch_multiplier": 1,
            # 重试配置
            "task_default_retry_delay": 60,  # 默认重试延迟1分钟
            "task_max_retries": 3,  # 最大重试次数
            # 路由配置
            "task_routes": self._get_task_routes(),
            # 队列配置
            "task_default_queue": "default",
            "task_queues": self._get_task_queues(),
            # Worker配置
            "worker_log_level": "INFO",
            "worker_concurrency": 4,
            "worker_max_tasks_per_child": 1000,
            # Beat配置（定时任务）
            "beat_schedule": {},  # 动态配置
            "beat_scheduler": "celery.beat:PersistentScheduler",
            # 监控配置
            "worker_send_task_events": True,
            "task_send_sent_event": True,
            "monitoring_frequency": 0.5,
            # 安全配置
            "worker_hijack_root_logger": False,
            "worker_disable_rate_limits": False,
        }

    def _get_task_routes(self) -> Dict[str, Dict[str, str]]:
        """获取任务路由配置"""
        return {
            # AI相关任务
            "tasks.ai.*": {"queue": "ai_queue"},
            "tasks.nlp.*": {"queue": "ai_queue"},
            "tasks.generation.*": {"queue": "ai_queue"},
            # 消息处理任务
            "tasks.message.*": {"queue": "message_queue"},
            "tasks.notification.*": {"queue": "notification_queue"},
            "tasks.webhook.*": {"queue": "webhook_queue"},
            # 数据处理任务
            "tasks.data.*": {"queue": "data_queue"},
            "tasks.analytics.*": {"queue": "data_queue"},
            "tasks.export.*": {"queue": "data_queue"},
            # 定时任务
            "tasks.scheduled.*": {"queue": "scheduled_queue"},
            "tasks.maintenance.*": {"queue": "maintenance_queue"},
            # 高优先级任务
            "tasks.urgent.*": {"queue": "urgent_queue"},
            "tasks.priority.*": {"queue": "priority_queue"},
        }

    def _get_task_queues(self) -> tuple:
        """获取任务队列配置"""
        # 定义交换机
        default_exchange = Exchange("default", type="direct")
        priority_exchange = Exchange("priority", type="direct")

        return (
            # 默认队列
            Queue("default", default_exchange, routing_key="default"),
            # AI处理队列
            Queue(
                "ai_queue",
                default_exchange,
                routing_key="ai",
                queue_arguments={"x-max-priority": 5},
            ),
            # 消息处理队列
            Queue("message_queue", default_exchange, routing_key="message"),
            Queue("notification_queue", default_exchange, routing_key="notification"),
            Queue("webhook_queue", default_exchange, routing_key="webhook"),
            # 数据处理队列
            Queue("data_queue", default_exchange, routing_key="data"),
            # 定时任务队列
            Queue("scheduled_queue", default_exchange, routing_key="scheduled"),
            Queue("maintenance_queue", default_exchange, routing_key="maintenance"),
            # 高优先级队列
            Queue(
                "urgent_queue",
                priority_exchange,
                routing_key="urgent",
                queue_arguments={
                    "x-max-priority": 10,
                    "x-message-ttl": 300000,  # 5分钟TTL
                },
            ),
            Queue(
                "priority_queue",
                priority_exchange,
                routing_key="priority",
                queue_arguments={"x-max-priority": 8},
            ),
        )

    def update_beat_schedule(self, schedule: Dict[str, Any]) -> None:
        """更新定时任务调度配置"""
        config = self.get_celery_config()
        config["beat_schedule"].update(schedule)


class CeleryDevelopmentConfig(CeleryConfig):
    """开发环境Celery配置"""

    def get_celery_config(self) -> Dict[str, Any]:
        config = super().get_celery_config()
        config.update(
            {
                "task_always_eager": False,  # 开发环境也使用异步
                "task_eager_propagates": True,
                "worker_log_level": "DEBUG",
                "worker_concurrency": 2,  # 减少并发数
            }
        )
        return config


class CeleryProductionConfig(CeleryConfig):
    """生产环境Celery配置"""

    def get_celery_config(self) -> Dict[str, Any]:
        config = super().get_celery_config()
        config.update(
            {
                "worker_log_level": "WARNING",
                "worker_concurrency": 8,  # 增加并发数
                "worker_max_tasks_per_child": 2000,
                "task_time_limit": 600,  # 增加超时时间
                "task_soft_time_limit": 540,
            }
        )
        return config


class CeleryTestConfig(CeleryConfig):
    """测试环境Celery配置"""

    def get_celery_config(self) -> Dict[str, Any]:
        config = super().get_celery_config()
        config.update(
            {
                "task_always_eager": True,  # 测试环境同步执行
                "task_eager_propagates": True,
                "broker_url": "memory://",
                "result_backend": "cache+memory://",
            }
        )
        return config


def get_celery_config(environment: Optional[str] = None) -> CeleryConfig:
    """获取环境对应的Celery配置"""
    env = environment or os.getenv("ENVIRONMENT", "development")

    if env == "production":
        return CeleryProductionConfig()
    elif env == "test":
        return CeleryTestConfig()
    else:
        return CeleryDevelopmentConfig()
