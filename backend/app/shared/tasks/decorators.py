"""
任务装饰器模块

提供便捷的任务装饰器，包括：
- 基础任务装饰器
- 重试装饰器
- 监控装饰器
- 限流装饰器
"""

import functools
import inspect
import logging
from datetime import datetime, timezone
from typing import Any, Callable, Dict, Optional, Union

from .app import get_celery_app
from .models import TaskMetadata, TaskPriority, TaskStatus, TaskType
from .registry import TaskRegistry

logger = logging.getLogger(__name__)


def task(
    name: Optional[str] = None,
    task_type: TaskType = TaskType.SYSTEM_TASK,
    priority: TaskPriority = TaskPriority.NORMAL,
    queue: str = "default",
    max_retries: int = 3,
    timeout: int = 300,
    rate_limit: Optional[str] = None,
    bind: bool = True,
    **kwargs,
):
    """
    任务装饰器

    Args:
        name: 任务名称
        task_type: 任务类型
        priority: 任务优先级
        queue: 队列名称
        max_retries: 最大重试次数
        timeout: 超时时间（秒）
        rate_limit: 频率限制（如 "10/m" 表示每分钟10次）
        bind: 是否绑定self参数
        **kwargs: 其他Celery参数
    """

    def decorator(func: Callable) -> Callable:
        # 获取任务名称
        task_name = name or f"{func.__module__}.{func.__name__}"

        # 创建任务元数据
        metadata = TaskMetadata(
            task_name=task_name,
            task_type=task_type,
            priority=priority,
            queue_name=queue,
            max_retries=max_retries,
            timeout=timeout,
        )

        # 准备Celery任务参数
        celery_kwargs = {
            "name": task_name,
            "bind": bind,
            "queue": queue,
            "max_retries": max_retries,
            "default_retry_delay": 60,
            "rate_limit": rate_limit,
            "time_limit": timeout,
            "soft_time_limit": timeout - 30 if timeout > 30 else timeout,
            **kwargs,
        }

        # 包装原函数
        @functools.wraps(func)
        def wrapped_func(self_or_first_arg, *args, **kwargs):
            # 根据bind参数决定如何处理参数
            if bind:
                # self是Celery任务实例
                task_self = self_or_first_arg
                actual_args = args

                # 记录任务开始
                logger.info(f"Starting task {task_name}[{task_self.request.id}]")

                # 创建任务上下文
                task_context = {
                    "task_id": task_self.request.id,
                    "task_name": task_name,
                    "retries": task_self.request.retries,
                    "eta": task_self.request.eta,
                    "expires": task_self.request.expires,
                }

                try:
                    # 执行原函数
                    result = func(task_self, *actual_args, **kwargs)

                    # 记录任务成功
                    logger.info(
                        f"Task {task_name}[{task_self.request.id}] completed successfully"
                    )

                    return result

                except Exception as exc:
                    # 记录任务失败
                    logger.error(
                        f"Task {task_name}[{task_self.request.id}] failed: {exc}"
                    )

                    # 检查是否需要重试
                    if task_self.request.retries < max_retries:
                        logger.info(
                            f"Retrying task {task_name}[{task_self.request.id}] in 60 seconds"
                        )
                        raise task_self.retry(exc=exc, countdown=60)
                    else:
                        logger.error(
                            f"Task {task_name}[{task_self.request.id}] failed permanently after {max_retries} retries"
                        )
                        raise
            else:
                # 不绑定self参数
                actual_args = (self_or_first_arg,) + args

                try:
                    result = func(*actual_args, **kwargs)
                    logger.info(f"Task {task_name} completed successfully")
                    return result
                except Exception as exc:
                    logger.error(f"Task {task_name} failed: {exc}")
                    raise

        # 使用Celery装饰器
        celery_app = get_celery_app()
        celery_task = celery_app.task(**celery_kwargs)(wrapped_func)

        # 注册任务
        TaskRegistry.register_task(task_name, celery_task, metadata)

        return celery_task

    return decorator


def periodic_task(
    schedule: Union[str, Dict[str, Any]],
    name: Optional[str] = None,
    enabled: bool = True,
    **task_kwargs,
):
    """
    定时任务装饰器

    Args:
        schedule: 调度配置（cron表达式或间隔字典）
        name: 任务名称
        enabled: 是否启用
        **task_kwargs: 任务参数
    """

    def decorator(func: Callable) -> Callable:
        # 先创建普通任务
        task_func = task(name=name, **task_kwargs)(func)

        # 注册到定时任务调度器
        from .scheduler import TaskScheduler

        scheduler = TaskScheduler()
        scheduler.add_periodic_task(
            name=name or func.__name__,
            task=task_func.name,
            schedule=schedule,
            enabled=enabled,
        )

        return task_func

    return decorator


def retry_task(
    max_retries: int = 3, countdown: int = 60, backoff: bool = True, jitter: bool = True
):
    """
    重试装饰器

    Args:
        max_retries: 最大重试次数
        countdown: 重试延迟（秒）
        backoff: 是否使用退避策略
        jitter: 是否添加随机延迟
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            try:
                return func(self, *args, **kwargs)
            except Exception as exc:
                if self.request.retries < max_retries:
                    # 计算重试延迟
                    retry_countdown = countdown
                    if backoff:
                        retry_countdown = countdown * (2**self.request.retries)

                    if jitter:
                        import random

                        retry_countdown += random.randint(0, 30)

                    logger.warning(
                        f"Task {self.name} failed, retrying in {retry_countdown} seconds"
                    )
                    raise self.retry(exc=exc, countdown=retry_countdown)
                else:
                    logger.error(
                        f"Task {self.name} failed permanently after {max_retries} retries"
                    )
                    raise

        return wrapper

    return decorator


def monitor_task(
    track_progress: bool = True, track_memory: bool = False, track_time: bool = True
):
    """
    监控装饰器

    Args:
        track_progress: 是否跟踪进度
        track_memory: 是否跟踪内存使用
        track_time: 是否跟踪执行时间
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            start_time = datetime.now(timezone.utc)

            if track_memory:
                import psutil

                process = psutil.Process()
                start_memory = process.memory_info().rss

            try:
                # 更新任务状态
                self.update_state(
                    state=TaskStatus.STARTED.value,
                    meta={
                        "started_at": start_time.isoformat(),
                        "progress": 0 if track_progress else None,
                    },
                )

                # 执行任务
                result = func(self, *args, **kwargs)

                # 计算执行时间
                end_time = datetime.now(timezone.utc)
                execution_time = (end_time - start_time).total_seconds()

                # 准备最终状态
                final_meta = {
                    "started_at": start_time.isoformat(),
                    "completed_at": end_time.isoformat(),
                    "execution_time": execution_time,
                    "progress": 100 if track_progress else None,
                }

                if track_memory:
                    end_memory = process.memory_info().rss
                    final_meta["memory_used"] = end_memory - start_memory

                # 更新任务状态为成功
                self.update_state(state=TaskStatus.SUCCESS.value, meta=final_meta)

                return result

            except Exception as exc:
                # 更新任务状态为失败
                self.update_state(
                    state=TaskStatus.FAILURE.value,
                    meta={
                        "error": str(exc),
                        "started_at": start_time.isoformat(),
                        "failed_at": datetime.now(timezone.utc).isoformat(),
                    },
                )
                raise

        return wrapper

    return decorator


def rate_limit(limit: str):
    """
    频率限制装饰器

    Args:
        limit: 限制格式，如 "10/m" (每分钟10次), "100/h" (每小时100次)
    """

    def decorator(func: Callable) -> Callable:
        # 这个装饰器主要是标记，实际限制由Celery处理
        func._rate_limit = limit
        return func

    return decorator


def priority_task(priority: TaskPriority):
    """
    优先级装饰器

    Args:
        priority: 任务优先级
    """

    def decorator(func: Callable) -> Callable:
        func._priority = priority
        return func

    return decorator


def queue_task(queue_name: str):
    """
    队列装饰器

    Args:
        queue_name: 队列名称
    """

    def decorator(func: Callable) -> Callable:
        func._queue = queue_name
        return func

    return decorator
