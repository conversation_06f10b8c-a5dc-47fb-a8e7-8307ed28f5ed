"""
任务系统使用示例

演示如何使用任务处理系统的各种功能，包括：
- 基本任务提交和执行
- 定时任务配置
- 任务监控和管理
- 错误处理和重试
- 批量任务处理
"""

import asyncio
import logging
import time
from datetime import datetime, timezone

from .app import create_celery_app
from .manager import get_task_manager
from .models import TaskPriority, TaskType
from .monitor import get_task_monitor, start_monitoring
from .retry_handler import RetryConfig, RetryStrategy, get_retry_handler
from .scheduler import TaskScheduler, add_cron_task, add_interval_task

logger = logging.getLogger(__name__)


def basic_task_example():
    """基本任务使用示例"""
    print("=== 基本任务使用示例 ===")

    # 获取任务管理器
    manager = get_task_manager()

    # 1. 提交简单任务
    print("1. 提交健康检查任务...")
    task_id = manager.submit_task("tasks.system.health_check")
    if task_id:
        print(f"   任务已提交，ID: {task_id}")

        # 等待任务完成
        result = None
        for i in range(30):  # 等待最多30秒
            result = manager.get_task_result(task_id)
            if result and result.is_finished():
                break
            time.sleep(1)

        if result:
            print(f"   任务状态: {result.status}")
            print(f"   执行结果: {result.result}")
        else:
            print("   任务执行超时")

    # 2. 提交带参数的任务
    print("\n2. 提交邮件发送任务...")
    email_task_id = manager.submit_task(
        "tasks.notification.send_email",
        kwargs={
            "to_email": "<EMAIL>",
            "subject": "Test Email",
            "body": "This is a test email from task system.",
        },
        priority=TaskPriority.HIGH,
    )
    if email_task_id:
        print(f"   邮件任务已提交，ID: {email_task_id}")

    # 3. 提交到指定队列
    print("\n3. 提交AI处理任务...")
    ai_task_id = manager.submit_task(
        "tasks.ai.process_nlp",
        kwargs={"text": "这是一段需要进行情感分析的文本", "task_type": "sentiment"},
        queue="ai_queue",
        priority=TaskPriority.HIGH,
    )
    if ai_task_id:
        print(f"   AI任务已提交，ID: {ai_task_id}")


def scheduled_task_example():
    """定时任务使用示例"""
    print("\n=== 定时任务使用示例 ===")

    scheduler = TaskScheduler.get_instance()

    # 1. 添加Cron格式的定时任务
    print("1. 添加每5分钟执行的清理任务...")
    success = add_cron_task(
        name="cleanup_temp_files",
        task="tasks.system.cleanup_logs",
        cron_expr="*/5 * * * *",  # 每5分钟
        kwargs={"days_to_keep": 7},
    )
    print(f"   添加结果: {'成功' if success else '失败'}")

    # 2. 添加间隔任务
    print("\n2. 添加每30秒执行的监控任务...")
    success = add_interval_task(
        name="system_monitor", task="tasks.scheduled.monitor_system", seconds=30
    )
    print(f"   添加结果: {'成功' if success else '失败'}")

    # 3. 查看已调度的任务
    print("\n3. 当前调度任务:")
    scheduled_tasks = scheduler.get_all_periodic_tasks()
    for name, task in scheduled_tasks.items():
        print(f"   - {name}: {task.task} ({'启用' if task.enabled else '禁用'})")


def batch_task_example():
    """批量任务处理示例"""
    print("\n=== 批量任务处理示例 ===")

    manager = get_task_manager()

    # 1. 准备批量任务
    batch_tasks = []
    for i in range(5):
        batch_tasks.append(
            {
                "name": "tasks.notification.send_sms",
                "kwargs": {
                    "phone_number": f"1380000000{i}",
                    "message": f"Batch message {i+1}",
                },
                "queue": "notification_queue",
            }
        )

    print(f"1. 提交 {len(batch_tasks)} 个批量任务...")
    task_ids = manager.submit_batch_tasks(batch_tasks, batch_size=2)
    print(f"   成功提交 {len(task_ids)} 个任务")

    # 2. 等待批量任务完成
    if task_ids:
        print("\n2. 等待批量任务完成...")
        results = manager.wait_for_tasks(task_ids, timeout=60)

        successful = sum(1 for r in results.values() if r.is_successful())
        failed = sum(1 for r in results.values() if r.is_failed())

        print(f"   完成情况: 成功 {successful}, 失败 {failed}")


def monitoring_example():
    """任务监控示例"""
    print("\n=== 任务监控示例 ===")

    # 启动监控
    print("1. 启动任务监控...")
    start_monitoring()

    monitor = get_task_monitor()

    # 等待一会儿收集数据
    time.sleep(5)

    # 2. 获取统计信息
    print("\n2. 获取监控统计:")
    stats = monitor.get_statistics()
    print(f"   总任务数: {stats.total_tasks}")
    print(f"   运行中任务: {stats.running_tasks}")
    print(f"   已完成任务: {stats.completed_tasks}")
    print(f"   失败任务: {stats.failed_tasks}")
    print(f"   成功率: {stats.success_rate:.2f}%")

    # 3. 获取队列状态
    print("\n3. 队列状态:")
    queue_status = monitor.get_queue_status()
    print(f"   队列长度: {queue_status.get('queue_lengths', {})}")
    print(f"   Worker数量: {queue_status.get('worker_count', 0)}")

    # 4. 获取性能指标
    print("\n4. 性能指标:")
    performance = monitor.get_performance_metrics()
    print(f"   平均执行时间: {performance.get('average_execution_time', 0):.2f}秒")
    print(f"   每分钟处理量: {performance.get('tasks_per_minute', 0)}")


def error_handling_example():
    """错误处理和重试示例"""
    print("\n=== 错误处理和重试示例 ===")

    retry_handler = get_retry_handler()
    manager = get_task_manager()

    # 1. 查看错误统计
    print("1. 当前错误统计:")
    error_stats = retry_handler.get_error_statistics()
    print(f"   总错误数: {error_stats['total_errors']}")
    print(f"   按分类统计: {error_stats['error_by_category']}")

    # 2. 提交一个可能失败的任务（模拟）
    print("\n2. 提交测试任务（可能失败）...")
    # 这里可以提交一个模拟失败的任务来测试重试机制

    # 3. 熔断器状态
    print("\n3. 熔断器状态:")
    circuit_states = error_stats.get("circuit_breaker_states", {})
    if circuit_states:
        for task_name, state in circuit_states.items():
            print(
                f"   {task_name}: {state['state']} (失败次数: {state['failure_count']})"
            )
    else:
        print("   当前没有熔断器状态记录")


def task_management_example():
    """任务管理示例"""
    print("\n=== 任务管理示例 ===")

    manager = get_task_manager()

    # 1. 获取任务概览
    print("1. 任务系统概览:")
    summary = manager.get_task_summary()
    print(f"   时间戳: {summary.get('timestamp')}")
    print(f"   注册任务数: {summary.get('registry', {}).get('total_tasks', 0)}")
    print(f"   活跃任务数: {summary.get('active_tasks', 0)}")
    print(f"   调度任务数: {summary.get('scheduled_tasks', 0)}")

    # 2. 获取队列信息
    print("\n2. 队列信息:")
    queues = summary.get("queues", {})
    for queue_name, length in queues.items():
        print(f"   {queue_name}: {length} 个任务")

    # 3. Worker信息
    print("\n3. Worker信息:")
    workers = summary.get("workers", {})
    print(f"   Worker数量: {workers.get('count', 0)}")


def performance_test_example():
    """性能测试示例"""
    print("\n=== 性能测试示例 ===")

    manager = get_task_manager()

    # 1. 准备性能测试任务
    start_time = time.time()
    test_tasks = []

    print("1. 准备100个轻量级任务...")
    for i in range(100):
        test_tasks.append({"name": "tasks.system.health_check", "queue": "default"})

    # 2. 批量提交
    print("2. 批量提交任务...")
    task_ids = manager.submit_batch_tasks(test_tasks, batch_size=20)
    submit_time = time.time() - start_time

    print(f"   提交时间: {submit_time:.2f}秒")
    print(f"   提交速度: {len(task_ids)/submit_time:.2f} 任务/秒")

    # 3. 等待完成并统计
    print("3. 等待任务完成...")
    start_wait = time.time()
    results = manager.wait_for_tasks(task_ids, timeout=300)  # 5分钟超时
    total_time = time.time() - start_time

    successful = sum(1 for r in results.values() if r.is_successful())
    failed = sum(1 for r in results.values() if r.is_failed())

    print(f"   总执行时间: {total_time:.2f}秒")
    print(f"   处理速度: {successful/total_time:.2f} 任务/秒")
    print(f"   成功率: {successful/(successful+failed)*100:.1f}%")


def main():
    """运行所有示例"""
    print("柴管家任务处理系统使用示例")
    print("=" * 50)

    try:
        # 确保Celery应用已创建
        celery_app = create_celery_app()
        print(f"Celery应用已创建: {celery_app.main}")

        # 运行各种示例
        basic_task_example()
        scheduled_task_example()
        batch_task_example()
        monitoring_example()
        error_handling_example()
        task_management_example()
        performance_test_example()

        print("\n" + "=" * 50)
        print("所有示例运行完成！")

    except Exception as e:
        logger.error(f"示例运行出错: {e}")
        print(f"示例运行出错: {e}")


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    main()
