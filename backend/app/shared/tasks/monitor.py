"""
任务监控模块

提供任务执行状态监控，包括：
- 实时任务状态跟踪
- 任务执行统计
- 性能指标收集
- 告警和通知
"""

import logging
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta, timezone
from threading import Event, RLock, Thread
from typing import Any, Callable, Dict, List, Optional

import psutil
from celery import Celery
from celery.events import Event as CeleryEvent
from celery.events.state import State

from .app import get_celery_app
from .models import TaskResult, TaskStatistics, TaskStatus

logger = logging.getLogger(__name__)


class TaskMonitor:
    """任务监控器"""

    def __init__(self, celery_app: Optional[Celery] = None):
        self.celery_app = celery_app or get_celery_app()
        self.state = State()
        self.statistics = TaskStatistics()

        # 监控配置
        self.monitoring_interval = 5  # 监控间隔（秒）
        self.history_window = 3600  # 历史数据窗口（秒）

        # 线程控制
        self._monitor_thread: Optional[Thread] = None
        self._stop_event = Event()
        self._lock = RLock()

        # 数据存储
        self._task_history: deque = deque(maxlen=1000)  # 任务历史记录
        self._execution_times: deque = deque(maxlen=100)  # 执行时间历史
        self._error_history: deque = deque(maxlen=100)  # 错误历史

        # 回调函数
        self._alert_callbacks: List[Callable] = []
        self._metric_callbacks: List[Callable] = []

        # 阈值配置
        self.thresholds = {
            "max_failed_tasks": 10,  # 最大失败任务数
            "max_avg_execution_time": 300,  # 最大平均执行时间（秒）
            "min_success_rate": 80.0,  # 最小成功率（%）
            "max_queue_length": 1000,  # 最大队列长度
            "max_memory_usage": 80.0,  # 最大内存使用率（%）
            "max_cpu_usage": 80.0,  # 最大CPU使用率（%）
        }

    def start_monitoring(self) -> None:
        """开始监控"""
        if self._monitor_thread and self._monitor_thread.is_alive():
            logger.warning("Monitor already running")
            return

        self._stop_event.clear()
        self._monitor_thread = Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()

        logger.info("Task monitor started")

    def stop_monitoring(self) -> None:
        """停止监控"""
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._stop_event.set()
            self._monitor_thread.join(timeout=10)

        logger.info("Task monitor stopped")

    def _monitor_loop(self) -> None:
        """监控主循环"""
        while not self._stop_event.is_set():
            try:
                self._collect_metrics()
                self._check_thresholds()
                self._cleanup_old_data()

                # 等待下次监控
                self._stop_event.wait(self.monitoring_interval)

            except Exception as e:
                logger.error(f"Error in monitor loop: {e}")
                self._stop_event.wait(1)  # 出错时短暂等待

    def _collect_metrics(self) -> None:
        """收集监控指标"""
        with self._lock:
            current_time = datetime.now(timezone.utc)

            # 收集Celery状态信息
            self._collect_celery_metrics()

            # 收集系统资源信息
            self._collect_system_metrics()

            # 更新统计信息
            self._update_statistics()

            # 调用指标回调函数
            for callback in self._metric_callbacks:
                try:
                    callback(self.statistics)
                except Exception as e:
                    logger.error(f"Error in metric callback: {e}")

    def _collect_celery_metrics(self) -> None:
        """收集Celery指标"""
        try:
            # 使用Celery inspect获取信息
            inspect = self.celery_app.control.inspect()

            # 获取活跃任务
            active_tasks = inspect.active()
            if active_tasks:
                total_active = sum(len(tasks) for tasks in active_tasks.values())
                self.statistics.running_tasks = total_active

            # 获取队列长度
            queue_lengths = {}
            try:
                with self.celery_app.connection() as conn:
                    for queue_name in [
                        "default",
                        "ai_queue",
                        "message_queue",
                        "notification_queue",
                    ]:
                        try:
                            queue = conn.SimpleQueue(queue_name)
                            queue_lengths[queue_name] = queue.qsize()
                            queue.close()
                        except Exception:
                            queue_lengths[queue_name] = 0
            except Exception as e:
                logger.debug(f"Failed to get queue lengths: {e}")

            self.statistics.queue_lengths = queue_lengths

            # 获取worker数量
            stats = inspect.stats()
            if stats:
                self.statistics.worker_count = len(stats)

        except Exception as e:
            logger.debug(f"Failed to collect Celery metrics: {e}")

    def _collect_system_metrics(self) -> None:
        """收集系统资源指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # 存储在统计对象中（扩展字段）
            if not hasattr(self.statistics, "system_metrics"):
                self.statistics.system_metrics = {}

            self.statistics.system_metrics.update(
                {
                    "cpu_usage": cpu_percent,
                    "memory_usage": memory_percent,
                    "memory_available": memory.available,
                    "memory_total": memory.total,
                }
            )

        except Exception as e:
            logger.debug(f"Failed to collect system metrics: {e}")

    def _update_statistics(self) -> None:
        """更新统计信息"""
        # 计算任务执行统计
        if self._execution_times:
            self.statistics.average_execution_time = sum(self._execution_times) / len(
                self._execution_times
            )

        # 计算任务处理速率（每分钟）
        now = time.time()
        recent_tasks = [
            task for task in self._task_history if now - task.get("timestamp", 0) <= 60
        ]
        self.statistics.tasks_per_minute = len(recent_tasks)

        # 更新成功率
        self.statistics.calculate_success_rate()

        # 更新最后更新时间
        self.statistics.last_updated = datetime.now(timezone.utc)

    def _check_thresholds(self) -> None:
        """检查阈值并触发告警"""
        alerts = []

        # 检查失败任务数
        if self.statistics.failed_tasks > self.thresholds["max_failed_tasks"]:
            alerts.append(
                {
                    "type": "high_failure_rate",
                    "message": f"Failed tasks: {self.statistics.failed_tasks}",
                    "severity": "warning",
                }
            )

        # 检查平均执行时间
        if (
            self.statistics.average_execution_time
            > self.thresholds["max_avg_execution_time"]
        ):
            alerts.append(
                {
                    "type": "slow_execution",
                    "message": f"Average execution time: {self.statistics.average_execution_time:.2f}s",
                    "severity": "warning",
                }
            )

        # 检查成功率
        if self.statistics.success_rate < self.thresholds["min_success_rate"]:
            alerts.append(
                {
                    "type": "low_success_rate",
                    "message": f"Success rate: {self.statistics.success_rate:.2f}%",
                    "severity": "critical",
                }
            )

        # 检查队列长度
        for queue_name, length in self.statistics.queue_lengths.items():
            if length > self.thresholds["max_queue_length"]:
                alerts.append(
                    {
                        "type": "queue_overflow",
                        "message": f"Queue {queue_name} length: {length}",
                        "severity": "warning",
                    }
                )

        # 检查系统资源
        if hasattr(self.statistics, "system_metrics"):
            metrics = self.statistics.system_metrics

            if metrics.get("cpu_usage", 0) > self.thresholds["max_cpu_usage"]:
                alerts.append(
                    {
                        "type": "high_cpu_usage",
                        "message": f"CPU usage: {metrics['cpu_usage']:.1f}%",
                        "severity": "warning",
                    }
                )

            if metrics.get("memory_usage", 0) > self.thresholds["max_memory_usage"]:
                alerts.append(
                    {
                        "type": "high_memory_usage",
                        "message": f"Memory usage: {metrics['memory_usage']:.1f}%",
                        "severity": "warning",
                    }
                )

        # 触发告警回调
        for alert in alerts:
            self._trigger_alert(alert)

    def _trigger_alert(self, alert: Dict[str, Any]) -> None:
        """触发告警"""
        logger.warning(f"Task monitor alert: {alert}")

        for callback in self._alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")

    def _cleanup_old_data(self) -> None:
        """清理过期数据"""
        now = time.time()
        cutoff_time = now - self.history_window

        # 清理任务历史
        while (
            self._task_history
            and self._task_history[0].get("timestamp", 0) < cutoff_time
        ):
            self._task_history.popleft()

    def record_task_event(self, event_type: str, task_data: Dict[str, Any]) -> None:
        """记录任务事件"""
        with self._lock:
            timestamp = time.time()

            event = {
                "type": event_type,
                "timestamp": timestamp,
                "task_id": task_data.get("task_id"),
                "task_name": task_data.get("task_name"),
                "data": task_data,
            }

            self._task_history.append(event)

            # 更新统计计数器
            if event_type == "task-started":
                self.statistics.running_tasks += 1
            elif event_type == "task-succeeded":
                self.statistics.completed_tasks += 1
                self.statistics.running_tasks = max(
                    0, self.statistics.running_tasks - 1
                )

                # 记录执行时间
                execution_time = task_data.get("execution_time")
                if execution_time:
                    self._execution_times.append(execution_time)

            elif event_type == "task-failed":
                self.statistics.failed_tasks += 1
                self.statistics.running_tasks = max(
                    0, self.statistics.running_tasks - 1
                )

                # 记录错误
                error_info = {
                    "timestamp": timestamp,
                    "task_id": task_data.get("task_id"),
                    "error": task_data.get("error"),
                }
                self._error_history.append(error_info)

            # 更新总任务数
            self.statistics.total_tasks = len(self._task_history)

    def get_statistics(self) -> TaskStatistics:
        """获取当前统计信息"""
        with self._lock:
            return self.statistics.copy()

    def get_task_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取任务历史记录"""
        with self._lock:
            return list(self._task_history)[-limit:]

    def get_error_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取错误历史记录"""
        with self._lock:
            return list(self._error_history)[-limit:]

    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        return {
            "queue_lengths": self.statistics.queue_lengths,
            "total_queued": sum(self.statistics.queue_lengths.values()),
            "worker_count": self.statistics.worker_count,
        }

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            "average_execution_time": self.statistics.average_execution_time,
            "tasks_per_minute": self.statistics.tasks_per_minute,
            "success_rate": self.statistics.success_rate,
            "execution_time_history": list(self._execution_times),
        }

    def add_alert_callback(self, callback: Callable) -> None:
        """添加告警回调函数"""
        self._alert_callbacks.append(callback)

    def add_metric_callback(self, callback: Callable) -> None:
        """添加指标回调函数"""
        self._metric_callbacks.append(callback)

    def remove_alert_callback(self, callback: Callable) -> None:
        """移除告警回调函数"""
        if callback in self._alert_callbacks:
            self._alert_callbacks.remove(callback)

    def remove_metric_callback(self, callback: Callable) -> None:
        """移除指标回调函数"""
        if callback in self._metric_callbacks:
            self._metric_callbacks.remove(callback)

    def update_thresholds(self, thresholds: Dict[str, Any]) -> None:
        """更新告警阈值"""
        self.thresholds.update(thresholds)
        logger.info(f"Updated monitor thresholds: {thresholds}")

    def reset_statistics(self) -> None:
        """重置统计信息"""
        with self._lock:
            self.statistics = TaskStatistics()
            self._task_history.clear()
            self._execution_times.clear()
            self._error_history.clear()
            logger.info("Task monitor statistics reset")

    def get_monitor_status(self) -> Dict[str, Any]:
        """获取监控器状态"""
        return {
            "is_running": self._monitor_thread and self._monitor_thread.is_alive(),
            "monitoring_interval": self.monitoring_interval,
            "history_window": self.history_window,
            "thresholds": self.thresholds,
            "callback_count": {
                "alert_callbacks": len(self._alert_callbacks),
                "metric_callbacks": len(self._metric_callbacks),
            },
        }


class TaskEventProcessor:
    """任务事件处理器"""

    def __init__(self, monitor: TaskMonitor):
        self.monitor = monitor
        self.celery_app = monitor.celery_app

    def start_event_processing(self) -> None:
        """开始处理Celery事件"""

        def process_events():
            try:
                with self.celery_app.connection() as connection:
                    recv = self.celery_app.events.Receiver(
                        connection,
                        handlers={
                            "task-sent": self._on_task_sent,
                            "task-received": self._on_task_received,
                            "task-started": self._on_task_started,
                            "task-succeeded": self._on_task_succeeded,
                            "task-failed": self._on_task_failed,
                            "task-retried": self._on_task_retried,
                            "task-revoked": self._on_task_revoked,
                        },
                    )
                    recv.capture(limit=None, timeout=None, wakeup=True)
            except Exception as e:
                logger.error(f"Error processing Celery events: {e}")

        # 在后台线程中处理事件
        event_thread = Thread(target=process_events, daemon=True)
        event_thread.start()

        logger.info("Task event processor started")

    def _on_task_sent(self, event: CeleryEvent) -> None:
        """任务发送事件处理"""
        self.monitor.record_task_event(
            "task-sent",
            {
                "task_id": event["uuid"],
                "task_name": event["name"],
                "timestamp": event["timestamp"],
            },
        )

    def _on_task_received(self, event: CeleryEvent) -> None:
        """任务接收事件处理"""
        self.monitor.record_task_event(
            "task-received",
            {
                "task_id": event["uuid"],
                "task_name": event["name"],
                "timestamp": event["timestamp"],
            },
        )

    def _on_task_started(self, event: CeleryEvent) -> None:
        """任务开始事件处理"""
        self.monitor.record_task_event(
            "task-started",
            {
                "task_id": event["uuid"],
                "task_name": event["name"],
                "timestamp": event["timestamp"],
            },
        )

    def _on_task_succeeded(self, event: CeleryEvent) -> None:
        """任务成功事件处理"""
        self.monitor.record_task_event(
            "task-succeeded",
            {
                "task_id": event["uuid"],
                "task_name": event["name"],
                "timestamp": event["timestamp"],
                "execution_time": event.get("runtime", 0),
                "result": event.get("result"),
            },
        )

    def _on_task_failed(self, event: CeleryEvent) -> None:
        """任务失败事件处理"""
        self.monitor.record_task_event(
            "task-failed",
            {
                "task_id": event["uuid"],
                "task_name": event["name"],
                "timestamp": event["timestamp"],
                "error": event.get("exception"),
                "traceback": event.get("traceback"),
            },
        )

    def _on_task_retried(self, event: CeleryEvent) -> None:
        """任务重试事件处理"""
        self.monitor.record_task_event(
            "task-retried",
            {
                "task_id": event["uuid"],
                "task_name": event["name"],
                "timestamp": event["timestamp"],
                "reason": event.get("reason"),
            },
        )

    def _on_task_revoked(self, event: CeleryEvent) -> None:
        """任务撤销事件处理"""
        self.monitor.record_task_event(
            "task-revoked",
            {
                "task_id": event["uuid"],
                "task_name": event["name"],
                "timestamp": event["timestamp"],
                "reason": event.get("reason"),
            },
        )


# 全局监控器实例
_monitor_instance: Optional[TaskMonitor] = None


def get_task_monitor() -> TaskMonitor:
    """获取全局任务监控器实例"""
    global _monitor_instance
    if _monitor_instance is None:
        _monitor_instance = TaskMonitor()
    return _monitor_instance


def start_task_monitoring() -> None:
    """启动任务监控"""
    monitor = get_task_monitor()
    monitor.start_monitoring()

    # 启动事件处理
    event_processor = TaskEventProcessor(monitor)
    event_processor.start_event_processing()


def stop_task_monitoring() -> None:
    """停止任务监控"""
    monitor = get_task_monitor()
    monitor.stop_monitoring()
