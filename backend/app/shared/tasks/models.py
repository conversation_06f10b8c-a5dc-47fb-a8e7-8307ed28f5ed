"""
任务处理系统的数据模型

定义任务相关的数据结构，包括：
- 任务状态枚举
- 任务优先级枚举
- 任务元数据模型
- 任务结果模型
"""

import json
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, validator


class TaskStatus(str, Enum):
    """任务状态枚举"""

    PENDING = "PENDING"  # 等待执行
    RECEIVED = "RECEIVED"  # 已接收
    STARTED = "STARTED"  # 已开始
    RETRY = "RETRY"  # 重试中
    FAILURE = "FAILURE"  # 执行失败
    SUCCESS = "SUCCESS"  # 执行成功
    REVOKED = "REVOKED"  # 已撤销
    IGNORED = "IGNORED"  # 已忽略


class TaskPriority(int, Enum):
    """任务优先级枚举"""

    LOW = 1  # 低优先级
    NORMAL = 5  # 普通优先级
    HIGH = 8  # 高优先级
    URGENT = 10  # 紧急优先级


class TaskType(str, Enum):
    """任务类型枚举"""

    AI_PROCESSING = "ai_processing"  # AI处理任务
    MESSAGE_PROCESSING = "message_processing"  # 消息处理任务
    NOTIFICATION = "notification"  # 通知任务
    WEBHOOK = "webhook"  # Webhook任务
    DATA_EXPORT = "data_export"  # 数据导出任务
    DATA_IMPORT = "data_import"  # 数据导入任务
    MAINTENANCE = "maintenance"  # 维护任务
    SCHEDULED = "scheduled"  # 定时任务
    USER_ACTION = "user_action"  # 用户操作任务
    SYSTEM_TASK = "system_task"  # 系统任务


@dataclass
class TaskMetadata:
    """任务元数据"""

    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    task_name: str = ""
    task_type: TaskType = TaskType.SYSTEM_TASK
    priority: TaskPriority = TaskPriority.NORMAL
    queue_name: str = "default"
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    timeout: int = 300  # 5分钟超时
    owner_id: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "task_name": self.task_name,
            "task_type": self.task_type.value,
            "priority": self.priority.value,
            "queue_name": self.queue_name,
            "created_at": self.created_at.isoformat(),
            "scheduled_at": (
                self.scheduled_at.isoformat() if self.scheduled_at else None
            ),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": (
                self.completed_at.isoformat() if self.completed_at else None
            ),
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "timeout": self.timeout,
            "owner_id": self.owner_id,
            "tags": self.tags,
            "context": self.context,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TaskMetadata":
        """从字典创建实例"""
        metadata = cls()
        metadata.task_id = data.get("task_id", metadata.task_id)
        metadata.task_name = data.get("task_name", "")
        metadata.task_type = TaskType(data.get("task_type", TaskType.SYSTEM_TASK.value))
        metadata.priority = TaskPriority(
            data.get("priority", TaskPriority.NORMAL.value)
        )
        metadata.queue_name = data.get("queue_name", "default")

        # 处理日期时间字段
        if data.get("created_at"):
            metadata.created_at = datetime.fromisoformat(data["created_at"])
        if data.get("scheduled_at"):
            metadata.scheduled_at = datetime.fromisoformat(data["scheduled_at"])
        if data.get("started_at"):
            metadata.started_at = datetime.fromisoformat(data["started_at"])
        if data.get("completed_at"):
            metadata.completed_at = datetime.fromisoformat(data["completed_at"])

        metadata.retry_count = data.get("retry_count", 0)
        metadata.max_retries = data.get("max_retries", 3)
        metadata.timeout = data.get("timeout", 300)
        metadata.owner_id = data.get("owner_id")
        metadata.tags = data.get("tags", [])
        metadata.context = data.get("context", {})

        return metadata


class Task(BaseModel):
    """任务模型"""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    func: str  # 任务函数名
    args: List[Any] = Field(default_factory=list)
    kwargs: Dict[str, Any] = Field(default_factory=dict)
    metadata: TaskMetadata = Field(default_factory=TaskMetadata)
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[Any] = None
    error: Optional[str] = None
    traceback: Optional[str] = None

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            TaskStatus: lambda v: v.value,
            TaskPriority: lambda v: v.value,
            TaskType: lambda v: v.value,
        }

    @validator("metadata", pre=True)
    def validate_metadata(cls, v):
        """验证元数据"""
        if isinstance(v, dict):
            return TaskMetadata.from_dict(v)
        return v

    def to_celery_signature(self):
        """转换为Celery签名"""
        from celery import signature

        return signature(
            self.func,
            args=self.args,
            kwargs=self.kwargs,
            task_id=self.id,
            queue=self.metadata.queue_name,
            priority=self.metadata.priority.value,
            retry_policy={
                "max_retries": self.metadata.max_retries,
                "interval_start": 60,
                "interval_step": 60,
                "interval_max": 600,
            },
        )

    def update_status(
        self, status: TaskStatus, result: Any = None, error: str = None
    ) -> None:
        """更新任务状态"""
        self.status = status

        if status == TaskStatus.STARTED:
            self.metadata.started_at = datetime.now(timezone.utc)
        elif status in [TaskStatus.SUCCESS, TaskStatus.FAILURE, TaskStatus.REVOKED]:
            self.metadata.completed_at = datetime.now(timezone.utc)

        if result is not None:
            self.result = result
        if error is not None:
            self.error = error

    def increment_retry(self) -> None:
        """增加重试次数"""
        self.metadata.retry_count += 1
        self.status = TaskStatus.RETRY

    def is_retryable(self) -> bool:
        """判断是否可以重试"""
        return (
            self.status == TaskStatus.FAILURE
            and self.metadata.retry_count < self.metadata.max_retries
        )

    def get_execution_time(self) -> Optional[float]:
        """获取执行时间（秒）"""
        if self.metadata.started_at and self.metadata.completed_at:
            return (
                self.metadata.completed_at - self.metadata.started_at
            ).total_seconds()
        return None


class TaskResult(BaseModel):
    """任务结果模型"""

    task_id: str
    status: TaskStatus
    result: Optional[Any] = None
    error: Optional[str] = None
    traceback: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time: Optional[float] = None
    retry_count: int = 0
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            TaskStatus: lambda v: v.value,
        }

    @classmethod
    def from_task(cls, task: Task) -> "TaskResult":
        """从任务对象创建结果"""
        return cls(
            task_id=task.id,
            status=task.status,
            result=task.result,
            error=task.error,
            traceback=task.traceback,
            started_at=task.metadata.started_at,
            completed_at=task.metadata.completed_at,
            execution_time=task.get_execution_time(),
            retry_count=task.metadata.retry_count,
            metadata=task.metadata.to_dict(),
        )

    def is_successful(self) -> bool:
        """判断是否成功"""
        return self.status == TaskStatus.SUCCESS

    def is_failed(self) -> bool:
        """判断是否失败"""
        return self.status == TaskStatus.FAILURE

    def is_finished(self) -> bool:
        """判断是否已完成"""
        return self.status in [
            TaskStatus.SUCCESS,
            TaskStatus.FAILURE,
            TaskStatus.REVOKED,
        ]


class ScheduledTask(BaseModel):
    """定时任务模型"""

    name: str
    task: str  # 任务函数名
    schedule: Union[str, Dict[str, Any]]  # cron表达式或间隔配置
    args: List[Any] = Field(default_factory=list)
    kwargs: Dict[str, Any] = Field(default_factory=dict)
    options: Dict[str, Any] = Field(default_factory=dict)
    enabled: bool = True
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    total_runs: int = 0

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }

    def to_beat_schedule_entry(self) -> Dict[str, Any]:
        """转换为Celery Beat调度条目"""
        entry = {
            "task": self.task,
            "args": self.args,
            "kwargs": self.kwargs,
            "options": self.options,
        }

        if isinstance(self.schedule, str):
            # Cron表达式
            from celery.schedules import crontab

            parts = self.schedule.split()
            if len(parts) == 5:
                minute, hour, day, month, day_of_week = parts
                entry["schedule"] = crontab(
                    minute=minute,
                    hour=hour,
                    day_of_month=day,
                    month_of_year=month,
                    day_of_week=day_of_week,
                )
        else:
            # 间隔配置
            from celery.schedules import schedule

            entry["schedule"] = schedule(seconds=self.schedule.get("seconds", 60))

        return entry


class TaskStatistics(BaseModel):
    """任务统计模型"""

    total_tasks: int = 0
    pending_tasks: int = 0
    running_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    success_rate: float = 0.0
    average_execution_time: float = 0.0
    tasks_per_minute: float = 0.0
    worker_count: int = 0
    queue_lengths: Dict[str, int] = Field(default_factory=dict)
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }

    def calculate_success_rate(self) -> None:
        """计算成功率"""
        if self.completed_tasks > 0:
            self.success_rate = (
                (self.completed_tasks - self.failed_tasks) / self.completed_tasks * 100
            )
        else:
            self.success_rate = 0.0
