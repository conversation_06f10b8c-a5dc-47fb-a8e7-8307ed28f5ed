# 柴管家任务处理系统

基于 Celery 的高性能异步任务处理系统，支持任务调度、监控、重试和错误处理。

## 🚀 功能特性

- **异步任务执行**: 支持各种类型的后台任务
- **定时任务调度**: 基于 Celery Beat 的定时任务系统
- **队列管理**: 多队列支持，任务路由和优先级
- **监控系统**: 实时监控任务状态、性能指标
- **错误处理**: 智能重试策略、熔断器、死信队列
- **任务管理**: 批量操作、任务链、状态管理

## 📋 验收标准

✅ **AC1**: Celery 任务处理器正常运行，支持不同优先级 ✅ **AC2**: 任务定义和注册机制有效 ✅ **AC3**:
Celery Beat 定时任务调度正常 ✅ **AC4**: 任务监控和管理界面功能正常 ✅ **AC5**: 任务重试和错误处理机
制有效

## 🏗️ 系统架构

```
任务处理系统
├── app.py              # Celery应用创建和配置
├── config.py           # 配置管理
├── models.py           # 数据模型
├── decorators.py       # 任务装饰器
├── registry.py         # 任务注册表
├── scheduler.py        # 定时任务调度
├── manager.py          # 任务管理器
├── monitor.py          # 任务监控
├── retry_handler.py    # 重试和错误处理
├── handlers.py         # 任务处理器实现
└── examples.py         # 使用示例
```

## 🔧 快速开始

### 1. 基本配置

```python
# 环境变量配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=admin123
```

### 2. 创建任务

```python
from app.shared.tasks import task, TaskType, TaskPriority

@task(
    name="tasks.example.hello_world",
    task_type=TaskType.SYSTEM_TASK,
    priority=TaskPriority.NORMAL,
    queue="default",
    timeout=300
)
def hello_world_task(self, name: str):
    """示例任务"""
    return f"Hello, {name}!"
```

### 3. 提交任务

```python
from app.shared.tasks import get_task_manager

manager = get_task_manager()

# 提交简单任务
task_id = manager.submit_task("tasks.example.hello_world", kwargs={"name": "World"})

# 获取结果
result = manager.get_task_result(task_id)
print(result.result)  # "Hello, World!"
```

### 4. 定时任务

```python
from app.shared.tasks import periodic_task

@periodic_task(
    schedule="0 2 * * *",  # 每天凌晨2点
    name="tasks.maintenance.daily_cleanup"
)
def daily_cleanup_task(self):
    """每日清理任务"""
    # 执行清理逻辑
    return {"cleaned_files": 100}
```

## 📚 详细使用指南

### 任务装饰器

#### @task - 基础任务装饰器

```python
@task(
    name="tasks.module.function_name",      # 任务名称
    task_type=TaskType.SYSTEM_TASK,         # 任务类型
    priority=TaskPriority.NORMAL,           # 优先级
    queue="default",                        # 队列名称
    timeout=300,                           # 超时时间（秒）
    max_retries=3,                         # 最大重试次数
    bind=True                              # 绑定self参数
)
def my_task(self, arg1, arg2, kwarg1=None):
    # 任务逻辑
    return result
```

#### @periodic_task - 定时任务装饰器

```python
@periodic_task(
    schedule="*/15 * * * *",               # Cron表达式
    name="tasks.monitoring.system_check",  # 任务名称
    enabled=True                           # 是否启用
)
def system_check_task(self):
    # 定时任务逻辑
    return status
```

#### @monitor_task - 监控装饰器

```python
@monitor_task(
    track_progress=True,    # 跟踪进度
    track_memory=True,      # 跟踪内存使用
    track_time=True         # 跟踪执行时间
)
def monitored_task(self, data):
    # 更新进度
    self.update_state(
        state="PROGRESS",
        meta={"progress": 50, "message": "Processing..."}
    )
    # 任务逻辑
    return result
```

### 任务类型和优先级

#### 任务类型

```python
class TaskType(str, Enum):
    AI_PROCESSING = "ai_processing"          # AI处理任务
    MESSAGE_PROCESSING = "message_processing" # 消息处理任务
    NOTIFICATION = "notification"            # 通知任务
    WEBHOOK = "webhook"                      # Webhook任务
    DATA_EXPORT = "data_export"             # 数据导出任务
    DATA_IMPORT = "data_import"             # 数据导入任务
    MAINTENANCE = "maintenance"              # 维护任务
    SCHEDULED = "scheduled"                  # 定时任务
    USER_ACTION = "user_action"             # 用户操作任务
    SYSTEM_TASK = "system_task"             # 系统任务
```

#### 任务优先级

```python
class TaskPriority(int, Enum):
    LOW = 1         # 低优先级
    NORMAL = 5      # 普通优先级
    HIGH = 8        # 高优先级
    URGENT = 10     # 紧急优先级
```

### 队列配置

系统预配置了多个专用队列：

- `default`: 默认队列
- `ai_queue`: AI 处理队列（高优先级）
- `message_queue`: 消息处理队列
- `notification_queue`: 通知队列
- `webhook_queue`: Webhook 队列
- `data_queue`: 数据处理队列
- `scheduled_queue`: 定时任务队列
- `maintenance_queue`: 维护任务队列
- `urgent_queue`: 紧急任务队列
- `priority_queue`: 高优先级队列

### 任务管理

#### 基本操作

```python
from app.shared.tasks import get_task_manager

manager = get_task_manager()

# 提交任务
task_id = manager.submit_task(
    "tasks.example.process_data",
    args=(data,),
    kwargs={"options": options},
    queue="data_queue",
    priority=TaskPriority.HIGH,
    countdown=60  # 延迟60秒执行
)

# 获取任务状态
status = manager.get_task_status(task_id)

# 获取任务结果
result = manager.get_task_result(task_id)

# 取消任务
manager.cancel_task(task_id)

# 重试任务
new_task_id = manager.retry_task(task_id)
```

#### 批量操作

```python
# 批量提交任务
batch_tasks = [
    {
        "name": "tasks.batch.process_item",
        "kwargs": {"item_id": i},
        "queue": "data_queue"
    }
    for i in range(100)
]

task_ids = manager.submit_batch_tasks(batch_tasks, batch_size=20)

# 等待批量任务完成
results = manager.wait_for_tasks(task_ids, timeout=300)
```

### 定时任务管理

#### 添加定时任务

```python
from app.shared.tasks import TaskScheduler, add_cron_task, add_interval_task

# Cron格式定时任务
add_cron_task(
    name="daily_backup",
    task="tasks.maintenance.backup_data",
    cron_expr="0 3 * * *",  # 每天凌晨3点
    kwargs={"backup_type": "daily"}
)

# 间隔定时任务
add_interval_task(
    name="health_check",
    task="tasks.monitoring.health_check",
    seconds=300  # 每5分钟
)
```

#### 管理定时任务

```python
scheduler = TaskScheduler.get_instance()

# 启用/禁用任务
scheduler.enable_periodic_task("daily_backup")
scheduler.disable_periodic_task("health_check")

# 更新任务
scheduler.update_periodic_task(
    "daily_backup",
    schedule="0 2 * * *",  # 改为凌晨2点
    enabled=True
)

# 删除任务
scheduler.remove_periodic_task("old_task")

# 获取任务列表
tasks = scheduler.get_all_periodic_tasks()
```

### 任务监控

#### 启动监控

```python
from app.shared.tasks import get_task_monitor, start_monitoring

# 启动监控
start_monitoring()

monitor = get_task_monitor()

# 获取统计信息
stats = monitor.get_statistics()
print(f"总任务数: {stats.total_tasks}")
print(f"成功率: {stats.success_rate:.2f}%")

# 获取队列状态
queue_status = monitor.get_queue_status()
print(f"队列长度: {queue_status['queue_lengths']}")

# 获取性能指标
performance = monitor.get_performance_metrics()
print(f"平均执行时间: {performance['average_execution_time']:.2f}s")
```

#### 添加监控回调

```python
def alert_callback(alert):
    """告警回调函数"""
    print(f"⚠️ 告警: {alert['message']}")
    # 发送邮件、短信等

def metric_callback(metrics):
    """指标回调函数"""
    # 发送到监控系统
    pass

monitor.add_alert_callback(alert_callback)
monitor.add_metric_callback(metric_callback)
```

### 错误处理和重试

#### 重试配置

```python
from app.shared.tasks import RetryConfig, RetryStrategy, create_retry_decorator

# 自定义重试配置
retry_config = RetryConfig(
    strategy=RetryStrategy.EXPONENTIAL,
    max_retries=5,
    base_delay=60,
    backoff_factor=2.0,
    jitter=True,
    circuit_breaker_enabled=True,
    failure_threshold=10
)

# 创建重试装饰器
@create_retry_decorator(retry_config)
@task(name="tasks.robust.process_data")
def robust_task(self, data):
    # 可能失败的任务逻辑
    if random.random() < 0.3:  # 30%失败率
        raise Exception("Random failure")
    return {"processed": len(data)}
```

#### 错误处理

```python
from app.shared.tasks import get_retry_handler

retry_handler = get_retry_handler()

# 获取错误统计
error_stats = retry_handler.get_error_statistics()
print(f"总错误数: {error_stats['total_errors']}")
print(f"错误分类: {error_stats['error_by_category']}")

# 重置熔断器
retry_handler.reset_circuit_breaker("tasks.problematic.task")

# 清空错误历史
retry_handler.clear_error_history()
```

## 🔧 运维工具

### 启动 Worker

```bash
# 基本启动
python scripts/start_task_worker.py worker

# 指定队列和并发数
python scripts/start_task_worker.py worker -Q default,ai_queue -c 4

# 使用预设配置
python scripts/start_task_worker.py preset ai

# 启动Beat调度器
python scripts/start_task_worker.py beat
```

### 任务监控

```bash
# 系统概览
python scripts/monitor_tasks.py overview

# 队列监控
python scripts/monitor_tasks.py queues

# Worker监控
python scripts/monitor_tasks.py workers

# 实时监控
python scripts/monitor_tasks.py realtime -i 10

# 导出报告
python scripts/monitor_tasks.py export -o report.json
```

### 系统测试

```bash
# 运行集成测试
python scripts/test_task_system.py

# 查看详细测试结果
python scripts/test_task_system.py 2>&1 | tee test_results.log
```

## 📊 性能优化

### Worker 配置

```python
# 生产环境配置
CELERY_WORKER_CONCURRENCY = 8              # 并发数
CELERY_WORKER_MAX_TASKS_PER_CHILD = 2000   # 子进程最大任务数
CELERY_WORKER_PREFETCH_MULTIPLIER = 1      # 预取倍数
CELERY_TASK_TIME_LIMIT = 600               # 任务超时时间
CELERY_TASK_SOFT_TIME_LIMIT = 540          # 软超时时间
```

### 队列优化

```python
# 高优先级队列配置
Queue(
    "urgent_queue",
    priority_exchange,
    routing_key="urgent",
    queue_arguments={
        "x-max-priority": 10,           # 最高优先级
        "x-message-ttl": 300000,        # 5分钟TTL
    }
)
```

### 监控优化

```python
# 监控配置优化
monitor.thresholds = {
    "max_failed_tasks": 50,           # 增加失败任务阈值
    "max_avg_execution_time": 600,    # 增加平均执行时间阈值
    "min_success_rate": 90.0,         # 提高成功率要求
    "max_queue_length": 2000,         # 增加队列长度阈值
}
```

## 🐛 故障排除

### 常见问题

#### 1. 任务不执行

**症状**: 任务提交成功但不执行

**可能原因**:

- Worker 未启动或已停止
- 队列配置错误
- 网络连接问题

**解决方法**:

```bash
# 检查Worker状态
python scripts/monitor_tasks.py workers

# 检查队列状态
python scripts/monitor_tasks.py queues

# 重启Worker
python scripts/start_task_worker.py worker
```

#### 2. 任务执行缓慢

**症状**: 任务执行时间过长

**可能原因**:

- Worker 并发数不足
- 任务本身耗时长
- 系统资源不足

**解决方法**:

```bash
# 增加Worker并发数
python scripts/start_task_worker.py worker -c 8

# 监控系统资源
python scripts/monitor_tasks.py realtime

# 检查性能指标
python scripts/monitor_tasks.py full
```

#### 3. 内存泄漏

**症状**: Worker 内存使用持续增长

**可能原因**:

- 任务处理大量数据未释放
- 子进程任务数过多

**解决方法**:

```bash
# 限制子进程任务数
python scripts/start_task_worker.py worker --max-tasks-per-child 100

# 使用AI队列预设（已优化）
python scripts/start_task_worker.py preset ai
```

#### 4. 任务重复执行

**症状**: 同一个任务被执行多次

**可能原因**:

- 任务确认机制问题
- 网络不稳定导致超时

**解决方法**:

```python
# 确保任务幂等性
@task(name="tasks.idempotent.process")
def idempotent_task(self, data_id):
    # 检查是否已处理
    if is_already_processed(data_id):
        return get_cached_result(data_id)

    # 处理逻辑
    result = process_data(data_id)
    cache_result(data_id, result)
    return result
```

### 日志配置

```python
# 详细日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/chaiguanjia/tasks.log',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'app.shared.tasks': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## 📈 监控指标

### 关键指标

| 指标         | 描述             | 正常范围 |
| ------------ | ---------------- | -------- |
| 成功率       | 任务执行成功率   | > 95%    |
| 平均执行时间 | 任务平均执行时间 | < 300s   |
| 队列长度     | 各队列消息数量   | < 1000   |
| Worker 数量  | 活跃 Worker 数量 | >= 1     |
| CPU 使用率   | 系统 CPU 使用率  | < 80%    |
| 内存使用率   | 系统内存使用率   | < 85%    |

### 告警规则

```python
# 告警阈值配置
ALERT_THRESHOLDS = {
    "high_failure_rate": 10,          # 失败任务数 > 10
    "slow_execution": 300,            # 平均执行时间 > 300s
    "low_success_rate": 80.0,         # 成功率 < 80%
    "queue_overflow": 1000,           # 队列长度 > 1000
    "high_cpu_usage": 80.0,           # CPU使用率 > 80%
    "high_memory_usage": 85.0,        # 内存使用率 > 85%
}
```

## 🔗 集成示例

### 与 FastAPI 集成

```python
from fastapi import FastAPI, BackgroundTasks
from app.shared.tasks import get_task_manager

app = FastAPI()
task_manager = get_task_manager()

@app.post("/process-data/")
async def process_data(data: dict):
    """提交数据处理任务"""
    task_id = task_manager.submit_task(
        "tasks.data.process_user_data",
        kwargs={"data": data},
        priority=TaskPriority.HIGH
    )
    return {"task_id": task_id, "status": "submitted"}

@app.get("/task-status/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态"""
    result = task_manager.get_task_result(task_id)
    if result:
        return {
            "task_id": task_id,
            "status": result.status.value,
            "result": result.result,
            "error": result.error
        }
    return {"error": "Task not found"}
```

### 与消息系统集成

```python
from app.shared.messaging import get_message_consumer
from app.shared.tasks import get_task_manager

@get_message_consumer().handler("user.registered")
def handle_user_registration(message):
    """处理用户注册消息"""
    manager = get_task_manager()

    # 提交用户注册处理任务链
    manager.submit_task(
        "tasks.chain.process_user_registration",
        kwargs={"user_data": message.payload},
        priority=TaskPriority.HIGH
    )
```

## 📋 最佳实践

### 1. 任务设计

- **保持任务小而专一**: 每个任务只做一件事
- **确保幂等性**: 任务可以安全地重复执行
- **处理异常**: 合理捕获和处理异常
- **参数验证**: 验证输入参数的有效性

### 2. 性能优化

- **合理设置超时**: 避免任务无限期运行
- **使用适当队列**: 根据任务特性选择队列
- **监控资源使用**: 及时发现性能瓶颈
- **批量处理**: 对大量小任务进行批量处理

### 3. 运维管理

- **定期监控**: 建立完善的监控体系
- **日志管理**: 保持详细的操作日志
- **备份策略**: 定期备份重要数据
- **故障预案**: 建立完善的故障处理流程

### 4. 安全考虑

- **输入验证**: 严格验证任务参数
- **权限控制**: 限制任务执行权限
- **敏感数据**: 保护敏感信息不泄露
- **审计日志**: 记录重要操作日志

---

## 📞 技术支持

如有问题或建议，请通过以下方式联系：

- 📧 邮箱: <EMAIL>
- 📱 微信群: 柴管家技术交流群
- 🐛 问题反馈: [GitHub Issues](https://github.com/chaiguanjia/issues)

---

_最后更新: 2024-12-19_
