"""
任务重试和错误处理模块

提供完善的任务重试策略和错误处理机制，包括：
- 多种重试策略（固定间隔、指数退避、线性退避）
- 死信队列处理
- 错误分类和处理
- 重试限制和熔断机制
"""

import logging
import random
import time
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Type, Union

from celery import Task as CeleryTask
from celery.exceptions import Retry

from .models import TaskPriority, TaskStatus

logger = logging.getLogger(__name__)


class RetryStrategy(str, Enum):
    """重试策略枚举"""

    FIXED = "fixed"  # 固定间隔
    EXPONENTIAL = "exponential"  # 指数退避
    LINEAR = "linear"  # 线性增长
    FIBONACCI = "fibonacci"  # 斐波那契数列
    RANDOM = "random"  # 随机间隔


class ErrorCategory(str, Enum):
    """错误分类枚举"""

    NETWORK_ERROR = "network_error"  # 网络错误
    TIMEOUT_ERROR = "timeout_error"  # 超时错误
    PERMISSION_ERROR = "permission_error"  # 权限错误
    VALIDATION_ERROR = "validation_error"  # 验证错误
    RESOURCE_ERROR = "resource_error"  # 资源错误
    SYSTEM_ERROR = "system_error"  # 系统错误
    BUSINESS_ERROR = "business_error"  # 业务错误
    UNKNOWN_ERROR = "unknown_error"  # 未知错误


@dataclass
class RetryConfig:
    """重试配置"""

    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    max_retries: int = 3
    base_delay: int = 60  # 基础延迟（秒）
    max_delay: int = 3600  # 最大延迟（秒）
    backoff_factor: float = 2.0  # 退避因子
    jitter: bool = True  # 是否添加随机延迟
    jitter_range: tuple = (0, 30)  # 随机延迟范围（秒）

    # 重试条件
    retry_on_exceptions: List[Type[Exception]] = None
    stop_on_exceptions: List[Type[Exception]] = None

    # 熔断配置
    circuit_breaker_enabled: bool = False
    failure_threshold: int = 5  # 失败阈值
    recovery_timeout: int = 300  # 恢复超时（秒）


@dataclass
class ErrorInfo:
    """错误信息"""

    exception: Exception
    category: ErrorCategory
    retry_count: int
    timestamp: datetime
    task_id: str
    task_name: str
    traceback: str
    context: Dict[str, Any] = None


class RetryHandler:
    """重试处理器"""

    def __init__(self, config: RetryConfig = None):
        self.config = config or RetryConfig()
        self._error_history: List[ErrorInfo] = []
        self._circuit_breaker_state: Dict[str, Dict[str, Any]] = {}

        # 错误分类器
        self._error_classifiers: Dict[Type[Exception], ErrorCategory] = {
            ConnectionError: ErrorCategory.NETWORK_ERROR,
            TimeoutError: ErrorCategory.TIMEOUT_ERROR,
            PermissionError: ErrorCategory.PERMISSION_ERROR,
            ValueError: ErrorCategory.VALIDATION_ERROR,
            MemoryError: ErrorCategory.RESOURCE_ERROR,
            OSError: ErrorCategory.SYSTEM_ERROR,
        }

    def calculate_delay(self, retry_count: int) -> int:
        """
        计算重试延迟

        Args:
            retry_count: 当前重试次数

        Returns:
            延迟秒数
        """
        if self.config.strategy == RetryStrategy.FIXED:
            delay = self.config.base_delay

        elif self.config.strategy == RetryStrategy.EXPONENTIAL:
            delay = min(
                self.config.base_delay * (self.config.backoff_factor**retry_count),
                self.config.max_delay,
            )

        elif self.config.strategy == RetryStrategy.LINEAR:
            delay = min(
                self.config.base_delay + (retry_count * self.config.base_delay),
                self.config.max_delay,
            )

        elif self.config.strategy == RetryStrategy.FIBONACCI:
            fib_value = self._fibonacci(retry_count + 1)
            delay = min(self.config.base_delay * fib_value, self.config.max_delay)

        elif self.config.strategy == RetryStrategy.RANDOM:
            delay = random.randint(
                self.config.base_delay,
                min(self.config.base_delay * 2, self.config.max_delay),
            )

        else:
            delay = self.config.base_delay

        # 添加随机延迟（抖动）
        if self.config.jitter:
            jitter_min, jitter_max = self.config.jitter_range
            jitter = random.randint(jitter_min, jitter_max)
            delay += jitter

        return int(delay)

    def _fibonacci(self, n: int) -> int:
        """计算斐波那契数列第n项"""
        if n <= 1:
            return 1
        a, b = 1, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b

    def classify_error(self, exception: Exception) -> ErrorCategory:
        """
        错误分类

        Args:
            exception: 异常对象

        Returns:
            错误分类
        """
        # 精确匹配
        exception_type = type(exception)
        if exception_type in self._error_classifiers:
            return self._error_classifiers[exception_type]

        # 基类匹配
        for exc_type, category in self._error_classifiers.items():
            if isinstance(exception, exc_type):
                return category

        # 根据异常信息进行智能分类
        error_message = str(exception).lower()

        if any(
            keyword in error_message
            for keyword in ["connection", "network", "dns", "host"]
        ):
            return ErrorCategory.NETWORK_ERROR
        elif any(keyword in error_message for keyword in ["timeout", "time out"]):
            return ErrorCategory.TIMEOUT_ERROR
        elif any(
            keyword in error_message
            for keyword in ["permission", "access", "forbidden"]
        ):
            return ErrorCategory.PERMISSION_ERROR
        elif any(
            keyword in error_message for keyword in ["validation", "invalid", "format"]
        ):
            return ErrorCategory.VALIDATION_ERROR
        elif any(
            keyword in error_message for keyword in ["memory", "disk", "resource"]
        ):
            return ErrorCategory.RESOURCE_ERROR

        return ErrorCategory.UNKNOWN_ERROR

    def should_retry(
        self, exception: Exception, retry_count: int, task_name: str
    ) -> bool:
        """
        判断是否应该重试

        Args:
            exception: 异常对象
            retry_count: 当前重试次数
            task_name: 任务名称

        Returns:
            是否应该重试
        """
        # 检查重试次数限制
        if retry_count >= self.config.max_retries:
            logger.info(
                f"Task {task_name} reached max retries ({self.config.max_retries})"
            )
            return False

        # 检查熔断器状态
        if self.config.circuit_breaker_enabled and self._is_circuit_breaker_open(
            task_name
        ):
            logger.warning(f"Circuit breaker open for task {task_name}")
            return False

        # 检查不可重试的异常
        if self.config.stop_on_exceptions:
            for exc_type in self.config.stop_on_exceptions:
                if isinstance(exception, exc_type):
                    logger.info(
                        f"Task {task_name} failed with non-retryable exception: {exc_type.__name__}"
                    )
                    return False

        # 检查可重试的异常
        if self.config.retry_on_exceptions:
            for exc_type in self.config.retry_on_exceptions:
                if isinstance(exception, exc_type):
                    return True
            # 如果指定了可重试异常列表，但当前异常不在列表中，则不重试
            logger.info(
                f"Task {task_name} failed with non-specified exception: {type(exception).__name__}"
            )
            return False

        # 根据错误分类决定是否重试
        error_category = self.classify_error(exception)

        # 这些错误通常不应该重试
        non_retryable_categories = {
            ErrorCategory.PERMISSION_ERROR,
            ErrorCategory.VALIDATION_ERROR,
            ErrorCategory.BUSINESS_ERROR,
        }

        if error_category in non_retryable_categories:
            logger.info(
                f"Task {task_name} failed with non-retryable error category: {error_category.value}"
            )
            return False

        return True

    def handle_error(
        self,
        task: CeleryTask,
        exception: Exception,
        task_id: str,
        args: tuple,
        kwargs: Dict[str, Any],
        einfo: Any,
    ) -> None:
        """
        处理任务错误

        Args:
            task: Celery任务对象
            exception: 异常对象
            task_id: 任务ID
            args: 任务参数
            kwargs: 任务关键字参数
            einfo: 异常信息
        """
        retry_count = task.request.retries
        task_name = task.name

        # 记录错误信息
        error_info = ErrorInfo(
            exception=exception,
            category=self.classify_error(exception),
            retry_count=retry_count,
            timestamp=datetime.now(timezone.utc),
            task_id=task_id,
            task_name=task_name,
            traceback=str(einfo),
            context={
                "args": args,
                "kwargs": kwargs,
                "request": {
                    "id": task.request.id,
                    "retries": task.request.retries,
                    "eta": task.request.eta,
                    "expires": task.request.expires,
                },
            },
        )

        self._error_history.append(error_info)

        # 更新熔断器状态
        if self.config.circuit_breaker_enabled:
            self._update_circuit_breaker(task_name, False)

        # 决定是否重试
        if self.should_retry(exception, retry_count, task_name):
            delay = self.calculate_delay(retry_count)

            logger.warning(
                f"Task {task_name}[{task_id}] failed (attempt {retry_count + 1}/{self.config.max_retries + 1}), "
                f"retrying in {delay} seconds. Error: {exception}"
            )

            # 执行重试
            raise task.retry(
                exc=exception, countdown=delay, max_retries=self.config.max_retries
            )
        else:
            logger.error(
                f"Task {task_name}[{task_id}] failed permanently after {retry_count} retries. "
                f"Error: {exception}"
            )

            # 发送到死信队列
            self._send_to_dead_letter_queue(error_info)

            # 重新抛出异常
            raise exception

    def _is_circuit_breaker_open(self, task_name: str) -> bool:
        """检查熔断器是否开启"""
        breaker_info = self._circuit_breaker_state.get(task_name)
        if not breaker_info:
            return False

        # 检查是否到了恢复时间
        if breaker_info["state"] == "open":
            recovery_time = breaker_info["opened_at"] + timedelta(
                seconds=self.config.recovery_timeout
            )
            if datetime.now(timezone.utc) >= recovery_time:
                # 进入半开状态
                breaker_info["state"] = "half_open"
                logger.info(f"Circuit breaker for {task_name} entering half-open state")
                return False
            return True

        return breaker_info["state"] == "open"

    def _update_circuit_breaker(self, task_name: str, success: bool) -> None:
        """更新熔断器状态"""
        if task_name not in self._circuit_breaker_state:
            self._circuit_breaker_state[task_name] = {
                "state": "closed",
                "failure_count": 0,
                "success_count": 0,
                "opened_at": None,
            }

        breaker_info = self._circuit_breaker_state[task_name]

        if success:
            breaker_info["success_count"] += 1
            breaker_info["failure_count"] = 0  # 重置失败计数

            # 如果在半开状态且成功，则关闭熔断器
            if breaker_info["state"] == "half_open":
                breaker_info["state"] = "closed"
                logger.info(
                    f"Circuit breaker for {task_name} closed after successful execution"
                )
        else:
            breaker_info["failure_count"] += 1

            # 检查是否需要开启熔断器
            if (
                breaker_info["state"] in ["closed", "half_open"]
                and breaker_info["failure_count"] >= self.config.failure_threshold
            ):
                breaker_info["state"] = "open"
                breaker_info["opened_at"] = datetime.now(timezone.utc)
                logger.warning(
                    f"Circuit breaker for {task_name} opened after {breaker_info['failure_count']} failures"
                )

    def _send_to_dead_letter_queue(self, error_info: ErrorInfo) -> None:
        """发送到死信队列"""
        try:
            from .manager import get_task_manager

            manager = get_task_manager()

            # 构建死信消息
            dead_letter_data = {
                "original_task_id": error_info.task_id,
                "original_task_name": error_info.task_name,
                "error_category": error_info.category.value,
                "error_message": str(error_info.exception),
                "retry_count": error_info.retry_count,
                "failed_at": error_info.timestamp.isoformat(),
                "traceback": error_info.traceback,
                "context": error_info.context,
            }

            # 发送到死信处理任务
            manager.submit_task(
                task_name="tasks.system.process_dead_letter",
                kwargs={"dead_letter_data": dead_letter_data},
                queue="dead_letter_queue",
                priority=TaskPriority.LOW,
            )

            logger.info(f"Sent task {error_info.task_id} to dead letter queue")

        except Exception as e:
            logger.error(f"Failed to send task to dead letter queue: {e}")

    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        if not self._error_history:
            return {
                "total_errors": 0,
                "error_by_category": {},
                "error_by_task": {},
                "recent_errors": [],
            }

        # 按分类统计
        error_by_category = {}
        for error in self._error_history:
            category = error.category.value
            error_by_category[category] = error_by_category.get(category, 0) + 1

        # 按任务统计
        error_by_task = {}
        for error in self._error_history:
            task_name = error.task_name
            error_by_task[task_name] = error_by_task.get(task_name, 0) + 1

        # 最近的错误
        recent_errors = [
            {
                "task_id": error.task_id,
                "task_name": error.task_name,
                "category": error.category.value,
                "message": str(error.exception),
                "timestamp": error.timestamp.isoformat(),
                "retry_count": error.retry_count,
            }
            for error in sorted(
                self._error_history, key=lambda x: x.timestamp, reverse=True
            )[:10]
        ]

        return {
            "total_errors": len(self._error_history),
            "error_by_category": error_by_category,
            "error_by_task": error_by_task,
            "recent_errors": recent_errors,
            "circuit_breaker_states": self._circuit_breaker_state.copy(),
        }

    def clear_error_history(self) -> None:
        """清空错误历史"""
        self._error_history.clear()
        logger.info("Error history cleared")

    def add_error_classifier(
        self, exception_type: Type[Exception], category: ErrorCategory
    ) -> None:
        """添加错误分类器"""
        self._error_classifiers[exception_type] = category
        logger.info(
            f"Added error classifier: {exception_type.__name__} -> {category.value}"
        )

    def reset_circuit_breaker(self, task_name: str) -> None:
        """重置熔断器状态"""
        if task_name in self._circuit_breaker_state:
            del self._circuit_breaker_state[task_name]
            logger.info(f"Reset circuit breaker for task {task_name}")


# 全局重试处理器
_retry_handler: Optional[RetryHandler] = None


def get_retry_handler() -> RetryHandler:
    """获取全局重试处理器实例"""
    global _retry_handler

    if _retry_handler is None:
        _retry_handler = RetryHandler()

    return _retry_handler


def create_retry_decorator(config: RetryConfig = None):
    """
    创建重试装饰器

    Args:
        config: 重试配置

    Returns:
        装饰器函数
    """
    handler = RetryHandler(config)

    def decorator(func):
        def wrapper(self, *args, **kwargs):
            try:
                result = func(self, *args, **kwargs)

                # 更新熔断器状态（成功）
                if handler.config.circuit_breaker_enabled:
                    handler._update_circuit_breaker(self.name, True)

                return result

            except Exception as exc:
                handler.handle_error(
                    task=self,
                    exception=exc,
                    task_id=self.request.id,
                    args=args,
                    kwargs=kwargs,
                    einfo=exc,
                )

        return wrapper

    return decorator
