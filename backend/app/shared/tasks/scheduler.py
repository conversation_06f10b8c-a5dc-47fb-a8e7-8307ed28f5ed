"""
任务调度器模块

管理定时任务的调度，包括：
- Celery Beat定时任务配置
- Cron表达式解析
- 定时任务的添加、删除、修改
- 定时任务状态监控
"""

import logging
import os
from datetime import datetime, timezone
from threading import RLock
from typing import Any, Dict, List, Optional, Union

from celery.beat import ScheduleEntry
from celery.schedules import crontab, schedule

from .app import get_celery_app
from .models import ScheduledTask

logger = logging.getLogger(__name__)


class TaskScheduler:
    """任务调度器"""

    _instance: Optional["TaskScheduler"] = None
    _lock = RLock()

    def __init__(self):
        self._scheduled_tasks: Dict[str, ScheduledTask] = {}
        self._beat_schedule: Dict[str, Dict[str, Any]] = {}
        self._celery_app = get_celery_app()

    @classmethod
    def get_instance(cls) -> "TaskScheduler":
        """获取单例实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def add_periodic_task(
        self,
        name: str,
        task: str,
        schedule: Union[str, Dict[str, Any]],
        args: List[Any] = None,
        kwargs: Dict[str, Any] = None,
        options: Dict[str, Any] = None,
        enabled: bool = True,
    ) -> bool:
        """
        添加定时任务

        Args:
            name: 任务名称
            task: 任务函数名
            schedule: 调度配置（cron表达式或间隔配置）
            args: 任务参数
            kwargs: 任务关键字参数
            options: 任务选项
            enabled: 是否启用

        Returns:
            是否添加成功
        """
        with self._lock:
            try:
                # 创建定时任务对象
                scheduled_task = ScheduledTask(
                    name=name,
                    task=task,
                    schedule=schedule,
                    args=args or [],
                    kwargs=kwargs or {},
                    options=options or {},
                    enabled=enabled,
                )

                # 转换为Beat调度条目
                beat_entry = scheduled_task.to_beat_schedule_entry()

                # 存储到内存
                self._scheduled_tasks[name] = scheduled_task
                self._beat_schedule[name] = beat_entry

                # 更新Celery Beat配置
                self._update_celery_beat_schedule()

                logger.info(f"Added periodic task: {name}")
                return True

            except Exception as e:
                logger.error(f"Failed to add periodic task {name}: {e}")
                return False

    def remove_periodic_task(self, name: str) -> bool:
        """
        删除定时任务

        Args:
            name: 任务名称

        Returns:
            是否删除成功
        """
        with self._lock:
            if name not in self._scheduled_tasks:
                logger.warning(f"Periodic task {name} not found")
                return False

            try:
                # 从内存中删除
                del self._scheduled_tasks[name]
                del self._beat_schedule[name]

                # 更新Celery Beat配置
                self._update_celery_beat_schedule()

                logger.info(f"Removed periodic task: {name}")
                return True

            except Exception as e:
                logger.error(f"Failed to remove periodic task {name}: {e}")
                return False

    def update_periodic_task(
        self,
        name: str,
        schedule: Optional[Union[str, Dict[str, Any]]] = None,
        args: Optional[List[Any]] = None,
        kwargs: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None,
        enabled: Optional[bool] = None,
    ) -> bool:
        """
        更新定时任务

        Args:
            name: 任务名称
            schedule: 新的调度配置
            args: 新的任务参数
            kwargs: 新的任务关键字参数
            options: 新的任务选项
            enabled: 是否启用

        Returns:
            是否更新成功
        """
        with self._lock:
            if name not in self._scheduled_tasks:
                logger.warning(f"Periodic task {name} not found")
                return False

            try:
                scheduled_task = self._scheduled_tasks[name]

                # 更新任务属性
                if schedule is not None:
                    scheduled_task.schedule = schedule
                if args is not None:
                    scheduled_task.args = args
                if kwargs is not None:
                    scheduled_task.kwargs = kwargs
                if options is not None:
                    scheduled_task.options = options
                if enabled is not None:
                    scheduled_task.enabled = enabled

                # 重新生成Beat条目
                beat_entry = scheduled_task.to_beat_schedule_entry()
                self._beat_schedule[name] = beat_entry

                # 更新Celery Beat配置
                self._update_celery_beat_schedule()

                logger.info(f"Updated periodic task: {name}")
                return True

            except Exception as e:
                logger.error(f"Failed to update periodic task {name}: {e}")
                return False

    def enable_periodic_task(self, name: str) -> bool:
        """
        启用定时任务

        Args:
            name: 任务名称

        Returns:
            是否成功
        """
        return self.update_periodic_task(name, enabled=True)

    def disable_periodic_task(self, name: str) -> bool:
        """
        禁用定时任务

        Args:
            name: 任务名称

        Returns:
            是否成功
        """
        return self.update_periodic_task(name, enabled=False)

    def get_periodic_task(self, name: str) -> Optional[ScheduledTask]:
        """
        获取定时任务

        Args:
            name: 任务名称

        Returns:
            定时任务对象或None
        """
        return self._scheduled_tasks.get(name)

    def get_all_periodic_tasks(self) -> Dict[str, ScheduledTask]:
        """获取所有定时任务"""
        return self._scheduled_tasks.copy()

    def get_enabled_periodic_tasks(self) -> Dict[str, ScheduledTask]:
        """获取所有启用的定时任务"""
        return {
            name: task for name, task in self._scheduled_tasks.items() if task.enabled
        }

    def get_disabled_periodic_tasks(self) -> Dict[str, ScheduledTask]:
        """获取所有禁用的定时任务"""
        return {
            name: task
            for name, task in self._scheduled_tasks.items()
            if not task.enabled
        }

    def is_task_scheduled(self, name: str) -> bool:
        """
        检查任务是否已调度

        Args:
            name: 任务名称

        Returns:
            是否已调度
        """
        return name in self._scheduled_tasks

    def get_task_count(self) -> int:
        """获取定时任务总数"""
        return len(self._scheduled_tasks)

    def get_enabled_task_count(self) -> int:
        """获取启用的定时任务数量"""
        return len(self.get_enabled_periodic_tasks())

    def get_beat_schedule(self) -> Dict[str, Dict[str, Any]]:
        """获取Beat调度配置"""
        return self._beat_schedule.copy()

    def _update_celery_beat_schedule(self) -> None:
        """更新Celery Beat调度配置"""
        try:
            # 只包含启用的任务
            enabled_schedule = {
                name: entry
                for name, entry in self._beat_schedule.items()
                if self._scheduled_tasks[name].enabled
            }

            # 更新Celery应用配置
            self._celery_app.conf.beat_schedule = enabled_schedule

            logger.debug(
                f"Updated Celery beat schedule with {len(enabled_schedule)} tasks"
            )

        except Exception as e:
            logger.error(f"Failed to update Celery beat schedule: {e}")

    def add_cron_task(
        self,
        name: str,
        task: str,
        minute: str = "*",
        hour: str = "*",
        day_of_week: str = "*",
        day_of_month: str = "*",
        month_of_year: str = "*",
        **kwargs,
    ) -> bool:
        """
        添加Cron格式的定时任务

        Args:
            name: 任务名称
            task: 任务函数名
            minute: 分钟 (0-59)
            hour: 小时 (0-23)
            day_of_week: 星期几 (0-6, 0是周日)
            day_of_month: 月份中的第几天 (1-31)
            month_of_year: 月份 (1-12)
            **kwargs: 其他参数

        Returns:
            是否添加成功
        """
        schedule_obj = crontab(
            minute=minute,
            hour=hour,
            day_of_week=day_of_week,
            day_of_month=day_of_month,
            month_of_year=month_of_year,
        )

        return self.add_periodic_task(
            name=name, task=task, schedule=schedule_obj, **kwargs
        )

    def add_interval_task(self, name: str, task: str, seconds: int, **kwargs) -> bool:
        """
        添加间隔执行的定时任务

        Args:
            name: 任务名称
            task: 任务函数名
            seconds: 间隔秒数
            **kwargs: 其他参数

        Returns:
            是否添加成功
        """
        schedule_obj = schedule(run_every=seconds)

        return self.add_periodic_task(
            name=name, task=task, schedule=schedule_obj, **kwargs
        )

    def parse_cron_expression(self, cron_expr: str) -> Optional[crontab]:
        """
        解析Cron表达式

        Args:
            cron_expr: Cron表达式 (格式: "分 时 日 月 周")

        Returns:
            Crontab对象或None
        """
        try:
            parts = cron_expr.strip().split()
            if len(parts) != 5:
                logger.error(f"Invalid cron expression: {cron_expr}")
                return None

            minute, hour, day, month, day_of_week = parts

            return crontab(
                minute=minute,
                hour=hour,
                day_of_month=day,
                month_of_year=month,
                day_of_week=day_of_week,
            )

        except Exception as e:
            logger.error(f"Failed to parse cron expression {cron_expr}: {e}")
            return None

    def validate_cron_expression(self, cron_expr: str) -> bool:
        """
        验证Cron表达式是否有效

        Args:
            cron_expr: Cron表达式

        Returns:
            是否有效
        """
        return self.parse_cron_expression(cron_expr) is not None

    def get_next_run_time(self, name: str) -> Optional[datetime]:
        """
        获取定时任务的下次执行时间

        Args:
            name: 任务名称

        Returns:
            下次执行时间或None
        """
        if name not in self._scheduled_tasks:
            return None

        try:
            beat_entry = self._beat_schedule.get(name)
            if not beat_entry:
                return None

            schedule_obj = beat_entry["schedule"]
            if hasattr(schedule_obj, "remaining_estimate"):
                # 获取下次执行的剩余时间
                remaining = schedule_obj.remaining_estimate(None)
                if remaining:
                    return datetime.now(timezone.utc) + remaining

            return None

        except Exception as e:
            logger.error(f"Failed to get next run time for task {name}: {e}")
            return None

    def update_task_last_run(self, name: str, run_time: datetime = None) -> None:
        """
        更新任务的最后执行时间

        Args:
            name: 任务名称
            run_time: 执行时间，默认为当前时间
        """
        if name in self._scheduled_tasks:
            task = self._scheduled_tasks[name]
            task.last_run = run_time or datetime.now(timezone.utc)
            task.total_runs += 1

    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态信息"""
        enabled_tasks = self.get_enabled_periodic_tasks()
        disabled_tasks = self.get_disabled_periodic_tasks()

        return {
            "total_tasks": len(self._scheduled_tasks),
            "enabled_tasks": len(enabled_tasks),
            "disabled_tasks": len(disabled_tasks),
            "task_list": {
                name: {
                    "enabled": task.enabled,
                    "last_run": task.last_run.isoformat() if task.last_run else None,
                    "total_runs": task.total_runs,
                    "next_run": (
                        self.get_next_run_time(name).isoformat()
                        if self.get_next_run_time(name)
                        else None
                    ),
                }
                for name, task in self._scheduled_tasks.items()
            },
        }

    def clear_all_tasks(self) -> None:
        """清空所有定时任务（主要用于测试）"""
        with self._lock:
            self._scheduled_tasks.clear()
            self._beat_schedule.clear()
            self._update_celery_beat_schedule()
            logger.info("Cleared all periodic tasks")


# 预定义的常用调度模式
class SchedulePatterns:
    """常用调度模式"""

    # 每分钟执行
    EVERY_MINUTE = crontab(minute="*")

    # 每5分钟执行
    EVERY_5_MINUTES = crontab(minute="*/5")

    # 每10分钟执行
    EVERY_10_MINUTES = crontab(minute="*/10")

    # 每30分钟执行
    EVERY_30_MINUTES = crontab(minute="*/30")

    # 每小时执行
    EVERY_HOUR = crontab(minute=0)

    # 每天午夜执行
    DAILY_MIDNIGHT = crontab(hour=0, minute=0)

    # 每天上午9点执行
    DAILY_9AM = crontab(hour=9, minute=0)

    # 每周一午夜执行
    WEEKLY_MONDAY = crontab(hour=0, minute=0, day_of_week=1)

    # 每月1号午夜执行
    MONTHLY_FIRST = crontab(hour=0, minute=0, day_of_month=1)

    # 工作日每天上午9点执行
    WEEKDAYS_9AM = crontab(hour=9, minute=0, day_of_week="1-5")


# 便捷函数
def add_cron_task(name: str, task: str, cron_expr: str, **kwargs) -> bool:
    """
    便捷函数：添加Cron定时任务

    Args:
        name: 任务名称
        task: 任务函数名
        cron_expr: Cron表达式
        **kwargs: 其他参数

    Returns:
        是否添加成功
    """
    scheduler = TaskScheduler.get_instance()
    schedule_obj = scheduler.parse_cron_expression(cron_expr)

    if schedule_obj is None:
        return False

    return scheduler.add_periodic_task(
        name=name, task=task, schedule=schedule_obj, **kwargs
    )


def add_interval_task(name: str, task: str, seconds: int, **kwargs) -> bool:
    """
    便捷函数：添加间隔定时任务

    Args:
        name: 任务名称
        task: 任务函数名
        seconds: 间隔秒数
        **kwargs: 其他参数

    Returns:
        是否添加成功
    """
    scheduler = TaskScheduler.get_instance()
    return scheduler.add_interval_task(name, task, seconds, **kwargs)
