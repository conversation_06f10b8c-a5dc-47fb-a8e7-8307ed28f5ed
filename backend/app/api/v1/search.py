"""
搜索系统 API 端点

提供全文搜索、多字段搜索、聚合查询等功能的 REST API。
"""

import logging
from typing import Any, Dict, List, Optional

from app.shared.search import (
    SearchFilter,
    SearchHighlight,
    SearchQuery,
    SearchResult,
    SearchSort,
    get_search_service,
    init_elasticsearch,
)
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/search", tags=["搜索"])


# 请求模型
class SearchRequest(BaseModel):
    """搜索请求"""

    query: str = Field("", description="搜索关键词")
    index: str = Field(..., description="搜索索引")
    fields: List[str] = Field(default_factory=list, description="搜索字段")
    filters: List[Dict[str, Any]] = Field(default_factory=list, description="过滤条件")
    sort: List[Dict[str, Any]] = Field(default_factory=list, description="排序规则")
    highlight_fields: List[str] = Field(default_factory=list, description="高亮字段")
    size: int = Field(10, ge=1, le=100, description="返回结果数量")
    from_: int = Field(0, ge=0, alias="from", description="起始位置")
    include_source: bool = Field(True, description="是否包含原始数据")
    min_score: Optional[float] = Field(None, description="最小相关性得分")


class IndexDocumentRequest(BaseModel):
    """索引文档请求"""

    index: str = Field(..., description="索引名称")
    document: Dict[str, Any] = Field(..., description="文档内容")
    doc_id: Optional[str] = Field(None, description="文档ID")
    refresh: bool = Field(False, description="是否立即刷新索引")


class BulkIndexRequest(BaseModel):
    """批量索引请求"""

    index: str = Field(..., description="索引名称")
    documents: List[Dict[str, Any]] = Field(..., description="文档列表")
    doc_ids: Optional[List[str]] = Field(None, description="文档ID列表")
    refresh: bool = Field(False, description="是否立即刷新索引")


class UpdateDocumentRequest(BaseModel):
    """更新文档请求"""

    index: str = Field(..., description="索引名称")
    doc_id: str = Field(..., description="文档ID")
    document: Dict[str, Any] = Field(..., description="更新内容")
    refresh: bool = Field(False, description="是否立即刷新索引")


class AnalyzeTextRequest(BaseModel):
    """文本分析请求"""

    text: str = Field(..., description="待分析文本")
    analyzer: str = Field("ik_max_word", description="分析器")
    index: Optional[str] = Field(None, description="索引名称")


# 响应模型
class SearchResponse(BaseModel):
    """搜索响应"""

    success: bool = Field(..., description="请求是否成功")
    data: Optional[SearchResult] = Field(None, description="搜索结果")
    message: str = Field("", description="响应消息")


class IndexResponse(BaseModel):
    """索引响应"""

    success: bool = Field(..., description="请求是否成功")
    doc_id: Optional[str] = Field(None, description="文档ID")
    message: str = Field("", description="响应消息")


class BulkResponse(BaseModel):
    """批量操作响应"""

    success: bool = Field(..., description="请求是否成功")
    indexed_count: int = Field(0, description="成功索引数量")
    error_count: int = Field(0, description="失败数量")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    message: str = Field("", description="响应消息")


class AnalyzeResponse(BaseModel):
    """文本分析响应"""

    success: bool = Field(..., description="请求是否成功")
    tokens: List[str] = Field(default_factory=list, description="分词结果")
    message: str = Field("", description="响应消息")


# API 端点
@router.post("/search", response_model=SearchResponse, summary="全文搜索")
async def search_documents(request: SearchRequest):
    """
    全文搜索文档

    支持多字段搜索、过滤、排序、高亮等功能。
    """
    try:
        search_service = get_search_service()

        # 构建搜索查询
        search_query = SearchQuery(
            query=request.query,
            fields=request.fields,
            size=request.size,
            from_=request.from_,
            include_source=request.include_source,
            min_score=request.min_score,
        )

        # 添加过滤条件
        for filter_data in request.filters:
            search_filter = SearchFilter(
                field=filter_data.get("field", ""),
                values=filter_data.get("values", []),
                operator=filter_data.get("operator", "terms"),
            )
            search_query.filters.append(search_filter)

        # 添加排序规则
        for sort_data in request.sort:
            search_sort = SearchSort(
                field=sort_data.get("field", ""),
                order=sort_data.get("order", "desc"),
                missing=sort_data.get("missing"),
            )
            search_query.sort.append(search_sort)

        # 添加高亮配置
        if request.highlight_fields:
            search_query.highlight = SearchHighlight(
                fields=request.highlight_fields,
                fragment_size=150,
                number_of_fragments=3,
            )

        # 执行搜索
        result = await search_service.search(request.index, search_query)

        return SearchResponse(
            success=True, data=result, message=f"搜索完成，找到 {result.total} 条结果"
        )

    except Exception as e:
        logger.error(f"搜索失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}",
        )


@router.post("/index", response_model=IndexResponse, summary="索引文档")
async def index_document(request: IndexDocumentRequest):
    """
    索引单个文档

    将文档添加到指定索引中。
    """
    try:
        search_service = get_search_service()

        doc_id = await search_service.index_document(
            index=request.index,
            document=request.document,
            doc_id=request.doc_id,
            refresh=request.refresh,
        )

        if doc_id:
            return IndexResponse(success=True, doc_id=doc_id, message="文档索引成功")
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="文档索引失败"
            )

    except Exception as e:
        logger.error(f"索引文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"索引文档失败: {str(e)}",
        )


@router.post("/bulk", response_model=BulkResponse, summary="批量索引")
async def bulk_index_documents(request: BulkIndexRequest):
    """
    批量索引文档

    一次性索引多个文档，提高索引效率。
    """
    try:
        search_service = get_search_service()

        result = await search_service.bulk_index_documents(
            index=request.index,
            documents=request.documents,
            doc_ids=request.doc_ids,
            refresh=request.refresh,
        )

        # 统计结果
        indexed_count = 0
        error_count = 0
        errors = []

        for item in result.get("items", []):
            if "index" in item:
                if item["index"].get("status", 0) < 300:
                    indexed_count += 1
                else:
                    error_count += 1
                    error_info = item["index"].get("error", {})
                    errors.append(f"文档索引失败: {error_info}")

        return BulkResponse(
            success=error_count == 0,
            indexed_count=indexed_count,
            error_count=error_count,
            errors=errors,
            message=f"批量索引完成: 成功 {indexed_count}, 失败 {error_count}",
        )

    except Exception as e:
        logger.error(f"批量索引失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量索引失败: {str(e)}",
        )


@router.put("/document", response_model=IndexResponse, summary="更新文档")
async def update_document(request: UpdateDocumentRequest):
    """
    更新现有文档

    部分更新指定文档的内容。
    """
    try:
        search_service = get_search_service()

        success = await search_service.update_document(
            index=request.index,
            doc_id=request.doc_id,
            document=request.document,
            refresh=request.refresh,
        )

        if success:
            return IndexResponse(
                success=True, doc_id=request.doc_id, message="文档更新成功"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="文档不存在或更新失败"
            )

    except Exception as e:
        logger.error(f"更新文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新文档失败: {str(e)}",
        )


@router.delete(
    "/document/{index}/{doc_id}", response_model=IndexResponse, summary="删除文档"
)
async def delete_document(
    index: str,
    doc_id: str,
    refresh: bool = Query(False, description="是否立即刷新索引"),
):
    """
    删除指定文档

    从索引中删除指定的文档。
    """
    try:
        search_service = get_search_service()

        success = await search_service.delete_document(
            index=index, doc_id=doc_id, refresh=refresh
        )

        if success:
            return IndexResponse(success=True, doc_id=doc_id, message="文档删除成功")
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="文档不存在或删除失败"
            )

    except Exception as e:
        logger.error(f"删除文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除文档失败: {str(e)}",
        )


@router.get("/document/{index}/{doc_id}", summary="获取文档")
async def get_document(index: str, doc_id: str):
    """
    获取指定文档

    根据文档ID获取完整的文档内容。
    """
    try:
        search_service = get_search_service()

        document = await search_service.get_document(index, doc_id)

        if document:
            return {"success": True, "data": document, "message": "文档获取成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="文档不存在"
            )

    except Exception as e:
        logger.error(f"获取文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文档失败: {str(e)}",
        )


@router.post("/analyze", response_model=AnalyzeResponse, summary="文本分析")
async def analyze_text(request: AnalyzeTextRequest):
    """
    分析文本

    使用指定的分析器对文本进行分词分析。
    """
    try:
        search_service = get_search_service()

        tokens = await search_service.analyze_text(
            text=request.text, analyzer=request.analyzer, index=request.index
        )

        return AnalyzeResponse(
            success=True,
            tokens=tokens,
            message=f"文本分析完成，共 {len(tokens)} 个词元",
        )

    except Exception as e:
        logger.error(f"文本分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文本分析失败: {str(e)}",
        )


@router.get("/suggest/{index}", summary="搜索建议")
async def get_suggestions(
    index: str,
    text: str = Query(..., description="搜索文本"),
    field: str = Query("title", description="建议字段"),
    size: int = Query(5, ge=1, le=20, description="建议数量"),
):
    """
    获取搜索建议

    根据输入文本提供搜索建议。
    """
    try:
        search_service = get_search_service()

        suggestions = await search_service.suggest(
            index=index, text=text, field=field, size=size
        )

        return {
            "success": True,
            "data": suggestions,
            "message": f"获取到 {len(suggestions)} 个建议",
        }

    except Exception as e:
        logger.error(f"获取搜索建议失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取搜索建议失败: {str(e)}",
        )


@router.get("/count/{index}", summary="统计文档数量")
async def count_documents(
    index: str, q: Optional[str] = Query(None, description="查询条件")
):
    """
    统计索引中的文档数量

    可选择性地根据查询条件统计匹配的文档数量。
    """
    try:
        search_service = get_search_service()

        query = None
        if q:
            query = {"query_string": {"query": q}}

        count = await search_service.count_documents(index, query)

        return {
            "success": True,
            "data": {"count": count},
            "message": f"索引 {index} 中有 {count} 个文档",
        }

    except Exception as e:
        logger.error(f"统计文档数量失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"统计文档数量失败: {str(e)}",
        )


# 业务特定的搜索端点
@router.get("/messages", response_model=SearchResponse, summary="搜索消息")
async def search_messages(
    q: str = Query(..., description="搜索关键词"),
    category: Optional[str] = Query(None, description="消息分类"),
    sender: Optional[str] = Query(None, description="发送者"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    size: int = Query(10, ge=1, le=100, description="返回数量"),
    from_: int = Query(0, ge=0, alias="from", description="起始位置"),
):
    """
    搜索消息

    在消息索引中搜索指定关键词的消息。
    """
    try:
        search_service = get_search_service()

        # 构建过滤条件
        filters = {}
        if category:
            filters["category"] = [category]
        if sender:
            filters["sender"] = [sender]
        if start_date and end_date:
            filters["created_at"] = [start_date, end_date]

        result = await search_service.search_messages(
            query=q, filters=filters, size=size, from_=from_
        )

        return SearchResponse(
            success=True,
            data=result,
            message=f"消息搜索完成，找到 {result.total} 条结果",
        )

    except Exception as e:
        logger.error(f"搜索消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索消息失败: {str(e)}",
        )


@router.get("/knowledge", response_model=SearchResponse, summary="搜索知识库")
async def search_knowledge(
    q: str = Query(..., description="搜索关键词"),
    category: Optional[str] = Query(None, description="知识分类"),
    size: int = Query(10, ge=1, le=50, description="返回数量"),
):
    """
    搜索知识库

    在知识库中搜索相关内容。
    """
    try:
        search_service = get_search_service()

        result = await search_service.search_knowledge(
            query=q, category=category, size=size
        )

        return SearchResponse(
            success=True,
            data=result,
            message=f"知识库搜索完成，找到 {result.total} 条结果",
        )

    except Exception as e:
        logger.error(f"搜索知识库失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索知识库失败: {str(e)}",
        )


@router.get("/health", summary="搜索系统健康检查")
async def search_health_check():
    """
    搜索系统健康检查

    检查 Elasticsearch 连接和基本功能。
    """
    try:
        from app.shared.search import get_es_client

        es_client = get_es_client()

        # 检查连接
        if not es_client.is_connected:
            await init_elasticsearch()

        if await es_client.ping():
            cluster_info = await es_client.get_cluster_info()

            return {
                "success": True,
                "data": {
                    "status": "healthy",
                    "elasticsearch": {
                        "connected": True,
                        "cluster_name": cluster_info.get("cluster_name"),
                        "version": cluster_info.get("version"),
                        "status": cluster_info.get("status"),
                        "nodes": cluster_info.get("number_of_nodes"),
                        "indices": cluster_info.get("indices_count"),
                        "documents": cluster_info.get("docs_count"),
                    },
                },
                "message": "搜索系统运行正常",
            }
        else:
            return {
                "success": False,
                "data": {"status": "unhealthy"},
                "message": "Elasticsearch 连接失败",
            }

    except Exception as e:
        logger.error(f"搜索系统健康检查失败: {e}")
        return {
            "success": False,
            "data": {"status": "error", "error": str(e)},
            "message": "搜索系统检查异常",
        }
