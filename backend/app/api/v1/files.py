"""
文件管理API端点

提供文件上传、下载、管理等RESTful API接口
"""

import logging
from typing import List, Optional

from app.shared.storage import (
    BulkFileOperation,
    FileAccessType,
    FileDownloadResponse,
    FileManager,
    FileMetadata,
    FileOperationResult,
    FileSearchRequest,
    FileSearchResponse,
    FileStorageStats,
    FileThumbnailRequest,
    FileThumbnailResponse,
    FileUploadRequest,
    FileUploadResponse,
    get_file_manager,
)
from fastapi import APIRouter, Depends, File, Form, HTTPException, Query, UploadFile
from fastapi.responses import StreamingResponse

logger = logging.getLogger(__name__)


# 创建路由器
router = APIRouter(prefix="/files", tags=["文件管理"])


# API响应模型
class FileUploadAPIRequest(FileUploadRequest):
    """API文件上传请求模型"""

    pass


class FileListResponse(FileSearchResponse):
    """文件列表响应模型"""

    pass


class FileStatsResponse(FileStorageStats):
    """文件统计响应模型"""

    pass


@router.post("/upload", response_model=FileUploadResponse, summary="上传文件")
async def upload_file(
    file: UploadFile = File(..., description="要上传的文件"),
    filename: Optional[str] = Form(None, description="自定义文件名"),
    access_type: FileAccessType = Form(FileAccessType.PRIVATE, description="访问类型"),
    folder_path: Optional[str] = Form(None, description="文件夹路径"),
    generate_thumbnail: bool = Form(True, description="是否生成缩略图"),
    expires_in: Optional[int] = Form(None, description="过期时间（秒）"),
    file_manager: FileManager = Depends(get_file_manager),
):
    """
    上传文件接口

    - **file**: 要上传的文件
    - **filename**: 自定义文件名（可选）
    - **access_type**: 访问类型（private/public/shared）
    - **folder_path**: 文件夹路径（可选）
    - **generate_thumbnail**: 是否生成缩略图
    - **expires_in**: 过期时间（秒）
    """
    try:
        # 验证文件
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file selected")

        # 创建上传请求
        upload_request = FileUploadRequest(
            filename=filename or file.filename,
            content_type=file.content_type,
            file_size=None,  # 将在文件管理器中计算
            access_type=access_type,
            folder_path=folder_path,
            generate_thumbnail=generate_thumbnail,
            expires_in=expires_in,
        )

        # 执行上传
        result = await file_manager.upload_file(
            file=file, request=upload_request, owner_id=None  # TODO: 从认证中获取用户ID
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in upload_file API: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/download/{file_id}",
    response_model=FileDownloadResponse,
    summary="获取文件下载链接",
)
async def get_download_url(
    file_id: str, file_manager: FileManager = Depends(get_file_manager)
):
    """
    获取文件下载链接

    - **file_id**: 文件ID
    """
    try:
        result = await file_manager.download_file(
            file_id=file_id, user_id=None  # TODO: 从认证中获取用户ID
        )
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_download_url API: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/{file_id}/thumbnail",
    response_model=FileThumbnailResponse,
    summary="获取文件缩略图",
)
async def get_file_thumbnail(
    file_id: str,
    width: int = Query(200, description="缩略图宽度"),
    height: int = Query(200, description="缩略图高度"),
    quality: int = Query(85, description="图片质量"),
    file_manager: FileManager = Depends(get_file_manager),
):
    """
    获取文件缩略图

    - **file_id**: 文件ID
    - **width**: 缩略图宽度
    - **height**: 缩略图高度
    - **quality**: 图片质量（1-100）
    """
    try:
        request = FileThumbnailRequest(
            file_id=file_id, width=width, height=height, quality=quality
        )

        result = await file_manager.get_file_thumbnail(
            file_id=file_id, request=request, user_id=None  # TODO: 从认证中获取用户ID
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_file_thumbnail API: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{file_id}", summary="删除文件")
async def delete_file(
    file_id: str, file_manager: FileManager = Depends(get_file_manager)
):
    """
    删除文件

    - **file_id**: 文件ID
    """
    try:
        success = await file_manager.delete_file(
            file_id=file_id, user_id=None  # TODO: 从认证中获取用户ID
        )

        if success:
            return {"message": "File deleted successfully", "file_id": file_id}
        else:
            raise HTTPException(
                status_code=404, detail="File not found or deletion failed"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in delete_file API: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/{file_id}/metadata", response_model=FileMetadata, summary="获取文件元数据"
)
async def get_file_metadata(
    file_id: str, file_manager: FileManager = Depends(get_file_manager)
):
    """
    获取文件元数据

    - **file_id**: 文件ID
    """
    try:
        metadata = await file_manager.get_file_metadata(file_id)

        if not metadata:
            raise HTTPException(status_code=404, detail="File not found")

        return metadata

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_file_metadata API: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=FileListResponse, summary="搜索文件")
async def search_files(
    query: Optional[str] = Query(None, description="搜索关键词"),
    file_type: Optional[str] = Query(None, description="文件类型"),
    access_type: Optional[FileAccessType] = Query(None, description="访问类型"),
    folder_path: Optional[str] = Query(None, description="文件夹路径"),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    file_manager: FileManager = Depends(get_file_manager),
):
    """
    搜索文件

    - **query**: 搜索关键词
    - **file_type**: 文件类型过滤
    - **access_type**: 访问类型过滤
    - **folder_path**: 文件夹路径过滤
    - **page**: 页码
    - **page_size**: 每页数量
    - **sort_by**: 排序字段
    - **sort_order**: 排序方向（asc/desc）
    """
    try:
        request = FileSearchRequest(
            query=query,
            file_type=file_type,
            access_type=access_type,
            folder_path=folder_path,
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order,
        )

        result = await file_manager.search_files(
            request=request, user_id=None  # TODO: 从认证中获取用户ID
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in search_files API: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/bulk-upload", response_model=List[FileUploadResponse], summary="批量上传文件"
)
async def bulk_upload_files(
    files: List[UploadFile] = File(..., description="要上传的文件列表"),
    access_type: FileAccessType = Form(FileAccessType.PRIVATE, description="访问类型"),
    folder_path: Optional[str] = Form(None, description="文件夹路径"),
    generate_thumbnail: bool = Form(True, description="是否生成缩略图"),
    file_manager: FileManager = Depends(get_file_manager),
):
    """
    批量上传文件

    - **files**: 要上传的文件列表
    - **access_type**: 访问类型
    - **folder_path**: 文件夹路径
    - **generate_thumbnail**: 是否生成缩略图
    """
    try:
        results = []

        for file in files:
            if not file.filename:
                continue

            upload_request = FileUploadRequest(
                filename=file.filename,
                content_type=file.content_type,
                access_type=access_type,
                folder_path=folder_path,
                generate_thumbnail=generate_thumbnail,
            )

            try:
                result = await file_manager.upload_file(
                    file=file,
                    request=upload_request,
                    owner_id=None,  # TODO: 从认证中获取用户ID
                )
                results.append(result)
            except Exception as e:
                logger.warning(f"Failed to upload file {file.filename}: {e}")
                # 继续处理其他文件
                continue

        return results

    except Exception as e:
        logger.error(f"Error in bulk_upload_files API: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/bulk", summary="批量删除文件")
async def bulk_delete_files(
    file_ids: List[str], file_manager: FileManager = Depends(get_file_manager)
):
    """
    批量删除文件

    - **file_ids**: 要删除的文件ID列表
    """
    try:
        results = []

        for file_id in file_ids:
            try:
                success = await file_manager.delete_file(
                    file_id=file_id, user_id=None  # TODO: 从认证中获取用户ID
                )

                results.append(
                    FileOperationResult(
                        file_id=file_id,
                        operation="delete",
                        success=success,
                        message=(
                            "Deleted successfully" if success else "Deletion failed"
                        ),
                    )
                )

            except Exception as e:
                results.append(
                    FileOperationResult(
                        file_id=file_id,
                        operation="delete",
                        success=False,
                        message=str(e),
                    )
                )

        return {"results": results}

    except Exception as e:
        logger.error(f"Error in bulk_delete_files API: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats", response_model=FileStatsResponse, summary="获取存储统计信息")
async def get_storage_stats(file_manager: FileManager = Depends(get_file_manager)):
    """
    获取存储统计信息

    返回文件总数、总大小、类型分布等统计信息
    """
    try:
        stats = await file_manager.get_storage_stats()
        return stats

    except Exception as e:
        logger.error(f"Error in get_storage_stats API: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health", summary="文件存储健康检查")
async def storage_health_check(file_manager: FileManager = Depends(get_file_manager)):
    """
    文件存储系统健康检查

    检查MinIO连接状态和基本功能
    """
    try:
        # 获取存储统计信息来验证连接
        stats = await file_manager.get_storage_stats()

        return {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",  # TODO: 使用实际时间
            "minio_connected": True,
            "total_files": stats.total_files,
            "total_size": stats.total_size,
            "message": "File storage system is operational",
        }

    except Exception as e:
        logger.error(f"Storage health check failed: {e}")
        return {
            "status": "unhealthy",
            "timestamp": "2024-01-01T00:00:00Z",  # TODO: 使用实际时间
            "minio_connected": False,
            "error": str(e),
            "message": "File storage system is not operational",
        }
