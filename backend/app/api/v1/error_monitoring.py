"""
错误监控API端点
提供错误统计、事件查询、告警管理等功能
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field

from ...core.error_monitor import (
    ErrorMonitor,
    ErrorReporter,
    get_error_monitor,
    get_error_reporter,
)
from ...core.exceptions import ErrorCode, ErrorSeverity

router = APIRouter(prefix="/error-monitoring", tags=["错误监控"])


class ErrorSummaryResponse(BaseModel):
    """错误摘要响应"""

    total_errors: int
    error_rate: float
    severity_distribution: Dict[str, int]
    top_errors: List[Dict[str, Any]]
    recent_trend: List[float]


class IncidentResponse(BaseModel):
    """错误事件响应"""

    incident_id: str
    error_code: str
    message: str
    severity: str
    first_seen: str
    last_seen: str
    count: int
    affected_users_count: int
    affected_paths_count: int
    resolved: bool
    resolution_notes: Optional[str] = None


class ErrorTrendsResponse(BaseModel):
    """错误趋势响应"""

    time_range: str
    total_incidents: int
    total_errors: int
    hourly_distribution: Dict[str, int]
    error_code_distribution: Dict[str, int]
    severity_distribution: Dict[str, int]


class ResolveIncidentRequest(BaseModel):
    """解决事件请求"""

    incident_id: str = Field(..., description="事件ID")
    resolution_notes: str = Field(..., description="解决方案说明")


class ErrorReportResponse(BaseModel):
    """错误报告响应"""

    report_type: str
    generated_at: str
    summary: Dict[str, Any]
    trends: Dict[str, Any]
    critical_incidents: List[Dict[str, Any]]
    unresolved_incidents: List[Dict[str, Any]]
    recommendations: List[str]


@router.get("/summary", response_model=ErrorSummaryResponse, summary="获取错误统计摘要")
async def get_error_summary(monitor: ErrorMonitor = Depends(get_error_monitor)):
    """获取系统错误统计摘要"""
    summary = monitor.get_error_summary()
    return ErrorSummaryResponse(**summary)


@router.get(
    "/incidents", response_model=List[IncidentResponse], summary="获取错误事件列表"
)
async def get_recent_incidents(
    limit: int = Query(10, ge=1, le=100, description="返回数量限制"),
    severity: Optional[ErrorSeverity] = Query(None, description="按严重程度过滤"),
    resolved: Optional[bool] = Query(None, description="按解决状态过滤"),
    monitor: ErrorMonitor = Depends(get_error_monitor),
):
    """获取最近的错误事件列表"""
    incidents = monitor.get_recent_incidents(limit=limit)

    # 应用过滤条件
    if severity:
        incidents = [inc for inc in incidents if inc["severity"] == severity.value]

    if resolved is not None:
        incidents = [inc for inc in incidents if inc["resolved"] == resolved]

    return [IncidentResponse(**incident) for incident in incidents]


@router.get("/trends", response_model=ErrorTrendsResponse, summary="获取错误趋势分析")
async def get_error_trends(
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）"),
    monitor: ErrorMonitor = Depends(get_error_monitor),
):
    """获取指定时间范围内的错误趋势分析"""
    trends = monitor.get_error_trends(hours=hours)
    return ErrorTrendsResponse(**trends)


@router.post("/incidents/resolve", summary="解决错误事件")
async def resolve_incident(
    request: ResolveIncidentRequest,
    monitor: ErrorMonitor = Depends(get_error_monitor),
):
    """标记错误事件为已解决"""
    success = monitor.resolve_incident(
        incident_id=request.incident_id, resolution_notes=request.resolution_notes
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"事件 {request.incident_id} 不存在",
        )

    return {
        "success": True,
        "message": f"事件 {request.incident_id} 已标记为已解决",
        "resolved_at": datetime.now().isoformat(),
    }


@router.get(
    "/reports/daily", response_model=ErrorReportResponse, summary="获取日错误报告"
)
async def get_daily_error_report(reporter: ErrorReporter = Depends(get_error_reporter)):
    """生成并获取日错误报告"""
    report = reporter.generate_daily_report()
    return ErrorReportResponse(**report)


@router.get("/health-check", summary="错误监控系统健康检查")
async def error_monitoring_health_check(
    monitor: ErrorMonitor = Depends(get_error_monitor),
):
    """检查错误监控系统的健康状态"""
    summary = monitor.get_error_summary()

    # 判断系统健康状态
    health_status = "healthy"
    issues = []

    # 检查错误率
    if summary["error_rate"] > 10.0:
        health_status = "warning"
        issues.append(f"错误率过高: {summary['error_rate']:.2f}%")

    # 检查严重错误
    if summary["severity_distribution"]["critical"] > 0:
        health_status = "critical"
        issues.append(f"存在 {summary['severity_distribution']['critical']} 个严重错误")

    # 检查高级错误
    elif summary["severity_distribution"]["high"] > 10:
        if health_status == "healthy":
            health_status = "warning"
        issues.append(f"高级错误较多: {summary['severity_distribution']['high']} 个")

    return {
        "status": health_status,
        "timestamp": datetime.now().isoformat(),
        "error_rate": summary["error_rate"],
        "total_errors": summary["total_errors"],
        "issues": issues,
        "recommendations": _get_health_recommendations(summary) if issues else [],
    }


@router.delete("/metrics", summary="重置错误监控指标")
async def reset_error_metrics(monitor: ErrorMonitor = Depends(get_error_monitor)):
    """重置所有错误监控指标（谨慎使用）"""
    monitor.reset_metrics()

    return {
        "success": True,
        "message": "错误监控指标已重置",
        "reset_at": datetime.now().isoformat(),
    }


@router.get("/error-codes", summary="获取错误码列表")
async def get_error_codes():
    """获取系统定义的所有错误码"""
    error_codes = []

    for code in ErrorCode:
        error_codes.append(
            {
                "code": code.value,
                "name": code.name,
                "description": _get_error_code_description(code),
                "category": _get_error_code_category(code),
            }
        )

    return {
        "total_codes": len(error_codes),
        "categories": {
            "通用错误": [
                code for code in error_codes if code["category"] == "通用错误"
            ],
            "请求错误": [
                code for code in error_codes if code["category"] == "请求错误"
            ],
            "认证授权错误": [
                code for code in error_codes if code["category"] == "认证授权错误"
            ],
            "资源错误": [
                code for code in error_codes if code["category"] == "资源错误"
            ],
            "业务逻辑错误": [
                code for code in error_codes if code["category"] == "业务逻辑错误"
            ],
            "限流错误": [
                code for code in error_codes if code["category"] == "限流错误"
            ],
            "数据库错误": [
                code for code in error_codes if code["category"] == "数据库错误"
            ],
            "外部服务错误": [
                code for code in error_codes if code["category"] == "外部服务错误"
            ],
            "网络错误": [
                code for code in error_codes if code["category"] == "网络错误"
            ],
        },
    }


def _get_health_recommendations(summary: Dict[str, Any]) -> List[str]:
    """获取健康状态改进建议"""
    recommendations = []

    if summary["error_rate"] > 10.0:
        recommendations.append("立即检查系统核心功能，错误率过高可能影响用户体验")

    if summary["severity_distribution"]["critical"] > 0:
        recommendations.append("优先处理所有严重错误，这些错误可能导致系统不可用")

    if summary["severity_distribution"]["high"] > 10:
        recommendations.append("关注高级错误的处理，防止问题恶化")

    # 基于热门错误的建议
    if summary["top_errors"]:
        top_error = summary["top_errors"][0]
        if top_error["percentage"] > 30:
            recommendations.append(
                f"错误 {top_error['error_code']} 占比达到 {top_error['percentage']:.1f}%，"
                "建议优先修复此问题"
            )

    return recommendations


def _get_error_code_description(error_code: ErrorCode) -> str:
    """获取错误码描述"""
    descriptions = {
        ErrorCode.UNKNOWN_ERROR: "未知错误",
        ErrorCode.INTERNAL_SERVER_ERROR: "服务器内部错误",
        ErrorCode.SERVICE_UNAVAILABLE: "服务不可用",
        ErrorCode.TIMEOUT_ERROR: "请求超时",
        ErrorCode.CONFIGURATION_ERROR: "配置错误",
        ErrorCode.BAD_REQUEST: "错误的请求",
        ErrorCode.VALIDATION_ERROR: "参数验证错误",
        ErrorCode.MISSING_PARAMETER: "缺少必需参数",
        ErrorCode.INVALID_PARAMETER: "无效参数",
        ErrorCode.REQUEST_TOO_LARGE: "请求体过大",
        ErrorCode.UNSUPPORTED_MEDIA_TYPE: "不支持的媒体类型",
        ErrorCode.UNAUTHORIZED: "未授权访问",
        ErrorCode.INVALID_TOKEN: "无效的访问令牌",
        ErrorCode.TOKEN_EXPIRED: "访问令牌已过期",
        ErrorCode.INSUFFICIENT_PERMISSIONS: "权限不足",
        ErrorCode.ACCOUNT_DISABLED: "账户已禁用",
        ErrorCode.LOGIN_REQUIRED: "需要登录",
        ErrorCode.NOT_FOUND: "资源未找到",
        ErrorCode.RESOURCE_NOT_FOUND: "指定资源不存在",
        ErrorCode.USER_NOT_FOUND: "用户不存在",
        ErrorCode.FILE_NOT_FOUND: "文件不存在",
        ErrorCode.ENDPOINT_NOT_FOUND: "接口不存在",
        ErrorCode.BUSINESS_LOGIC_ERROR: "业务逻辑错误",
        ErrorCode.DUPLICATE_RESOURCE: "资源重复",
        ErrorCode.RESOURCE_CONFLICT: "资源冲突",
        ErrorCode.INVALID_OPERATION: "无效操作",
        ErrorCode.QUOTA_EXCEEDED: "配额超限",
        ErrorCode.RATE_LIMIT_EXCEEDED: "请求频率超限",
        ErrorCode.TOO_MANY_REQUESTS: "请求过多",
        ErrorCode.CONCURRENT_LIMIT_EXCEEDED: "并发限制超限",
        ErrorCode.DATABASE_ERROR: "数据库错误",
        ErrorCode.DATABASE_CONNECTION_ERROR: "数据库连接错误",
        ErrorCode.DATABASE_QUERY_ERROR: "数据库查询错误",
        ErrorCode.DATABASE_CONSTRAINT_ERROR: "数据库约束错误",
        ErrorCode.DATABASE_TIMEOUT: "数据库操作超时",
        ErrorCode.EXTERNAL_SERVICE_ERROR: "外部服务错误",
        ErrorCode.REDIS_CONNECTION_ERROR: "Redis连接错误",
        ErrorCode.MESSAGE_QUEUE_ERROR: "消息队列错误",
        ErrorCode.FILE_STORAGE_ERROR: "文件存储错误",
        ErrorCode.THIRD_PARTY_API_ERROR: "第三方API错误",
        ErrorCode.NETWORK_ERROR: "网络错误",
        ErrorCode.CONNECTION_TIMEOUT: "连接超时",
        ErrorCode.DNS_RESOLUTION_ERROR: "DNS解析错误",
        ErrorCode.SSL_ERROR: "SSL错误",
    }

    return descriptions.get(error_code, "未定义错误")


def _get_error_code_category(error_code: ErrorCode) -> str:
    """获取错误码分类"""
    code_value = error_code.value

    if code_value.startswith("E1"):
        return "通用错误"
    elif code_value.startswith("E2"):
        return "请求错误"
    elif code_value.startswith("E3"):
        return "认证授权错误"
    elif code_value.startswith("E4"):
        return "资源错误"
    elif code_value.startswith("E5"):
        return "业务逻辑错误"
    elif code_value.startswith("E6"):
        return "限流错误"
    elif code_value.startswith("E7"):
        return "数据库错误"
    elif code_value.startswith("E8"):
        return "外部服务错误"
    elif code_value.startswith("E9"):
        return "网络错误"
    else:
        return "其他错误"
