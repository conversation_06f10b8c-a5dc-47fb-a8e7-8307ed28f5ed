"""
API v1 路由聚合器
统一管理v1版本的所有API路由
"""

from fastapi import APIRouter

# 导入用户管理模块路由
from app.modules.user_management.api.auth_endpoints import auth_router
from app.modules.user_management.api.permission_endpoints import (
    permission_router,
)
from app.modules.user_management.api.role_endpoints import role_router
from app.modules.user_management.api.user_endpoints import user_router

from .files import router as files_router
from .health import router as health_router
from .monitoring import router as monitoring_router
from .search import router as search_router

# 创建v1版本的主路由
router = APIRouter(prefix="/v1", tags=["API v1"])

# 注册各个模块的路由
router.include_router(health_router)
router.include_router(monitoring_router)
router.include_router(search_router)
router.include_router(files_router)

# 注册用户管理模块路由
router.include_router(auth_router, prefix="/auth", tags=["认证"])
router.include_router(user_router, prefix="/users", tags=["用户管理"])
router.include_router(role_router, prefix="/roles", tags=["角色管理"])
router.include_router(permission_router, prefix="/permissions", tags=["权限管理"])
