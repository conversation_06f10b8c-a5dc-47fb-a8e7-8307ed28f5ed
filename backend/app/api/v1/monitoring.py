"""
监控系统API端点
提供系统状态、性能指标、中间件监控等功能
"""

from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse

from ...core.config import get_settings
from ...core.middleware_utils import (
    MiddlewareConfig,
    MiddlewareMonitor,
    RequestTracker,
    get_middleware_config,
    get_middleware_monitor,
    get_request_tracker,
)
from ...shared.cache import get_redis

router = APIRouter(prefix="/monitoring", tags=["监控"])


@router.get("/health", summary="健康检查")
async def health_check():
    """系统健康检查"""
    try:
        # 检查Redis连接
        redis_client = await get_redis()
        redis_status = "healthy"
        try:
            await redis_client.redis.ping()
        except Exception:
            redis_status = "unhealthy"

        # 获取基本系统信息
        settings = get_settings()
        monitor = get_middleware_monitor()

        total_reqs = max(monitor.metrics.total_requests, 1)
        error_rate = round(monitor.metrics.error_count / total_reqs * 100, 2)

        return {
            "status": "healthy",
            "version": settings.app_version,
            "environment": settings.environment,
            "services": {"redis": redis_status, "api": "healthy"},
            "metrics": {
                "total_requests": monitor.metrics.total_requests,
                "current_active_requests": (monitor.metrics.current_active_requests),
                "error_rate": error_rate,
            },
        }
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"status": "unhealthy", "error": str(e)},
        )


@router.get("/metrics", summary="获取性能指标")
async def get_metrics(monitor: MiddlewareMonitor = Depends(get_middleware_monitor)):
    """获取详细的性能指标"""
    return {
        "global_metrics": monitor.get_global_metrics(),
        "endpoint_metrics": monitor.get_endpoint_metrics(limit=10),
        "ip_metrics": monitor.get_ip_metrics(limit=10),
    }


@router.get("/active-requests", summary="获取活跃请求")
async def get_active_requests(tracker: RequestTracker = Depends(get_request_tracker)):
    """获取当前活跃的请求列表"""
    active_requests = tracker.get_active_requests()

    return {"total_active": len(active_requests), "requests": active_requests}


@router.get("/middleware-config", summary="获取中间件配置")
async def get_middleware_config_info(
    config: MiddlewareConfig = Depends(get_middleware_config),
):
    """获取当前中间件配置"""
    return config.to_dict()


@router.post("/middleware-config/rate-limit", summary="更新限流配置")
async def update_rate_limit_config(
    requests: int,
    window: int,
    config: MiddlewareConfig = Depends(get_middleware_config),
):
    """动态更新限流配置"""
    if requests <= 0 or window <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="请求数和时间窗口必须大于0"
        )

    config.update_rate_limit(requests, window)

    return {
        "message": "限流配置已更新",
        "rate_limit_requests": requests,
        "rate_limit_window": window,
    }


@router.post("/middleware-config/ip-whitelist", summary="更新IP白名单")
async def update_ip_whitelist(
    whitelist: List[str], config: MiddlewareConfig = Depends(get_middleware_config)
):
    """动态更新IP白名单"""
    if not whitelist:
        config.disable_ip_whitelist()
        return {"message": "IP白名单已禁用"}

    config.enable_ip_whitelist(whitelist)

    return {"message": "IP白名单已更新", "whitelist": whitelist}


@router.delete("/metrics", summary="重置监控指标")
async def reset_metrics(monitor: MiddlewareMonitor = Depends(get_middleware_monitor)):
    """重置所有监控指标"""
    monitor.reset_metrics()

    return {"message": "监控指标已重置"}


@router.get("/system-info", summary="获取系统信息")
async def get_system_info():
    """获取系统基本信息"""
    import platform
    import sys
    from datetime import datetime

    import psutil

    settings = get_settings()

    try:
        # 系统信息
        system_info = {
            "platform": platform.platform(),
            "python_version": sys.version,
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total,
            "memory_available": psutil.virtual_memory().available,
            "memory_percent": psutil.virtual_memory().percent,
            "cpu_percent": psutil.cpu_percent(interval=1),
            "disk_usage": {
                "total": psutil.disk_usage("/").total,
                "used": psutil.disk_usage("/").used,
                "free": psutil.disk_usage("/").free,
                "percent": psutil.disk_usage("/").percent,
            },
        }

        # 应用信息
        app_info = {
            "name": settings.app_name,
            "version": settings.app_version,
            "environment": settings.environment,
            "debug_mode": settings.debug,
            "host": settings.host,
            "port": settings.port,
            "startup_time": datetime.now().isoformat(),
        }

        return {"system": system_info, "application": app_info}

    except Exception as e:
        # 如果psutil不可用，返回基本信息
        return {
            "system": {
                "platform": platform.platform(),
                "python_version": sys.version,
                "note": "详细系统信息需要安装psutil包",
            },
            "application": {
                "name": settings.app_name,
                "version": settings.app_version,
                "environment": settings.environment,
                "debug_mode": settings.debug,
            },
            "error": str(e),
        }


@router.get("/redis-info", summary="获取Redis信息")
async def get_redis_info():
    """获取Redis连接和状态信息"""
    try:
        redis_client = await get_redis()

        # 获取Redis信息
        info = await redis_client.redis.info()

        # 提取关键信息
        redis_info = {
            "version": info.get("redis_version", "unknown"),
            "mode": info.get("redis_mode", "unknown"),
            "connected_clients": info.get("connected_clients", 0),
            "used_memory": info.get("used_memory", 0),
            "used_memory_human": info.get("used_memory_human", "unknown"),
            "total_commands_processed": info.get("total_commands_processed", 0),
            "keyspace_hits": info.get("keyspace_hits", 0),
            "keyspace_misses": info.get("keyspace_misses", 0),
            "expired_keys": info.get("expired_keys", 0),
            "uptime_in_seconds": info.get("uptime_in_seconds", 0),
        }

        # 计算命中率
        hits = redis_info["keyspace_hits"]
        misses = redis_info["keyspace_misses"]
        total_requests = hits + misses

        if total_requests > 0:
            redis_info["hit_rate"] = round((hits / total_requests) * 100, 2)
        else:
            redis_info["hit_rate"] = 0

        return {"status": "connected", "info": redis_info}

    except Exception as e:
        return {"status": "error", "error": str(e)}


@router.get("/rate-limit-status", summary="获取限流状态")
async def get_rate_limit_status():
    """获取当前限流状态和统计"""
    try:
        redis_client = await get_redis()
        monitor = get_middleware_monitor()

        # 获取限流相关的Redis key
        rate_limit_keys = []
        async for key in redis_client.redis.scan_iter(match="rate_limit:*"):
            key_str = key.decode("utf-8")
            ttl = await redis_client.redis.ttl(key_str)
            count = await redis_client.redis.zcard(key_str)

            rate_limit_keys.append(
                {"key": key_str, "current_requests": count, "ttl": ttl}
            )

        return {
            "total_rate_limit_hits": monitor.metrics.rate_limit_hits,
            "active_rate_limits": len(rate_limit_keys),
            "rate_limit_details": rate_limit_keys[:20],  # 限制返回数量
        }

    except Exception as e:
        return {
            "error": str(e),
            "total_rate_limit_hits": 0,
            "active_rate_limits": 0,
            "rate_limit_details": [],
        }
