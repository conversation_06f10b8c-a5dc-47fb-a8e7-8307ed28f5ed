"""
健康检查和系统状态API
提供服务健康检查、版本信息、状态监控等接口
"""

import time
from datetime import datetime
from typing import Any, Dict

import psutil
import structlog
from app.core.config import get_settings
from app.core.schemas import (
    BaseResponse,
    HealthCheck,
    StatusInfo,
    TestResult,
    VersionInfo,
)
from fastapi import APIRouter, HTTPException

logger = structlog.get_logger()
router = APIRouter(prefix="/health", tags=["健康检查"])

# 应用启动时间
start_time = time.time()


@router.get(
    "/",
    response_model=BaseResponse[HealthCheck],
    summary="基础健康检查",
    description="检查API服务基本状态",
)
async def health_check():
    """基础健康检查接口"""
    settings = get_settings()
    uptime = int(time.time() - start_time)

    # 检查依赖服务配置状态
    dependencies = {}

    # 检查数据库配置
    if settings.database_url:
        dependencies["database"] = "configured"
    else:
        dependencies["database"] = "not_configured"

    # 检查Redis配置
    if settings.redis_url:
        dependencies["redis"] = "configured"
    else:
        dependencies["redis"] = "not_configured"

    # 检查消息队列配置
    if settings.rabbitmq_url:
        dependencies["rabbitmq"] = "configured"
    else:
        dependencies["rabbitmq"] = "not_configured"

    health_data = HealthCheck(
        status="healthy",
        version=settings.app_version,
        uptime_seconds=uptime,
        service="chaiguanjia-backend",
        dependencies=dependencies,
    )

    return BaseResponse(success=True, message="服务运行正常", data=health_data)


@router.get(
    "/detailed",
    response_model=BaseResponse[Dict[str, Any]],
    summary="详细健康检查",
    description="检查系统资源使用情况和详细状态",
)
async def detailed_health_check():
    """详细健康检查接口"""
    try:
        settings = get_settings()
        uptime = int(time.time() - start_time)

        # 获取系统资源信息
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage("/")
        cpu_percent = psutil.cpu_percent(interval=0.1)

        # 获取进程信息
        process = psutil.Process()
        process_memory = process.memory_info()

        detailed_status = {
            "service": {
                "name": "chaiguanjia-backend",
                "version": settings.app_version,
                "status": "healthy",
                "uptime_seconds": uptime,
                "uptime_human": f"{uptime // 3600}h {(uptime % 3600) // 60}m {uptime % 60}s",
            },
            "system": {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "used_percent": memory.percent,
                },
                "disk": {
                    "total_gb": round(disk.total / (1024**3), 2),
                    "free_gb": round(disk.free / (1024**3), 2),
                    "used_percent": round((disk.used / disk.total) * 100, 2),
                },
            },
            "process": {
                "pid": process.pid,
                "memory_mb": round(process_memory.rss / (1024**2), 2),
                "cpu_percent": process.cpu_percent(),
            },
            "environment": {
                "python_version": f"{psutil.sys.version_info.major}.{psutil.sys.version_info.minor}.{psutil.sys.version_info.micro}",
                "platform": psutil.sys.platform,
                "debug_mode": settings.debug,
            },
        }

        return BaseResponse(
            success=True, message="详细状态检查完成", data=detailed_status
        )

    except Exception as e:
        logger.error("详细健康检查失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.get(
    "/version",
    response_model=BaseResponse[VersionInfo],
    summary="获取版本信息",
    description="获取应用版本和构建信息",
)
async def get_version():
    """获取版本信息"""
    settings = get_settings()

    version_info = VersionInfo(
        version=settings.app_version,
        build_time=datetime.now(),
        python_version=f"{psutil.sys.version_info.major}.{psutil.sys.version_info.minor}.{psutil.sys.version_info.micro}",
        framework="FastAPI",
        environment="development" if settings.debug else "production",
    )

    return BaseResponse(success=True, message="版本信息获取成功", data=version_info)


@router.get(
    "/status",
    response_model=BaseResponse[StatusInfo],
    summary="获取运行状态",
    description="获取服务运行状态和资源使用情况",
)
async def get_status():
    """获取运行状态"""
    try:
        uptime = int(time.time() - start_time)

        # 获取内存和CPU信息
        memory = psutil.virtual_memory()
        process = psutil.Process()
        process_memory = process.memory_info()
        cpu_percent = psutil.cpu_percent(interval=0)

        status_info = StatusInfo(
            status="running",
            uptime_seconds=uptime,
            uptime_human=f"{uptime // 3600}h {(uptime % 3600) // 60}m {uptime % 60}s",
            memory_usage={
                "system_total_mb": round(memory.total / (1024**2), 2),
                "system_available_mb": round(memory.available / (1024**2), 2),
                "system_used_percent": memory.percent,
                "process_used_mb": round(process_memory.rss / (1024**2), 2),
            },
            cpu_usage=cpu_percent,
        )

        return BaseResponse(success=True, message="状态信息获取成功", data=status_info)

    except Exception as e:
        logger.error("状态检查失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"状态检查失败: {str(e)}")


@router.get(
    "/test/database",
    response_model=BaseResponse[TestResult],
    summary="数据库连接测试",
    description="测试数据库连接状态",
)
async def test_database():
    """测试数据库连接"""
    start_time_test = time.time()

    try:
        settings = get_settings()

        if not settings.database_url:
            raise HTTPException(status_code=500, detail="数据库未配置")

        # TODO: 实际的数据库连接测试
        # 这里应该实际连接数据库并执行简单查询
        # 暂时返回模拟结果

        latency = round((time.time() - start_time_test) * 1000, 2)

        test_result = TestResult(
            service="PostgreSQL",
            status="success",
            message="数据库连接正常",
            latency_ms=latency,
            details={
                "configured": True,
                "url_configured": bool(settings.database_url),
            },
        )

        return BaseResponse(success=True, message="数据库测试完成", data=test_result)

    except Exception as e:
        logger.error("数据库连接测试失败", error=str(e))

        test_result = TestResult(
            service="PostgreSQL",
            status="failed",
            message=f"数据库连接失败: {str(e)}",
            details={"error": str(e)},
        )

        return BaseResponse(success=False, message="数据库测试失败", data=test_result)


@router.get(
    "/test/redis",
    response_model=BaseResponse[TestResult],
    summary="Redis连接测试",
    description="测试Redis连接状态",
)
async def test_redis():
    """测试Redis连接"""
    start_time_test = time.time()

    try:
        settings = get_settings()

        if not settings.redis_url:
            raise HTTPException(status_code=500, detail="Redis未配置")

        # TODO: 实际的Redis连接测试
        # 这里应该实际连接Redis并执行ping命令
        # 暂时返回模拟结果

        latency = round((time.time() - start_time_test) * 1000, 2)

        test_result = TestResult(
            service="Redis",
            status="success",
            message="Redis连接正常",
            latency_ms=latency,
            details={
                "configured": True,
                "url_configured": bool(settings.redis_url),
            },
        )

        return BaseResponse(success=True, message="Redis测试完成", data=test_result)

    except Exception as e:
        logger.error("Redis连接测试失败", error=str(e))

        test_result = TestResult(
            service="Redis",
            status="failed",
            message=f"Redis连接失败: {str(e)}",
            details={"error": str(e)},
        )

        return BaseResponse(success=False, message="Redis测试失败", data=test_result)


@router.get(
    "/test/rabbitmq",
    response_model=BaseResponse[TestResult],
    summary="RabbitMQ连接测试",
    description="测试RabbitMQ连接状态",
)
async def test_rabbitmq():
    """测试RabbitMQ连接"""
    start_time_test = time.time()

    try:
        settings = get_settings()

        if not settings.rabbitmq_url:
            raise HTTPException(status_code=500, detail="RabbitMQ未配置")

        # TODO: 实际的RabbitMQ连接测试
        # 这里应该实际连接RabbitMQ并检查连接状态
        # 暂时返回模拟结果

        latency = round((time.time() - start_time_test) * 1000, 2)

        test_result = TestResult(
            service="RabbitMQ",
            status="success",
            message="RabbitMQ连接正常",
            latency_ms=latency,
            details={
                "configured": True,
                "url_configured": bool(settings.rabbitmq_url),
            },
        )

        return BaseResponse(success=True, message="RabbitMQ测试完成", data=test_result)

    except Exception as e:
        logger.error("RabbitMQ连接测试失败", error=str(e))

        test_result = TestResult(
            service="RabbitMQ",
            status="failed",
            message=f"RabbitMQ连接失败: {str(e)}",
            details={"error": str(e)},
        )

        return BaseResponse(success=False, message="RabbitMQ测试失败", data=test_result)
