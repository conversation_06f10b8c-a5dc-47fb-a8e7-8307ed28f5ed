# FastAPI核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库相关
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis缓存
redis==5.0.1
aioredis==2.0.1

# Elasticsearch搜索引擎
elasticsearch==8.11.0
elasticsearch-dsl==8.11.0

# 文件存储 (MinIO对象存储)
minio==7.2.0
boto3==1.34.0
botocore==1.34.0

# 图像处理
Pillow==10.1.0
python-magic==0.4.27

# 消息队列
aio-pika==9.3.1
celery==5.3.4

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# AI模型集成
openai==1.3.7
anthropic==0.7.7

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 工具库
python-dotenv==1.0.0
loguru==0.7.2
structlog==23.2.0
tenacity==8.2.3

# 数据处理
pandas==2.1.3
numpy==1.25.2

# 文档处理
python-docx==1.1.0
PyPDF2==3.0.1

# 时间处理
pytz==2023.3
python-dateutil==2.8.2

# JSON处理
orjson==3.9.10

# 网络相关
websockets==12.0
