# migrations/manage.py - 数据库迁移管理工具
"""
数据库迁移管理脚本
提供常用的数据库操作命令
"""

import os
import subprocess
import sys
from pathlib import Path
from typing import Optional

import click

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import logging

from database.connection import db_manager, init_database
from database.migrations.initial_data import initialize_database_data

logger = logging.getLogger(__name__)

# Alembic命令路径
ALEMBIC_CONFIG = Path(__file__).parent / "alembic.ini"


@click.group()
def cli():
    """数据库迁移管理工具"""
    pass


@cli.command()
def init():
    """初始化数据库和Alembic"""
    click.echo("正在初始化数据库...")

    try:
        # 初始化数据库连接
        init_database()
        click.echo("✓ 数据库连接初始化成功")

        # 初始化Alembic
        result = subprocess.run(
            ["alembic", "-c", str(ALEMBIC_CONFIG), "init", "migrations"],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            click.echo("✓ Alembic初始化成功")
        else:
            click.echo(f"✗ Alembic初始化失败: {result.stderr}")
            return

    except Exception as e:
        click.echo(f"✗ 初始化失败: {e}")
        sys.exit(1)


@cli.command()
@click.option("--message", "-m", required=True, help="迁移说明信息")
def revision(message: str):
    """创建新的迁移文件"""
    click.echo(f"正在创建迁移: {message}")

    try:
        result = subprocess.run(
            [
                "alembic",
                "-c",
                str(ALEMBIC_CONFIG),
                "revision",
                "--autogenerate",
                "-m",
                message,
            ],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            click.echo("✓ 迁移文件创建成功")
            click.echo(result.stdout)
        else:
            click.echo(f"✗ 创建迁移失败: {result.stderr}")

    except Exception as e:
        click.echo(f"✗ 创建迁移失败: {e}")


@cli.command()
@click.option("--revision", "-r", default="head", help="升级到指定版本")
def upgrade(revision: str):
    """升级数据库到指定版本"""
    click.echo(f"正在升级数据库到: {revision}")

    try:
        result = subprocess.run(
            ["alembic", "-c", str(ALEMBIC_CONFIG), "upgrade", revision],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            click.echo("✓ 数据库升级成功")
            click.echo(result.stdout)
        else:
            click.echo(f"✗ 数据库升级失败: {result.stderr}")

    except Exception as e:
        click.echo(f"✗ 数据库升级失败: {e}")


@cli.command()
@click.option("--revision", "-r", required=True, help="降级到指定版本")
def downgrade(revision: str):
    """降级数据库到指定版本"""
    if not click.confirm(f"确定要降级数据库到 {revision}? 这可能会丢失数据!"):
        return

    click.echo(f"正在降级数据库到: {revision}")

    try:
        result = subprocess.run(
            ["alembic", "-c", str(ALEMBIC_CONFIG), "downgrade", revision],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            click.echo("✓ 数据库降级成功")
            click.echo(result.stdout)
        else:
            click.echo(f"✗ 数据库降级失败: {result.stderr}")

    except Exception as e:
        click.echo(f"✗ 数据库降级失败: {e}")


@cli.command()
def current():
    """显示当前数据库版本"""
    try:
        result = subprocess.run(
            ["alembic", "-c", str(ALEMBIC_CONFIG), "current"],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            click.echo("当前数据库版本:")
            click.echo(result.stdout)
        else:
            click.echo(f"✗ 获取版本信息失败: {result.stderr}")

    except Exception as e:
        click.echo(f"✗ 获取版本信息失败: {e}")


@cli.command()
def history():
    """显示迁移历史"""
    try:
        result = subprocess.run(
            ["alembic", "-c", str(ALEMBIC_CONFIG), "history"],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            click.echo("迁移历史:")
            click.echo(result.stdout)
        else:
            click.echo(f"✗ 获取历史失败: {result.stderr}")

    except Exception as e:
        click.echo(f"✗ 获取历史失败: {e}")


@cli.command()
def create_tables():
    """直接创建所有数据表（不使用迁移）"""
    if not click.confirm("这将直接创建所有数据表，确定继续?"):
        return

    click.echo("正在创建数据表...")

    try:
        db_manager.create_all_tables()
        click.echo("✓ 数据表创建成功")

    except Exception as e:
        click.echo(f"✗ 创建数据表失败: {e}")


@cli.command()
def drop_tables():
    """删除所有数据表"""
    if not click.confirm("这将删除所有数据表和数据，确定继续?"):
        return

    click.echo("正在删除数据表...")

    try:
        db_manager.drop_all_tables()
        click.echo("✓ 数据表删除成功")

    except Exception as e:
        click.echo(f"✗ 删除数据表失败: {e}")


@cli.command()
def init_data():
    """初始化基础数据"""
    click.echo("正在初始化基础数据...")

    try:
        initialize_database_data()
        click.echo("✓ 基础数据初始化成功")

    except Exception as e:
        click.echo(f"✗ 基础数据初始化失败: {e}")


@cli.command()
def test_connection():
    """测试数据库连接"""
    click.echo("正在测试数据库连接...")

    try:
        if db_manager.test_connection():
            click.echo("✓ 数据库连接正常")
        else:
            click.echo("✗ 数据库连接失败")

    except Exception as e:
        click.echo(f"✗ 连接测试失败: {e}")


@cli.command()
def reset():
    """重置数据库（删除所有表并重新创建）"""
    if not click.confirm("这将重置整个数据库，所有数据将丢失，确定继续?"):
        return

    click.echo("正在重置数据库...")

    try:
        # 删除所有表
        db_manager.drop_all_tables()
        click.echo("✓ 删除所有表成功")

        # 重新创建表
        db_manager.create_all_tables()
        click.echo("✓ 创建表成功")

        # 初始化基础数据
        initialize_database_data()
        click.echo("✓ 初始化数据成功")

        click.echo("✓ 数据库重置完成")

    except Exception as e:
        click.echo(f"✗ 数据库重置失败: {e}")


@cli.command()
def show_info():
    """显示数据库信息"""
    try:
        from database.config import get_database_config

        config = get_database_config()

        click.echo("数据库配置信息:")
        click.echo(f"  主机: {config.DB_HOST}")
        click.echo(f"  端口: {config.DB_PORT}")
        click.echo(f"  数据库: {config.DB_NAME}")
        click.echo(f"  用户: {config.DB_USER}")
        click.echo(f"  连接池大小: {config.DB_POOL_SIZE}")
        click.echo(f"  最大溢出: {config.DB_MAX_OVERFLOW}")

        # 测试连接
        if db_manager.test_connection():
            click.echo("  状态: ✓ 连接正常")
        else:
            click.echo("  状态: ✗ 连接失败")

    except Exception as e:
        click.echo(f"✗ 获取数据库信息失败: {e}")


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    cli()
