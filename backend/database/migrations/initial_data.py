# migrations/initial_data.py - 初始化数据脚本
"""
数据库初始化数据脚本
包含系统启动必需的基础数据
"""

import logging

from database.connection import get_db_session
from models import (
    Account,
    AccountUser,
    ChannelType,
    ContentType,
    MessageType,
    PlatformAdapter,
    PlatformStatus,
    User,
    UserRole,
)
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


def create_default_account(session: Session) -> Account:
    """创建默认账户"""
    account = Account(
        name="柴管家默认账户",
        subdomain="default",
        ai_enabled=True,
        settings={
            "timezone": "Asia/Shanghai",
            "language": "zh-CN",
            "business_hours": {
                "enabled": True,
                "timezone": "Asia/Shanghai",
                "working_hours": [
                    {
                        "day": "monday",
                        "enabled": True,
                        "open_hour": 9,
                        "close_hour": 18,
                    },
                    {
                        "day": "tuesday",
                        "enabled": True,
                        "open_hour": 9,
                        "close_hour": 18,
                    },
                    {
                        "day": "wednesday",
                        "enabled": True,
                        "open_hour": 9,
                        "close_hour": 18,
                    },
                    {
                        "day": "thursday",
                        "enabled": True,
                        "open_hour": 9,
                        "close_hour": 18,
                    },
                    {
                        "day": "friday",
                        "enabled": True,
                        "open_hour": 9,
                        "close_hour": 18,
                    },
                    {"day": "saturday", "enabled": False},
                    {"day": "sunday", "enabled": False},
                ],
            },
        },
        ai_config={
            "default_model": "gpt-3.5-turbo",
            "temperature": 0.7,
            "max_tokens": 1000,
            "confidence_threshold": 0.8,
            "auto_reply_enabled": True,
            "escalation_rules": {
                "low_confidence_threshold": 0.5,
                "negative_sentiment_threshold": -0.6,
                "keywords_requiring_human": ["投诉", "退款", "问题", "不满意"],
            },
        },
    )
    session.add(account)
    session.flush()
    logger.info(f"Created default account: {account.id}")
    return account


def create_admin_user(session: Session, account: Account) -> User:
    """创建管理员用户"""
    # 检查是否已存在管理员用户
    existing_user = session.query(User).filter_by(email="<EMAIL>").first()
    if existing_user:
        logger.info("Admin user already exists")
        return existing_user

    user = User(
        email="<EMAIL>",
        name="系统管理员",
        password_digest="$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJCef.yei",  # password: admin123
        role=UserRole.SUPER_ADMIN,
        ai_assistant_enabled=True,
        preferences={
            "language": "zh-CN",
            "timezone": "Asia/Shanghai",
            "notifications": {"email": True, "push": True, "escalations": True},
        },
    )
    session.add(user)
    session.flush()

    # 创建账户用户关联
    account_user = AccountUser(
        account_id=account.id,
        user_id=user.id,
        role=1,  # 管理员角色
        availability=1,  # 可用
        permissions={
            "manage_channels": True,
            "manage_users": True,
            "manage_ai_config": True,
            "view_analytics": True,
            "manage_knowledge_base": True,
        },
    )
    session.add(account_user)

    logger.info(f"Created admin user: {user.id}")
    return user


def create_platform_adapters(session: Session):
    """创建平台适配器配置"""
    adapters = [
        {
            "platform_name": ChannelType.WECHAT,
            "adapter_version": "1.0.0",
            "field_mappings": {
                "message_id": "MsgId",
                "sender_id": "FromUserName",
                "content": "Content",
                "message_type": "MsgType",
                "timestamp": "CreateTime",
            },
            "transformation_rules": {
                "message_type_mapping": {
                    "text": ContentType.TEXT,
                    "image": ContentType.IMAGE,
                    "voice": ContentType.AUDIO,
                    "video": ContentType.VIDEO,
                    "file": ContentType.FILE,
                },
                "content_processing": {
                    "emoji_support": True,
                    "mention_support": True,
                    "url_extraction": True,
                },
            },
        },
        {
            "platform_name": ChannelType.DOUYIN,
            "adapter_version": "1.0.0",
            "field_mappings": {
                "message_id": "message_id",
                "sender_id": "from_user_id",
                "content": "content",
                "message_type": "content_type",
                "timestamp": "create_time",
            },
            "transformation_rules": {
                "message_type_mapping": {
                    "0": ContentType.TEXT,
                    "1": ContentType.IMAGE,
                    "2": ContentType.VIDEO,
                }
            },
        },
        {
            "platform_name": ChannelType.XIAOHONGSHU,
            "adapter_version": "1.0.0",
            "field_mappings": {
                "message_id": "msg_id",
                "sender_id": "user_id",
                "content": "text",
                "message_type": "type",
                "timestamp": "created_at",
            },
            "transformation_rules": {
                "message_type_mapping": {
                    "text": ContentType.TEXT,
                    "image": ContentType.IMAGE,
                    "video": ContentType.VIDEO,
                }
            },
        },
    ]

    for adapter_config in adapters:
        # 检查是否已存在
        existing = (
            session.query(PlatformAdapter)
            .filter_by(platform_name=adapter_config["platform_name"])
            .first()
        )

        if not existing:
            adapter = PlatformAdapter(**adapter_config)
            session.add(adapter)
            logger.info(f"Created platform adapter: {adapter_config['platform_name']}")
        else:
            logger.info(
                f"Platform adapter already exists: {adapter_config['platform_name']}"
            )


def create_default_knowledge_base(session: Session, account: Account):
    """创建默认知识库条目"""
    from models import KnowledgeBase

    knowledge_items = [
        {
            "title": "欢迎语",
            "question": "你好|您好|hi|hello",
            "answer": "您好！欢迎使用柴管家智能客服系统。我是您的AI助手，很高兴为您服务！请问有什么可以帮助您的吗？",
            "category": "greeting",
            "metadata": {
                "intent": "greeting",
                "confidence": 0.9,
                "keywords": ["你好", "您好", "hi", "hello"],
            },
        },
        {
            "title": "功能介绍",
            "question": "功能|能做什么|介绍",
            "answer": "柴管家是一个多平台聚合智能客服系统，主要功能包括：\n1. 多平台消息统一管理\n2. AI智能自动回复\n3. 人机协作处理\n4. 数据分析和洞察\n\n我可以帮助您处理日常咨询，提高客服效率。",
            "category": "product_info",
            "metadata": {"intent": "product_inquiry", "confidence": 0.85},
        },
        {
            "title": "联系方式",
            "question": "联系|客服|电话|邮箱",
            "answer": "如需人工客服协助，您可以：\n1. 在此对话中输入'转人工'或'转客服'\n2. 发送邮件至：<EMAIL>\n3. 客服电话：400-123-4567\n\n工作时间：周一至周五 9:00-18:00",
            "category": "contact",
            "metadata": {"intent": "contact_inquiry", "confidence": 0.9},
        },
    ]

    for item in knowledge_items:
        # 检查是否已存在相同问题
        existing = (
            session.query(KnowledgeBase)
            .filter_by(account_id=account.id, question=item["question"])
            .first()
        )

        if not existing:
            knowledge = KnowledgeBase(account_id=account.id, **item)
            session.add(knowledge)
            logger.info(f"Created knowledge base item: {item['title']}")


def initialize_database_data():
    """初始化数据库数据"""
    logger.info("Starting database initialization...")

    try:
        with get_db_session() as session:
            # 创建默认账户
            account = create_default_account(session)

            # 创建管理员用户
            admin_user = create_admin_user(session, account)

            # 创建平台适配器
            create_platform_adapters(session)

            # 创建默认知识库
            create_default_knowledge_base(session, account)

            session.commit()
            logger.info("Database initialization completed successfully")

    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # 执行初始化
    initialize_database_data()
