# migrations/env.py - Alembic环境配置
import os
import sys
from logging.config import fileConfig

from alembic import context
from sqlalchemy import engine_from_config, pool

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from database.config import get_database_config

# 导入模型和配置
from models import Base

# Alembic配置对象
config = context.config

# 设置日志配置
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 设置元数据目标
target_metadata = Base.metadata


def get_url():
    """获取数据库连接URL"""
    db_config = get_database_config()
    return db_config.database_url


def run_migrations_offline() -> None:
    """离线模式运行迁移"""
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        render_as_batch=True,
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """在线模式运行迁移"""
    # 创建数据库引擎配置
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = get_url()

    # 添加连接池配置
    db_config = get_database_config()
    configuration.update(
        {
            "sqlalchemy.poolclass": "QueuePool",
            "sqlalchemy.pool_size": str(db_config.DB_POOL_SIZE),
            "sqlalchemy.max_overflow": str(db_config.DB_MAX_OVERFLOW),
            "sqlalchemy.pool_timeout": str(db_config.DB_POOL_TIMEOUT),
            "sqlalchemy.pool_recycle": str(db_config.DB_POOL_RECYCLE),
            "sqlalchemy.pool_pre_ping": str(db_config.DB_POOL_PRE_PING),
        }
    )

    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            render_as_batch=True,
            compare_type=True,
            compare_server_default=True,
            # 自定义比较函数
            include_object=include_object,
            # 自定义命名约定
            process_revision_directives=process_revision_directives,
        )

        with context.begin_transaction():
            context.run_migrations()


def include_object(object, name, type_, reflected, compare_to):
    """
    控制哪些对象应该包含在迁移中
    """
    # 跳过临时表和系统表
    if type_ == "table" and name.startswith(("tmp_", "temp_", "alembic_")):
        return False

    # 跳过某些索引
    if type_ == "index" and name.startswith("_"):
        return False

    return True


def process_revision_directives(context, revision, directives):
    """
    处理修订指令，可以自定义迁移脚本生成
    """
    # 如果没有变更，不生成迁移文件
    if getattr(config.cmd_opts, "autogenerate", False):
        script = directives[0]
        if script.upgrade_ops.is_empty():
            directives[:] = []
            print("No changes detected, skipping migration generation.")


# 根据context模式运行对应的迁移函数
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
