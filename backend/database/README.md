# 柴管家数据库架构设计

## Task I-2.2：数据库架构设计 - 完成报告

### 设计概述

本数据库架构为柴管家多平台聚合智能客服系统设计，基于以下核心原则：

1. **标准化数据结构**：统一的消息、会话、联系人、渠道模型
2. **平台适配器解耦**：外部平台数据通过适配器转换为标准结构
3. **AI 原生优化**：针对 AI 处理场景优化的字段和索引设计
4. **人机协作支持**：完整的人工干预和协作机制
5. **高性能查询**：合理的索引策略和关系设计

### 核心数据模型

#### 主要实体关系

```
Account (账户)
├── Channel (渠道)
│   └── Inbox (收件箱)
├── Contact (联系人)
│   └── ContactInbox (联系人-收件箱关联)
├── Conversation (会话)
│   └── Message (消息)
│       ├── Attachment (附件)
│       └── AIProcessingLog (AI处理日志)
├── User (用户)
│   └── AccountUser (账户用户关联)
├── KnowledgeBase (知识库)
├── AutomationRule (自动化规则)
└── HumanTakeoverLog (人工接管日志)
```

#### 平台适配器架构

```
外部平台 → 平台适配器 → 标准数据结构 → 业务逻辑
```

- **PlatformAdapter**: 定义各平台的字段映射和转换规则
- **SyncLog**: 记录数据同步状态和错误
- **标准消息格式**: 统一的 Message 模型支持所有平台

### 文件结构

```
backend/database/
├── __init__.py              # 模块初始化
├── README.md               # 本文档
├── config.py               # 数据库配置管理
├── connection.py           # 连接池和会话管理
├── examples.py             # 操作示例代码
├── tests.py                # 单元测试
└── migrations/             # 数据库迁移
    ├── alembic.ini         # Alembic配置
    ├── env.py              # 迁移环境配置
    ├── script.py.mako      # 迁移脚本模板
    ├── initial_data.py     # 初始化数据
    └── manage.py           # 迁移管理工具
```

### 快速开始

#### 1. 环境配置

创建 `.env` 文件：

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=chaiguanjia
DB_USER=postgres
DB_PASSWORD=your_password

# 环境设置
ENVIRONMENT=development
AUTO_INIT_DB=true
```

#### 2. 安装依赖

```bash
pip install sqlalchemy psycopg2-binary alembic pydantic python-dotenv
```

#### 3. 初始化数据库

```python
from database import init_database, quick_init

# 方式1: 标准初始化
init_database()

# 方式2: 快速初始化（开发环境）
quick_init()
```

#### 4. 使用数据库会话

```python
from database import get_db_session
from models import Account, Contact, Message

# 同步操作
with get_db_session() as session:
    account = Account(name="我的账户", subdomain="demo")
    session.add(account)
    session.commit()

# 查询操作
with get_db_session() as session:
    accounts = session.query(Account).all()
```

#### 5. 运行迁移

```bash
# 进入迁移目录
cd backend/database/migrations

# 创建迁移
python manage.py revision -m "初始化数据库"

# 执行迁移
python manage.py upgrade

# 查看状态
python manage.py current
```

### 核心功能示例

#### 创建多平台消息流

```python
from database.examples import DatabaseExamples

examples = DatabaseExamples()

# 创建完整的消息处理链路
account, user = examples.create_account_and_user()
channel, inbox = examples.create_channel_and_inbox(account.id)
contact, conversation = examples.create_contact_and_conversation(account.id, inbox.id)
messages = examples.create_messages_with_ai_processing(conversation)
```

#### 平台数据转换

```python
from models import PlatformAdapter, Message, ContentType

# 微信消息转换示例
wechat_adapter = session.query(PlatformAdapter).filter_by(
    platform_name="wechat"
).first()

# 原始微信数据
wechat_data = {
    "MsgId": "*********",
    "FromUserName": "openid123",
    "Content": "你好",
    "MsgType": "text",
    "CreateTime": **********
}

# 转换为标准格式
standard_message = Message(
    source_id=wechat_data["MsgId"],
    content=wechat_data["Content"],
    content_type=ContentType.TEXT,
    sender_type="contact",
    external_source_ids={"wechat": wechat_data}
)
```

#### AI 处理流程

```python
from models import AIProcessingLog, MessageType

# 记录AI处理过程
ai_log = AIProcessingLog(
    message_id=message.id,
    conversation_id=conversation.id,
    ai_model="gpt-3.5-turbo",
    processing_type="intent_detection",
    intent_detected="product_inquiry",
    confidence_score=0.92,
    input_data={"message": message.content},
    output_data={"intent": "product_inquiry", "entities": ["产品", "价格"]},
    processing_time_ms=150
)

# 生成AI回复
ai_reply = Message(
    conversation_id=conversation.id,
    message_type=MessageType.OUTGOING,
    content="感谢咨询，我来为您介绍产品信息...",
    ai_generated=True,
    ai_confidence=0.88,
    sender_type="ai_assistant"
)
```

### 性能优化

#### 索引策略

- **消息查询优化**: `conversation_id`, `created_at`, `ai_generated`
- **会话管理优化**: `account_id`, `status`, `last_activity_at`
- **联系人检索优化**: `email`, `phone_number`, `platform_identifiers`
- **AI 日志分析**: `ai_model`, `processing_type`, `confidence_score`

#### 查询优化示例

```python
from sqlalchemy.orm import joinedload, selectinload

# 预加载关联数据
conversations = session.query(Conversation).options(
    joinedload(Conversation.contact),
    joinedload(Conversation.inbox),
    selectinload(Conversation.messages).selectinload(Message.ai_processing_logs)
).filter(Conversation.account_id == account_id).all()

# 分页查询
from sqlalchemy import desc
recent_messages = session.query(Message).filter_by(
    conversation_id=conversation_id
).order_by(desc(Message.created_at)).limit(20).all()
```

### 数据迁移管理

#### 常用命令

```bash
# 测试数据库连接
python manage.py test-connection

# 查看数据库信息
python manage.py show-info

# 创建迁移
python manage.py revision -m "添加新字段"

# 升级数据库
python manage.py upgrade

# 降级数据库（谨慎使用）
python manage.py downgrade -r head-1

# 查看迁移历史
python manage.py history

# 重置数据库（开发环境）
python manage.py reset

# 初始化基础数据
python manage.py init-data
```

### 测试运行

```bash
# 运行所有测试
python -m pytest database/tests.py -v

# 运行操作示例
python database/examples.py

# 数据库健康检查
python -c "from database import health_check; health_check()"
```

### 扩展指南

#### 添加新平台支持

1. **创建平台适配器配置**：

```python
new_adapter = PlatformAdapter(
    platform_name="new_platform",
    adapter_version="1.0.0",
    field_mappings={
        "message_id": "id",
        "content": "text",
        "sender_id": "from_user"
    },
    transformation_rules={
        "message_type_mapping": {
            "text": ContentType.TEXT,
            "image": ContentType.IMAGE
        }
    }
)
```

2. **实现数据转换逻辑**：

```python
def transform_platform_message(raw_data, adapter):
    mappings = adapter.field_mappings
    return {
        "content": raw_data.get(mappings["content"]),
        "source_id": raw_data.get(mappings["message_id"]),
        "external_source_ids": {adapter.platform_name: raw_data}
    }
```

#### 自定义 AI 处理

```python
# 扩展AI处理类型
class CustomAIProcessingType:
    EMOTION_ANALYSIS = "emotion_analysis"
    TOPIC_CLASSIFICATION = "topic_classification"
    URGENCY_DETECTION = "urgency_detection"

# 记录自定义处理
custom_log = AIProcessingLog(
    processing_type=CustomAIProcessingType.EMOTION_ANALYSIS,
    output_data={
        "emotion": "happy",
        "intensity": 0.8,
        "confidence": 0.92
    }
)
```

### 验收标准检查

✅ **数据库表结构设计合理，关系清晰**

- 15 个核心表，覆盖完整业务流程
- 明确的外键关系和数据完整性约束
- 支持多租户架构（Account 隔离）

✅ **ORM 模型定义完整，字段类型正确**

- 基于 SQLAlchemy 的完整模型定义
- 类型安全的字段定义（使用 PostgreSQL 专有类型）
- 完整的关系映射和级联删除策略

✅ **数据库连接池配置合理，支持并发**

- 可配置的连接池参数
- 支持同步和异步连接
- 生产环境优化配置

✅ **支持数据库版本迁移和回滚**

- 基于 Alembic 的完整迁移体系
- 自动生成迁移脚本
- 支持升级和回滚操作

✅ **基础 CRUD 操作测试通过**

- 完整的单元测试覆盖
- 操作示例代码
- 性能测试基础框架

### 技术特性

- **多平台数据统一**: 通过适配器模式实现不同平台数据的标准化
- **AI 原生设计**: 专为 AI 处理优化的字段和索引
- **人机协作**: 完整的人工接管和协作流程支持
- **高性能查询**: 优化的索引策略和查询模式
- **完整监控**: 详细的操作日志和性能监控
- **数据安全**: 支持 SSL 连接和数据加密

### 后续优化建议

1. **引入 pgvector 扩展**: 支持向量相似度搜索的知识库
2. **分区策略**: 对大表（如 messages）实施时间分区
3. **读写分离**: 配置主从数据库提升查询性能
4. **缓存策略**: Redis 缓存热点数据
5. **监控完善**: 集成 Prometheus 监控数据库性能

---

_Task I-2.2：数据库架构设计 已完成_ ✅

_交付物：完整的数据库架构设计，包含 ORM 模型、迁移脚本、配置管理、操作示例和测试代码_
