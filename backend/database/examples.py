# database/examples.py - 数据库操作示例
"""
数据库操作示例代码
展示如何使用ORM模型进行常见的数据库操作
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from database.connection import get_db_session
from models import (
    Account,
    AccountUser,
    AIMode,
    AIProcessingLog,
    Attachment,
    AutomationRule,
    Channel,
    ChannelType,
    Contact,
    ContactInbox,
    ContentType,
    Conversation,
    ConversationStatus,
    HumanTakeoverLog,
    Inbox,
    KnowledgeBase,
    Message,
    MessageType,
    PlatformAdapter,
    User,
)
from sqlalchemy import and_, asc, desc, func, or_
from sqlalchemy.orm import Session, joinedload, selectinload

logger = logging.getLogger(__name__)


class DatabaseExamples:
    """数据库操作示例类"""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    def create_account_and_user(self) -> tuple[Account, User]:
        """示例: 创建账户和用户"""
        with get_db_session() as session:
            # 创建账户
            account = Account(
                name="示例企业",
                subdomain="demo",
                ai_enabled=True,
                settings={
                    "timezone": "Asia/Shanghai",
                    "language": "zh-CN",
                    "business_hours": {
                        "enabled": True,
                        "working_hours": [
                            {
                                "day": "monday",
                                "enabled": True,
                                "open_hour": 9,
                                "close_hour": 18,
                            }
                        ],
                    },
                },
                ai_config={
                    "default_model": "gpt-3.5-turbo",
                    "temperature": 0.7,
                    "confidence_threshold": 0.8,
                },
            )
            session.add(account)
            session.flush()  # 获取account.id

            # 创建用户
            user = User(
                email="<EMAIL>",
                name="演示用户",
                password_digest="hashed_password",
                role="agent",
                ai_assistant_enabled=True,
            )
            session.add(user)
            session.flush()

            # 创建账户用户关联
            account_user = AccountUser(
                account_id=account.id,
                user_id=user.id,
                role=1,
                availability=1,
                permissions={"manage_channels": True},
            )
            session.add(account_user)

            session.commit()
            self.logger.info(f"Created account {account.id} and user {user.id}")
            return account, user

    def create_channel_and_inbox(self, account_id: int) -> tuple[Channel, Inbox]:
        """示例: 创建渠道和收件箱"""
        with get_db_session() as session:
            # 创建渠道
            channel = Channel(
                account_id=account_id,
                channel_type=ChannelType.WECHAT,
                platform_name="微信公众号",
                provider_config={
                    "app_id": "wx1234567890",
                    "app_secret": "secret_key",
                    "token": "token123",
                },
                ai_settings={"auto_reply_enabled": True, "confidence_threshold": 0.8},
                status=1,
            )
            session.add(channel)
            session.flush()

            # 创建收件箱
            inbox = Inbox(
                account_id=account_id,
                channel_id=channel.id,
                name="微信公众号收件箱",
                display_name="微信客服",
                ai_enabled=True,
                auto_reply_enabled=True,
                greeting_message="欢迎使用我们的客服系统！",
                ai_personality={
                    "tone": "professional",
                    "style": "helpful",
                    "personality": "friendly",
                },
                ai_confidence_threshold=0.8,
            )
            session.add(inbox)

            session.commit()
            self.logger.info(f"Created channel {channel.id} and inbox {inbox.id}")
            return channel, inbox

    def create_contact_and_conversation(
        self, account_id: int, inbox_id: int
    ) -> tuple[Contact, Conversation]:
        """示例: 创建联系人和会话"""
        with get_db_session() as session:
            # 创建联系人
            contact = Contact(
                account_id=account_id,
                name="张三",
                email="<EMAIL>",
                phone_number="+86138****8888",
                platform_identifiers={
                    "wechat": "wechat_openid_123",
                    "phone": "+86138****8888",
                },
                custom_attributes={"source": "公众号", "interest": "产品咨询"},
                ai_profile={
                    "sentiment": "positive",
                    "interests": ["产品", "价格"],
                    "interaction_style": "direct",
                },
                engagement_score=75.5,
                preferred_language="zh-CN",
            )
            session.add(contact)
            session.flush()

            # 创建联系人收件箱关联
            contact_inbox = ContactInbox(
                contact_id=contact.id,
                inbox_id=inbox_id,
                source_id="wechat_openid_123",
                platform_data={
                    "nickname": "张三",
                    "avatar_url": "https://example.com/avatar.jpg",
                    "subscribe_time": datetime.now().isoformat(),
                },
                ai_enabled=True,
            )
            session.add(contact_inbox)
            session.flush()

            # 创建会话
            conversation = Conversation(
                account_id=account_id,
                inbox_id=inbox_id,
                contact_id=contact.id,
                contact_inbox_id=contact_inbox.id,
                display_id=f"CONV_{datetime.now().strftime('%Y%m%d')}_{contact.id}",
                status=ConversationStatus.OPEN,
                priority=1,
                ai_mode=AIMode.ASSISTANT,
                ai_confidence=0.0,
                ai_context={
                    "user_intent": "unknown",
                    "conversation_stage": "greeting",
                    "topics_discussed": [],
                },
                labels=["新客户", "产品咨询"],
            )
            session.add(conversation)

            session.commit()
            self.logger.info(
                f"Created contact {contact.id} and conversation {conversation.id}"
            )
            return contact, conversation

    def create_messages_with_ai_processing(
        self, conversation: Conversation
    ) -> List[Message]:
        """示例: 创建消息和AI处理日志"""
        with get_db_session() as session:
            messages = []

            # 用户消息
            user_message = Message(
                account_id=conversation.account_id,
                inbox_id=conversation.inbox_id,
                conversation_id=conversation.id,
                message_type=MessageType.INCOMING,
                content_type=ContentType.TEXT,
                content="你好，请问你们的产品价格是多少？",
                source_id="wechat_msg_001",
                ai_generated=False,
                sender_type="contact",
                sender_id=conversation.contact_id,
            )
            session.add(user_message)
            session.flush()

            # AI处理日志
            ai_log = AIProcessingLog(
                message_id=user_message.id,
                conversation_id=conversation.id,
                ai_model="gpt-3.5-turbo",
                processing_type="intent_detection",
                intent_detected="pricing_inquiry",
                confidence_score=0.92,
                input_data={
                    "message": user_message.content,
                    "context": "product_inquiry",
                },
                output_data={
                    "intent": "pricing_inquiry",
                    "entities": ["产品", "价格"],
                    "sentiment": "neutral",
                },
                processing_time_ms=150,
            )
            session.add(ai_log)

            # AI回复消息
            ai_message = Message(
                account_id=conversation.account_id,
                inbox_id=conversation.inbox_id,
                conversation_id=conversation.id,
                message_type=MessageType.OUTGOING,
                content_type=ContentType.TEXT,
                content="您好！感谢您对我们产品的关注。我们有多款产品，价格从299元到1999元不等。请问您想了解哪款产品的具体信息呢？",
                ai_generated=True,
                ai_confidence=0.88,
                ai_metadata={
                    "model": "gpt-3.5-turbo",
                    "intent_matched": "pricing_inquiry",
                    "knowledge_base_used": True,
                },
                sender_type="ai_assistant",
                sender_id=None,
            )
            session.add(ai_message)

            messages.extend([user_message, ai_message])
            session.commit()

            self.logger.info(f"Created {len(messages)} messages with AI processing")
            return messages

    def query_conversations_with_filters(self, account_id: int) -> List[Conversation]:
        """示例: 复杂查询 - 筛选会话"""
        with get_db_session() as session:
            # 查询最近7天的开放会话，包含相关数据
            seven_days_ago = datetime.now() - timedelta(days=7)

            conversations = (
                session.query(Conversation)
                .options(
                    joinedload(Conversation.contact),  # 预加载联系人信息
                    joinedload(Conversation.inbox),  # 预加载收件箱信息
                    selectinload(Conversation.messages).selectinload(
                        Message.ai_processing_logs
                    ),  # 预加载消息和AI日志
                )
                .filter(
                    and_(
                        Conversation.account_id == account_id,
                        Conversation.status == ConversationStatus.OPEN,
                        Conversation.created_at >= seven_days_ago,
                    )
                )
                .order_by(desc(Conversation.last_activity_at))
                .limit(20)
                .all()
            )

            self.logger.info(f"Found {len(conversations)} conversations")
            return conversations

    def query_ai_performance_stats(self, account_id: int) -> Dict[str, Any]:
        """示例: 聚合查询 - AI性能统计"""
        with get_db_session() as session:
            # 统计AI处理性能
            stats = (
                session.query(
                    func.count(AIProcessingLog.id).label("total_processed"),
                    func.avg(AIProcessingLog.confidence_score).label("avg_confidence"),
                    func.avg(AIProcessingLog.processing_time_ms).label(
                        "avg_processing_time"
                    ),
                    func.count(
                        case([(AIProcessingLog.confidence_score >= 0.8, 1)])
                    ).label("high_confidence_count"),
                )
                .join(Conversation, AIProcessingLog.conversation_id == Conversation.id)
                .filter(Conversation.account_id == account_id)
                .first()
            )

            # 统计AI生成的消息
            ai_message_stats = (
                session.query(
                    func.count(Message.id).label("ai_messages"),
                    func.count(case([(Message.ai_confidence >= 0.8, 1)])).label(
                        "high_confidence_messages"
                    ),
                )
                .filter(
                    and_(Message.account_id == account_id, Message.ai_generated == True)
                )
                .first()
            )

            result = {
                "total_ai_processed": stats.total_processed or 0,
                "avg_confidence": float(stats.avg_confidence or 0),
                "avg_processing_time_ms": float(stats.avg_processing_time or 0),
                "high_confidence_rate": (stats.high_confidence_count or 0)
                / max(stats.total_processed or 1, 1),
                "ai_messages_sent": ai_message_stats.ai_messages or 0,
                "ai_high_confidence_messages": ai_message_stats.high_confidence_messages
                or 0,
            }

            self.logger.info(f"AI performance stats: {result}")
            return result

    def manage_knowledge_base(self, account_id: int) -> List[KnowledgeBase]:
        """示例: 知识库管理"""
        with get_db_session() as session:
            # 创建知识库条目
            knowledge_items = [
                KnowledgeBase(
                    account_id=account_id,
                    title="产品价格",
                    question="价格|多少钱|费用",
                    answer="我们的产品价格从299元到1999元不等，具体价格根据配置而定。",
                    category="pricing",
                    relevance_score=0.9,
                    metadata={"keywords": ["价格", "费用", "多少钱"]},
                ),
                KnowledgeBase(
                    account_id=account_id,
                    title="售后服务",
                    question="售后|保修|维修",
                    answer="我们提供1年免费保修服务，7天无理由退换货。",
                    category="service",
                    relevance_score=0.85,
                    metadata={"keywords": ["售后", "保修", "维修"]},
                ),
            ]

            for item in knowledge_items:
                session.add(item)

            session.commit()

            # 查询知识库
            active_knowledge = (
                session.query(KnowledgeBase)
                .filter(
                    and_(
                        KnowledgeBase.account_id == account_id,
                        KnowledgeBase.active == True,
                    )
                )
                .order_by(desc(KnowledgeBase.usage_count))
                .all()
            )

            self.logger.info(
                f"Created and queried {len(active_knowledge)} knowledge base items"
            )
            return active_knowledge

    def create_automation_rule(self, account_id: int, inbox_id: int) -> AutomationRule:
        """示例: 创建自动化规则"""
        with get_db_session() as session:
            rule = AutomationRule(
                account_id=account_id,
                inbox_id=inbox_id,
                rule_name="价格咨询自动回复",
                trigger_type="keyword_detected",
                conditions={
                    "keywords": ["价格", "多少钱", "费用"],
                    "confidence_threshold": 0.8,
                },
                actions={
                    "send_message": {
                        "content": "感谢您的咨询！我们的客服会尽快为您提供详细的价格信息。",
                        "delay_seconds": 0,
                    },
                    "assign_to_human": {
                        "condition": "high_value_customer",
                        "department": "sales",
                    },
                },
                ai_enhanced=True,
                active=True,
            )
            session.add(rule)
            session.commit()

            self.logger.info(f"Created automation rule: {rule.id}")
            return rule

    def simulate_human_takeover(
        self, conversation_id: int, user_id: int
    ) -> HumanTakeoverLog:
        """示例: 模拟人工接管"""
        with get_db_session() as session:
            takeover = HumanTakeoverLog(
                conversation_id=conversation_id,
                user_id=user_id,
                takeover_reason="客户要求人工服务",
                ai_last_action="auto_reply",
                ai_confidence_before=0.65,
                context_data={
                    "customer_tier": "VIP",
                    "issue_complexity": "high",
                    "ai_attempts": 3,
                },
                takeover_at=datetime.now(),
            )
            session.add(takeover)
            session.commit()

            self.logger.info(f"Created human takeover log: {takeover.id}")
            return takeover

    def run_all_examples(self):
        """运行所有示例"""
        self.logger.info("Starting database examples...")

        try:
            # 1. 创建账户和用户
            account, user = self.create_account_and_user()

            # 2. 创建渠道和收件箱
            channel, inbox = self.create_channel_and_inbox(account.id)

            # 3. 创建联系人和会话
            contact, conversation = self.create_contact_and_conversation(
                account.id, inbox.id
            )

            # 4. 创建消息和AI处理
            messages = self.create_messages_with_ai_processing(conversation)

            # 5. 查询会话
            conversations = self.query_conversations_with_filters(account.id)

            # 6. AI性能统计
            ai_stats = self.query_ai_performance_stats(account.id)

            # 7. 知识库管理
            knowledge_items = self.manage_knowledge_base(account.id)

            # 8. 创建自动化规则
            automation_rule = self.create_automation_rule(account.id, inbox.id)

            # 9. 模拟人工接管
            takeover_log = self.simulate_human_takeover(conversation.id, user.id)

            self.logger.info("All examples completed successfully!")

        except Exception as e:
            self.logger.error(f"Examples failed: {e}")
            raise


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # 运行示例
    examples = DatabaseExamples()
    examples.run_all_examples()
