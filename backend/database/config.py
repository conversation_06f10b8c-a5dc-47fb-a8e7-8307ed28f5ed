# database/config.py - 数据库配置管理
import os
from typing import Optional

try:
    from pydantic import validator
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings, validator

from sqlalchemy.pool import QueuePool


class DatabaseSettings(BaseSettings):
    """数据库配置设置"""

    # 基础连接配置
    DB_HOST: str = "localhost"
    DB_PORT: int = 5432
    DB_NAME: str = "chaiguanjia"
    DB_USER: str = "postgres"
    DB_PASSWORD: str = "password"

    # 连接池配置
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600
    DB_POOL_PRE_PING: bool = True

    # SSL配置
    DB_SSL_MODE: str = "prefer"
    DB_SSL_CERT: Optional[str] = None
    DB_SSL_KEY: Optional[str] = None
    DB_SSL_ROOT_CERT: Optional[str] = None

    # 调试配置
    DB_ECHO: bool = False
    DB_ECHO_POOL: bool = False

    # 连接字符串构建
    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return (
            f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}"
            f"@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
        )

    @property
    def async_database_url(self) -> str:
        """构建异步数据库连接URL"""
        return (
            f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASSWORD}"
            f"@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
        )

    @validator("DB_PASSWORD")
    def validate_password(cls, v):
        if not v:
            raise ValueError("数据库密码不能为空")
        return v

    @validator("DB_NAME")
    def validate_db_name(cls, v):
        if not v:
            raise ValueError("数据库名称不能为空")
        return v

    class Config:
        env_file = ".env"
        case_sensitive = True


# 不同环境的配置
class DevelopmentConfig(DatabaseSettings):
    """开发环境配置"""

    DB_ECHO: bool = True
    DB_POOL_SIZE: int = 5
    DB_MAX_OVERFLOW: int = 10


class ProductionConfig(DatabaseSettings):
    """生产环境配置"""

    DB_ECHO: bool = False
    DB_POOL_SIZE: int = 50
    DB_MAX_OVERFLOW: int = 100
    DB_POOL_RECYCLE: int = 1800
    DB_SSL_MODE: str = "require"


class TestConfig(DatabaseSettings):
    """测试环境配置"""

    DB_NAME: str = "chaiguanjia_test"
    DB_ECHO: bool = False
    DB_POOL_SIZE: int = 1
    DB_MAX_OVERFLOW: int = 5


def get_database_config() -> DatabaseSettings:
    """根据环境变量获取数据库配置"""
    env = os.getenv("ENVIRONMENT", "development").lower()

    if env == "production":
        return ProductionConfig()
    elif env == "test":
        return TestConfig()
    else:
        return DevelopmentConfig()


# 数据库连接配置映射
DB_CONFIG_MAP = {
    "development": DevelopmentConfig,
    "production": ProductionConfig,
    "test": TestConfig,
}

# 默认配置实例
db_config = get_database_config()
