# database/__init__.py - 数据库模块初始化
"""
柴管家数据库模块

该模块提供完整的数据库管理功能，包括：
1. 数据库连接管理
2. ORM模型定义
3. 数据库迁移
4. 初始化数据
5. 操作示例和测试

模块结构：
- config.py: 数据库配置管理
- connection.py: 连接池和会话管理
- migrations/: 数据库迁移脚本
- examples.py: 操作示例
- tests.py: 单元测试
"""

from .config import (
    DatabaseSettings,
    DevelopmentConfig,
    ProductionConfig,
    TestConfig,
    db_config,
    get_database_config,
)
from .connection import (
    DatabaseManager,
    db_manager,
    get_async_db_session,
    get_db_session,
    init_database,
)

# 导出核心功能
__all__ = [
    # 配置相关
    "DatabaseSettings",
    "DevelopmentConfig",
    "ProductionConfig",
    "TestConfig",
    "get_database_config",
    "db_config",
    # 连接管理
    "DatabaseManager",
    "db_manager",
    "get_db_session",
    "get_async_db_session",
    "init_database",
]

# 版本信息
__version__ = "1.0.0"
__author__ = "柴管家开发团队"


# 模块级别的便捷函数
def quick_init():
    """快速初始化数据库（用于开发环境）"""
    try:
        init_database()
        print("✓ 数据库初始化成功")
        return True
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        return False


def health_check():
    """数据库健康检查"""
    try:
        if db_manager.test_connection():
            print("✓ 数据库连接正常")
            return True
        else:
            print("✗ 数据库连接失败")
            return False
    except Exception as e:
        print(f"✗ 数据库健康检查失败: {e}")
        return False
