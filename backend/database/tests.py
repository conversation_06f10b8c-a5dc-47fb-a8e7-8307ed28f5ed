# database/tests.py - 数据库测试
"""
数据库模型和操作的单元测试
"""

import logging
from datetime import datetime, timedelta

import pytest
from database.config import TestConfig
from models import (
    Account,
    AIMode,
    Base,
    Channel,
    ChannelType,
    Contact,
    ContentType,
    Conversation,
    ConversationStatus,
    Inbox,
    Message,
    MessageType,
    User,
)
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

logger = logging.getLogger(__name__)


class TestDatabase:
    """数据库测试类"""

    @pytest.fixture(scope="function")
    def test_db(self):
        """测试数据库fixture"""
        # 使用内存SQLite数据库进行测试
        engine = create_engine(
            "sqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={
                "check_same_thread": False,
            },
            echo=False,
        )

        # 创建所有表
        Base.metadata.create_all(bind=engine)

        # 创建会话
        TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = TestSessionLocal()

        yield session

        session.close()
        Base.metadata.drop_all(bind=engine)

    def test_account_creation(self, test_db):
        """测试账户创建"""
        account = Account(
            name="测试账户",
            subdomain="test",
            ai_enabled=True,
            settings={"timezone": "Asia/Shanghai"},
            ai_config={"model": "gpt-3.5-turbo"},
        )

        test_db.add(account)
        test_db.commit()

        # 验证创建成功
        assert account.id is not None
        assert account.name == "测试账户"
        assert account.ai_enabled is True

        # 查询验证
        saved_account = test_db.query(Account).filter_by(name="测试账户").first()
        assert saved_account is not None
        assert saved_account.subdomain == "test"

    def test_channel_and_inbox_relationship(self, test_db):
        """测试渠道和收件箱关系"""
        # 创建账户
        account = Account(name="测试账户", subdomain="test")
        test_db.add(account)
        test_db.flush()

        # 创建渠道
        channel = Channel(
            account_id=account.id,
            channel_type=ChannelType.WECHAT,
            platform_name="微信测试",
            provider_config={"app_id": "test123"},
        )
        test_db.add(channel)
        test_db.flush()

        # 创建收件箱
        inbox = Inbox(
            account_id=account.id,
            channel_id=channel.id,
            name="测试收件箱",
            ai_enabled=True,
        )
        test_db.add(inbox)
        test_db.commit()

        # 验证关系
        assert inbox.channel.id == channel.id
        assert inbox.account.id == account.id
        assert channel.inbox.id == inbox.id

    def test_contact_and_conversation(self, test_db):
        """测试联系人和会话"""
        # 创建基础数据
        account = Account(name="测试账户", subdomain="test")
        test_db.add(account)
        test_db.flush()

        channel = Channel(
            account_id=account.id,
            channel_type=ChannelType.WECHAT,
            platform_name="微信测试",
        )
        test_db.add(channel)
        test_db.flush()

        inbox = Inbox(account_id=account.id, channel_id=channel.id, name="测试收件箱")
        test_db.add(inbox)
        test_db.flush()

        # 创建联系人
        contact = Contact(
            account_id=account.id,
            name="测试联系人",
            email="<EMAIL>",
            platform_identifiers={"wechat": "openid123"},
        )
        test_db.add(contact)
        test_db.flush()

        # 创建联系人收件箱关联
        from models import ContactInbox

        contact_inbox = ContactInbox(
            contact_id=contact.id, inbox_id=inbox.id, source_id="openid123"
        )
        test_db.add(contact_inbox)
        test_db.flush()

        # 创建会话
        conversation = Conversation(
            account_id=account.id,
            inbox_id=inbox.id,
            contact_id=contact.id,
            contact_inbox_id=contact_inbox.id,
            status=ConversationStatus.OPEN,
            ai_mode=AIMode.ASSISTANT,
        )
        test_db.add(conversation)
        test_db.commit()

        # 验证关系
        assert conversation.contact.name == "测试联系人"
        assert conversation.inbox.name == "测试收件箱"
        assert conversation.account.name == "测试账户"

    def test_message_creation_and_query(self, test_db):
        """测试消息创建和查询"""
        # 创建基础数据（简化版）
        account = Account(name="测试账户", subdomain="test")
        test_db.add(account)
        test_db.flush()

        channel = Channel(
            account_id=account.id, channel_type=ChannelType.WECHAT, platform_name="测试"
        )
        test_db.add(channel)
        test_db.flush()

        inbox = Inbox(account_id=account.id, channel_id=channel.id, name="测试收件箱")
        test_db.add(inbox)
        test_db.flush()

        contact = Contact(account_id=account.id, name="测试联系人")
        test_db.add(contact)
        test_db.flush()

        from models import ContactInbox

        contact_inbox = ContactInbox(
            contact_id=contact.id, inbox_id=inbox.id, source_id="test123"
        )
        test_db.add(contact_inbox)
        test_db.flush()

        conversation = Conversation(
            account_id=account.id,
            inbox_id=inbox.id,
            contact_id=contact.id,
            contact_inbox_id=contact_inbox.id,
        )
        test_db.add(conversation)
        test_db.flush()

        # 创建多条消息
        messages = []
        for i in range(5):
            message = Message(
                account_id=account.id,
                inbox_id=inbox.id,
                conversation_id=conversation.id,
                message_type=(
                    MessageType.INCOMING if i % 2 == 0 else MessageType.OUTGOING
                ),
                content_type=ContentType.TEXT,
                content=f"测试消息 {i}",
                sender_type="contact" if i % 2 == 0 else "ai",
                ai_generated=(i % 2 == 1),
            )
            messages.append(message)
            test_db.add(message)

        test_db.commit()

        # 查询测试
        all_messages = (
            test_db.query(Message).filter_by(conversation_id=conversation.id).all()
        )
        assert len(all_messages) == 5

        # 查询AI生成的消息
        ai_messages = test_db.query(Message).filter_by(ai_generated=True).all()
        assert len(ai_messages) == 2

        # 查询最近消息
        recent_messages = (
            test_db.query(Message)
            .filter_by(conversation_id=conversation.id)
            .order_by(Message.created_at.desc())
            .limit(3)
            .all()
        )
        assert len(recent_messages) == 3

    def test_user_and_account_relationship(self, test_db):
        """测试用户和账户关系"""
        # 创建用户
        user = User(
            email="<EMAIL>",
            name="测试用户",
            password_digest="hashed_password",
            role="agent",
        )
        test_db.add(user)
        test_db.flush()

        # 创建账户
        account = Account(name="测试账户", subdomain="test")
        test_db.add(account)
        test_db.flush()

        # 创建账户用户关联
        from models import AccountUser

        account_user = AccountUser(
            account_id=account.id,
            user_id=user.id,
            role=1,
            permissions={"manage_channels": True},
        )
        test_db.add(account_user)
        test_db.commit()

        # 验证关系
        assert len(user.account_users) == 1
        assert user.account_users[0].account.name == "测试账户"
        assert len(account.account_users) == 1
        assert account.account_users[0].user.email == "<EMAIL>"

    def test_ai_processing_log(self, test_db):
        """测试AI处理日志"""
        # 创建基础数据
        account = Account(name="测试账户", subdomain="test")
        test_db.add(account)
        test_db.flush()

        channel = Channel(
            account_id=account.id, channel_type=ChannelType.WECHAT, platform_name="测试"
        )
        test_db.add(channel)
        test_db.flush()

        inbox = Inbox(account_id=account.id, channel_id=channel.id, name="测试收件箱")
        test_db.add(inbox)
        test_db.flush()

        contact = Contact(account_id=account.id, name="测试联系人")
        test_db.add(contact)
        test_db.flush()

        from models import ContactInbox

        contact_inbox = ContactInbox(
            contact_id=contact.id, inbox_id=inbox.id, source_id="test123"
        )
        test_db.add(contact_inbox)
        test_db.flush()

        conversation = Conversation(
            account_id=account.id,
            inbox_id=inbox.id,
            contact_id=contact.id,
            contact_inbox_id=contact_inbox.id,
        )
        test_db.add(conversation)
        test_db.flush()

        message = Message(
            account_id=account.id,
            inbox_id=inbox.id,
            conversation_id=conversation.id,
            content="测试消息",
            sender_type="contact",
        )
        test_db.add(message)
        test_db.flush()

        # 创建AI处理日志
        from models import AIProcessingLog

        ai_log = AIProcessingLog(
            message_id=message.id,
            conversation_id=conversation.id,
            ai_model="gpt-3.5-turbo",
            processing_type="intent_detection",
            intent_detected="greeting",
            confidence_score=0.95,
            input_data={"message": "测试消息"},
            output_data={"intent": "greeting"},
            processing_time_ms=100,
        )
        test_db.add(ai_log)
        test_db.commit()

        # 验证AI日志
        assert ai_log.confidence_score == 0.95
        assert ai_log.intent_detected == "greeting"
        assert message.ai_processing_logs[0].ai_model == "gpt-3.5-turbo"

    def test_knowledge_base(self, test_db):
        """测试知识库"""
        account = Account(name="测试账户", subdomain="test")
        test_db.add(account)
        test_db.flush()

        from models import KnowledgeBase

        # 创建知识库条目
        knowledge = KnowledgeBase(
            account_id=account.id,
            title="测试知识",
            question="你好|hello",
            answer="您好！欢迎使用我们的服务！",
            category="greeting",
            relevance_score=0.9,
            usage_count=0,
            active=True,
        )
        test_db.add(knowledge)
        test_db.commit()

        # 查询测试
        kb_item = test_db.query(KnowledgeBase).filter_by(account_id=account.id).first()
        assert kb_item.title == "测试知识"
        assert kb_item.active is True

        # 更新使用次数
        kb_item.usage_count += 1
        test_db.commit()

        assert kb_item.usage_count == 1

    def test_automation_rule(self, test_db):
        """测试自动化规则"""
        account = Account(name="测试账户", subdomain="test")
        test_db.add(account)
        test_db.flush()

        channel = Channel(
            account_id=account.id, channel_type=ChannelType.WECHAT, platform_name="测试"
        )
        test_db.add(channel)
        test_db.flush()

        inbox = Inbox(account_id=account.id, channel_id=channel.id, name="测试收件箱")
        test_db.add(inbox)
        test_db.flush()

        from models import AutomationRule

        rule = AutomationRule(
            account_id=account.id,
            inbox_id=inbox.id,
            rule_name="自动回复测试",
            trigger_type="keyword_detected",
            conditions={"keywords": ["你好", "hello"]},
            actions={"send_message": {"content": "欢迎！"}},
            ai_enhanced=True,
            active=True,
        )
        test_db.add(rule)
        test_db.commit()

        # 验证规则
        saved_rule = (
            test_db.query(AutomationRule).filter_by(account_id=account.id).first()
        )
        assert saved_rule.rule_name == "自动回复测试"
        assert saved_rule.ai_enhanced is True
        assert "keywords" in saved_rule.conditions

    def test_data_integrity_constraints(self, test_db):
        """测试数据完整性约束"""
        # 测试必填字段
        with pytest.raises(Exception):
            account = Account()  # 缺少必填字段
            test_db.add(account)
            test_db.commit()

        # 测试外键约束
        with pytest.raises(Exception):
            channel = Channel(
                account_id=999999,  # 不存在的账户ID
                channel_type=ChannelType.WECHAT,
                platform_name="测试",
            )
            test_db.add(channel)
            test_db.commit()

    def test_query_performance(self, test_db):
        """测试查询性能（基础测试）"""
        # 创建大量测试数据
        account = Account(name="性能测试账户", subdomain="perf")
        test_db.add(account)
        test_db.flush()

        # 批量创建联系人
        contacts = []
        for i in range(100):
            contact = Contact(
                account_id=account.id,
                name=f"联系人{i}",
                email=f"contact{i}@example.com",
            )
            contacts.append(contact)

        test_db.add_all(contacts)
        test_db.commit()

        # 测试查询
        import time

        start_time = time.time()

        result = test_db.query(Contact).filter_by(account_id=account.id).all()

        end_time = time.time()
        query_time = end_time - start_time

        assert len(result) == 100
        assert query_time < 1.0  # 查询应该在1秒内完成

        logger.info(f"Query time for 100 contacts: {query_time:.4f} seconds")


def run_tests():
    """运行所有测试"""
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    run_tests()
