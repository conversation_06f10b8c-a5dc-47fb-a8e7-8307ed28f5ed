"""
错误处理系统测试
验证错误处理机制的各项功能
"""

import asyncio
from datetime import datetime
from unittest.mock import Mock, patch

import pytest
from app.api.v1.error_monitoring import router as error_monitoring_router
from app.core.error_handlers import (
    base_api_exception_handler,
    database_exception_handler,
    generic_exception_handler,
    http_exception_handler,
    raise_business_error,
    raise_forbidden,
    raise_not_found,
    raise_rate_limit_exceeded,
    raise_unauthorized,
    raise_validation_error,
    setup_error_handlers,
    validation_exception_handler,
)
from app.core.error_monitor import (
    ErrorIncident,
    ErrorMetrics,
    ErrorMonitor,
    get_error_monitor,
    record_error_event,
)
from app.core.exceptions import (
    AuthenticationException,
    AuthorizationException,
    BaseAPIException,
    BusinessLogicException,
    DatabaseException,
    ErrorCode,
    ErrorResponse,
    ErrorSeverity,
    ExternalServiceException,
    RateLimitException,
    ResourceNotFoundException,
    ValidationException,
    create_error_response,
    get_user_friendly_message,
    log_error_details,
)
from fastapi import FastAPI, HTTPException, Request
from fastapi.exceptions import RequestValidationError
from fastapi.testclient import TestClient


class TestErrorExceptions:
    """错误异常类测试"""

    def test_base_api_exception(self):
        """测试基础API异常"""
        exc = BaseAPIException(
            message="测试错误",
            error_code=ErrorCode.INTERNAL_SERVER_ERROR,
            details="详细错误信息",
        )

        assert exc.message == "测试错误"
        assert exc.error_code == ErrorCode.INTERNAL_SERVER_ERROR
        assert exc.details == "详细错误信息"
        assert exc.severity == ErrorSeverity.MEDIUM

    def test_validation_exception(self):
        """测试验证异常"""
        validation_errors = [{"field": "email", "message": "无效的邮箱格式"}]
        exc = ValidationException(
            message="参数验证失败", validation_errors=validation_errors
        )

        assert exc.error_code == ErrorCode.VALIDATION_ERROR
        assert exc.validation_errors == validation_errors
        assert exc.severity == ErrorSeverity.LOW

    def test_resource_not_found_exception(self):
        """测试资源未找到异常"""
        exc = ResourceNotFoundException(
            message="用户不存在", resource_type="user", resource_id="123"
        )

        assert exc.error_code == ErrorCode.RESOURCE_NOT_FOUND
        assert exc.resource_type == "user"
        assert exc.resource_id == "123"

    def test_authentication_exception(self):
        """测试认证异常"""
        exc = AuthenticationException(message="登录失败")

        assert exc.error_code == ErrorCode.UNAUTHORIZED
        assert exc.status_code == 401
        assert exc.severity == ErrorSeverity.MEDIUM

    def test_rate_limit_exception(self):
        """测试限流异常"""
        exc = RateLimitException(retry_after=60)

        assert exc.error_code == ErrorCode.RATE_LIMIT_EXCEEDED
        assert exc.retry_after == 60
        assert exc.status_code == 429


class TestErrorResponse:
    """错误响应格式测试"""

    def test_error_response_creation(self):
        """测试错误响应创建"""
        response = create_error_response(
            error_code=ErrorCode.BAD_REQUEST,
            message="请求参数错误",
            status_code=400,
            details="参数格式不正确",
        )

        assert response.success is False
        assert response.error_code == ErrorCode.BAD_REQUEST.value
        assert response.message == "请求参数错误"
        assert response.details == "参数格式不正确"
        assert response.severity == ErrorSeverity.MEDIUM
        assert isinstance(response.timestamp, datetime)

    def test_error_response_with_request(self):
        """测试带请求信息的错误响应"""
        # 创建模拟请求
        request = Mock(spec=Request)
        request.method = "POST"
        request.url.path = "/api/test"
        request.state.request_id = "test-request-id"

        response = create_error_response(
            error_code=ErrorCode.VALIDATION_ERROR, message="验证失败", request=request
        )

        assert response.request_id == "test-request-id"
        assert response.path == "/api/test"
        assert response.method == "POST"

    def test_get_user_friendly_message(self):
        """测试用户友好消息"""
        message = get_user_friendly_message(ErrorCode.UNAUTHORIZED)
        assert "身份验证失败" in message

        message = get_user_friendly_message(ErrorCode.NOT_FOUND)
        assert "不存在" in message

        # 测试未定义的错误码
        message = get_user_friendly_message(ErrorCode.UNKNOWN_ERROR)
        assert "系统发生" in message


class TestErrorHandlers:
    """错误处理器测试"""

    def setup_method(self):
        """测试前准备"""
        self.app = FastAPI()
        setup_error_handlers(self.app)

        # 添加测试路由
        @self.app.get("/test/custom-error")
        async def custom_error():
            raise BusinessLogicException("业务逻辑错误测试")

        @self.app.get("/test/http-error")
        async def http_error():
            raise HTTPException(status_code=404, detail="资源不存在")

        @self.app.get("/test/validation-error")
        async def validation_error(email: str):
            # 这会触发验证错误
            return {"email": email}

        @self.app.get("/test/generic-error")
        async def generic_error():
            raise ValueError("通用错误测试")

        self.client = TestClient(self.app)

    def test_custom_api_exception_handler(self):
        """测试自定义API异常处理"""
        response = self.client.get("/test/custom-error")

        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert data["error_code"] == ErrorCode.BUSINESS_LOGIC_ERROR.value
        assert "业务逻辑错误" in data["message"]

    def test_http_exception_handler(self):
        """测试HTTP异常处理"""
        response = self.client.get("/test/http-error")

        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
        assert data["error_code"] == ErrorCode.NOT_FOUND.value

    def test_validation_exception_handler(self):
        """测试验证异常处理"""
        # 发送无效的请求参数
        response = self.client.get("/test/validation-error")

        assert response.status_code == 422  # FastAPI默认验证错误状态码
        data = response.json()

        # 检查是否是我们的错误格式（如果集成了验证处理器）
        if "error_code" in data:
            assert data["success"] is False
            assert data["error_code"] == ErrorCode.VALIDATION_ERROR.value

    def test_generic_exception_handler(self):
        """测试通用异常处理"""
        response = self.client.get("/test/generic-error")

        assert response.status_code == 400  # ValueError被映射为400
        data = response.json()
        assert data["success"] is False
        assert data["error_code"] == ErrorCode.INVALID_PARAMETER.value

    def test_raise_helper_functions(self):
        """测试便捷的异常抛出函数"""
        # 测试raise_not_found
        with pytest.raises(ResourceNotFoundException):
            raise_not_found("user", "123")

        # 测试raise_validation_error
        with pytest.raises(ValidationException):
            raise_validation_error("email", "无效格式")

        # 测试raise_business_error
        with pytest.raises(BusinessLogicException):
            raise_business_error("业务错误")

        # 测试raise_unauthorized
        with pytest.raises(AuthenticationException):
            raise_unauthorized()

        # 测试raise_forbidden
        with pytest.raises(AuthorizationException):
            raise_forbidden()

        # 测试raise_rate_limit_exceeded
        with pytest.raises(RateLimitException):
            raise_rate_limit_exceeded(60)


class TestErrorMonitor:
    """错误监控器测试"""

    def setup_method(self):
        """测试前准备"""
        self.monitor = ErrorMonitor()

    def test_error_metrics_initialization(self):
        """测试错误指标初始化"""
        metrics = ErrorMetrics()

        assert metrics.total_errors == 0
        assert metrics.error_rate == 0.0
        assert metrics.critical_errors == 0
        assert len(metrics.error_code_counts) == 0

    def test_error_metrics_add_error(self):
        """测试添加错误记录"""
        metrics = ErrorMetrics()

        # 添加错误
        metrics.add_error("E1001", ErrorSeverity.HIGH)
        metrics.add_error("E2001", ErrorSeverity.LOW)
        metrics.add_error("E1001", ErrorSeverity.HIGH)  # 重复错误

        assert metrics.total_errors == 3
        assert metrics.high_errors == 2
        assert metrics.low_errors == 1
        assert metrics.error_code_counts["E1001"] == 2
        assert metrics.error_code_counts["E2001"] == 1

    def test_error_metrics_summary(self):
        """测试错误指标摘要"""
        metrics = ErrorMetrics()

        # 添加一些错误
        metrics.add_error("E1001", ErrorSeverity.CRITICAL)
        metrics.add_error("E2001", ErrorSeverity.HIGH)
        metrics.add_error("E3001", ErrorSeverity.MEDIUM)
        metrics.add_error("E4001", ErrorSeverity.LOW)

        summary = metrics.get_summary()

        assert summary["total_errors"] == 4
        assert summary["severity_distribution"]["critical"] == 1
        assert summary["severity_distribution"]["high"] == 1
        assert summary["severity_distribution"]["medium"] == 1
        assert summary["severity_distribution"]["low"] == 1
        assert len(summary["top_errors"]) > 0

    @pytest.mark.asyncio
    async def test_error_monitor_record_error(self):
        """测试错误监控器记录错误"""
        await self.monitor.record_error(
            error_code="E1001",
            message="测试错误",
            severity=ErrorSeverity.HIGH,
            user_id="user123",
            request_path="/api/test",
        )

        # 检查指标更新
        assert self.monitor.metrics.total_errors == 1
        assert self.monitor.metrics.high_errors == 1
        assert "E1001" in self.monitor.metrics.error_code_counts

        # 检查事件创建
        assert len(self.monitor.incidents) == 1
        incident_key = list(self.monitor.incidents.keys())[0]
        incident = self.monitor.incidents[incident_key]

        assert incident.error_code == "E1001"
        assert incident.message == "测试错误"
        assert incident.severity == ErrorSeverity.HIGH
        assert "user123" in incident.affected_users
        assert "/api/test" in incident.request_paths

    @pytest.mark.asyncio
    async def test_error_monitor_duplicate_incidents(self):
        """测试重复错误事件处理"""
        # 记录相同的错误两次
        await self.monitor.record_error(
            error_code="E1001",
            message="重复错误",
            severity=ErrorSeverity.MEDIUM,
            user_id="user1",
        )

        await self.monitor.record_error(
            error_code="E1001",
            message="重复错误",
            severity=ErrorSeverity.MEDIUM,
            user_id="user2",
        )

        # 应该只有一个事件，但计数为2
        assert len(self.monitor.incidents) == 1
        incident = list(self.monitor.incidents.values())[0]

        assert incident.count == 2
        assert len(incident.affected_users) == 2
        assert "user1" in incident.affected_users
        assert "user2" in incident.affected_users

    def test_error_monitor_get_trends(self):
        """测试错误趋势分析"""
        # 手动添加一些历史事件
        from datetime import datetime, timedelta

        past_time = datetime.now() - timedelta(hours=2)
        incident = ErrorIncident(
            incident_id="test-incident",
            error_code="E1001",
            message="历史错误",
            severity=ErrorSeverity.HIGH,
            first_seen=past_time,
            last_seen=past_time,
            count=5,
        )

        self.monitor.incidents["test_key"] = incident

        trends = self.monitor.get_error_trends(hours=24)

        assert trends["time_range"] == "最近24小时"
        assert trends["total_incidents"] == 1
        assert trends["total_errors"] == 5
        assert "E1001" in trends["error_code_distribution"]

    def test_error_monitor_resolve_incident(self):
        """测试解决错误事件"""
        # 创建一个事件
        incident = ErrorIncident(
            incident_id="test-incident-123",
            error_code="E1001",
            message="测试事件",
            severity=ErrorSeverity.MEDIUM,
            first_seen=datetime.now(),
            last_seen=datetime.now(),
        )

        self.monitor.incidents["test_key"] = incident

        # 解决事件
        success = self.monitor.resolve_incident(
            incident_id="test-incident-123", resolution_notes="问题已修复"
        )

        assert success is True
        assert incident.resolved is True
        assert incident.resolution_notes == "问题已修复"

    @pytest.mark.asyncio
    async def test_record_error_event_function(self):
        """测试便捷的错误记录函数"""
        await record_error_event(
            error_code=ErrorCode.DATABASE_ERROR,
            message="数据库连接失败",
            severity=ErrorSeverity.HIGH,
            user_id="user123",
        )

        monitor = get_error_monitor()
        assert monitor.metrics.total_errors >= 1
        assert monitor.metrics.high_errors >= 1


class TestErrorMonitoringAPI:
    """错误监控API测试"""

    def setup_method(self):
        """测试前准备"""
        self.app = FastAPI()
        self.app.include_router(error_monitoring_router)
        self.client = TestClient(self.app)

        # 重置错误监控器
        monitor = get_error_monitor()
        monitor.reset_metrics()

    def test_get_error_summary(self):
        """测试获取错误摘要"""
        response = self.client.get("/error-monitoring/summary")

        assert response.status_code == 200
        data = response.json()

        assert "total_errors" in data
        assert "error_rate" in data
        assert "severity_distribution" in data
        assert "top_errors" in data
        assert "recent_trend" in data

    def test_get_recent_incidents(self):
        """测试获取错误事件列表"""
        response = self.client.get("/error-monitoring/incidents?limit=5")

        assert response.status_code == 200
        data = response.json()

        assert isinstance(data, list)
        assert len(data) <= 5

    def test_get_error_trends(self):
        """测试获取错误趋势"""
        response = self.client.get("/error-monitoring/trends?hours=24")

        assert response.status_code == 200
        data = response.json()

        assert "time_range" in data
        assert "total_incidents" in data
        assert "total_errors" in data
        assert "hourly_distribution" in data
        assert "error_code_distribution" in data
        assert "severity_distribution" in data

    def test_get_daily_report(self):
        """测试获取日报"""
        response = self.client.get("/error-monitoring/reports/daily")

        assert response.status_code == 200
        data = response.json()

        assert data["report_type"] == "daily"
        assert "generated_at" in data
        assert "summary" in data
        assert "trends" in data
        assert "recommendations" in data

    def test_error_monitoring_health_check(self):
        """测试错误监控健康检查"""
        response = self.client.get("/error-monitoring/health-check")

        assert response.status_code == 200
        data = response.json()

        assert "status" in data
        assert "timestamp" in data
        assert "error_rate" in data
        assert "total_errors" in data
        assert "issues" in data

    def test_get_error_codes(self):
        """测试获取错误码列表"""
        response = self.client.get("/error-monitoring/error-codes")

        assert response.status_code == 200
        data = response.json()

        assert "total_codes" in data
        assert "categories" in data
        assert len(data["categories"]) > 0

        # 检查是否包含主要分类
        assert "通用错误" in data["categories"]
        assert "请求错误" in data["categories"]
        assert "认证授权错误" in data["categories"]

    def test_reset_error_metrics(self):
        """测试重置错误指标"""
        response = self.client.delete("/error-monitoring/metrics")

        assert response.status_code == 200
        data = response.json()

        assert data["success"] is True
        assert "reset_at" in data


class TestErrorHandlingIntegration:
    """错误处理集成测试"""

    def setup_method(self):
        """测试前准备"""
        self.app = FastAPI()
        setup_error_handlers(self.app)

        # 添加测试路由来模拟各种错误场景
        @self.app.get("/test/business-error")
        async def business_error():
            raise_business_error("用户余额不足")

        @self.app.get("/test/not-found")
        async def not_found_error():
            raise_not_found("用户", "123")

        @self.app.get("/test/unauthorized")
        async def unauthorized_error():
            raise_unauthorized("登录令牌无效")

        @self.app.get("/test/rate-limit")
        async def rate_limit_error():
            raise_rate_limit_exceeded(retry_after=60)

        self.client = TestClient(self.app)

    def test_business_error_handling(self):
        """测试业务错误处理"""
        response = self.client.get("/test/business-error")

        assert response.status_code == 400
        data = response.json()

        assert data["success"] is False
        assert data["error_code"] == ErrorCode.BUSINESS_LOGIC_ERROR.value
        assert "余额不足" in data["message"]
        assert data["severity"] == ErrorSeverity.MEDIUM.value

    def test_not_found_error_handling(self):
        """测试资源未找到错误处理"""
        response = self.client.get("/test/not-found")

        assert response.status_code == 404
        data = response.json()

        assert data["success"] is False
        assert data["error_code"] == ErrorCode.RESOURCE_NOT_FOUND.value
        assert "用户不存在" in data["message"]

    def test_unauthorized_error_handling(self):
        """测试认证错误处理"""
        response = self.client.get("/test/unauthorized")

        assert response.status_code == 401
        data = response.json()

        assert data["success"] is False
        assert data["error_code"] == ErrorCode.UNAUTHORIZED.value
        assert "令牌无效" in data["message"]

    def test_rate_limit_error_handling(self):
        """测试限流错误处理"""
        response = self.client.get("/test/rate-limit")

        assert response.status_code == 429
        data = response.json()

        assert data["success"] is False
        assert data["error_code"] == ErrorCode.RATE_LIMIT_EXCEEDED.value
        assert "Retry-After" in response.headers
        assert response.headers["Retry-After"] == "60"

    def test_error_response_format_consistency(self):
        """测试错误响应格式一致性"""
        # 测试多种不同的错误，确保响应格式一致
        test_endpoints = [
            "/test/business-error",
            "/test/not-found",
            "/test/unauthorized",
            "/test/rate-limit",
        ]

        for endpoint in test_endpoints:
            response = self.client.get(endpoint)
            data = response.json()

            # 检查必需字段
            required_fields = [
                "success",
                "error_code",
                "message",
                "timestamp",
                "severity",
            ]

            for field in required_fields:
                assert field in data, f"字段 {field} 在端点 {endpoint} 的响应中缺失"

            # 检查数据类型
            assert isinstance(data["success"], bool)
            assert isinstance(data["error_code"], str)
            assert isinstance(data["message"], str)
            assert isinstance(data["timestamp"], str)
            assert isinstance(data["severity"], str)

            # 确保success始终为False（错误响应）
            assert data["success"] is False


if __name__ == "__main__":
    # 运行错误处理测试
    pytest.main([__file__, "-v", "--tb=short"])
