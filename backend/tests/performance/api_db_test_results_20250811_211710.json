{"timestamp": "20250811_211710", "test_type": "api_based_database_performance", "results": {"/api/test/database": {"endpoint": "/api/test/database", "total_requests": 500, "concurrent_requests": 50, "successful_requests": 100, "failed_requests": 0, "error_rate": 0.0, "total_time_seconds": 0.03835105895996094, "qps": 13037.449644402446, "avg_response_time_ms": 2.6910042762756348, "median_response_time_ms": 0.9249448776245117, "p95_response_time_ms": 19.4242000579834, "p99_response_time_ms": 20.889759063720703}, "/api/test/redis": {"endpoint": "/api/test/redis", "total_requests": 1000, "concurrent_requests": 100, "successful_requests": 100, "failed_requests": 0, "error_rate": 0.0, "total_time_seconds": 0.015725135803222656, "qps": 63592.**********, "avg_response_time_ms": 0.9855580329895018, "median_response_time_ms": 0.8084774017333984, "p95_response_time_ms": 2.402067184448242, "p99_response_time_ms": 2.5191307067871094}, "/api/health": {"endpoint": "/api/health", "total_requests": 1000, "concurrent_requests": 100, "successful_requests": 100, "failed_requests": 0, "error_rate": 0.0, "total_time_seconds": 0.015401124954223633, "qps": 64930.32184157159, "avg_response_time_ms": 0.9460854530334473, "median_response_time_ms": 0.8069276809692383, "p95_response_time_ms": 2.043008804321289, "p99_response_time_ms": 2.371072769165039}, "/api/status": {"endpoint": "/api/status", "total_requests": 500, "concurrent_requests": 50, "successful_requests": 100, "failed_requests": 0, "error_rate": 0.0, "total_time_seconds": 0.012841939926147461, "qps": 38934.92750125318, "avg_response_time_ms": 0.7611298561096191, "median_response_time_ms": 0.6459951400756836, "p95_response_time_ms": 1.9612312316894531, "p99_response_time_ms": 2.052783966064453}}}