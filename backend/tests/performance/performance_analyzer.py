#!/usr/bin/env python3
"""
性能瓶颈分析工具
分析性能测试结果，识别瓶颈并提供优化建议
"""

import argparse
import json
import os
import statistics
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple


class PerformanceAnalyzer:
    """性能分析器"""

    def __init__(self):
        self.analysis_results = {}
        self.bottlenecks = []
        self.recommendations = []

    def load_test_results(self, results_dir: str) -> Dict[str, Any]:
        """加载测试结果文件"""
        results = {}

        # 加载API基准测试结果
        api_files = [
            f
            for f in os.listdir(results_dir)
            if f.startswith("api_benchmark_") and f.endswith(".json")
        ]
        if api_files:
            latest_api = max(api_files)
            with open(os.path.join(results_dir, latest_api), "r") as f:
                results["api_benchmark"] = json.load(f)

        # 加载数据库性能测试结果
        db_files = [
            f
            for f in os.listdir(results_dir)
            if f.startswith("database_performance_") and f.endswith(".json")
        ]
        if db_files:
            latest_db = max(db_files)
            with open(os.path.join(results_dir, latest_db), "r") as f:
                results["database_performance"] = json.load(f)

        # 加载系统监控结果
        monitor_file = os.path.join(results_dir, "system_monitor.json")
        if os.path.exists(monitor_file):
            with open(monitor_file, "r") as f:
                results["system_monitor"] = json.load(f)

        # 加载Locust结果
        locust_files = [
            f
            for f in os.listdir(results_dir)
            if f.startswith("locust_results_") and f.endswith("_stats.csv")
        ]
        if locust_files:
            latest_locust = max(locust_files)
            results["locust_stats"] = self._parse_locust_csv(
                os.path.join(results_dir, latest_locust)
            )

        return results

    def _parse_locust_csv(self, csv_file: str) -> Dict[str, Any]:
        """解析Locust CSV结果"""
        try:
            with open(csv_file, "r") as f:
                lines = f.readlines()
                if len(lines) > 1:
                    headers = lines[0].strip().split(",")
                    data = lines[1].strip().split(",")

                    result = {}
                    for i, header in enumerate(headers):
                        if i < len(data):
                            try:
                                result[header] = (
                                    float(data[i]) if "." in data[i] else int(data[i])
                                )
                            except ValueError:
                                result[header] = data[i]
                    return result
        except Exception as e:
            print(f"解析Locust CSV失败: {e}")

        return {}

    def analyze_api_performance(self, api_results: Dict) -> Dict[str, Any]:
        """分析API性能"""
        analysis = {
            "overall_grade": "A",
            "bottlenecks": [],
            "recommendations": [],
            "endpoints": {},
        }

        total_endpoints = 0
        good_endpoints = 0

        for endpoint, results in api_results.items():
            if isinstance(results, dict):
                endpoint_analysis = self._analyze_single_endpoint(endpoint, results)
                analysis["endpoints"][endpoint] = endpoint_analysis

                total_endpoints += 1
                if endpoint_analysis["grade"] in ["A", "B"]:
                    good_endpoints += 1

                # 收集瓶颈
                if endpoint_analysis["grade"] in ["C", "D"]:
                    analysis["bottlenecks"].append(
                        {
                            "type": "API性能瓶颈",
                            "location": endpoint,
                            "issue": endpoint_analysis["issues"],
                            "severity": (
                                "high"
                                if endpoint_analysis["grade"] == "D"
                                else "medium"
                            ),
                        }
                    )

        # 计算整体等级
        if total_endpoints > 0:
            good_ratio = good_endpoints / total_endpoints
            if good_ratio >= 0.9:
                analysis["overall_grade"] = "A"
            elif good_ratio >= 0.7:
                analysis["overall_grade"] = "B"
            elif good_ratio >= 0.5:
                analysis["overall_grade"] = "C"
            else:
                analysis["overall_grade"] = "D"

        # 生成API推荐
        if analysis["overall_grade"] in ["C", "D"]:
            analysis["recommendations"].extend(
                [
                    "考虑添加Redis缓存以减少数据库查询",
                    "优化数据库查询语句和索引",
                    "实现API响应压缩",
                    "考虑使用CDN缓存静态资源",
                ]
            )

        return analysis

    def _analyze_single_endpoint(self, endpoint: str, results: Dict) -> Dict[str, Any]:
        """分析单个端点性能"""
        analysis = {"endpoint": endpoint, "grade": "A", "issues": [], "metrics": {}}

        # 分析高负载测试结果
        high_load = results.get("高负载_async", {})
        if high_load and "error" not in high_load:
            qps = high_load.get("requests_per_second", 0)
            avg_time = high_load.get("mean_response_time", 0) * 1000
            p99_time = high_load.get("p99_response_time", 0) * 1000
            error_rate = high_load.get("error_rate", 0)

            analysis["metrics"] = {
                "qps": qps,
                "avg_response_time_ms": avg_time,
                "p99_response_time_ms": p99_time,
                "error_rate": error_rate,
            }

            # 评估性能等级
            issues = []

            if avg_time > 1000:
                issues.append(f"平均响应时间过长: {avg_time:.1f}ms")
                analysis["grade"] = "D"
            elif avg_time > 500:
                issues.append(f"平均响应时间较长: {avg_time:.1f}ms")
                analysis["grade"] = "C"

            if p99_time > 2000:
                issues.append(f"P99响应时间过长: {p99_time:.1f}ms")
                if analysis["grade"] in ["A", "B"]:
                    analysis["grade"] = "C"

            if error_rate > 10:
                issues.append(f"错误率过高: {error_rate:.1f}%")
                analysis["grade"] = "D"
            elif error_rate > 5:
                issues.append(f"错误率较高: {error_rate:.1f}%")
                if analysis["grade"] == "A":
                    analysis["grade"] = "B"

            if qps < 50:
                issues.append(f"QPS过低: {qps:.1f}")
                if analysis["grade"] in ["A", "B"]:
                    analysis["grade"] = "C"

            analysis["issues"] = issues

        return analysis

    def analyze_database_performance(self, db_results: Dict) -> Dict[str, Any]:
        """分析数据库性能"""
        analysis = {
            "postgresql": {},
            "redis": {},
            "bottlenecks": [],
            "recommendations": [],
        }

        # 分析PostgreSQL
        postgres_results = db_results.get("postgresql", {})
        if postgres_results:
            pg_analysis = self._analyze_postgresql(postgres_results)
            analysis["postgresql"] = pg_analysis

            if pg_analysis.get("bottlenecks"):
                analysis["bottlenecks"].extend(pg_analysis["bottlenecks"])
            if pg_analysis.get("recommendations"):
                analysis["recommendations"].extend(pg_analysis["recommendations"])

        # 分析Redis
        redis_results = db_results.get("redis", {})
        if redis_results:
            redis_analysis = self._analyze_redis(redis_results)
            analysis["redis"] = redis_analysis

            if redis_analysis.get("bottlenecks"):
                analysis["bottlenecks"].extend(redis_analysis["bottlenecks"])
            if redis_analysis.get("recommendations"):
                analysis["recommendations"].extend(redis_analysis["recommendations"])

        return analysis

    def _analyze_postgresql(self, postgres_results: Dict) -> Dict[str, Any]:
        """分析PostgreSQL性能"""
        analysis = {
            "grade": "A",
            "bottlenecks": [],
            "recommendations": [],
            "metrics": {},
        }

        high_load = postgres_results.get("高负载_async", {})
        if high_load and "error" not in high_load:
            qps = high_load.get("operations_per_second", 0)
            avg_time = high_load.get("avg_response_time_ms", 0)
            p99_time = high_load.get("p99_response_time_ms", 0)

            analysis["metrics"] = {
                "qps": qps,
                "avg_response_time_ms": avg_time,
                "p99_response_time_ms": p99_time,
            }

            # 性能评估
            if qps < 5000:
                analysis["grade"] = "D"
                analysis["bottlenecks"].append(
                    {
                        "type": "PostgreSQL QPS过低",
                        "location": "PostgreSQL数据库",
                        "issue": f"QPS仅为{qps:.0f}，远低于目标10000+",
                        "severity": "high",
                    }
                )
                analysis["recommendations"].extend(
                    [
                        "增加数据库连接池大小",
                        "优化SQL查询语句",
                        "添加适当的数据库索引",
                        "考虑数据库分区或分库分表",
                        "升级数据库硬件配置",
                    ]
                )
            elif qps < 8000:
                analysis["grade"] = "C"
                analysis["bottlenecks"].append(
                    {
                        "type": "PostgreSQL QPS偏低",
                        "location": "PostgreSQL数据库",
                        "issue": f"QPS为{qps:.0f}，未达到目标10000+",
                        "severity": "medium",
                    }
                )
                analysis["recommendations"].extend(
                    ["优化数据库连接池配置", "检查和优化慢查询", "考虑读写分离"]
                )
            elif qps < 10000:
                analysis["grade"] = "B"
                analysis["recommendations"].append("QPS接近目标，可进行微调优化")

            if avg_time > 50:
                analysis["recommendations"].append(
                    f"平均响应时间{avg_time:.1f}ms较长，需要优化查询"
                )

        return analysis

    def _analyze_redis(self, redis_results: Dict) -> Dict[str, Any]:
        """分析Redis性能"""
        analysis = {
            "grade": "A",
            "bottlenecks": [],
            "recommendations": [],
            "metrics": {},
        }

        high_load = redis_results.get("高负载_async", {})
        if high_load and "error" not in high_load:
            qps = high_load.get("operations_per_second", 0)
            avg_time = high_load.get("avg_response_time_ms", 0)

            analysis["metrics"] = {"qps": qps, "avg_response_time_ms": avg_time}

            # 性能评估
            if qps < 10000:
                analysis["grade"] = "D"
                analysis["bottlenecks"].append(
                    {
                        "type": "Redis QPS过低",
                        "location": "Redis缓存",
                        "issue": f"QPS仅为{qps:.0f}，低于预期",
                        "severity": "medium",
                    }
                )
                analysis["recommendations"].extend(
                    [
                        "检查Redis内存配置",
                        "优化Redis网络连接",
                        "检查Redis持久化配置",
                        "考虑Redis集群部署",
                    ]
                )
            elif qps < 30000:
                analysis["grade"] = "C"
                analysis["recommendations"].append("Redis QPS可以进一步优化")
            elif qps < 50000:
                analysis["grade"] = "B"

            if avg_time > 10:
                analysis["recommendations"].append(
                    f"Redis响应时间{avg_time:.1f}ms较长，检查网络延迟"
                )

        return analysis

    def analyze_system_resources(self, monitor_data: Dict) -> Dict[str, Any]:
        """分析系统资源使用"""
        analysis = {
            "cpu": {},
            "memory": {},
            "disk": {},
            "network": {},
            "bottlenecks": [],
            "recommendations": [],
        }

        metrics = monitor_data.get("metrics", [])
        if not metrics:
            return analysis

        # 分析CPU使用
        cpu_values = [m["cpu"]["percent"] for m in metrics if "cpu" in m]
        if cpu_values:
            cpu_analysis = self._analyze_cpu_usage(cpu_values)
            analysis["cpu"] = cpu_analysis
            if cpu_analysis.get("bottlenecks"):
                analysis["bottlenecks"].extend(cpu_analysis["bottlenecks"])
            if cpu_analysis.get("recommendations"):
                analysis["recommendations"].extend(cpu_analysis["recommendations"])

        # 分析内存使用
        memory_values = [m["memory"]["percent"] for m in metrics if "memory" in m]
        if memory_values:
            memory_analysis = self._analyze_memory_usage(memory_values)
            analysis["memory"] = memory_analysis
            if memory_analysis.get("bottlenecks"):
                analysis["bottlenecks"].extend(memory_analysis["bottlenecks"])
            if memory_analysis.get("recommendations"):
                analysis["recommendations"].extend(memory_analysis["recommendations"])

        return analysis

    def _analyze_cpu_usage(self, cpu_values: List[float]) -> Dict[str, Any]:
        """分析CPU使用率"""
        analysis = {
            "avg": statistics.mean(cpu_values),
            "max": max(cpu_values),
            "min": min(cpu_values),
            "violations": sum(1 for cpu in cpu_values if cpu > 70),
            "bottlenecks": [],
            "recommendations": [],
        }

        if analysis["max"] > 90:
            analysis["bottlenecks"].append(
                {
                    "type": "CPU使用率过高",
                    "location": "系统CPU",
                    "issue": f'最大CPU使用率{analysis["max"]:.1f}%，超过90%',
                    "severity": "high",
                }
            )
            analysis["recommendations"].extend(
                [
                    "增加CPU核心数",
                    "优化应用程序算法",
                    "考虑负载均衡",
                    "检查CPU密集型进程",
                ]
            )
        elif analysis["max"] > 70:
            analysis["bottlenecks"].append(
                {
                    "type": "CPU使用率较高",
                    "location": "系统CPU",
                    "issue": f'最大CPU使用率{analysis["max"]:.1f}%，超过70%阈值',
                    "severity": "medium",
                }
            )
            analysis["recommendations"].append("监控CPU使用率，考虑优化性能")

        if analysis["avg"] > 60:
            analysis["recommendations"].append(
                f'平均CPU使用率{analysis["avg"]:.1f}%较高，需要优化'
            )

        return analysis

    def _analyze_memory_usage(self, memory_values: List[float]) -> Dict[str, Any]:
        """分析内存使用率"""
        analysis = {
            "avg": statistics.mean(memory_values),
            "max": max(memory_values),
            "min": min(memory_values),
            "violations": sum(1 for mem in memory_values if mem > 80),
            "bottlenecks": [],
            "recommendations": [],
        }

        if analysis["max"] > 95:
            analysis["bottlenecks"].append(
                {
                    "type": "内存使用率过高",
                    "location": "系统内存",
                    "issue": f'最大内存使用率{analysis["max"]:.1f}%，接近系统极限',
                    "severity": "high",
                }
            )
            analysis["recommendations"].extend(
                ["增加系统内存", "检查内存泄漏", "优化应用内存使用", "考虑内存缓存优化"]
            )
        elif analysis["max"] > 80:
            analysis["bottlenecks"].append(
                {
                    "type": "内存使用率较高",
                    "location": "系统内存",
                    "issue": f'最大内存使用率{analysis["max"]:.1f}%，超过80%阈值',
                    "severity": "medium",
                }
            )
            analysis["recommendations"].append("监控内存使用，考虑内存优化")

        if analysis["avg"] > 70:
            analysis["recommendations"].append(
                f'平均内存使用率{analysis["avg"]:.1f}%较高'
            )

        return analysis

    def identify_critical_bottlenecks(
        self, all_results: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """识别关键性能瓶颈"""
        bottlenecks = []

        # 收集所有瓶颈
        for category, results in all_results.items():
            if isinstance(results, dict) and "bottlenecks" in results:
                bottlenecks.extend(results["bottlenecks"])

        # 按严重程度排序
        severity_order = {"high": 0, "medium": 1, "low": 2}
        bottlenecks.sort(key=lambda x: severity_order.get(x.get("severity", "low"), 2))

        return bottlenecks

    def generate_optimization_recommendations(
        self, all_results: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成优化建议"""
        recommendations = []

        # 按优先级分类建议
        high_priority = []
        medium_priority = []
        low_priority = []

        # 收集所有建议
        all_recs = []
        for category, results in all_results.items():
            if isinstance(results, dict) and "recommendations" in results:
                all_recs.extend(results["recommendations"])

        # 去重
        unique_recs = list(set(all_recs))

        # 分类建议
        for rec in unique_recs:
            if any(
                keyword in rec.lower()
                for keyword in ["qps过低", "过高", "升级", "增加"]
            ):
                high_priority.append(
                    {
                        "priority": "high",
                        "category": "性能瓶颈",
                        "recommendation": rec,
                        "impact": "high",
                    }
                )
            elif any(keyword in rec.lower() for keyword in ["优化", "检查", "考虑"]):
                medium_priority.append(
                    {
                        "priority": "medium",
                        "category": "性能优化",
                        "recommendation": rec,
                        "impact": "medium",
                    }
                )
            else:
                low_priority.append(
                    {
                        "priority": "low",
                        "category": "一般建议",
                        "recommendation": rec,
                        "impact": "low",
                    }
                )

        # 合并建议
        recommendations.extend(high_priority)
        recommendations.extend(medium_priority)
        recommendations.extend(low_priority)

        return recommendations

    def comprehensive_analysis(self, results_dir: str) -> Dict[str, Any]:
        """综合性能分析"""
        print("=" * 60)
        print("性能瓶颈分析")
        print("=" * 60)

        # 加载测试结果
        test_results = self.load_test_results(results_dir)

        if not test_results:
            print("❌ 未找到测试结果文件")
            return {}

        analysis_results = {}

        # 分析API性能
        if "api_benchmark" in test_results:
            print("🌐 分析API性能...")
            api_analysis = self.analyze_api_performance(test_results["api_benchmark"])
            analysis_results["api"] = api_analysis
            print(f"  API整体等级: {api_analysis['overall_grade']}")

        # 分析数据库性能
        if "database_performance" in test_results:
            print("🗄️ 分析数据库性能...")
            db_analysis = self.analyze_database_performance(
                test_results["database_performance"].get("test_results", {})
            )
            analysis_results["database"] = db_analysis

        # 分析系统资源
        if "system_monitor" in test_results:
            print("📊 分析系统资源...")
            resource_analysis = self.analyze_system_resources(
                test_results["system_monitor"]
            )
            analysis_results["resources"] = resource_analysis

        # 识别关键瓶颈
        critical_bottlenecks = self.identify_critical_bottlenecks(analysis_results)

        # 生成优化建议
        recommendations = self.generate_optimization_recommendations(analysis_results)

        # 生成最终分析报告
        final_analysis = {
            "timestamp": datetime.now().isoformat(),
            "analysis_results": analysis_results,
            "critical_bottlenecks": critical_bottlenecks,
            "optimization_recommendations": recommendations,
            "summary": self._generate_summary(analysis_results, critical_bottlenecks),
        }

        # 保存分析结果
        output_file = os.path.join(
            results_dir,
            f'performance_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json',
        )
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(final_analysis, f, indent=2, ensure_ascii=False)

        # 生成报告
        self.generate_analysis_report(final_analysis)

        print(f"\n📄 分析结果已保存到: {output_file}")

        return final_analysis

    def _generate_summary(
        self, analysis_results: Dict, bottlenecks: List[Dict]
    ) -> Dict[str, Any]:
        """生成分析摘要"""
        summary = {
            "overall_health": "good",
            "critical_issues": len(
                [b for b in bottlenecks if b.get("severity") == "high"]
            ),
            "medium_issues": len(
                [b for b in bottlenecks if b.get("severity") == "medium"]
            ),
            "performance_score": 85,  # 默认分数
        }

        # 计算性能分数
        score = 100

        # API性能影响
        api_grade = analysis_results.get("api", {}).get("overall_grade", "A")
        grade_scores = {"A": 0, "B": -5, "C": -15, "D": -30}
        score += grade_scores.get(api_grade, -30)

        # 数据库性能影响
        pg_grade = (
            analysis_results.get("database", {}).get("postgresql", {}).get("grade", "A")
        )
        score += grade_scores.get(pg_grade, -30)

        redis_grade = (
            analysis_results.get("database", {}).get("redis", {}).get("grade", "A")
        )
        score += grade_scores.get(redis_grade, -10)

        # 资源使用影响
        if summary["critical_issues"] > 0:
            score -= summary["critical_issues"] * 20
        if summary["medium_issues"] > 0:
            score -= summary["medium_issues"] * 10

        summary["performance_score"] = max(0, min(100, score))

        # 整体健康状态
        if summary["performance_score"] >= 80:
            summary["overall_health"] = "good"
        elif summary["performance_score"] >= 60:
            summary["overall_health"] = "fair"
        else:
            summary["overall_health"] = "poor"

        return summary

    def generate_analysis_report(self, analysis: Dict[str, Any]):
        """生成分析报告"""
        print("\n" + "=" * 60)
        print("性能瓶颈分析报告")
        print("=" * 60)

        summary = analysis.get("summary", {})
        bottlenecks = analysis.get("critical_bottlenecks", [])
        recommendations = analysis.get("optimization_recommendations", [])

        # 总体评估
        print(f"\n📊 总体评估:")
        print(f"  性能分数: {summary.get('performance_score', 0)}/100")
        print(f"  健康状态: {summary.get('overall_health', 'unknown')}")
        print(f"  严重问题: {summary.get('critical_issues', 0)}个")
        print(f"  一般问题: {summary.get('medium_issues', 0)}个")

        # 关键瓶颈
        if bottlenecks:
            print(f"\n🚨 关键性能瓶颈:")
            for i, bottleneck in enumerate(bottlenecks[:5], 1):  # 显示前5个
                severity = bottleneck.get("severity", "unknown")
                emoji = "🔴" if severity == "high" else "🟡"
                print(f"  {i}. {emoji} {bottleneck.get('type', 'Unknown')}")
                print(f"     位置: {bottleneck.get('location', 'Unknown')}")
                print(f"     问题: {bottleneck.get('issue', 'Unknown')}")
                print()
        else:
            print(f"\n✅ 未发现关键性能瓶颈")

        # 优化建议
        high_priority = [r for r in recommendations if r.get("priority") == "high"]
        medium_priority = [r for r in recommendations if r.get("priority") == "medium"]

        if high_priority:
            print(f"\n🔥 高优先级优化建议:")
            for i, rec in enumerate(high_priority[:5], 1):
                print(f"  {i}. {rec.get('recommendation', 'Unknown')}")

        if medium_priority:
            print(f"\n💡 中优先级优化建议:")
            for i, rec in enumerate(medium_priority[:5], 1):
                print(f"  {i}. {rec.get('recommendation', 'Unknown')}")

        # 详细分析结果
        analysis_results = analysis.get("analysis_results", {})

        if "api" in analysis_results:
            api_analysis = analysis_results["api"]
            print(f"\n🌐 API性能分析:")
            print(f"  整体等级: {api_analysis.get('overall_grade', 'Unknown')}")

            endpoints = api_analysis.get("endpoints", {})
            poor_endpoints = [
                ep for ep, data in endpoints.items() if data.get("grade") in ["C", "D"]
            ]
            if poor_endpoints:
                print(f"  需要优化的端点: {', '.join(poor_endpoints[:3])}")

        if "database" in analysis_results:
            db_analysis = analysis_results["database"]
            print(f"\n🗄️ 数据库性能分析:")

            pg_data = db_analysis.get("postgresql", {})
            if pg_data:
                pg_qps = pg_data.get("metrics", {}).get("qps", 0)
                print(f"  PostgreSQL QPS: {pg_qps:.0f} (目标: 10000+)")
                print(f"  PostgreSQL等级: {pg_data.get('grade', 'Unknown')}")

            redis_data = db_analysis.get("redis", {})
            if redis_data:
                redis_qps = redis_data.get("metrics", {}).get("qps", 0)
                print(f"  Redis QPS: {redis_qps:.0f}")
                print(f"  Redis等级: {redis_data.get('grade', 'Unknown')}")

        if "resources" in analysis_results:
            resource_analysis = analysis_results["resources"]
            print(f"\n📊 系统资源分析:")

            cpu_data = resource_analysis.get("cpu", {})
            if cpu_data:
                print(
                    f"  CPU使用率: 平均{cpu_data.get('avg', 0):.1f}%, "
                    f"最大{cpu_data.get('max', 0):.1f}%"
                )

            memory_data = resource_analysis.get("memory", {})
            if memory_data:
                print(
                    f"  内存使用率: 平均{memory_data.get('avg', 0):.1f}%, "
                    f"最大{memory_data.get('max', 0):.1f}%"
                )

        print("=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="性能瓶颈分析工具")
    parser.add_argument("--results-dir", required=True, help="测试结果目录")

    args = parser.parse_args()

    if not os.path.exists(args.results_dir):
        print(f"❌ 结果目录不存在: {args.results_dir}")
        return

    analyzer = PerformanceAnalyzer()

    try:
        analysis = analyzer.comprehensive_analysis(args.results_dir)

        # 返回分析结果
        if analysis:
            summary = analysis.get("summary", {})
            score = summary.get("performance_score", 0)
            if score >= 80:
                print("\n✅ 系统性能良好")
                return 0
            elif score >= 60:
                print("\n⚠️  系统性能一般，建议优化")
                return 1
            else:
                print("\n❌ 系统性能较差，需要重点优化")
                return 2
        else:
            print("\n❌ 分析失败")
            return 3

    except Exception as e:
        print(f"\n❌ 分析过程中发生错误: {e}")
        return 3


if __name__ == "__main__":
    import sys

    sys.exit(main())
