#!/usr/bin/env python3
"""
性能测试最终报告生成器
生成符合验收标准的正式性能测试报告
"""

import argparse
import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional


class PerformanceReportGenerator:
    """性能测试报告生成器"""

    def __init__(self):
        self.test_requirements = {
            "api_response_time": {
                "threshold": 500,
                "unit": "ms",
                "description": "API接口在1000并发下响应时间<500ms",
            },
            "database_qps": {
                "threshold": 10000,
                "unit": "QPS",
                "description": "数据库支持10000+QPS查询",
            },
            "memory_usage": {
                "threshold": 80,
                "unit": "%",
                "description": "内存使用率在高负载下<80%",
            },
            "cpu_usage": {
                "threshold": 70,
                "unit": "%",
                "description": "CPU使用率在高负载下<70%",
            },
            "system_recovery": {"description": "系统在压力测试后可以正常恢复"},
        }

    def load_test_data(self, results_dir: str) -> Dict[str, Any]:
        """加载所有测试数据"""
        data = {
            "api_benchmark": None,
            "database_performance": None,
            "system_monitor": None,
            "performance_analysis": None,
            "locust_results": None,
        }

        try:
            # 加载API基准测试结果
            api_files = [
                f
                for f in os.listdir(results_dir)
                if f.startswith("api_benchmark_") and f.endswith(".json")
            ]
            if api_files:
                latest_api = max(api_files)
                with open(os.path.join(results_dir, latest_api), "r") as f:
                    data["api_benchmark"] = json.load(f)

            # 加载数据库性能测试结果
            db_files = [
                f
                for f in os.listdir(results_dir)
                if f.startswith("database_performance_") and f.endswith(".json")
            ]
            if db_files:
                latest_db = max(db_files)
                with open(os.path.join(results_dir, latest_db), "r") as f:
                    data["database_performance"] = json.load(f)

            # 加载系统监控结果
            monitor_file = os.path.join(results_dir, "system_monitor.json")
            if os.path.exists(monitor_file):
                with open(monitor_file, "r") as f:
                    data["system_monitor"] = json.load(f)

            # 加载性能分析结果
            analysis_files = [
                f
                for f in os.listdir(results_dir)
                if f.startswith("performance_analysis_") and f.endswith(".json")
            ]
            if analysis_files:
                latest_analysis = max(analysis_files)
                with open(os.path.join(results_dir, latest_analysis), "r") as f:
                    data["performance_analysis"] = json.load(f)

            # 加载最终综合报告
            final_files = [
                f
                for f in os.listdir(results_dir)
                if f.startswith("final_report_") and f.endswith(".json")
            ]
            if final_files:
                latest_final = max(final_files)
                with open(os.path.join(results_dir, latest_final), "r") as f:
                    data["final_comprehensive"] = json.load(f)

        except Exception as e:
            print(f"加载测试数据时发生错误: {e}")

        return data

    def check_compliance(self, test_data: Dict[str, Any]) -> Dict[str, Any]:
        """检查验收标准合规性"""
        compliance = {
            "api_response_time": {"passed": False, "value": None, "details": []},
            "database_qps": {"passed": False, "value": None, "details": []},
            "memory_usage": {"passed": False, "value": None, "details": []},
            "cpu_usage": {"passed": False, "value": None, "details": []},
            "system_recovery": {"passed": False, "details": []},
            "overall": False,
        }

        # 检查API响应时间
        api_data = test_data.get("api_benchmark")
        if api_data:
            api_compliance = self._check_api_compliance(api_data)
            compliance["api_response_time"] = api_compliance

        # 检查数据库QPS
        db_data = test_data.get("database_performance")
        if db_data:
            db_compliance = self._check_database_compliance(db_data)
            compliance["database_qps"] = db_compliance

        # 检查系统资源使用
        monitor_data = test_data.get("system_monitor")
        if monitor_data:
            resource_compliance = self._check_resource_compliance(monitor_data)
            compliance["memory_usage"] = resource_compliance["memory"]
            compliance["cpu_usage"] = resource_compliance["cpu"]

        # 检查系统恢复
        recovery_compliance = self._check_system_recovery(test_data)
        compliance["system_recovery"] = recovery_compliance

        # 整体合规性
        compliance["overall"] = all(
            [
                compliance["api_response_time"]["passed"],
                compliance["database_qps"]["passed"],
                compliance["memory_usage"]["passed"],
                compliance["cpu_usage"]["passed"],
                compliance["system_recovery"]["passed"],
            ]
        )

        return compliance

    def _check_api_compliance(self, api_data: Dict) -> Dict[str, Any]:
        """检查API性能合规性"""
        result = {"passed": False, "value": None, "details": [], "test_endpoints": {}}

        passed_endpoints = 0
        total_endpoints = 0
        max_response_time = 0

        for endpoint, endpoint_data in api_data.items():
            if isinstance(endpoint_data, dict):
                high_load = endpoint_data.get("高负载_async", {})
                if high_load and "error" not in high_load:
                    total_endpoints += 1
                    avg_time = high_load.get("mean_response_time", 0) * 1000
                    max_response_time = max(max_response_time, avg_time)

                    endpoint_passed = avg_time < 500
                    if endpoint_passed:
                        passed_endpoints += 1

                    result["test_endpoints"][endpoint] = {
                        "response_time_ms": avg_time,
                        "passed": endpoint_passed,
                        "qps": high_load.get("requests_per_second", 0),
                        "error_rate": high_load.get("error_rate", 0),
                    }

                    result["details"].append(
                        f"{endpoint}: {avg_time:.1f}ms ({'✅' if endpoint_passed else '❌'})"
                    )

        if total_endpoints > 0:
            result["passed"] = (
                passed_endpoints / total_endpoints
            ) >= 0.8  # 80%端点需要通过
            result["value"] = max_response_time
            result["pass_rate"] = (passed_endpoints / total_endpoints) * 100

        return result

    def _check_database_compliance(self, db_data: Dict) -> Dict[str, Any]:
        """检查数据库性能合规性"""
        result = {
            "passed": False,
            "value": None,
            "details": [],
            "postgresql_qps": 0,
            "redis_qps": 0,
        }

        test_results = db_data.get("test_results", {})

        # 检查PostgreSQL QPS
        postgres_data = test_results.get("postgresql", {})
        if postgres_data:
            high_load = postgres_data.get("高负载_async", {})
            if high_load and "error" not in high_load:
                pg_qps = high_load.get("operations_per_second", 0)
                result["postgresql_qps"] = pg_qps
                result["value"] = pg_qps
                result["passed"] = pg_qps >= 10000

                result["details"].append(
                    f"PostgreSQL QPS: {pg_qps:.0f} ({'✅' if pg_qps >= 10000 else '❌'})"
                )

        # 检查Redis QPS（额外信息）
        redis_data = test_results.get("redis", {})
        if redis_data:
            high_load = redis_data.get("高负载_async", {})
            if high_load and "error" not in high_load:
                redis_qps = high_load.get("operations_per_second", 0)
                result["redis_qps"] = redis_qps

                result["details"].append(f"Redis QPS: {redis_qps:.0f}")

        return result

    def _check_resource_compliance(self, monitor_data: Dict) -> Dict[str, Any]:
        """检查系统资源使用合规性"""
        result = {
            "memory": {"passed": False, "value": None, "details": []},
            "cpu": {"passed": False, "value": None, "details": []},
        }

        metrics = monitor_data.get("metrics", [])
        if not metrics:
            return result

        # 分析CPU使用率
        cpu_values = [m["cpu"]["percent"] for m in metrics if "cpu" in m]
        if cpu_values:
            max_cpu = max(cpu_values)
            avg_cpu = sum(cpu_values) / len(cpu_values)

            result["cpu"]["value"] = max_cpu
            result["cpu"]["passed"] = max_cpu < 70
            result["cpu"]["details"] = [
                f"最大CPU使用率: {max_cpu:.1f}% ({'✅' if max_cpu < 70 else '❌'})",
                f"平均CPU使用率: {avg_cpu:.1f}%",
            ]

        # 分析内存使用率
        memory_values = [m["memory"]["percent"] for m in metrics if "memory" in m]
        if memory_values:
            max_memory = max(memory_values)
            avg_memory = sum(memory_values) / len(memory_values)

            result["memory"]["value"] = max_memory
            result["memory"]["passed"] = max_memory < 80
            result["memory"]["details"] = [
                f"最大内存使用率: {max_memory:.1f}% ({'✅' if max_memory < 80 else '❌'})",
                f"平均内存使用率: {avg_memory:.1f}%",
            ]

        return result

    def _check_system_recovery(self, test_data: Dict) -> Dict[str, Any]:
        """检查系统恢复能力"""
        result = {"passed": True, "details": []}  # 默认通过，除非发现问题

        # 检查是否有严重错误或系统崩溃记录
        analysis_data = test_data.get("performance_analysis")
        if analysis_data:
            bottlenecks = analysis_data.get("critical_bottlenecks", [])
            critical_issues = [b for b in bottlenecks if b.get("severity") == "high"]

            if critical_issues:
                result["passed"] = False
                result["details"].append(f"发现{len(critical_issues)}个严重性能问题")
                for issue in critical_issues[:3]:  # 只显示前3个
                    result["details"].append(f"- {issue.get('type', 'Unknown')}")
            else:
                result["details"].append("未发现系统严重问题")

        # 检查监控数据中的异常
        monitor_data = test_data.get("system_monitor")
        if monitor_data:
            alerts_count = monitor_data.get("alerts_count", {})
            total_alerts = sum(alerts_count.values())

            if total_alerts > 100:  # 如果警告过多，认为系统不稳定
                result["passed"] = False
                result["details"].append(f"系统资源警告过多: {total_alerts}次")
            else:
                result["details"].append("系统资源使用稳定")

        return result

    def generate_executive_summary(
        self, compliance: Dict[str, Any], test_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成执行摘要"""
        summary = {
            "overall_result": "PASS" if compliance["overall"] else "FAIL",
            "test_date": datetime.now().strftime("%Y年%m月%d日"),
            "test_duration": "未知",
            "key_findings": [],
            "compliance_score": 0,
            "recommendations": [],
        }

        # 计算合规分数
        passed_checks = sum(
            1
            for key in [
                "api_response_time",
                "database_qps",
                "memory_usage",
                "cpu_usage",
                "system_recovery",
            ]
            if compliance[key]["passed"]
        )
        summary["compliance_score"] = (passed_checks / 5) * 100

        # 关键发现
        if compliance["api_response_time"]["passed"]:
            summary["key_findings"].append("✅ API响应时间满足要求")
        else:
            max_time = compliance["api_response_time"].get("value", 0)
            summary["key_findings"].append(
                f"❌ API最大响应时间{max_time:.1f}ms超过500ms阈值"
            )

        if compliance["database_qps"]["passed"]:
            qps = compliance["database_qps"].get("value", 0)
            summary["key_findings"].append(f"✅ 数据库QPS达到{qps:.0f}，满足10000+要求")
        else:
            qps = compliance["database_qps"].get("value", 0)
            summary["key_findings"].append(f"❌ 数据库QPS仅{qps:.0f}，未达到10000要求")

        if compliance["memory_usage"]["passed"]:
            summary["key_findings"].append("✅ 内存使用率控制良好")
        else:
            max_mem = compliance["memory_usage"].get("value", 0)
            summary["key_findings"].append(
                f"❌ 内存使用率最高{max_mem:.1f}%超过80%阈值"
            )

        if compliance["cpu_usage"]["passed"]:
            summary["key_findings"].append("✅ CPU使用率控制良好")
        else:
            max_cpu = compliance["cpu_usage"].get("value", 0)
            summary["key_findings"].append(f"❌ CPU使用率最高{max_cpu:.1f}%超过70%阈值")

        # 生成建议
        if not compliance["overall"]:
            summary["recommendations"].extend(
                [
                    "建议进行性能优化后重新测试",
                    "重点关注未通过的性能指标",
                    "考虑硬件升级或架构优化",
                ]
            )
        else:
            summary["recommendations"].extend(
                [
                    "系统性能良好，可以投入生产使用",
                    "建议建立性能监控体系",
                    "定期进行性能回归测试",
                ]
            )

        return summary

    def generate_html_report(
        self,
        compliance: Dict[str, Any],
        summary: Dict[str, Any],
        test_data: Dict[str, Any],
    ) -> str:
        """生成HTML格式报告"""

        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>柴管家系统性能测试报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #007acc;
            margin: 0;
            font-size: 2.5em;
        }}
        .header .subtitle {{
            color: #666;
            font-size: 1.2em;
            margin-top: 10px;
        }}
        .summary-box {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }}
        .summary-box h2 {{
            margin: 0 0 15px 0;
        }}
        .result-badge {{
            display: inline-block;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2em;
            margin: 10px;
        }}
        .pass {{
            background-color: #28a745;
            color: white;
        }}
        .fail {{
            background-color: #dc3545;
            color: white;
        }}
        .score {{
            font-size: 3em;
            font-weight: bold;
            margin: 10px 0;
        }}
        .requirements-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        .requirements-table th,
        .requirements-table td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        .requirements-table th {{
            background-color: #f8f9fa;
            font-weight: bold;
        }}
        .requirements-table .status {{
            text-align: center;
            font-weight: bold;
        }}
        .status.pass {{
            color: #28a745;
        }}
        .status.fail {{
            color: #dc3545;
        }}
        .section {{
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }}
        .section h2 {{
            color: #007acc;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }}
        .metric-card {{
            display: inline-block;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            min-width: 200px;
            text-align: center;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            color: #007acc;
        }}
        .metric-label {{
            color: #666;
            font-size: 0.9em;
        }}
        .details-list {{
            list-style: none;
            padding: 0;
        }}
        .details-list li {{
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }}
        .details-list li:last-child {{
            border-bottom: none;
        }}
        .footer {{
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }}
        .chart-placeholder {{
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            margin: 20px 0;
            border-radius: 8px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>柴管家系统性能测试报告</h1>
            <div class="subtitle">Task I-4.1：性能压力测试验收报告</div>
            <div class="subtitle">测试日期: {summary['test_date']}</div>
        </div>

        <div class="summary-box">
            <h2>测试结果总览</h2>
            <div class="result-badge {'pass' if summary['overall_result'] == 'PASS' else 'fail'}">
                {summary['overall_result']}
            </div>
            <div class="score">{summary['compliance_score']:.0f}分</div>
            <div>合规性评分 (满分100分)</div>
        </div>

        <div class="section">
            <h2>📋 验收标准检查结果</h2>
            <table class="requirements-table">
                <thead>
                    <tr>
                        <th>验收标准</th>
                        <th>要求</th>
                        <th>实际结果</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>API响应时间</td>
                        <td>1000并发下 &lt; 500ms</td>
                        <td>{compliance['api_response_time'].get('value', 'N/A'):.1f}ms</td>
                        <td class="status {'pass' if compliance['api_response_time']['passed'] else 'fail'}">
                            {'✅ 通过' if compliance['api_response_time']['passed'] else '❌ 未通过'}
                        </td>
                    </tr>
                    <tr>
                        <td>数据库QPS</td>
                        <td>&gt; 10000 QPS</td>
                        <td>{compliance['database_qps'].get('value', 'N/A'):.0f} QPS</td>
                        <td class="status {'pass' if compliance['database_qps']['passed'] else 'fail'}">
                            {'✅ 通过' if compliance['database_qps']['passed'] else '❌ 未通过'}
                        </td>
                    </tr>
                    <tr>
                        <td>内存使用率</td>
                        <td>高负载下 &lt; 80%</td>
                        <td>{compliance['memory_usage'].get('value', 'N/A'):.1f}%</td>
                        <td class="status {'pass' if compliance['memory_usage']['passed'] else 'fail'}">
                            {'✅ 通过' if compliance['memory_usage']['passed'] else '❌ 未通过'}
                        </td>
                    </tr>
                    <tr>
                        <td>CPU使用率</td>
                        <td>高负载下 &lt; 70%</td>
                        <td>{compliance['cpu_usage'].get('value', 'N/A'):.1f}%</td>
                        <td class="status {'pass' if compliance['cpu_usage']['passed'] else 'fail'}">
                            {'✅ 通过' if compliance['cpu_usage']['passed'] else '❌ 未通过'}
                        </td>
                    </tr>
                    <tr>
                        <td>系统恢复能力</td>
                        <td>测试后正常恢复</td>
                        <td>{'正常' if compliance['system_recovery']['passed'] else '异常'}</td>
                        <td class="status {'pass' if compliance['system_recovery']['passed'] else 'fail'}">
                            {'✅ 通过' if compliance['system_recovery']['passed'] else '❌ 未通过'}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>📊 关键性能指标</h2>
            <div class="metric-card">
                <div class="metric-value">{compliance['api_response_time'].get('pass_rate', 0):.0f}%</div>
                <div class="metric-label">API端点通过率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{compliance['database_qps'].get('postgresql_qps', 0):.0f}</div>
                <div class="metric-label">PostgreSQL QPS</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{compliance['database_qps'].get('redis_qps', 0):.0f}</div>
                <div class="metric-label">Redis QPS</div>
            </div>
        </div>

        <div class="section">
            <h2>🔍 详细测试结果</h2>

            <h3>API性能测试</h3>
            <ul class="details-list">
                {''.join(f'<li>{detail}</li>' for detail in compliance['api_response_time']['details'])}
            </ul>

            <h3>数据库性能测试</h3>
            <ul class="details-list">
                {''.join(f'<li>{detail}</li>' for detail in compliance['database_qps']['details'])}
            </ul>

            <h3>系统资源使用</h3>
            <ul class="details-list">
                {''.join(f'<li>{detail}</li>' for detail in compliance['cpu_usage']['details'])}
                {''.join(f'<li>{detail}</li>' for detail in compliance['memory_usage']['details'])}
            </ul>

            <h3>系统恢复能力</h3>
            <ul class="details-list">
                {''.join(f'<li>{detail}</li>' for detail in compliance['system_recovery']['details'])}
            </ul>
        </div>

        <div class="section">
            <h2>💡 建议和结论</h2>
            <h3>关键发现:</h3>
            <ul class="details-list">
                {''.join(f'<li>{finding}</li>' for finding in summary['key_findings'])}
            </ul>

            <h3>建议:</h3>
            <ul class="details-list">
                {''.join(f'<li>{rec}</li>' for rec in summary['recommendations'])}
            </ul>
        </div>

        <div class="footer">
            <p>本报告由柴管家性能测试系统自动生成</p>
            <p>生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>
        """

        return html_content

    def generate_comprehensive_report(self, results_dir: str) -> Dict[str, str]:
        """生成综合测试报告"""
        print("=" * 60)
        print("生成性能测试验收报告")
        print("=" * 60)

        # 加载测试数据
        test_data = self.load_test_data(results_dir)

        if not any(test_data.values()):
            print("❌ 未找到测试结果数据")
            return {}

        # 检查合规性
        compliance = self.check_compliance(test_data)

        # 生成执行摘要
        summary = self.generate_executive_summary(compliance, test_data)

        # 生成HTML报告
        html_report = self.generate_html_report(compliance, summary, test_data)

        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # JSON格式详细报告
        json_report = {
            "metadata": {
                "title": "柴管家系统性能测试验收报告",
                "test_date": summary["test_date"],
                "generated_at": datetime.now().isoformat(),
                "version": "1.0",
            },
            "executive_summary": summary,
            "compliance_check": compliance,
            "requirements": self.test_requirements,
            "test_data_summary": self._summarize_test_data(test_data),
        }

        # 保存文件
        json_file = os.path.join(results_dir, f"acceptance_report_{timestamp}.json")
        html_file = os.path.join(results_dir, f"acceptance_report_{timestamp}.html")

        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(json_report, f, indent=2, ensure_ascii=False)

        with open(html_file, "w", encoding="utf-8") as f:
            f.write(html_report)

        # 打印报告摘要
        self.print_report_summary(summary, compliance)

        print(f"\n📄 验收报告已生成:")
        print(f"  JSON报告: {json_file}")
        print(f"  HTML报告: {html_file}")

        return {
            "json_file": json_file,
            "html_file": html_file,
            "overall_result": summary["overall_result"],
        }

    def _summarize_test_data(self, test_data: Dict[str, Any]) -> Dict[str, str]:
        """汇总测试数据信息"""
        summary = {}

        for key, data in test_data.items():
            if data:
                if isinstance(data, dict):
                    summary[key] = f"已加载 ({len(data)} 项数据)"
                else:
                    summary[key] = "已加载"
            else:
                summary[key] = "未找到数据"

        return summary

    def print_report_summary(self, summary: Dict[str, Any], compliance: Dict[str, Any]):
        """打印报告摘要"""
        print("\n" + "=" * 60)
        print("性能测试验收报告摘要")
        print("=" * 60)

        # 总体结果
        result_emoji = "✅" if summary["overall_result"] == "PASS" else "❌"
        print(f"\n{result_emoji} 总体结果: {summary['overall_result']}")
        print(f"📊 合规性评分: {summary['compliance_score']:.0f}/100")
        print(f"📅 测试日期: {summary['test_date']}")

        # 验收标准检查
        print(f"\n📋 验收标准检查:")
        checks = [
            ("API响应时间", "api_response_time"),
            ("数据库QPS", "database_qps"),
            ("内存使用率", "memory_usage"),
            ("CPU使用率", "cpu_usage"),
            ("系统恢复", "system_recovery"),
        ]

        for name, key in checks:
            status = "✅" if compliance[key]["passed"] else "❌"
            value = compliance[key].get("value")
            if value is not None:
                if key in ["memory_usage", "cpu_usage"]:
                    print(f"  {status} {name}: {value:.1f}%")
                elif key == "database_qps":
                    print(f"  {status} {name}: {value:.0f} QPS")
                elif key == "api_response_time":
                    print(f"  {status} {name}: {value:.1f}ms")
                else:
                    print(f"  {status} {name}")
            else:
                print(f"  {status} {name}")

        # 关键发现
        print(f"\n🔍 关键发现:")
        for finding in summary["key_findings"][:5]:  # 只显示前5个
            print(f"  • {finding}")

        # 建议
        print(f"\n💡 主要建议:")
        for rec in summary["recommendations"][:3]:  # 只显示前3个
            print(f"  • {rec}")

        print("=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成性能测试验收报告")
    parser.add_argument("--results-dir", required=True, help="测试结果目录")

    args = parser.parse_args()

    if not os.path.exists(args.results_dir):
        print(f"❌ 结果目录不存在: {args.results_dir}")
        return 1

    generator = PerformanceReportGenerator()

    try:
        report_files = generator.generate_comprehensive_report(args.results_dir)

        if report_files and "overall_result" in report_files:
            if report_files["overall_result"] == "PASS":
                print("\n🎉 性能测试验收通过！")
                return 0
            else:
                print("\n⚠️  性能测试验收未通过，请查看报告详情")
                return 1
        else:
            print("\n❌ 报告生成失败")
            return 2

    except Exception as e:
        print(f"\n❌ 生成报告过程中发生错误: {e}")
        return 3


if __name__ == "__main__":
    import sys

    sys.exit(main())
