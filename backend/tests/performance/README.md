# 柴管家系统性能测试套件

## 概述

这是柴管家系统的完整性能测试套件，用于验证系统在高负载下的性能表现，确保满足以下验收标准：

- ✅ API 接口在 1000 并发下响应时间<500ms
- ✅ 数据库支持 10000+QPS 查询
- ✅ 内存使用率在高负载下<80%
- ✅ CPU 使用率在高负载下<70%
- ✅ 系统在压力测试后可以正常恢复

## 文件结构

```
performance/
├── README.md                      # 本文件
├── requirements.txt               # Python依赖
├── performance_test_plan.md       # 详细测试计划
├── locustfile.py                 # Locust压力测试脚本
├── benchmark_test.py             # API基准测试脚本
├── database_performance_test.py   # 数据库性能测试脚本
├── system_monitor.py             # 系统资源监控脚本
└── run_performance_tests.py      # 综合测试执行脚本
```

## 环境准备

### 1. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装系统工具 (Ubuntu/Debian)
sudo apt-get install apache2-utils redis-tools postgresql-client

# 安装系统工具 (macOS)
brew install apache2-utils redis postgresql
```

### 2. 配置环境变量

```bash
# 数据库连接
export POSTGRES_URL="postgresql://admin:password@localhost:5432/chaiguanjia"
export REDIS_URL="redis://localhost:6379/0"

# API服务地址
export API_URL="http://localhost:8000"
```

### 3. 启动系统服务

确保以下服务正在运行：

- 柴管家 API 服务 (端口 8000)
- PostgreSQL 数据库 (端口 5432)
- Redis 缓存 (端口 6379)

## 使用方法

### 1. 快速完整测试

运行所有性能测试：

```bash
python run_performance_tests.py
```

自定义参数：

```bash
python run_performance_tests.py \
    --api-url http://localhost:8000 \
    --postgres-url postgresql://admin:password@localhost:5432/chaiguanjia \
    --redis-url redis://localhost:6379/0 \
    --results-dir ./test_results \
    --locust-users 1000 \
    --locust-duration 300
```

### 2. 单独运行各项测试

#### API 基准测试

```bash
# 综合测试
python benchmark_test.py

# 测试特定端点
python benchmark_test.py --endpoint /health/ --requests 1000 --concurrency 100

# 指定服务器地址
python benchmark_test.py --host http://localhost:8000
```

#### 数据库性能测试

```bash
# 综合数据库测试
python database_performance_test.py

# 仅测试PostgreSQL
python database_performance_test.py --test-type postgres

# 仅测试Redis
python database_performance_test.py --test-type redis

# 自定义连接参数
python database_performance_test.py \
    --postgres-url postgresql://user:pass@localhost:5432/db \
    --redis-url redis://localhost:6379/0
```

#### 系统资源监控

```bash
# 实时监控
python system_monitor.py --real-time

# 监控指定时长并保存结果
python system_monitor.py --duration 300 --output monitor_results.json

# 自定义警告阈值
python system_monitor.py \
    --cpu-threshold 80 \
    --memory-threshold 85 \
    --disk-threshold 95
```

#### Locust 压力测试

```bash
# 使用Locust Web界面
locust -f locustfile.py --host http://localhost:8000

# 无头模式运行
locust -f locustfile.py \
    --host http://localhost:8000 \
    --users 1000 \
    --spawn-rate 50 \
    --run-time 300s \
    --headless
```

## 测试场景

### API 性能测试场景

1. **健康检查接口** (`/health/`)

   - 并发数: 100, 500, 1000, 2000
   - 期望响应时间: <100ms

2. **监控系统接口** (`/api/v1/monitoring/health`)

   - 并发数: 100, 500, 1000
   - 期望响应时间: <200ms

3. **搜索接口** (`/api/v1/search/search`)

   - 并发数: 50, 100, 200
   - 期望响应时间: <500ms

4. **混合负载测试**
   - 健康检查 50% + 监控 30% + 搜索 15% + 文件 5%
   - 总并发数: 1000
   - 持续时间: 5 分钟

### 数据库性能测试场景

1. **PostgreSQL 测试**

   - 读取密集型: SELECT 查询
   - 写入密集型: INSERT/UPDATE
   - 混合负载: 读写比例 7:3
   - 目标: 10000+ QPS

2. **Redis 测试**
   - 基本操作: GET/SET/INCR
   - 列表操作: LPUSH/LPOP
   - 信息查询: INFO
   - 目标: 50000+ QPS

## 结果分析

### 测试报告

测试完成后会生成以下文件：

```
performance_results/
├── final_report_YYYYMMDD_HHMMSS.json    # 综合测试报告
├── api_benchmark_YYYYMMDD_HHMMSS.json   # API基准测试结果
├── database_performance_YYYYMMDD_HHMMSS.json # 数据库性能结果
├── system_monitor.json                   # 系统监控数据
├── locust_results_YYYYMMDD_HHMMSS_stats.csv # Locust统计数据
└── locust_report_YYYYMMDD_HHMMSS.html   # Locust HTML报告
```

### 性能指标

#### API 性能指标

- **QPS** (Queries Per Second): 每秒查询数
- **响应时间**: 平均、P50、P95、P99 响应时间
- **错误率**: 4xx、5xx 错误百分比
- **并发数**: 同时处理的请求数

#### 数据库指标

- **QPS**: 每秒查询数
- **连接数**: 活跃连接数
- **响应时间**: 查询执行时间
- **缓存命中率**: 缓存效率

#### 系统资源指标

- **CPU 使用率**: 总体和分核心使用率
- **内存使用率**: 物理内存和虚拟内存
- **磁盘 I/O**: 读写速度和 IOPS
- **网络 I/O**: 网络带宽使用

### 性能等级

- **A 级**: 优秀性能，满足所有目标
- **B 级**: 良好性能，满足基本要求
- **C 级**: 一般性能，需要优化
- **D 级**: 较差性能，需要重点优化

## 故障排除

### 常见问题

1. **连接失败**

   ```bash
   # 检查服务状态
   curl http://localhost:8000/health/

   # 检查数据库连接
   psql postgresql://admin:password@localhost:5432/chaiguanjia -c "SELECT 1"

   # 检查Redis连接
   redis-cli ping
   ```

2. **依赖安装失败**

   ```bash
   # 更新pip
   pip install --upgrade pip

   # 使用国内镜像
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
   ```

3. **权限问题**

   ```bash
   # 给脚本执行权限
   chmod +x *.py

   # 检查文件写入权限
   mkdir -p ./performance_results
   ```

### 性能问题诊断

1. **API 响应慢**

   - 检查数据库查询性能
   - 检查网络延迟
   - 查看应用日志

2. **数据库 QPS 不达标**

   - 检查连接池配置
   - 优化 SQL 查询
   - 检查索引使用

3. **系统资源不足**
   - 增加 CPU 核心数
   - 扩大内存容量
   - 优化磁盘 I/O

## 最佳实践

### 测试环境

1. **隔离环境**: 在独立的测试环境中运行
2. **资源充足**: 确保测试机器有足够的资源
3. **网络稳定**: 使用稳定的网络连接
4. **数据一致**: 使用一致的测试数据

### 测试执行

1. **预热系统**: 先运行小负载预热
2. **逐步加压**: 逐步增加负载
3. **监控资源**: 实时监控系统资源
4. **记录异常**: 详细记录异常情况

### 结果分析

1. **多次测试**: 进行多次测试取平均值
2. **对比分析**: 与历史结果对比
3. **瓶颈定位**: 准确定位性能瓶颈
4. **优化验证**: 优化后重新测试验证

## 参考资料

- [性能测试计划](performance_test_plan.md)
- [Locust 文档](https://locust.io/)
- [Apache Bench 文档](https://httpd.apache.org/docs/2.4/programs/ab.html)
- [PostgreSQL 性能调优](https://www.postgresql.org/docs/current/runtime-config-resource.html)
- [Redis 性能优化](https://redis.io/docs/manual/config/)
