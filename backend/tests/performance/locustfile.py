"""
Locust API 压力测试脚本
用于测试柴管家系统API的性能和稳定性
"""

import json
import logging
import random
import time
from typing import Any, Dict

import psutil
from locust import HttpUser, between, events, task

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SystemMonitor:
    """系统资源监控器"""

    def __init__(self):
        self.metrics = []

    def collect_metrics(self):
        """收集系统指标"""
        cpu_percent = psutil.cpu_percent(interval=None)
        memory = psutil.virtual_memory()

        metric = {
            "timestamp": time.time(),
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_used_mb": memory.used / (1024 * 1024),
            "memory_available_mb": memory.available / (1024 * 1024),
        }

        self.metrics.append(metric)
        return metric


# 全局监控器实例
system_monitor = SystemMonitor()


class APIUser(HttpUser):
    """API用户行为模拟"""

    wait_time = between(1, 3)  # 用户请求间隔

    def on_start(self):
        """用户开始时的初始化"""
        logger.info("Starting API user simulation")

    def on_stop(self):
        """用户停止时的清理"""
        logger.info("Stopping API user simulation")

    @task(30)  # 权重: 30%
    def health_check(self):
        """健康检查接口测试"""
        with self.client.get(
            "/health/", catch_response=True, name="health_check"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"HTTP {response.status_code}")

    @task(25)  # 权重: 25%
    def detailed_health_check(self):
        """详细健康检查接口测试"""
        with self.client.get(
            "/health/detailed", catch_response=True, name="detailed_health"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get("success"):
                        response.success()
                    else:
                        response.failure("Response success is False")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"HTTP {response.status_code}")

    @task(20)  # 权重: 20%
    def monitoring_health(self):
        """监控系统健康检查"""
        with self.client.get(
            "/api/v1/monitoring/health", catch_response=True, name="monitoring_health"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"HTTP {response.status_code}")

    @task(15)  # 权重: 15%
    def get_metrics(self):
        """获取监控指标"""
        with self.client.get(
            "/api/v1/monitoring/metrics", catch_response=True, name="get_metrics"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"HTTP {response.status_code}")

    @task(5)  # 权重: 5%
    def search_test(self):
        """搜索接口测试"""
        search_queries = [
            "test",
            "用户",
            "系统",
            "消息",
            "文件",
            "健康",
            "监控",
            "数据",
            "服务",
            "API",
        ]

        search_data = {
            "query": random.choice(search_queries),
            "index": "test_index",
            "size": 10,
            "from": 0,
        }

        with self.client.post(
            "/api/v1/search/search",
            json=search_data,
            catch_response=True,
            name="search_documents",
        ) as response:
            if response.status_code in [200, 500]:  # 搜索可能因为索引不存在而返回500
                response.success()
            else:
                response.failure(f"HTTP {response.status_code}")

    @task(3)  # 权重: 3%
    def file_stats(self):
        """文件存储统计"""
        with self.client.get(
            "/api/v1/files/stats", catch_response=True, name="file_stats"
        ) as response:
            if response.status_code in [200, 500]:  # 可能因为存储未配置而返回500
                response.success()
            else:
                response.failure(f"HTTP {response.status_code}")

    @task(2)  # 权重: 2%
    def system_info(self):
        """系统信息获取"""
        with self.client.get(
            "/api/v1/monitoring/system-info", catch_response=True, name="system_info"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"HTTP {response.status_code}")


class HighLoadUser(APIUser):
    """高负载用户 - 更频繁的请求"""

    wait_time = between(0.1, 0.5)  # 更短的等待时间

    @task(50)
    def rapid_health_check(self):
        """快速健康检查"""
        self.health_check()

    @task(30)
    def rapid_monitoring(self):
        """快速监控检查"""
        self.monitoring_health()

    @task(20)
    def rapid_metrics(self):
        """快速指标获取"""
        self.get_metrics()


class DatabaseTestUser(HttpUser):
    """数据库压力测试用户"""

    wait_time = between(0.1, 1)

    @task(40)
    def test_database_connection(self):
        """测试数据库连接"""
        with self.client.get(
            "/health/test/database", catch_response=True, name="test_database"
        ) as response:
            if response.status_code in [200, 500]:
                response.success()
            else:
                response.failure(f"HTTP {response.status_code}")

    @task(30)
    def test_redis_connection(self):
        """测试Redis连接"""
        with self.client.get(
            "/health/test/redis", catch_response=True, name="test_redis"
        ) as response:
            if response.status_code in [200, 500]:
                response.success()
            else:
                response.failure(f"HTTP {response.status_code}")

    @task(30)
    def get_redis_info(self):
        """获取Redis信息"""
        with self.client.get(
            "/api/v1/monitoring/redis-info", catch_response=True, name="redis_info"
        ) as response:
            if response.status_code in [200, 500]:
                response.success()
            else:
                response.failure(f"HTTP {response.status_code}")


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """测试开始时的回调"""
    logger.info("=== 性能测试开始 ===")
    logger.info(f"目标主机: {environment.host}")

    # 记录初始系统状态
    initial_metrics = system_monitor.collect_metrics()
    logger.info(
        f"初始系统状态: CPU {initial_metrics['cpu_percent']:.1f}%, "
        f"内存 {initial_metrics['memory_percent']:.1f}%"
    )


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """测试结束时的回调"""
    logger.info("=== 性能测试结束 ===")

    # 记录最终系统状态
    final_metrics = system_monitor.collect_metrics()
    logger.info(
        f"最终系统状态: CPU {final_metrics['cpu_percent']:.1f}%, "
        f"内存 {final_metrics['memory_percent']:.1f}%"
    )

    # 保存监控数据
    if system_monitor.metrics:
        with open("performance_metrics.json", "w") as f:
            json.dump(system_monitor.metrics, f, indent=2)
        logger.info("系统监控数据已保存到 performance_metrics.json")


@events.request.add_listener
def on_request(
    request_type,
    name,
    response_time,
    response_length,
    response,
    context,
    exception,
    **kwargs,
):
    """请求完成时的回调"""
    # 每100个请求收集一次系统指标
    if random.randint(1, 100) == 1:
        metrics = system_monitor.collect_metrics()

        # 检查资源使用是否超过阈值
        if metrics["cpu_percent"] > 70:
            logger.warning(f"CPU使用率过高: {metrics['cpu_percent']:.1f}%")

        if metrics["memory_percent"] > 80:
            logger.warning(f"内存使用率过高: {metrics['memory_percent']:.1f}%")


# 自定义用户类，用于不同测试场景
class LightLoadUser(APIUser):
    """轻负载用户"""

    weight = 3


class MediumLoadUser(APIUser):
    """中等负载用户"""

    weight = 2


class HeavyLoadUser(HighLoadUser):
    """重负载用户"""

    weight = 1
