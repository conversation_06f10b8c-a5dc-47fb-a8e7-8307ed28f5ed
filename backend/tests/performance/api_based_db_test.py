#!/usr/bin/env python3
"""
基于API的数据库性能测试
通过API端点测试数据库性能，避免直接连接问题
"""

import asyncio
import aiohttp
import time
import json
from typing import Dict, List
import statistics


class APIBasedDatabaseTester:
    """基于API的数据库性能测试器"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.api_base_url = api_base_url
        self.results = {}
    
    async def test_database_endpoint_performance(
        self, 
        endpoint: str, 
        concurrent_requests: int = 100, 
        total_requests: int = 1000
    ) -> Dict:
        """测试数据库相关API端点的性能"""
        
        print(f"测试端点: {endpoint}")
        print(f"并发请求: {concurrent_requests}, 总请求: {total_requests}")
        
        url = f"{self.api_base_url}{endpoint}"
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        async def make_request(session: aiohttp.ClientSession) -> tuple:
            """发送单个请求"""
            start_time = time.time()
            try:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    await response.read()
                    end_time = time.time()
                    return end_time - start_time, response.status == 200
            except Exception:
                end_time = time.time()
                return end_time - start_time, False
        
        # 分批执行请求
        batch_size = concurrent_requests
        batches = [total_requests // batch_size] * (total_requests // batch_size)
        if total_requests % batch_size:
            batches.append(total_requests % batch_size)
        
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            for batch_requests in batches:
                tasks = [make_request(session) for _ in range(batch_requests)]
                batch_results = await asyncio.gather(*tasks)
                
                for response_time, success in batch_results:
                    response_times.append(response_time)
                    if success:
                        successful_requests += 1
                    else:
                        failed_requests += 1
        
        total_time = time.time() - start_time
        
        # 计算统计数据
        if response_times:
            avg_response_time = statistics.mean(response_times)
            median_response_time = statistics.median(response_times)
            p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)]
            p99_response_time = sorted(response_times)[int(len(response_times) * 0.99)]
            qps = total_requests / total_time
        else:
            avg_response_time = median_response_time = p95_response_time = p99_response_time = qps = 0
        
        result = {
            "endpoint": endpoint,
            "total_requests": total_requests,
            "concurrent_requests": concurrent_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "error_rate": failed_requests / total_requests * 100,
            "total_time_seconds": total_time,
            "qps": qps,
            "avg_response_time_ms": avg_response_time * 1000,
            "median_response_time_ms": median_response_time * 1000,
            "p95_response_time_ms": p95_response_time * 1000,
            "p99_response_time_ms": p99_response_time * 1000,
        }
        
        print(f"  QPS: {qps:.2f}")
        print(f"  平均响应时间: {avg_response_time * 1000:.2f}ms")
        print(f"  错误率: {result['error_rate']:.2f}%")
        print(f"  成功请求: {successful_requests}/{total_requests}")
        
        return result
    
    async def comprehensive_database_api_test(self):
        """综合数据库API性能测试"""
        print("=" * 60)
        print("基于API的数据库性能测试")
        print("=" * 60)
        
        # 测试不同的数据库相关端点
        test_configs = [
            ("/api/test/database", "PostgreSQL连接测试", 50, 500),
            ("/api/test/redis", "Redis连接测试", 100, 1000),
            ("/api/health", "健康检查（含数据库状态）", 100, 1000),
            ("/api/status", "状态检查（含系统信息）", 50, 500),
        ]
        
        results = {}
        
        for endpoint, description, concurrent, total in test_configs:
            print(f"\n🔍 {description}")
            print("-" * 40)
            
            try:
                result = await self.test_database_endpoint_performance(
                    endpoint, concurrent, total
                )
                results[endpoint] = result
                
                # 判断性能表现
                if result["error_rate"] < 1 and result["qps"] > 100:
                    print("  ✅ 性能表现良好")
                elif result["error_rate"] < 5:
                    print("  ⚠️  性能一般")
                else:
                    print("  ❌ 性能需要优化")
                    
            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
                results[endpoint] = {"error": str(e)}
        
        # 生成总结报告
        print("\n" + "=" * 60)
        print("数据库API性能测试总结")
        print("=" * 60)
        
        total_qps = 0
        successful_tests = 0
        
        for endpoint, result in results.items():
            if "error" not in result:
                print(f"\n📍 {endpoint}")
                print(f"  🚀 QPS: {result['qps']:.2f}")
                print(f"  ⏱️  平均响应时间: {result['avg_response_time_ms']:.2f}ms")
                print(f"  📊 P99响应时间: {result['p99_response_time_ms']:.2f}ms")
                print(f"  ❌ 错误率: {result['error_rate']:.2f}%")
                
                total_qps += result['qps']
                successful_tests += 1
        
        if successful_tests > 0:
            avg_qps = total_qps / successful_tests
            print(f"\n📊 平均QPS: {avg_qps:.2f}")
            
            # 评估是否达到10,000+ QPS目标
            if total_qps >= 10000:
                print("✅ 达到10,000+ QPS目标")
            else:
                print(f"⚠️  总QPS ({total_qps:.2f}) 未达到10,000目标")
        
        # 保存结果
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        results_file = f"api_db_test_results_{timestamp}.json"
        
        with open(results_file, "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": timestamp,
                "test_type": "api_based_database_performance",
                "results": results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n结果已保存到: {results_file}")
        return results


async def main():
    """主函数"""
    tester = APIBasedDatabaseTester()
    await tester.comprehensive_database_api_test()


if __name__ == "__main__":
    asyncio.run(main())
