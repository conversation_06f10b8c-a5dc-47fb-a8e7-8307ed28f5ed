#!/usr/bin/env python3
"""
API 基准性能测试脚本
使用多种工具进行快速基准测试
"""

import argparse
import asyncio
import json
import statistics
import subprocess
import sys
import time
from datetime import datetime
from typing import Dict, List, Tuple

import aiohttp
import psutil


class BenchmarkTester:
    """基准测试器"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip("/")
        self.results = {}

    def run_ab_test(
        self, endpoint: str, num_requests: int = 1000, concurrency: int = 100
    ) -> Dict:
        """使用Apache Bench进行测试"""
        url = f"{self.base_url}{endpoint}"
        cmd = [
            "ab",
            "-n",
            str(num_requests),
            "-c",
            str(concurrency),
            "-g",
            f"ab_results_{endpoint.replace('/', '_')}.csv",
            url,
        ]

        try:
            print(f"运行 ab 测试: {url} (请求:{num_requests}, 并发:{concurrency})")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                return self._parse_ab_output(result.stdout)
            else:
                print(f"ab测试失败: {result.stderr}")
                return {"error": result.stderr}

        except subprocess.TimeoutExpired:
            print("ab测试超时")
            return {"error": "timeout"}
        except FileNotFoundError:
            print("未找到ab命令，请安装Apache Bench")
            return {"error": "ab not found"}

    def _parse_ab_output(self, output: str) -> Dict:
        """解析ab输出结果"""
        lines = output.split("\n")
        result = {}

        for line in lines:
            if "Requests per second" in line:
                result["requests_per_second"] = float(
                    line.split(":")[1].strip().split()[0]
                )
            elif "Time per request" in line and "mean" in line:
                result["time_per_request_mean"] = float(
                    line.split(":")[1].strip().split()[0]
                )
            elif "Time per request" in line and "concurrent" in line:
                result["time_per_request_concurrent"] = float(
                    line.split(":")[1].strip().split()[0]
                )
            elif "Transfer rate" in line:
                result["transfer_rate"] = float(line.split(":")[1].strip().split()[0])
            elif "Failed requests" in line:
                result["failed_requests"] = int(line.split(":")[1].strip())

        return result

    async def run_async_test(
        self, endpoint: str, num_requests: int = 1000, concurrency: int = 100
    ) -> Dict:
        """异步HTTP测试"""
        url = f"{self.base_url}{endpoint}"

        async def make_request(session: aiohttp.ClientSession) -> Tuple[float, int]:
            """发送单个请求"""
            start_time = time.time()
            try:
                async with session.get(url) as response:
                    await response.read()
                    return time.time() - start_time, response.status
            except Exception as e:
                return time.time() - start_time, 0

        async def run_batch(batch_size: int) -> List[Tuple[float, int]]:
            """运行一批请求"""
            connector = aiohttp.TCPConnector(limit=concurrency)
            async with aiohttp.ClientSession(connector=connector) as session:
                tasks = [make_request(session) for _ in range(batch_size)]
                return await asyncio.gather(*tasks)

        print(f"运行异步测试: {url} (请求:{num_requests}, 并发:{concurrency})")

        start_time = time.time()
        results = await run_batch(num_requests)
        total_time = time.time() - start_time

        # 分析结果
        response_times = [r[0] for r in results]
        status_codes = [r[1] for r in results]

        successful_requests = len([s for s in status_codes if 200 <= s < 300])
        failed_requests = len(results) - successful_requests

        return {
            "total_time": total_time,
            "requests_per_second": num_requests / total_time,
            "mean_response_time": statistics.mean(response_times),
            "median_response_time": statistics.median(response_times),
            "p95_response_time": self._percentile(response_times, 95),
            "p99_response_time": self._percentile(response_times, 99),
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "error_rate": (failed_requests / num_requests) * 100,
        }

    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        sorted_data = sorted(data)
        index = int((percentile / 100) * len(sorted_data))
        return sorted_data[min(index, len(sorted_data) - 1)]

    def collect_system_metrics(self) -> Dict:
        """收集系统指标"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage("/")

        return {
            "timestamp": datetime.now().isoformat(),
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_used_gb": memory.used / (1024**3),
            "memory_available_gb": memory.available / (1024**3),
            "disk_percent": (disk.used / disk.total) * 100,
            "disk_free_gb": disk.free / (1024**3),
        }

    async def comprehensive_test(self):
        """综合性能测试"""
        print("=" * 50)
        print("柴管家系统基准性能测试")
        print("=" * 50)

        # 测试端点配置
        endpoints = [
            ("/health", "健康检查"),
            ("/api/health", "API健康检查"),
            ("/api/status", "API状态检查"),
            ("/api/version", "API版本信息"),
            ("/", "根路径"),
        ]

        # 测试配置
        test_configs = [
            (100, 10, "轻负载"),
            (500, 50, "中等负载"),
            (1000, 100, "高负载"),
        ]

        results = {}

        # 记录初始系统状态
        initial_metrics = self.collect_system_metrics()
        print(
            f"初始系统状态: CPU {initial_metrics['cpu_percent']:.1f}%, "
            f"内存 {initial_metrics['memory_percent']:.1f}%"
        )
        print()

        for endpoint, endpoint_name in endpoints:
            print(f"测试端点: {endpoint_name} ({endpoint})")
            endpoint_results = {}

            for num_requests, concurrency, load_name in test_configs:
                print(f"  {load_name} - 请求数:{num_requests}, 并发:{concurrency}")

                # 异步测试
                try:
                    async_result = await self.run_async_test(
                        endpoint, num_requests, concurrency
                    )
                    endpoint_results[f"{load_name}_async"] = async_result

                    print(
                        f"    异步测试: {async_result['requests_per_second']:.2f} QPS, "
                        f"平均响应时间: {async_result['mean_response_time']*1000:.2f}ms, "
                        f"错误率: {async_result['error_rate']:.2f}%"
                    )

                except Exception as e:
                    print(f"    异步测试失败: {e}")
                    endpoint_results[f"{load_name}_async"] = {"error": str(e)}

                # AB测试 (如果可用)
                if subprocess.run(["which", "ab"], capture_output=True).returncode == 0:
                    try:
                        ab_result = self.run_ab_test(
                            endpoint, num_requests, concurrency
                        )
                        if "error" not in ab_result:
                            endpoint_results[f"{load_name}_ab"] = ab_result
                            print(
                                f"    AB测试: {ab_result.get('requests_per_second', 0):.2f} QPS, "
                                f"平均响应时间: {ab_result.get('time_per_request_mean', 0):.2f}ms"
                            )
                    except Exception as e:
                        print(f"    AB测试失败: {e}")

                # 收集系统指标
                system_metrics = self.collect_system_metrics()
                endpoint_results[f"{load_name}_system"] = system_metrics

                # 检查资源使用
                if system_metrics["cpu_percent"] > 70:
                    print(f"    ⚠️  CPU使用率过高: {system_metrics['cpu_percent']:.1f}%")
                if system_metrics["memory_percent"] > 80:
                    print(
                        f"    ⚠️  内存使用率过高: {system_metrics['memory_percent']:.1f}%"
                    )

                # 测试间隔
                await asyncio.sleep(2)

            results[endpoint] = endpoint_results
            print()

        # 记录最终系统状态
        final_metrics = self.collect_system_metrics()
        print(
            f"最终系统状态: CPU {final_metrics['cpu_percent']:.1f}%, "
            f"内存 {final_metrics['memory_percent']:.1f}%"
        )

        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"benchmark_results_{timestamp}.json"

        with open(results_file, "w", encoding="utf-8") as f:
            json.dump(
                {
                    "timestamp": timestamp,
                    "initial_metrics": initial_metrics,
                    "final_metrics": final_metrics,
                    "test_results": results,
                },
                f,
                indent=2,
                ensure_ascii=False,
            )

        print(f"\n结果已保存到: {results_file}")

        # 生成摘要报告
        self.generate_summary_report(results)

        return results

    def generate_summary_report(self, results: Dict):
        """生成摘要报告"""
        print("\n" + "=" * 50)
        print("性能测试摘要报告")
        print("=" * 50)

        for endpoint, endpoint_results in results.items():
            print(f"\n📍 {endpoint}")

            # 分析高负载测试结果
            high_load_async = endpoint_results.get("高负载_async", {})
            if high_load_async and "error" not in high_load_async:
                qps = high_load_async.get("requests_per_second", 0)
                avg_time = high_load_async.get("mean_response_time", 0) * 1000
                p99_time = high_load_async.get("p99_response_time", 0) * 1000
                error_rate = high_load_async.get("error_rate", 0)

                print(f"  🚀 QPS: {qps:.2f}")
                print(f"  ⏱️  平均响应时间: {avg_time:.2f}ms")
                print(f"  📊 P99响应时间: {p99_time:.2f}ms")
                print(f"  ❌ 错误率: {error_rate:.2f}%")

                # 性能评估
                if qps >= 100 and avg_time < 500 and error_rate < 5:
                    print("  ✅ 性能表现良好")
                elif qps >= 50 and avg_time < 1000 and error_rate < 10:
                    print("  ⚠️  性能表现一般")
                else:
                    print("  ❌ 性能需要优化")

        print("\n" + "=" * 50)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="API基准性能测试")
    parser.add_argument(
        "--host",
        default="http://localhost:8000",
        help="API服务器地址 (默认: http://localhost:8000)",
    )
    parser.add_argument("--endpoint", help="特定端点测试")
    parser.add_argument("--requests", type=int, default=1000, help="请求数量")
    parser.add_argument("--concurrency", type=int, default=100, help="并发数")

    args = parser.parse_args()

    tester = BenchmarkTester(args.host)

    if args.endpoint:
        # 单端点测试
        print(f"测试单个端点: {args.endpoint}")
        result = await tester.run_async_test(
            args.endpoint, args.requests, args.concurrency
        )
        print(json.dumps(result, indent=2))
    else:
        # 综合测试
        await tester.comprehensive_test()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        sys.exit(1)
