#!/usr/bin/env python3
"""
数据库性能测试脚本
测试PostgreSQL和Redis的并发性能
"""

import argparse
import asyncio
import json
import statistics
import subprocess
import sys
import time
from datetime import datetime
from typing import Dict, List, Tuple

import psutil

try:
    import aioredis
    import asyncpg

    HAS_ASYNC_DRIVERS = True
except ImportError:
    HAS_ASYNC_DRIVERS = False
    print("警告: 未安装asyncpg和aioredis，某些测试可能无法运行")


class DatabasePerformanceTester:
    """数据库性能测试器"""

    def __init__(
        self,
        postgres_url: str = "postgresql://admin:chaiguanjia2024@localhost:5432/chaiguanjia",
        redis_url: str = "redis://:chaiguanjia2024@localhost:6379/0",
    ):
        self.postgres_url = postgres_url
        self.redis_url = redis_url
        self.results = {}

    async def test_postgresql_performance(
        self, concurrent_connections: int = 50, operations_per_connection: int = 100
    ) -> Dict:
        """测试PostgreSQL性能"""
        if not HAS_ASYNC_DRIVERS:
            return {"error": "缺少asyncpg驱动"}

        print(
            f"测试PostgreSQL性能 - 并发连接:{concurrent_connections}, "
            f"每连接操作数:{operations_per_connection}"
        )

        async def single_connection_test(connection_id: int) -> List[float]:
            """单连接测试"""
            times = []
            try:
                conn = await asyncpg.connect(self.postgres_url)

                for i in range(operations_per_connection):
                    start_time = time.time()

                    # 执行不同类型的查询
                    if i % 4 == 0:
                        # 简单查询
                        await conn.fetch("SELECT 1")
                    elif i % 4 == 1:
                        # 时间查询
                        await conn.fetch("SELECT NOW()")
                    elif i % 4 == 2:
                        # 数学计算
                        await conn.fetch("SELECT random(), sqrt(16), power(2, 3)")
                    else:
                        # 系统信息查询
                        await conn.fetch("SELECT version()")

                    times.append(time.time() - start_time)

                await conn.close()
                return times

            except Exception as e:
                print(f"连接 {connection_id} 测试失败: {e}")
                return []

        start_time = time.time()
        tasks = [single_connection_test(i) for i in range(concurrent_connections)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time

        # 收集所有响应时间
        all_times = []
        successful_connections = 0

        for result in results:
            if isinstance(result, list) and result:
                all_times.extend(result)
                successful_connections += 1

        if not all_times:
            return {"error": "所有数据库连接都失败了"}

        total_operations = len(all_times)

        return {
            "total_time": total_time,
            "total_operations": total_operations,
            "successful_connections": successful_connections,
            "failed_connections": concurrent_connections - successful_connections,
            "operations_per_second": total_operations / total_time,
            "avg_response_time_ms": statistics.mean(all_times) * 1000,
            "median_response_time_ms": statistics.median(all_times) * 1000,
            "p95_response_time_ms": self._percentile(all_times, 95) * 1000,
            "p99_response_time_ms": self._percentile(all_times, 99) * 1000,
            "min_response_time_ms": min(all_times) * 1000,
            "max_response_time_ms": max(all_times) * 1000,
        }

    async def test_redis_performance(
        self, concurrent_connections: int = 100, operations_per_connection: int = 1000
    ) -> Dict:
        """测试Redis性能"""
        if not HAS_ASYNC_DRIVERS:
            return {"error": "缺少aioredis驱动"}

        print(
            f"测试Redis性能 - 并发连接:{concurrent_connections}, "
            f"每连接操作数:{operations_per_connection}"
        )

        async def single_connection_test(connection_id: int) -> List[float]:
            """单连接Redis测试"""
            times = []
            try:
                redis = aioredis.from_url(self.redis_url)

                for i in range(operations_per_connection):
                    start_time = time.time()

                    # 执行不同类型的Redis操作
                    if i % 5 == 0:
                        # SET操作
                        await redis.set(f"test_key_{connection_id}_{i}", f"value_{i}")
                    elif i % 5 == 1:
                        # GET操作
                        await redis.get(f"test_key_{connection_id}_{i-1}")
                    elif i % 5 == 2:
                        # INCR操作
                        await redis.incr(f"counter_{connection_id}")
                    elif i % 5 == 3:
                        # LIST操作
                        await redis.lpush(f"list_{connection_id}", f"item_{i}")
                    else:
                        # INFO操作
                        await redis.info()

                    times.append(time.time() - start_time)

                await redis.close()
                return times

            except Exception as e:
                print(f"Redis连接 {connection_id} 测试失败: {e}")
                return []

        start_time = time.time()
        tasks = [single_connection_test(i) for i in range(concurrent_connections)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time

        # 收集所有响应时间
        all_times = []
        successful_connections = 0

        for result in results:
            if isinstance(result, list) and result:
                all_times.extend(result)
                successful_connections += 1

        if not all_times:
            return {"error": "所有Redis连接都失败了"}

        total_operations = len(all_times)

        return {
            "total_time": total_time,
            "total_operations": total_operations,
            "successful_connections": successful_connections,
            "failed_connections": concurrent_connections - successful_connections,
            "operations_per_second": total_operations / total_time,
            "avg_response_time_ms": statistics.mean(all_times) * 1000,
            "median_response_time_ms": statistics.median(all_times) * 1000,
            "p95_response_time_ms": self._percentile(all_times, 95) * 1000,
            "p99_response_time_ms": self._percentile(all_times, 99) * 1000,
            "min_response_time_ms": min(all_times) * 1000,
            "max_response_time_ms": max(all_times) * 1000,
        }

    def test_postgresql_with_pgbench(
        self, connections: int = 50, transactions: int = 1000
    ) -> Dict:
        """使用pgbench测试PostgreSQL"""
        print(f"使用pgbench测试PostgreSQL - 连接:{connections}, 事务:{transactions}")

        try:
            # 初始化pgbench数据库
            init_cmd = ["pgbench", "-i", "-s", "1", "chaiguanjia"]
            init_result = subprocess.run(
                init_cmd, capture_output=True, text=True, timeout=120
            )

            if init_result.returncode != 0:
                print(f"pgbench初始化失败: {init_result.stderr}")

            # 运行基准测试
            cmd = [
                "pgbench",
                "-c",
                str(connections),
                "-j",
                str(min(connections, 4)),  # 线程数不超过连接数和4
                "-t",
                str(transactions // connections),
                "-P",
                "10",  # 每10秒报告进度
                "chaiguanjia",
            ]

            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            total_time = time.time() - start_time

            if result.returncode == 0:
                return self._parse_pgbench_output(result.stdout, total_time)
            else:
                return {"error": f"pgbench失败: {result.stderr}"}

        except subprocess.TimeoutExpired:
            return {"error": "pgbench超时"}
        except FileNotFoundError:
            return {"error": "未找到pgbench命令"}

    def test_redis_with_benchmark(
        self, connections: int = 50, requests: int = 10000
    ) -> Dict:
        """使用redis-benchmark测试Redis"""
        print(f"使用redis-benchmark测试Redis - 连接:{connections}, 请求:{requests}")

        try:
            cmd = [
                "redis-benchmark",
                "-c",
                str(connections),
                "-n",
                str(requests),
                "-q",  # 安静模式
                "--csv",  # CSV输出
            ]

            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            total_time = time.time() - start_time

            if result.returncode == 0:
                return self._parse_redis_benchmark_output(result.stdout, total_time)
            else:
                return {"error": f"redis-benchmark失败: {result.stderr}"}

        except subprocess.TimeoutExpired:
            return {"error": "redis-benchmark超时"}
        except FileNotFoundError:
            return {"error": "未找到redis-benchmark命令"}

    def _parse_pgbench_output(self, output: str, total_time: float) -> Dict:
        """解析pgbench输出"""
        lines = output.split("\n")
        result = {"total_time": total_time}

        for line in lines:
            if "tps =" in line:
                # 提取TPS
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "tps":
                        result["transactions_per_second"] = float(parts[i + 2])
                        break
            elif "latency average" in line:
                # 提取平均延迟
                result["avg_latency_ms"] = float(line.split("=")[1].strip().split()[0])

        return result

    def _parse_redis_benchmark_output(self, output: str, total_time: float) -> Dict:
        """解析redis-benchmark输出"""
        lines = output.strip().split("\n")
        result = {"total_time": total_time}
        operations = {}

        for line in lines:
            parts = line.split(",")
            if len(parts) >= 2:
                operation = parts[0].strip('"')
                requests_per_second = float(parts[1].strip('"'))
                operations[operation] = requests_per_second

        result["operations"] = operations
        if operations:
            result["avg_requests_per_second"] = statistics.mean(operations.values())

        return result

    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        sorted_data = sorted(data)
        index = int((percentile / 100) * len(sorted_data))
        return sorted_data[min(index, len(sorted_data) - 1)]

    def collect_database_metrics(self) -> Dict:
        """收集数据库相关的系统指标"""
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_io": (
                psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {}
            ),
            "network_io": (
                psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {}
            ),
        }

        # 尝试获取数据库进程信息
        try:
            for proc in psutil.process_iter(
                ["pid", "name", "cpu_percent", "memory_percent"]
            ):
                if "postgres" in proc.info["name"].lower():
                    metrics["postgres_process"] = proc.info
                    break
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass

        try:
            for proc in psutil.process_iter(
                ["pid", "name", "cpu_percent", "memory_percent"]
            ):
                if "redis" in proc.info["name"].lower():
                    metrics["redis_process"] = proc.info
                    break
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass

        return metrics

    async def comprehensive_database_test(self):
        """综合数据库性能测试"""
        print("=" * 60)
        print("柴管家系统数据库性能测试")
        print("=" * 60)

        results = {}

        # 记录初始系统状态
        initial_metrics = self.collect_database_metrics()
        print(
            f"初始系统状态: CPU {initial_metrics['cpu_percent']:.1f}%, "
            f"内存 {initial_metrics['memory_percent']:.1f}%"
        )
        print()

        # PostgreSQL测试配置
        postgres_configs = [
            (10, 100, "轻负载"),
            (50, 100, "中等负载"),
            (100, 100, "高负载"),
        ]

        print("🐘 PostgreSQL性能测试")
        print("-" * 30)

        postgres_results = {}
        for connections, operations, load_name in postgres_configs:
            print(f"  {load_name} - 连接:{connections}, 操作:{operations}")

            # 异步测试
            async_result = await self.test_postgresql_performance(
                connections, operations
            )
            postgres_results[f"{load_name}_async"] = async_result

            if "error" not in async_result:
                qps = async_result.get("operations_per_second", 0)
                avg_time = async_result.get("avg_response_time_ms", 0)
                print(f"    异步测试: {qps:.2f} QPS, 平均响应: {avg_time:.2f}ms")

                # 检查是否达到目标QPS
                if qps >= 10000:
                    print(f"    ✅ 达到目标QPS (>10000)")
                elif qps >= 5000:
                    print(f"    ⚠️  接近目标QPS")
                else:
                    print(f"    ❌ 未达到目标QPS")
            else:
                print(f"    ❌ 测试失败: {async_result['error']}")

            # pgbench测试
            if connections <= 50:  # 避免过高负载
                pgbench_result = self.test_postgresql_with_pgbench(
                    connections, connections * operations
                )
                postgres_results[f"{load_name}_pgbench"] = pgbench_result

                if "error" not in pgbench_result:
                    tps = pgbench_result.get("transactions_per_second", 0)
                    print(f"    pgbench: {tps:.2f} TPS")

            # 收集系统指标
            metrics = self.collect_database_metrics()
            postgres_results[f"{load_name}_metrics"] = metrics

            await asyncio.sleep(3)  # 测试间隔

        results["postgresql"] = postgres_results
        print()

        # Redis测试配置
        redis_configs = [
            (50, 1000, "轻负载"),
            (100, 1000, "中等负载"),
            (200, 1000, "高负载"),
        ]

        print("🔴 Redis性能测试")
        print("-" * 30)

        redis_results = {}
        for connections, operations, load_name in redis_configs:
            print(f"  {load_name} - 连接:{connections}, 操作:{operations}")

            # 异步测试
            async_result = await self.test_redis_performance(connections, operations)
            redis_results[f"{load_name}_async"] = async_result

            if "error" not in async_result:
                qps = async_result.get("operations_per_second", 0)
                avg_time = async_result.get("avg_response_time_ms", 0)
                print(f"    异步测试: {qps:.2f} QPS, 平均响应: {avg_time:.2f}ms")

                # Redis通常有更高的QPS
                if qps >= 50000:
                    print(f"    ✅ 性能优秀")
                elif qps >= 20000:
                    print(f"    ✅ 性能良好")
                elif qps >= 10000:
                    print(f"    ⚠️  性能一般")
                else:
                    print(f"    ❌ 性能较差")
            else:
                print(f"    ❌ 测试失败: {async_result['error']}")

            # redis-benchmark测试
            benchmark_result = self.test_redis_with_benchmark(
                connections, connections * operations
            )
            redis_results[f"{load_name}_benchmark"] = benchmark_result

            if "error" not in benchmark_result:
                avg_rps = benchmark_result.get("avg_requests_per_second", 0)
                print(f"    redis-benchmark: {avg_rps:.2f} RPS")

            # 收集系统指标
            metrics = self.collect_database_metrics()
            redis_results[f"{load_name}_metrics"] = metrics

            await asyncio.sleep(3)  # 测试间隔

        results["redis"] = redis_results
        print()

        # 记录最终系统状态
        final_metrics = self.collect_database_metrics()
        print(
            f"最终系统状态: CPU {final_metrics['cpu_percent']:.1f}%, "
            f"内存 {final_metrics['memory_percent']:.1f}%"
        )

        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"database_performance_results_{timestamp}.json"

        with open(results_file, "w", encoding="utf-8") as f:
            json.dump(
                {
                    "timestamp": timestamp,
                    "initial_metrics": initial_metrics,
                    "final_metrics": final_metrics,
                    "test_results": results,
                },
                f,
                indent=2,
                ensure_ascii=False,
            )

        print(f"\n结果已保存到: {results_file}")

        # 生成摘要报告
        self.generate_database_summary_report(results)

        return results

    def generate_database_summary_report(self, results: Dict):
        """生成数据库性能摘要报告"""
        print("\n" + "=" * 60)
        print("数据库性能测试摘要报告")
        print("=" * 60)

        # PostgreSQL摘要
        print("\n🐘 PostgreSQL性能摘要:")
        postgres_high_load = results.get("postgresql", {}).get("高负载_async", {})
        if postgres_high_load and "error" not in postgres_high_load:
            qps = postgres_high_load.get("operations_per_second", 0)
            avg_time = postgres_high_load.get("avg_response_time_ms", 0)
            p99_time = postgres_high_load.get("p99_response_time_ms", 0)

            print(f"  📊 最大QPS: {qps:.2f}")
            print(f"  ⏱️  平均响应时间: {avg_time:.2f}ms")
            print(f"  🚀 P99响应时间: {p99_time:.2f}ms")

            if qps >= 10000:
                print("  ✅ PostgreSQL性能达标 (≥10000 QPS)")
            else:
                print(f"  ❌ PostgreSQL性能不达标 (目标≥10000 QPS)")

        # Redis摘要
        print("\n🔴 Redis性能摘要:")
        redis_high_load = results.get("redis", {}).get("高负载_async", {})
        if redis_high_load and "error" not in redis_high_load:
            qps = redis_high_load.get("operations_per_second", 0)
            avg_time = redis_high_load.get("avg_response_time_ms", 0)
            p99_time = redis_high_load.get("p99_response_time_ms", 0)

            print(f"  📊 最大QPS: {qps:.2f}")
            print(f"  ⏱️  平均响应时间: {avg_time:.2f}ms")
            print(f"  🚀 P99响应时间: {p99_time:.2f}ms")

            if qps >= 50000:
                print("  ✅ Redis性能优秀")
            elif qps >= 20000:
                print("  ✅ Redis性能良好")
            else:
                print("  ⚠️  Redis性能有提升空间")

        print("\n" + "=" * 60)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据库性能测试")
    parser.add_argument(
        "--postgres-url",
        default="postgresql://admin:chaiguanjia2024@localhost:5432/chaiguanjia",
        help="PostgreSQL连接URL",
    )
    parser.add_argument(
        "--redis-url",
        default="redis://:chaiguanjia2024@localhost:6379/0",
        help="Redis连接URL",
    )
    parser.add_argument(
        "--test-type",
        choices=["postgres", "redis", "all"],
        default="all",
        help="测试类型",
    )

    args = parser.parse_args()

    tester = DatabasePerformanceTester(args.postgres_url, args.redis_url)

    if args.test_type == "all":
        await tester.comprehensive_database_test()
    elif args.test_type == "postgres":
        result = await tester.test_postgresql_performance()
        print(json.dumps(result, indent=2))
    elif args.test_type == "redis":
        result = await tester.test_redis_performance()
        print(json.dumps(result, indent=2))


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        sys.exit(1)
