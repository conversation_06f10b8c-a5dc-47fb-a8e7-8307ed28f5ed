#!/usr/bin/env python3
"""
系统资源监控脚本
实时监控系统资源使用情况，用于性能测试期间的资源监控
"""

import argparse
import json
import signal
import sys
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional

import psutil


class SystemResourceMonitor:
    """系统资源监控器"""

    def __init__(self, interval: float = 1.0):
        self.interval = interval
        self.monitoring = False
        self.metrics_history: List[Dict] = []
        self.start_time = None
        self.alerts_config = {
            "cpu_threshold": 70.0,
            "memory_threshold": 80.0,
            "disk_threshold": 90.0,
            "network_threshold": 100 * 1024 * 1024,  # 100MB/s
        }
        self.alerts_count = {"cpu": 0, "memory": 0, "disk": 0, "network": 0}

    def collect_system_metrics(self) -> Dict:
        """收集系统指标"""
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=None)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()

        # 内存信息
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()

        # 磁盘信息
        disk_usage = psutil.disk_usage("/")
        disk_io = psutil.disk_io_counters()

        # 网络信息
        network_io = psutil.net_io_counters()

        # 进程信息
        process_count = len(psutil.pids())

        metrics = {
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": time.time() - self.start_time if self.start_time else 0,
            # CPU指标
            "cpu": {
                "percent": cpu_percent,
                "count": cpu_count,
                "frequency_mhz": cpu_freq.current if cpu_freq else None,
                "per_core": psutil.cpu_percent(percpu=True, interval=None),
            },
            # 内存指标
            "memory": {
                "total_gb": memory.total / (1024**3),
                "available_gb": memory.available / (1024**3),
                "used_gb": memory.used / (1024**3),
                "percent": memory.percent,
                "cached_gb": getattr(memory, "cached", 0) / (1024**3),
                "buffers_gb": getattr(memory, "buffers", 0) / (1024**3),
            },
            # 交换内存
            "swap": {
                "total_gb": swap.total / (1024**3),
                "used_gb": swap.used / (1024**3),
                "percent": swap.percent,
            },
            # 磁盘指标
            "disk": {
                "total_gb": disk_usage.total / (1024**3),
                "used_gb": disk_usage.used / (1024**3),
                "free_gb": disk_usage.free / (1024**3),
                "percent": (disk_usage.used / disk_usage.total) * 100,
                "io": (
                    {
                        "read_bytes": disk_io.read_bytes if disk_io else 0,
                        "write_bytes": disk_io.write_bytes if disk_io else 0,
                        "read_count": disk_io.read_count if disk_io else 0,
                        "write_count": disk_io.write_count if disk_io else 0,
                    }
                    if disk_io
                    else {}
                ),
            },
            # 网络指标
            "network": (
                {
                    "bytes_sent": network_io.bytes_sent if network_io else 0,
                    "bytes_recv": network_io.bytes_recv if network_io else 0,
                    "packets_sent": network_io.packets_sent if network_io else 0,
                    "packets_recv": network_io.packets_recv if network_io else 0,
                    "errors_in": network_io.errin if network_io else 0,
                    "errors_out": network_io.errout if network_io else 0,
                    "drops_in": network_io.dropin if network_io else 0,
                    "drops_out": network_io.dropout if network_io else 0,
                }
                if network_io
                else {}
            ),
            # 进程信息
            "processes": {
                "count": process_count,
                "top_cpu": self._get_top_processes_by_cpu(5),
                "top_memory": self._get_top_processes_by_memory(5),
            },
        }

        return metrics

    def _get_top_processes_by_cpu(self, limit: int = 5) -> List[Dict]:
        """获取CPU使用率最高的进程"""
        try:
            processes = []
            for proc in psutil.process_iter(
                ["pid", "name", "cpu_percent", "memory_percent"]
            ):
                try:
                    proc_info = proc.info
                    if proc_info["cpu_percent"] > 0:
                        processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # 按CPU使用率排序
            processes.sort(key=lambda x: x["cpu_percent"], reverse=True)
            return processes[:limit]
        except Exception:
            return []

    def _get_top_processes_by_memory(self, limit: int = 5) -> List[Dict]:
        """获取内存使用率最高的进程"""
        try:
            processes = []
            for proc in psutil.process_iter(
                ["pid", "name", "cpu_percent", "memory_percent"]
            ):
                try:
                    proc_info = proc.info
                    if proc_info["memory_percent"] > 0:
                        processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # 按内存使用率排序
            processes.sort(key=lambda x: x["memory_percent"], reverse=True)
            return processes[:limit]
        except Exception:
            return []

    def check_alerts(self, metrics: Dict) -> List[str]:
        """检查资源使用警告"""
        alerts = []

        # CPU警告
        cpu_percent = metrics["cpu"]["percent"]
        if cpu_percent > self.alerts_config["cpu_threshold"]:
            self.alerts_count["cpu"] += 1
            alerts.append(
                f"CPU使用率过高: {cpu_percent:.1f}% (阈值: {self.alerts_config['cpu_threshold']}%)"
            )

        # 内存警告
        memory_percent = metrics["memory"]["percent"]
        if memory_percent > self.alerts_config["memory_threshold"]:
            self.alerts_count["memory"] += 1
            alerts.append(
                f"内存使用率过高: {memory_percent:.1f}% (阈值: {self.alerts_config['memory_threshold']}%)"
            )

        # 磁盘警告
        disk_percent = metrics["disk"]["percent"]
        if disk_percent > self.alerts_config["disk_threshold"]:
            self.alerts_count["disk"] += 1
            alerts.append(
                f"磁盘使用率过高: {disk_percent:.1f}% (阈值: {self.alerts_config['disk_threshold']}%)"
            )

        return alerts

    def calculate_network_speed(
        self, current_metrics: Dict, previous_metrics: Optional[Dict]
    ) -> Dict:
        """计算网络速度"""
        if not previous_metrics:
            return {"bytes_sent_per_sec": 0, "bytes_recv_per_sec": 0}

        time_diff = (
            datetime.fromisoformat(current_metrics["timestamp"])
            - datetime.fromisoformat(previous_metrics["timestamp"])
        ).total_seconds()

        if time_diff <= 0:
            return {"bytes_sent_per_sec": 0, "bytes_recv_per_sec": 0}

        current_net = current_metrics["network"]
        previous_net = previous_metrics["network"]

        bytes_sent_per_sec = (
            current_net["bytes_sent"] - previous_net["bytes_sent"]
        ) / time_diff
        bytes_recv_per_sec = (
            current_net["bytes_recv"] - previous_net["bytes_recv"]
        ) / time_diff

        return {
            "bytes_sent_per_sec": bytes_sent_per_sec,
            "bytes_recv_per_sec": bytes_recv_per_sec,
            "total_speed_mbps": (bytes_sent_per_sec + bytes_recv_per_sec)
            / (1024 * 1024),
        }

    def start_monitoring(
        self,
        duration: Optional[float] = None,
        output_file: Optional[str] = None,
        real_time: bool = False,
    ):
        """开始监控"""
        self.monitoring = True
        self.start_time = time.time()
        self.metrics_history = []

        print(f"开始系统资源监控 (间隔: {self.interval}秒)")
        if duration:
            print(f"监控时长: {duration}秒")
        if output_file:
            print(f"输出文件: {output_file}")
        print("按 Ctrl+C 停止监控")
        print("-" * 60)

        try:
            end_time = time.time() + duration if duration else None

            while self.monitoring:
                if end_time and time.time() > end_time:
                    break

                # 收集指标
                metrics = self.collect_system_metrics()

                # 计算网络速度
                if self.metrics_history:
                    network_speed = self.calculate_network_speed(
                        metrics, self.metrics_history[-1]
                    )
                    metrics["network_speed"] = network_speed

                self.metrics_history.append(metrics)

                # 检查警告
                alerts = self.check_alerts(metrics)

                # 实时显示
                if real_time:
                    self._print_real_time_metrics(metrics, alerts)

                # 定期保存
                if output_file and len(self.metrics_history) % 10 == 0:
                    self._save_metrics_to_file(output_file)

                time.sleep(self.interval)

        except KeyboardInterrupt:
            print("\n监控被用户中断")

        finally:
            self.monitoring = False

            # 保存最终结果
            if output_file:
                self._save_metrics_to_file(output_file)

            # 生成摘要报告
            self.generate_summary_report()

    def _print_real_time_metrics(self, metrics: Dict, alerts: List[str]):
        """实时显示指标"""
        uptime = int(metrics["uptime_seconds"])
        cpu_percent = metrics["cpu"]["percent"]
        memory_percent = metrics["memory"]["percent"]
        disk_percent = metrics["disk"]["percent"]

        # 清除屏幕并移动光标到顶部 (ANSI转义序列)
        sys.stdout.write("\033[2J\033[H")

        print(
            f"系统资源监控 - 运行时间: {uptime//3600:02d}:{(uptime%3600)//60:02d}:{uptime%60:02d}"
        )
        print("=" * 60)
        print(f"CPU:    {cpu_percent:6.1f}% {'⚠️' if cpu_percent > 70 else '✅'}")
        print(f"内存:   {memory_percent:6.1f}% {'⚠️' if memory_percent > 80 else '✅'}")
        print(f"磁盘:   {disk_percent:6.1f}% {'⚠️' if disk_percent > 90 else '✅'}")

        if "network_speed" in metrics:
            speed_mbps = metrics["network_speed"]["total_speed_mbps"]
            print(f"网络:   {speed_mbps:6.1f} MB/s")

        print("-" * 60)

        # 显示Top进程
        top_cpu = metrics["processes"]["top_cpu"][:3]
        if top_cpu:
            print("Top CPU进程:")
            for proc in top_cpu:
                print(f"  {proc['name'][:20]:20} {proc['cpu_percent']:6.1f}%")

        print("-" * 60)

        # 显示警告
        if alerts:
            print("⚠️ 资源警告:")
            for alert in alerts:
                print(f"  {alert}")
        else:
            print("✅ 系统资源正常")

        print("-" * 60)
        print(
            f"总警告次数: CPU:{self.alerts_count['cpu']} 内存:{self.alerts_count['memory']} 磁盘:{self.alerts_count['disk']}"
        )

        sys.stdout.flush()

    def _save_metrics_to_file(self, filename: str):
        """保存指标到文件"""
        try:
            data = {
                "start_time": self.start_time,
                "interval": self.interval,
                "alerts_config": self.alerts_config,
                "alerts_count": self.alerts_count,
                "metrics": self.metrics_history,
            }

            with open(filename, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"保存文件失败: {e}")

    def generate_summary_report(self):
        """生成摘要报告"""
        if not self.metrics_history:
            return

        print("\n" + "=" * 60)
        print("系统资源监控摘要报告")
        print("=" * 60)

        # 基本信息
        total_duration = self.metrics_history[-1]["uptime_seconds"]
        sample_count = len(self.metrics_history)

        print(
            f"监控时长: {int(total_duration//3600)}小时{int((total_duration%3600)//60)}分{int(total_duration%60)}秒"
        )
        print(f"采样次数: {sample_count}")
        print(f"采样间隔: {self.interval}秒")
        print()

        # CPU统计
        cpu_values = [m["cpu"]["percent"] for m in self.metrics_history]
        print(f"🖥️  CPU使用率:")
        print(f"  平均值: {sum(cpu_values)/len(cpu_values):.1f}%")
        print(f"  最大值: {max(cpu_values):.1f}%")
        print(f"  最小值: {min(cpu_values):.1f}%")
        print(f"  超过70%次数: {self.alerts_count['cpu']}")

        # 内存统计
        memory_values = [m["memory"]["percent"] for m in self.metrics_history]
        print(f"\n💾 内存使用率:")
        print(f"  平均值: {sum(memory_values)/len(memory_values):.1f}%")
        print(f"  最大值: {max(memory_values):.1f}%")
        print(f"  最小值: {min(memory_values):.1f}%")
        print(f"  超过80%次数: {self.alerts_count['memory']}")

        # 磁盘统计
        disk_values = [m["disk"]["percent"] for m in self.metrics_history]
        print(f"\n💿 磁盘使用率:")
        print(f"  平均值: {sum(disk_values)/len(disk_values):.1f}%")
        print(f"  最大值: {max(disk_values):.1f}%")
        print(f"  最小值: {min(disk_values):.1f}%")
        print(f"  超过90%次数: {self.alerts_count['disk']}")

        # 网络统计
        if len(self.metrics_history) > 1:
            network_speeds = [
                m.get("network_speed", {}).get("total_speed_mbps", 0)
                for m in self.metrics_history
                if "network_speed" in m
            ]
            if network_speeds:
                print(f"\n🌐 网络速度:")
                print(f"  平均速度: {sum(network_speeds)/len(network_speeds):.2f} MB/s")
                print(f"  最大速度: {max(network_speeds):.2f} MB/s")

        # 性能评估
        print(f"\n📊 性能评估:")

        # CPU评估
        avg_cpu = sum(cpu_values) / len(cpu_values)
        max_cpu = max(cpu_values)
        if max_cpu < 70 and avg_cpu < 50:
            print("  ✅ CPU性能良好")
        elif max_cpu < 80 and avg_cpu < 60:
            print("  ⚠️  CPU性能一般")
        else:
            print("  ❌ CPU性能不佳，可能需要优化")

        # 内存评估
        avg_memory = sum(memory_values) / len(memory_values)
        max_memory = max(memory_values)
        if max_memory < 80 and avg_memory < 60:
            print("  ✅ 内存使用良好")
        elif max_memory < 90 and avg_memory < 70:
            print("  ⚠️  内存使用一般")
        else:
            print("  ❌ 内存使用过高，可能需要优化")

        # 稳定性评估
        cpu_variance = sum((x - avg_cpu) ** 2 for x in cpu_values) / len(cpu_values)
        memory_variance = sum((x - avg_memory) ** 2 for x in memory_values) / len(
            memory_values
        )

        if cpu_variance < 100 and memory_variance < 100:
            print("  ✅ 系统运行稳定")
        elif cpu_variance < 300 and memory_variance < 300:
            print("  ⚠️  系统运行较稳定")
        else:
            print("  ❌ 系统运行不稳定，资源使用波动较大")

        print("=" * 60)

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False


def signal_handler(signum, frame):
    """信号处理器"""
    print("\n收到停止信号，正在停止监控...")
    monitor.stop_monitoring()
    sys.exit(0)


# 全局监控器实例
monitor = SystemResourceMonitor()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="系统资源监控工具")
    parser.add_argument(
        "--interval", type=float, default=1.0, help="监控间隔(秒) (默认: 1.0)"
    )
    parser.add_argument("--duration", type=float, help="监控时长(秒) (默认: 无限制)")
    parser.add_argument("--output", "-o", help="输出文件路径")
    parser.add_argument("--real-time", action="store_true", help="实时显示资源使用情况")
    parser.add_argument(
        "--cpu-threshold", type=float, default=70.0, help="CPU警告阈值 (默认: 70%)"
    )
    parser.add_argument(
        "--memory-threshold", type=float, default=80.0, help="内存警告阈值 (默认: 80%)"
    )
    parser.add_argument(
        "--disk-threshold", type=float, default=90.0, help="磁盘警告阈值 (默认: 90%)"
    )

    args = parser.parse_args()

    # 配置监控器
    global monitor
    monitor = SystemResourceMonitor(args.interval)
    monitor.alerts_config.update(
        {
            "cpu_threshold": args.cpu_threshold,
            "memory_threshold": args.memory_threshold,
            "disk_threshold": args.disk_threshold,
        }
    )

    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 开始监控
    try:
        monitor.start_monitoring(
            duration=args.duration, output_file=args.output, real_time=args.real_time
        )
    except Exception as e:
        print(f"监控过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
