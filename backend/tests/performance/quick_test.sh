#!/bin/bash
# 快速性能测试脚本

set -e

echo "=============================="
echo "柴管家系统快速性能测试"
echo "=============================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查依赖
echo "检查Python依赖..."
if ! python3 -c "import psutil, aiohttp, requests" 2>/dev/null; then
    echo "⚠️  缺少必要依赖，正在安装..."
    pip3 install psutil aiohttp requests asyncpg aioredis
fi

# 检查API服务
echo "检查API服务状态..."
API_URL=${API_URL:-"http://localhost:8000"}
if curl -s "$API_URL/health/" > /dev/null; then
    echo "✅ API服务运行正常"
else
    echo "❌ API服务不可用，请启动服务后重试"
    exit 1
fi

# 创建结果目录
RESULTS_DIR="./quick_test_results"
mkdir -p "$RESULTS_DIR"

echo "开始性能测试..."
echo "结果将保存到: $RESULTS_DIR"
echo ""

# 1. 快速API基准测试
echo "🚀 运行API基准测试..."
python3 benchmark_test.py --host "$API_URL" &
API_PID=$!

# 2. 系统监控
echo "📊 启动系统监控..."
python3 system_monitor.py --duration 120 --output "$RESULTS_DIR/system_monitor.json" &
MONITOR_PID=$!

# 等待API测试完成
wait $API_PID
echo "✅ API基准测试完成"

# 3. 快速数据库测试（如果可用）
echo "🗄️ 运行数据库快速测试..."
if python3 database_performance_test.py --test-type postgres 2>/dev/null; then
    echo "✅ PostgreSQL测试完成"
else
    echo "⚠️  PostgreSQL测试跳过（连接失败）"
fi

if python3 database_performance_test.py --test-type redis 2>/dev/null; then
    echo "✅ Redis测试完成"
else
    echo "⚠️  Redis测试跳过（连接失败）"
fi

# 等待监控完成
wait $MONITOR_PID
echo "✅ 系统监控完成"

echo ""
echo "=============================="
echo "快速测试完成！"
echo "=============================="
echo "查看结果："
echo "- API测试结果: benchmark_results_*.json"
echo "- 数据库测试结果: database_performance_results_*.json"
echo "- 系统监控结果: $RESULTS_DIR/system_monitor.json"
echo ""
echo "如需完整测试，请运行："
echo "python3 run_performance_tests.py"
