#!/bin/bash

# 柴管家开发环境快速启动脚本
# 简化版启动脚本，用于快速启动开发环境

set -e

echo "🚀 启动柴管家开发环境..."

# 检查 .env 文件
if [ ! -f ".env" ]; then
    echo "📋 创建环境配置文件..."
    cp env.example .env
    echo "✅ 已创建 .env 文件，请根据需要修改配置"
fi

# 创建必要目录
echo "📁 创建必要目录..."
mkdir -p {database/{init,seeds/development},infrastructure/{nginx/conf.d,nginx/ssl,redis,rabbitmq,elasticsearch},logs,backups}

# 启动服务
echo "🐳 启动 Docker 容器..."
docker-compose up -d

echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

echo ""
echo "🎉 开发环境启动完成！"
echo ""
echo "📱 服务访问地址："
echo "   前端应用:          http://localhost:3000"
echo "   后端API文档:       http://localhost:8000/docs"
echo "   PostgreSQL:        localhost:5432"
echo "   Redis:             localhost:6379"
echo "   RabbitMQ管理:      http://localhost:15672"
echo "   pgAdmin:           http://localhost:5050"
echo "   MailHog:           http://localhost:8025"
echo ""
echo "📚 更多管理命令请运行: ./scripts/docker/start.sh help"
echo ""
echo "🛠️  初始化数据库请运行: ./scripts/docker/init-db.sh"
